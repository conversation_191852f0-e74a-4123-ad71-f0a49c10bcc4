codecov:
  url: "https://codecov.agodadev.io"
  notify:
    require_ci_to_pass: no
    wait_for_ci: no

coverage:
  precision: 2
  round: down
  range: 60...100

  status:
    project:
      default:
        target: 80%

    patch:
      default:
        target: 75%

ignore:
  #Downloader
  - "**/Main.scala"
  - "**/CleanUpProcessor.scala"
  - "**/DownloaderProcessor.scala"
  - "**/GroupInstancesProcessor.scala"
  - "**/Tracker.scala"
  #Uploader
  - "**/UploaderSlacker.scala"
  - "**/UploaderProcessor.scala" # will remove when have integration test
  - "**/Uploader.scala"
  - "**/UploaderPostProcessing.scala"
  #Normalizer
  - "**/FunctionMapper.scala"
  - "**/MirrorUtils.scala"
  - "**/UploaderHttpClient.scala"
  - "**/ProcessedContractPayment.scala"
  - "**/PreprocessedAgpSettlement.scala"
