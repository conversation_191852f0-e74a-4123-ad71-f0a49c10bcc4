include "settings.conf"

configUploader.jobFrequency = Weekly

configUploader.destinationsConfig = [
	{
		reportParameters = {"report_schema": "finance_uploader_staging", "supplier_id": "30011", "supplier_name": "TBO"},
		destinations = {"email": ["tboEmailConfig"]},
		instanceName = "30011",
	},
	{
		reportParameters = {"report_schema": "finance_uploader_staging", "supplier_id": "30084", "supplier_name": "EMT"},
		destinations = {"email": ["emtEmailConfig"]},
		instanceName = "30084",
	}
]

configUploader.tboEmailConfig = {
	subjectName = "TBO weekly unreconciled report"
	htmlBody = """<p><h1>TBO weekly unreconciled report</h1></p>"""
	sender = "<EMAIL>"
	recipients = "<EMAIL>"
	mimeType = "text/xlsx"
	hasTable = true
}

configUploader.emtEmailConfig = {
	subjectName = "EMT weekly unreconciled report"
	htmlBody = """<p><h1>EMT weekly unreconciled report</h1></p>"""
	sender = "<EMAIL>"
	recipients = "<EMAIL>"
	mimeType = "text/xlsx"
	hasTable = true
}

configUploader.enrichDFConfig {
	dropColumns = [] //override to show is_india_domestic column
}