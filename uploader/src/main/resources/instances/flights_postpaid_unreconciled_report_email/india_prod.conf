include classpath("_generic_prod.conf")
include "india_settings.conf"

database = "finance_uploader"

hadoop {
	hdfs.user = "hk-fin-supl-prod-svc"
	knox.enabled = true
	hive.username = "hk-fin-supl-prod-svc"
	credentials = "hadoop/hk-fin-supl-prod-svc/credentials"
}

slack-config.channel-id = "fintech-funnels"

configUploader.destinationsConfig = [
	{
		reportParameters = {"report_schema": "finance_uploader", "supplier_id": "30011", "supplier_name": "TBO"},
		destinations = {"email": ["tboEmailConfig"]},
		instanceName = "30011",
	},
	{
		reportParameters = {"report_schema": "finance_uploader", "supplier_id": "30084", "supplier_name": "EMT"},
		destinations = {"email": ["emtEmailConfig"]},
		instanceName = "30084",
	}
]

configUploader.emtEmailConfig.recipients = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
configUploader.tboEmailConfig.recipients = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"