app-name = "flights_postpaid_unreconciled_report_email"
database = "finance_uploader_staging"
hadoop.spark {
	executor {
		count = 15
		cores-per-executor = 6
		memory-per-executor = 20GB
	}
}
configUploader {
	jobFrequency: Daily
	dateFormat = "yyyyMMdd"
	filePath = /user/${hadoop.hdfs.user}/apps/${project-name}/${app-name}/

	queryInfos = [
		{
			queryName = "Reconcile",
			include "query.conf",
			fileName = "{supplier_name}_flight_postpaid_unreconciled_{date}",
			fileType = "xlsx"
		}
	]

	destinationsConfig = [
		{
			reportParameters = {"report_schema": "finance_uploader_staging", "supplier_id": "30038", "supplier_name": "JejuAir"},
			destinations = {"email": ["jejuEmailConfig"]},
			instanceName = "30038",
		},
		{
			reportParameters = {"report_schema": "finance_uploader_staging", "supplier_id": "30018", "supplier_name": "PKFare"},
			destinations = {"email": ["pkfareEmailConfig"]},
			instanceName = "30018",
		},
		{
			reportParameters = {"report_schema": "finance_uploader_staging", "supplier_id": "30065", "supplier_name": "ScootNDC"},
			destinations = {"email": ["scootndcEmailConfig"]},
			instanceName = "30065",
		},
		{
			reportParameters = {"report_schema": "finance_uploader_staging", "supplier_id": "30027", "supplier_name": "Scoot"},
			destinations = {"email": ["scootEmailConfig"]},
			instanceName = "30027",
		},
		{
			reportParameters = {"report_schema": "finance_uploader_staging", "supplier_id": "30085", "supplier_name": "Unififi"},
			destinations = {"email": ["unififiEmailConfig"]},
			instanceName = "30085",
		},
		{
			reportParameters = {"report_schema": "finance_uploader_staging", "supplier_id": "30013", "supplier_name": "Airtrip"},
			destinations = {"email": ["airtripEmailConfig"]},
			instanceName = "30013",
		},
		{
			reportParameters = {"report_schema": "finance_uploader_staging", "supplier_id": "30086", "supplier_name": "Flightroutes24"},
			destinations = {"email": ["fr24EmailConfig"]},
			instanceName = "30086",
		},
		{
			reportParameters = {"report_schema": "finance_uploader_staging", "supplier_id": "30091", "supplier_name": "DidaDC"},
			destinations = {"email": ["didaEmailConfig"]},
			instanceName = "30091",
		}
	]

	enrichDFConfig {
		dropColumns = ["domestic/international"] //drop column is_india_domestic
	}

	saveFile {
		checkEmptyFile = true
		xlsxConf = {
			needHeader = true
			footer = {
				0: { type: "STATIC", value: "Count transaction"},
				2: { type: "COUNT_DISTINCT", column: "PNR"},
				11: { type: "SUM", column: "Difference"},
				12: { type: "SUM", column: "Difference USD"},
				13: { type: "STATIC", value: "Total Difference"},
			}
		}
	}

	jejuEmailConfig = {
		subjectName = "Jeju bi-weekly unreconciled report"
		htmlBody = """<p><h1>JejuAir bi-weekly unreconciled report</h1></p>"""
		sender = "<EMAIL>"
		recipients = "<EMAIL>"
		mimeType = "text/xlsx"
		hasTable = true
	}

	pkfareEmailConfig = {
		subjectName = "PKFare unreconciled report"
		htmlBody = """<p><h1>PKFare unreconciled report</h1></p>"""
		sender = "<EMAIL>"
		recipients = "<EMAIL>"
		mimeType = "text/xlsx"
		hasTable = true
	}

	scootndcEmailConfig = {
		subjectName = "ScootNDC weekly unreconciled report"
		htmlBody = """<p><h1>ScootNDC weekly unreconciled report</h1></p>"""
		sender = "<EMAIL>"
		recipients = "<EMAIL>"
		mimeType = "text/xlsx"
		hasTable = true
	}

	scootEmailConfig = {
		subjectName = "Scoot weekly unreconciled report"
		htmlBody = """<p><h1>Scoot weekly unreconciled report</h1></p>"""
		sender = "<EMAIL>"
		recipients = "<EMAIL>"
		mimeType = "text/xlsx"
		hasTable = true
	}

	unififiEmailConfig = {
		subjectName = "Unififi weekly unreconciled report"
		htmlBody = """<p><h1>Unififi weekly unreconciled report</h1></p>"""
		sender = "<EMAIL>"
		recipients = "<EMAIL>"
		mimeType = "text/xlsx"
		hasTable = true
	}

	airtripEmailConfig = {
		subjectName = "Airtrip weekly unreconciled report"
		htmlBody = """<p><h1>Airtrip weekly unreconciled report</h1></p>"""
		sender = "<EMAIL>"
		recipients = "<EMAIL>"
		mimeType = "text/xlsx"
		hasTable = true
	}

	fr24EmailConfig = {
		subjectName = "Flightroutes24 weekly unreconciled report"
		htmlBody = """<p><h1>Flightroutes24 weekly unreconciled report</h1></p>"""
		sender = "<EMAIL>"
		recipients = "<EMAIL>"
		mimeType = "text/xlsx"
		hasTable = true
	}

	didaEmailConfig = {
		subjectName = "Dida unreconciled report"
		htmlBody = """<p><h1>Dida unreconciled report</h1></p>"""
		sender = "<EMAIL>"
		recipients = "<EMAIL>"
		mimeType = "text/xlsx"
		hasTable = true
	}

	deleteHdfsFiles = false
}

result {
	mail {
		alert-sender = "<EMAIL> (<EMAIL>)"
		alert-recipients = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
	}
}