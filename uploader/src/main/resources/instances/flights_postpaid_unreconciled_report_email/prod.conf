include classpath("_generic_prod.conf")
include "settings.conf"

database = "finance_uploader"

hadoop {
	hdfs.user = "hk-fin-supl-prod-svc"
	knox.enabled = true
	hive.username = "hk-fin-supl-prod-svc"
	credentials = "hadoop/hk-fin-supl-prod-svc/credentials"
}

slack-config.channel-id = "fintech-funnels"

configUploader.destinationsConfig = [
	{
		reportParameters = {"report_schema": "finance_uploader", "supplier_id": "30038", "supplier_name": "JejuAir"},
		destinations = {"email": ["jejuEmailConfig"]},
		instanceName = "30038",
	},
	{
		reportParameters = {"report_schema": "finance_uploader", "supplier_id": "30018", "supplier_name": "PKFare"},
		destinations = {"email": ["pkfareEmailConfig"]},
		instanceName = "30018",
	},
	{
		reportParameters = {"report_schema": "finance_uploader", "supplier_id": "30065", "supplier_name": "ScootNDC"},
		destinations = {"email": ["scootndcEmailConfig"]},
		instanceName = "30065",
	},
	{
		reportParameters = {"report_schema": "finance_uploader", "supplier_id": "30027", "supplier_name": "Scoot"},
		destinations = {"email": ["scootEmailConfig"]},
		instanceName = "30027",
	},
	{
		reportParameters = {"report_schema": "finance_uploader", "supplier_id": "30085", "supplier_name": "Unififi"},
		destinations = {"email": ["unififiEmailConfig"]},
		instanceName = "30085",
	},
	{
		reportParameters = {"report_schema": "finance_uploader", "supplier_id": "30013", "supplier_name": "Airtrip"},
		destinations = {"email": ["airtripEmailConfig"]},
		instanceName = "30013",
	},
	{
		reportParameters = {"report_schema": "finance_uploader", "supplier_id": "30086", "supplier_name": "Flightroutes24"},
		destinations = {"email": ["fr24EmailConfig"]},
		instanceName = "30086",
	},
	{
		reportParameters = {"report_schema": "finance_uploader", "supplier_id": "30091", "supplier_name": "DidaDC"},
		destinations = {"email": ["didaEmailConfig"]},
		instanceName = "30091",
	}
]

configUploader.jejuEmailConfig.recipients = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
configUploader.pkfareEmailConfig.recipients = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
configUploader.scootndcEmailConfig.recipients = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
configUploader.scootEmailConfig.recipients = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
configUploader.unififiEmailConfig.recipients = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
configUploader.airtripEmailConfig.recipients = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
configUploader.fr24EmailConfig.recipients = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
configUploader.didaEmailConfig.recipients = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
