query = """
WITH latest_validation AS (
    SELECT
        booking_id,
        datamonth,
        transaction_type,
        common_affiliate_name,
        description,
        validation_status,
        validation_datetime,
        ROW_NUMBER() OVER (
            PARTITION BY booking_id, datamonth, transaction_type, common_affiliate_name
            ORDER BY
                CASE WHEN validation_datetime IS NULL THEN 1 ELSE 0 END,
                validation_datetime DESC
        ) AS rn
    FROM {db_name}.affiliate_invalid_transactions
    WHERE
        datamonth = CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT)
        AND common_affiliate_name = '{partner_name}'
)
, joined_flight_transactions AS (
    SELECT
        ft.*,
        COALESCE(lv.validation_status, 'pass') AS validation_status,
        COALESCE(lv.description, 'No validation issues found') AS validation_description,
        lv.validation_datetime
    FROM {db_name}.flight_transactions ft
    LEFT JOIN latest_validation lv
        ON ft.booking_id = lv.booking_id
        AND ft.datamonth = lv.datamonth
        AND ft.transaction_type = lv.transaction_type
        AND ft.common_affiliate_name = lv.common_affiliate_name
        AND lv.rn = 1
    WHERE
        ft.datamonth = CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT)
        AND ft.common_affiliate_name = '{partner_name}'
        AND ft.transaction_type = '{transaction_type}'
        AND ft.affiliate_id = '{affiliate_id}'
)
, accrual_missing_in_payment AS (
    -- Accruals that are missing in payment (for payment run)
    SELECT
        ft.*,
        COALESCE(lv.validation_status, 'pass') AS validation_status,
        COALESCE(lv.description, 'No validation issues found') AS validation_description,
        lv.validation_datetime
    FROM {db_name}.flight_transactions ft
    JOIN latest_validation lv
        ON ft.booking_id = lv.booking_id
        AND ft.datamonth = lv.datamonth
        AND lv.transaction_type = 'payment'
        AND lv.description LIKE '%Missing in payment but existing in accrual%'
        AND ft.common_affiliate_name = lv.common_affiliate_name
    WHERE
        ft.datamonth = CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT)
        AND ft.common_affiliate_name = '{partner_name}'
        AND ft.transaction_type = 'accrual'
        AND ft.affiliate_id = '{affiliate_id}'
)
SELECT
    booking_id,
    affiliate_id,
    affiliate_name,
    site_id,
    site_name,
    itinerary_id,
    flight_state,
    is_cancelled,
    tag,
    booking_date,
    trip_start_date,
    trip_end_date,
    origin_country_name,
    trip_destination_country_name,
    original_selling_amount_exclusive,
    supplier_amount,
    original_selling_amount,
    tax_amount,
    supplier_basefare_amount,
    commission_base,
    booking_status,
    payment_method,
    accounting_date,
    reporting_date,
    commission_rate,
    commission_amount,
    validation_status,
    validation_description,
    validation_datetime,
    common_affiliate_name,
    transaction_type,
    datamonth
FROM joined_flight_transactions

UNION ALL

SELECT
    booking_id,
    affiliate_id,
    affiliate_name,
    site_id,
    site_name,
    itinerary_id,
    flight_state,
    is_cancelled,
    tag,
    booking_date,
    trip_start_date,
    trip_end_date,
    origin_country_name,
    trip_destination_country_name,
    original_selling_amount_exclusive,
    supplier_amount,
    original_selling_amount,
    tax_amount,
    supplier_basefare_amount,
    commission_base,
    booking_status,
    payment_method,
    accounting_date,
    reporting_date,
    commission_rate,
    NULL AS commission_amount,  -- Set to NULL for missing in payment
    validation_status,
    validation_description,
    validation_datetime,
    common_affiliate_name,
    transaction_type,
    datamonth
FROM accrual_missing_in_payment
WHERE '{transaction_type}' = 'payment'
"""