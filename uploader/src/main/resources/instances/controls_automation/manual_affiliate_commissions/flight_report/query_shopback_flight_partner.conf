query = """
with grouped_validation as (
    select
        booking_id,
        datamonth,
        transaction_type
    from {db_name}.affiliate_invalid_transactions
    where
        datamonth = CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT)
        AND common_affiliate_name = 'ana hotel'
        AND transaction_type = '{transaction_type}'
)
select
    flight_t.booking_id,
    flight_t.affiliate_id as aid,
    flight_t.itinerary_id,
    flight_t.affiliate_name as affiliate,
    flight_t.site_id as site_id,
    flight_t.flight_state as flight_state,
    flight_t.is_cancelled as is_cancelled,
    flight_t.tag,
    flight_t.booking_date,
    flight_t.trip_start_date,
    flight_t.trip_end_date,
    flight_t.origin_country_name,
    flight_t.trip_destination_country_name as destination,
    flight_t.original_selling_amount_exclusive as original_selling_amount_exclusive,
    flight_t.commission_rate,
    flight_t.commission_amount as commission
from
    {db_name}.flight_transactions flight_t
    left join grouped_validation gv on flight_t.booking_id = gv.booking_id
    and flight_t.datamonth = gv.datamonth
    and flight_t.transaction_type = gv.transaction_type
where
    flight_t.datamonth = CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT)
    and flight_t.transaction_type = '{transaction_type}'
    and flight_t.common_affiliate_name = '{partner_name}'
    and flight_t.affiliate_id = '{affiliate_id}'
    and gv.booking_id IS NULL;
"""