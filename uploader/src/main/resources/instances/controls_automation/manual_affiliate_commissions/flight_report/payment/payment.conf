transaction_type="payment"

configUploader {
    queryInfos = [
        {
          queryName = "Flight_Query"
          include "/instances/controls_automation/manual_affiliate_commissions/flight_report/query_shopback_flight_internal.conf"
          fileName= "{transaction_type}-reports/{partner_name}_{affiliate_id}_affiliate_commission_{transaction_type}_{year}{month}{day}",
          fileType = "csv",
          filePathPrefix = ${conaut_sharepoint_site_url}"/"${destination_path}"/",
          filePathSuffix = ".csv"${sharepoint_file_path_suffix}
        },
        {
          queryName = "SharedReport_Query"
          include "/instances/controls_automation/manual_affiliate_commissions/flight_report/query_shopback_flight_partner.conf"
          fileName= "partner_reports/{partner_name}_{affiliate_id}_affiliate_commission_partner_{year}{month}{day}",
          fileType = "csv",
          filePathPrefix = ${conaut_sharepoint_site_url}"/"${destination_path}"/",
          filePathSuffix = ".csv"${sharepoint_file_path_suffix}
        }
    ]
}