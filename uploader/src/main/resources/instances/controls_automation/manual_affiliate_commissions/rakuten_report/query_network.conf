query = """
WITH latest_validation AS (
    SELECT
        booking_id,
        datamonth,
        transaction_type,
        common_affiliate_name,
        description,
        validation_status,
        validation_datetime,
        ROW_NUMBER() OVER (
            PARTITION BY booking_id, datamonth, transaction_type, common_affiliate_name
            ORDER BY
                CASE WHEN validation_datetime IS NULL THEN 1 ELSE 0 END,
                validation_datetime DESC
        ) AS rn
    FROM {db_name}.affiliate_invalid_transactions
    WHERE
        datamonth = CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT)
        AND common_affiliate_name = 'rakuten'
)
, joined_transactions AS (
    SELECT
        npt.*,
        COALESCE(lv.validation_status, 'pass') AS validation_status,
        COALESCE(lv.description, 'No validation issues found') AS validation_description,
        lv.validation_datetime,
        pr.order_id AS order_id_partner,
        pr.sku AS sku_partner,
        pr.sales AS sales_partner,
        pr.total_commission AS total_commission_partner,
        pr.commission_rate AS commission_rate_partner,
        pr.override_fee_rate AS override_fee_rate_partner,
        pr.overrider_fee AS override_fee_partner,
        pr.total_expected_payment AS total_expected_payment_partner,
        pr.overall_commission_fee_rate AS overall_commission_fee_rate_partner,
        pr.currency AS currency_partner,
        pr.datamonth AS datamonth_partner
    FROM {db_name}.network_partner_transactions npt
    FULL OUTER JOIN {db_name}.rakuten_report pr
        ON npt.booking_id = pr.order_id
        AND npt.datamonth = pr.datamonth
    LEFT JOIN latest_validation lv
        ON npt.booking_id = lv.booking_id
        AND npt.datamonth = lv.datamonth
        AND npt.transaction_type = lv.transaction_type
        AND npt.common_affiliate_name = lv.common_affiliate_name
        AND lv.rn = 1
    WHERE
        npt.datamonth = CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT)
        AND npt.common_affiliate_name = 'rakuten'
        AND npt.transaction_type = '{transaction_type}'
)
, accrual_missing_in_payment AS (
    -- Accruals that are missing in payment (for payment run)
    SELECT
        npt.*,
        COALESCE(lv.validation_status, 'pass') AS validation_status,
        COALESCE(lv.description, 'No validation issues found') AS validation_description,
        lv.validation_datetime,
        NULL AS order_id_partner,
        NULL AS sku_partner,
        NULL AS sales_partner,
        NULL AS total_commission_partner,
        NULL AS commission_rate_partner,
        NULL AS override_fee_rate_partner,
        NULL AS override_fee_partner,
        NULL AS total_expected_payment_partner,
        NULL AS overall_commission_fee_rate_partner,
        NULL AS currency_partner,
        NULL AS datamonth_partner
    FROM {db_name}.network_partner_transactions npt
    JOIN latest_validation lv
        ON npt.booking_id = lv.booking_id
        AND npt.datamonth = lv.datamonth
        AND lv.transaction_type = 'payment'
        AND lv.description LIKE '%Missing in payment but existing in accrual%'
        AND npt.common_affiliate_name = lv.common_affiliate_name
    WHERE
        npt.datamonth = CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT)
        AND npt.common_affiliate_name = 'rakuten'
        AND npt.transaction_type = 'accrual'
)
SELECT
    booking_id,
    affiliate_id,
    affiliate_name,
    site_id,
    site_name,
    booking_date,
    booking_status,
    cancellation_date,
    checkin_date,
    checkout_date,
    commission_base,
    country_name,
    cta_before_tax_after_discount,
    discount_amount,
    hotel_id,
    regexp_replace(regexp_replace(hotel_name, "''", '"'), '"', '') as hotel_name,
    margin,
    origin_country_name,
    payment_method,
    partner_currency,
    selling_amount,
    affiliate_commission,
    tag,
    accounting_date,
    reporting_date,
    commission_amount,
    commission_amount_partner_currency,
    commission_base_partner_currency,
    fx_rate_amount_usd,
    commission_rate,
    validation_status,
    validation_description,
    validation_datetime,
    common_affiliate_name,
    transaction_type,
    accrued_datamonth,
    datamonth,
    -- Partner report columns
    order_id_partner,
    sku_partner,
    sales_partner,
    total_commission_partner,
    commission_rate_partner,
    override_fee_rate_partner,
    override_fee_partner,
    total_expected_payment_partner,
    overall_commission_fee_rate_partner,
    currency_partner,
    datamonth_partner
FROM joined_transactions

UNION ALL

SELECT
    booking_id,
    affiliate_id,
    affiliate_name,
    site_id,
    site_name,
    booking_date,
    booking_status,
    cancellation_date,
    checkin_date,
    checkout_date,
    commission_base,
    country_name,
    cta_before_tax_after_discount,
    discount_amount,
    hotel_id,
    regexp_replace(regexp_replace(hotel_name, "''", '"'), '"', '') as hotel_name,
    margin,
    origin_country_name,
    payment_method,
    partner_currency,
    selling_amount,
    affiliate_commission,
    tag,
    accounting_date,
    reporting_date,
    NULL AS commission_amount,  -- Set to NULL for missing in payment
    NULL AS commission_amount_partner_currency,  -- Set to NULL for missing in payment
    commission_base_partner_currency,
    fx_rate_amount_usd,
    commission_rate,
    validation_status,
    validation_description,
    validation_datetime,
    common_affiliate_name,
    transaction_type,
    accrued_datamonth,
    datamonth,
    -- Partner report columns (all NULL)
    NULL AS order_id_partner,
    NULL AS sku_partner,
    NULL AS sales_partner,
    NULL AS total_commission_partner,
    NULL AS commission_rate_partner,
    NULL AS override_fee_rate_partner,
    NULL AS override_fee_partner,
    NULL AS total_expected_payment_partner,
    NULL AS overall_commission_fee_rate_partner,
    NULL AS currency_partner,
    NULL AS datamonth_partner
FROM accrual_missing_in_payment
WHERE '{transaction_type}' = 'payment'
"""