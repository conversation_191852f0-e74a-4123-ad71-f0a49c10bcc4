# Root logger option
log4j.rootLogger=INFO, console, centrallog
# Direct log messages to a log file
log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n

log4j.appender.centrallog=com.agoda.adp.messaging.logging.appenders.Log4j12Appender
log4j.appender.centrallog.Threshold = INFO
# Your application name - REQUIRED FIELD
log4j.appender.centrallog.applicationName=conaut_affiliate_commission_payment_cj_summary_report_uploader
# Your application assembly name - REQUIRED FIELD
log4j.appender.centrallog.applicationAssemblyName=assembly.jar
# Your application version - REQUIRED FIELD
log4j.appender.centrallog.applicationVersion=v1
# Your apiKey - REQUIRED FIELD (REQUEST FROM ADP TEAM)
log4j.appender.centrallog.apiKey=finance
# pragma: allowlist secret
# Optional Field (Default to false)
#log4j.appender.centrallog.sendAsync=true
# Whether to retrieve logger invocation info
# Warning this is an expensive call and will degrade application performance
# This should only be turned on in debugging scenarios
log4j.appender.centrallog.getLocationInfo=false
log4j.logger.com.agoda.finance=INFO, console, centrallog
log4j.additivity.com.agoda.finance = false
