query = """
-- =========================================
-- Affiliate Commission Self invoice payment summary report
-- =========================================
with params as (
  select
    CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT) as this_month,
    CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT) - 1 as last_month
),

-- Unified CTE for last month data (all affiliates)
main_last_month_data as (
    select
        common_affiliate_name,
        ROUND(sum(case when transaction_type = 'accrual' then agoda_commission_amount_usd else 0 end), 2) as last_month_accrual,
        ROUND(sum(case when transaction_type = 'payment' then agoda_commission_amount_usd else 0 end), 2) as last_month_payment
    from (
        select
            common_affiliate_name,
            agoda_commission_amount_usd,
            transaction_type,
            datamonth,
            row_number() over (
                partition by common_affiliate_name, transaction_type, datamonth
                order by reporting_date desc
            ) as rn
        from {db_name}.accrual_payment_summary, params
        where datamonth = params.last_month
    ) temp
    where rn = 1
    group by common_affiliate_name
),

-- Unified CTE for this month's accrual (all affiliates)
main_this_month_accrual as (
    select
        common_affiliate_name,
        ROUND(sum(agoda_commission_amount_usd), 2) as current_month_accrual
    from (
        select
            common_affiliate_name,
            agoda_commission_amount_usd,
            transaction_type,
            datamonth,
            row_number() over (
                partition by common_affiliate_name, transaction_type, datamonth
                order by reporting_date desc
            ) as rn
        from {db_name}.accrual_payment_summary, params
        where datamonth = params.this_month
          and transaction_type = 'accrual'
    ) temp
    where rn = 1
    group by common_affiliate_name
),

-- Unified CTE for current month payment data (all affiliates)
main_current_month_payment as (
    select
        concat_ws('_', collect_set(affiliate_id)) as affiliate_ids,
        concat_ws('_', collect_set(site_id)) as site_ids,
        first(common_affiliate_name) as common_affiliate_name,
        first(partner_currency) as partner_currency,
        ROUND(sum(agoda_commission_base_usd), 2) as agoda_commission_base_usd,
        ROUND(sum(agoda_commission_amount_usd), 2) as agoda_commission_amount_usd,
        sum(agoda_commission_amount_partner_currency) as agoda_commission_amount_partner_currency,
        ROUND(sum(partner_commission_amount), 2) as partner_commission,
        sum(case when agoda_commission_amount_usd is not null then 1 else 0 end) as number_of_agoda_bookings,
        sum(case when partner_commission_amount is not null then 1 else 0 end) as number_of_partner_bookings,
        sum(case when agoda_commission_amount_partner_currency is not null and partner_commission_amount is not null then 1 else 0 end) as number_of_matched_bookings,
        sum(case when agoda_commission_amount_partner_currency is not null and partner_commission_amount is not null then agoda_commission_amount_partner_currency else 0 end) as agoda_commission_on_matched_bookings,
        sum(case when agoda_commission_amount_partner_currency is not null and partner_commission_amount is not null then partner_commission_amount else 0 end) as partner_commission_on_matched_bookings,
        first(transaction_type) as transaction_type,
        now() as reporting_date,
        first(datamonth) as datamonth
    from {db_name}.final_accrual_payment_transactions, params
    where datamonth = params.this_month
      and transaction_type = 'payment'
      and common_affiliate_name not in (
        'switchfly', 'rakuten', 'cj' ,'rakuten pah', 'ixigo pah', 'tiket pah', 'dida pah'
      )
    group by common_affiliate_name, partner_currency
),

-- Switchfly special case: commission config and calculation
switchfly_latest_config as (
    select
        max(datadate) as datadate
    from {db_name}.partner_commission_config_history, params
    where cast(datadate / 100 as int) = params.this_month
),
switchfly_partner_config as (
    select
        enum.calculation_type_name,
        enum.commission_identifier,
        his.*,
        case when end_with is not null then (cast(end_with as double) - cast(start_from as double)) else null end as tiered_amount,
        cast(start_from as double) as lower_bound,
        cast(end_with as double) as upper_bound,
        cast(his.commission_rate as double) / 100.00 as com_rate
    from {db_name}.partner_commission_config_history his
    inner join {db_name}.commission_calculation_type_enum enum on his.calculation_type_id = enum.calculation_type_id
    inner join switchfly_latest_config lc on his.datadate = lc.datadate
    where his.calculation_type_id in (4)
),
switchfly_total_booking as (
    select
        concat_ws('_', collect_set(affiliate_id)) as affiliate_ids,
        concat_ws('_', collect_set(site_id)) as site_ids,
        first(common_affiliate_name) as common_affiliate_name,
        first(partner_currency) as partner_currency,
        ROUND(sum(agoda_commission_base_usd), 2) as agoda_commission_base_usd,
        ROUND(sum(agoda_commission_amount_usd), 2) as agoda_commission_amount_usd,
        sum(agoda_commission_amount_partner_currency) as agoda_commission_amount_partner_currency,
        ROUND(sum(partner_commission_amount), 2) as partner_commission,
        sum(case when agoda_commission_amount_usd is not null then 1 else 0 end) as number_of_agoda_bookings,
        sum(case when partner_commission_amount is not null then 1 else 0 end) as number_of_partner_bookings,
        sum(case when agoda_commission_amount_partner_currency is not null and partner_commission_amount is not null then 1 else 0 end) as number_of_matched_bookings,
        ROUND(sum(case when agoda_commission_amount_partner_currency is not null and partner_commission_amount is not null then agoda_commission_amount_partner_currency else 0 end), 2) as agoda_commission_on_matched_bookings,
        ROUND(sum(case when agoda_commission_amount_partner_currency is not null and partner_commission_amount is not null then partner_commission_amount else 0 end), 2) as partner_commission_on_matched_bookings,
        first(transaction_type) as transaction_type,
        now() as reporting_date,
        first(datamonth) as datamonth
    from {db_name}.final_accrual_payment_transactions, params
    where datamonth = params.this_month
      and transaction_type = 'payment'
      and common_affiliate_name = 'switchfly'
    group by common_affiliate_name, partner_currency
),
switchfly_commission as (
    select
        t.common_affiliate_name,
        ROUND(sum(commission_amount), 2) as commission
    from (
        select
            ba.common_affiliate_name,
            case
                when ba.agoda_commission_base_usd >= pc.lower_bound then
                    case
                        when (pc.upper_bound is not null and ba.agoda_commission_base_usd >= pc.upper_bound) then pc.tiered_amount * pc.com_rate
                        when (pc.upper_bound is null or ba.agoda_commission_base_usd <= pc.upper_bound) then ba.agoda_commission_base_usd - pc.lower_bound + 1
                        else 0
                    end
                else 0
            end as commission_amount
        from switchfly_partner_config pc
        inner join switchfly_total_booking ba
            on pc.common_affiliate_name = ba.common_affiliate_name
    ) t
    group by common_affiliate_name
)

-- =========================
-- Final Output: UNION ALL
-- =========================

-- 1. Main Affiliates (excluding Switchfly and others)
select
    nd.affiliate_ids,
    nd.site_ids,
    nd.common_affiliate_name,
    nd.partner_currency,
    nd.agoda_commission_base_usd,
    nd.agoda_commission_amount_usd,
    nd.agoda_commission_amount_partner_currency,
    nd.partner_commission,
    nd.number_of_agoda_bookings,
    nd.number_of_partner_bookings,
    nd.number_of_matched_bookings,
    nd.agoda_commission_on_matched_bookings,
    nd.partner_commission_on_matched_bookings,
    nd.transaction_type,
    nd.reporting_date,
    nd.datamonth,
    ROUND(abs(nd.agoda_commission_amount_usd - md.current_month_accrual) * 100/ md.current_month_accrual, 4) as payment_vs_accrual_diff_percent,
    ROUND(abs(md.current_month_accrual - hd.last_month_accrual) * 100/ hd.last_month_accrual, 4) as accrual_movement_diff_percent,
    ROUND(abs(nd.agoda_commission_amount_usd - hd.last_month_payment) * 100/ hd.last_month_payment, 4) as payment_movement_diff_percent,
    CAST(null as double) as agoda_vs_partner_diff_percent,
    ROUND(abs(nd.agoda_commission_amount_usd - md.current_month_accrual), 2) as payment_vs_accrual_diff_amount,
    ROUND(abs(md.current_month_accrual - hd.last_month_accrual), 2) as accrual_movement_diff_amount,
    ROUND(abs(nd.agoda_commission_amount_usd - hd.last_month_payment), 2) as payment_movement_diff_amount,
    CAST(null as double) as agoda_vs_partner_diff_amount,
    'Supplier Bill' as partner_type
from main_current_month_payment nd
left join main_last_month_data hd on nd.common_affiliate_name = hd.common_affiliate_name
left join main_this_month_accrual md on nd.common_affiliate_name = md.common_affiliate_name

UNION ALL

-- 2. Switchfly Special Case (Tiered Commission)
select
    ba.affiliate_ids,
    ba.site_ids,
    ba.common_affiliate_name,
    ba.partner_currency,
    ba.agoda_commission_base_usd,
    c.commission as agoda_commission_amount_usd,
    c.commission as agoda_commission_amount_partner_currency,
    ba.partner_commission,
    ba.number_of_agoda_bookings,
    ba.number_of_partner_bookings,
    ba.number_of_matched_bookings,
    ba.agoda_commission_on_matched_bookings,
    ba.partner_commission_on_matched_bookings,
    ba.transaction_type,
    ba.reporting_date,
    ba.datamonth,
    ROUND(abs(c.commission - md.current_month_accrual) * 100/ md.current_month_accrual, 4) as payment_vs_accrual_diff_percent,
    ROUND(abs(md.current_month_accrual - hd.last_month_accrual) * 100/ hd.last_month_accrual, 4) as accrual_movement_diff_percent,
    ROUND(abs(c.commission - hd.last_month_payment) * 100/ hd.last_month_payment, 4) as payment_movement_diff_percent,
    CAST(null as double) as agoda_vs_partner_diff_percent,
    ROUND(abs(c.commission - md.current_month_accrual), 2) as payment_vs_accrual_diff_amount,
    ROUND(abs(md.current_month_accrual - hd.last_month_accrual), 2) as accrual_movement_diff_amount,
    ROUND(abs(c.commission - hd.last_month_payment), 2) as payment_movement_diff_amount,
    cast(null as double) as agoda_vs_partner_diff_amount,
    'Supplier Bill' as partner_type
from switchfly_total_booking ba
inner join switchfly_commission c on ba.common_affiliate_name = c.common_affiliate_name
inner join main_last_month_data hd on hd.common_affiliate_name = ba.common_affiliate_name
inner join main_this_month_accrual md on md.common_affiliate_name = ba.common_affiliate_name
"""