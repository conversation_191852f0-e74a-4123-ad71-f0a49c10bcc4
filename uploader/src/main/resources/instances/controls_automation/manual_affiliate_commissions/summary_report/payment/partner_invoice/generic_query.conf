query = """
with params as (
  select
    CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT) as this_month,
    CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT) - 1 as last_month
),
-- 1. Standard Affiliates: Historical Data
main_last_month_data as (
    -- Get last month's accrual and payment for all standard affiliates
    select
        common_affiliate_name,
        sum(case when transaction_type = 'accrual' then agoda_commission_amount_usd else 0 end) as last_month_accrual,
        sum(case when transaction_type = 'payment' then agoda_commission_amount_usd else 0 end) as last_month_payment
    from (
        select
            common_affiliate_name,
            agoda_commission_amount_usd,
            transaction_type,
            datamonth,
            row_number() over (
                partition by common_affiliate_name, transaction_type, datamonth
                order by reporting_date desc
            ) as rn
        from {db_name}.accrual_payment_summary, params
        where datamonth = params.last_month
    ) temp
    where rn = 1
    group by common_affiliate_name
),

main_this_month_accrual as (
    -- Get this month's accrual for all standard affiliates
    select
        common_affiliate_name,
        first(agoda_commission_amount_usd) as current_month_accrual
    from (
        select
            common_affiliate_name,
            agoda_commission_amount_usd,
            transaction_type,
            datamonth,
            row_number() over (
                partition by common_affiliate_name, transaction_type, datamonth
                order by reporting_date desc
            ) as rn
        from {db_name}.accrual_payment_summary,params
        where datamonth = params.this_month
        and transaction_type = 'accrual'
    ) temp
    where rn = 1
    group by common_affiliate_name
),

main_new_data as (
    -- Aggregate new data for all standard affiliates
    select
        concat_ws('_', collect_set(affiliate_id)) as affiliate_ids,
        concat_ws('_', collect_set(site_id)) as site_ids,
        first(common_affiliate_name) as common_affiliate_name,
        first(partner_currency) as partner_currency,
        sum(agoda_commission_base_usd) as agoda_commission_base_usd,
        sum(agoda_commission_amount_usd) as agoda_commission_amount_usd,
        sum(agoda_commission_amount_partner_currency) as agoda_commission_amount_partner_currency,
        sum(partner_commission_amount) as partner_commission,
        sum(case when agoda_commission_amount_usd is not null then 1 else 0 end) as number_of_agoda_bookings,
        sum(case when partner_commission_amount is not null then 1 else 0 end) as number_of_partner_bookings,
        sum(case when agoda_commission_amount_partner_currency is not null and partner_commission_amount is not null then 1 else 0 end) as number_of_matched_bookings,
        sum(case when agoda_commission_amount_partner_currency is not null and partner_commission_amount is not null then agoda_commission_amount_partner_currency else 0 end) as agoda_commission_on_matched_bookings,
        sum(case when agoda_commission_amount_partner_currency is not null and partner_commission_amount is not null then partner_commission_amount else 0 end) as partner_commission_on_matched_bookings,
        first(transaction_type) as transaction_type,
        now() as reporting_date,
        first(datamonth) as datamonth
    from {db_name}.final_accrual_payment_transactions, params
    where datamonth = params.this_month
      and transaction_type = 'payment'
      and common_affiliate_name in ({partner_name})
    group by common_affiliate_name, partner_currency
)

SELECT
    nd.affiliate_ids,
    nd.site_ids,
    nd.common_affiliate_name,
    nd.partner_currency,
    nd.agoda_commission_base_usd,
    nd.agoda_commission_amount_usd,
    nd.agoda_commission_amount_partner_currency,
    nd.partner_commission,
    nd.number_of_agoda_bookings,
    nd.number_of_partner_bookings,
    nd.number_of_matched_bookings,
    nd.agoda_commission_on_matched_bookings,
    nd.partner_commission_on_matched_bookings,
    nd.transaction_type,
    nd.reporting_date,
    nd.datamonth,
    -- Percent columns: 4 decimal places
    ROUND(
        abs(nd.agoda_commission_amount_usd - md.current_month_accrual) * 100 / md.current_month_accrual, 4
    ) AS payment_vs_accrual_diff_percent,
    ROUND(
        abs(md.current_month_accrual - hd.last_month_accrual) * 100 / hd.last_month_accrual, 4
    ) AS accrual_movement_diff_percent,
    ROUND(
        abs(nd.agoda_commission_amount_usd - hd.last_month_payment) * 100 / hd.last_month_payment, 4
    ) AS payment_movement_diff_percent,
    ROUND(
        abs(nd.agoda_commission_on_matched_bookings - hd.partner_commission_on_matched_bookings) * 100 / hd.agoda_commission_on_matched_bookings, 4
    ) AS agoda_vs_partner_diff_percent,
    -- Amount columns: 2 decimal places
    ROUND(abs(nd.agoda_commission_amount_usd - md.current_month_accrual), 2) AS payment_vs_accrual_diff_amount,
    ROUND(abs(md.current_month_accrual - hd.last_month_accrual), 2) AS accrual_movement_diff_amount,
    ROUND(abs(nd.agoda_commission_amount_usd - hd.last_month_payment), 2) AS payment_movement_diff_amount,
    ROUND(
        abs(nd.agoda_commission_on_matched_bookings - hd.partner_commission_on_matched_bookings) * 100 / hd.agoda_commission_on_matched_bookings, 4
    ) AS agoda_vs_partner_diff_amount,
    'Supplier Bill' as partner_type
FROM main_new_data nd
LEFT JOIN main_last_month_data hd ON nd.common_affiliate_name = hd.common_affiliate_name
LEFT JOIN main_this_month_accrual md ON nd.common_affiliate_name = md.common_affiliate_name
"""