include "/instances/controls_automation/manual_affiliate_commissions/generic_settings.conf"

app-name = "conaut_affiliate_commission_payment_self_invoice_summary_report_uploader"
oozie.yarn-queue-name = production
hadoop.spark {
  executor {
    count = 15
    cores-per-executor = 6
    memory-per-executor = "10GB"
  }
}

transaction_type="payment"

configUploader{
    jobFrequency: Monthly
    dateFormat = "yyyyMMdd"
    filePath = /user/${hadoop.hdfs.user}/apps/${project-name}/${app-name}/
     queryInfos = [
        {
          queryName = "PAYMENT_SELF_INVOICE_SUMMARY_QUERY"
          include "query.conf"
          fileName= "{transaction_type}_self_invoice_summary_{year}{month}",
          fileType = "csv",
          filePathPrefix = ${conaut_sharepoint_site_url}"/"${destination_path}"/",
          filePathSuffix = ".csv"${sharepoint_file_path_suffix},
          historicalTracking = {
                tableName = ${db_name}.accrual_payment_summary
                mode = "append"
          }
        }
     ]
     destinationsConfig = [
         {
             reportParameters={"transaction_type":${transaction_type}, "db_name":${db_name}},
             destinations={"sharepoint":["SharepointUploaderConfig"]},
             instanceName=conaut_affiliate_commission_self_invoice_payment_summary_report_uploader,
         }
     ]
     SharepointUploaderConfig {
         siteName = "ControlsAutomation"
         destinationPath = ${destination_path}
         vaultSecretLocation = "hadoop_cluster/uploader/MicrosoftGraph/graph_api_key"
         hdfsBackupCredentialLocation = /apps/microsoft-graph/microsoft_graph_credential.json
         retryCount = 3
     }

    saveFile{
        checkEmptyFile = false
        csvConf = {
            needHeader = true
        }
    }

    deleteHdfsFiles = true

    trackingStatus {
        monthLookbackPeriod = 3
        trackingTable = ${db_name}.uploader_status
    }
}

result {
    mail {
        alert-sender = "<EMAIL> (Uploader)"
        alert-recipients = "<EMAIL>"
    }
}
