query = """
WITH latest_validation AS (
    SELECT
        booking_id,
        datamonth,
        transaction_type,
        common_affiliate_name,
        description,
        validation_status,
        validation_datetime,
        ROW_NUMBER() OVER (
            PARTITION BY booking_id, datamonth, transaction_type, common_affiliate_name
            ORDER BY
                CASE WHEN validation_datetime IS NULL THEN 1 ELSE 0 END,
                validation_datetime DESC
        ) AS rn
    FROM {db_name}.affiliate_invalid_transactions
    WHERE
        datamonth = CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT)
        AND common_affiliate_name = '{partner_name}'
)
, joined_integrator_transactions AS (
    SELECT
        ipt.*,
        COALESCE(lv.validation_status, 'pass') AS validation_status,
        COALESCE(lv.description, 'No validation issues found') AS validation_description,
        lv.validation_datetime
    FROM {db_name}.integrator_partner_transactions ipt
    LEFT JOIN latest_validation lv
        ON ipt.booking_id = lv.booking_id
        AND ipt.datamonth = lv.datamonth
        AND ipt.transaction_type = lv.transaction_type
        AND ipt.common_affiliate_name = lv.common_affiliate_name
        AND lv.rn = 1
    WHERE
        ipt.datamonth = CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT)
        AND ipt.common_affiliate_name = '{partner_name}'
        AND ipt.transaction_type = '{transaction_type}'
)
, accrual_missing_in_payment AS (
    -- Accruals that are missing in payment (for payment run)
    SELECT
        ipt.*,
        COALESCE(lv.validation_status, 'pass') AS validation_status,
        COALESCE(lv.description, 'No validation issues found') AS validation_description,
        lv.validation_datetime
    FROM {db_name}.integrator_partner_transactions ipt
    JOIN latest_validation lv
        ON ipt.booking_id = lv.booking_id
        AND ipt.datamonth = lv.datamonth
        AND lv.transaction_type = 'payment'
        AND lv.description LIKE '%Missing in payment but existing in accrual%'
        AND ipt.common_affiliate_name = lv.common_affiliate_name
    WHERE
        ipt.datamonth = CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT)
        AND ipt.common_affiliate_name = '{partner_name}'
        AND ipt.transaction_type = 'accrual'
)
SELECT *
FROM (
        SELECT
            booking_id,
            booking_status,
            affiliate_id,
            affiliate_name,
            site_id,
            site_name,
            booking_date,
            checkin_date,
            checkout_date,
            payment_method,
            cancellation_date,
            partner_name,
            commission_base,
            platform_fee_amount,
            accounting_date,
            reporting_date,
            commission_rate,
            commission_amount,
            validation_status,
            validation_description,
            validation_datetime,
            -- Partition columns at the end
            common_affiliate_name,
            transaction_type,
            datamonth
        FROM joined_integrator_transactions

        UNION ALL

        SELECT
            booking_id,
            booking_status,
            affiliate_id,
            affiliate_name,
            site_id,
            site_name,
            booking_date,
            checkin_date,
            checkout_date,
            payment_method,
            cancellation_date,
            partner_name,
            commission_base,
            platform_fee_amount,
            accounting_date,
            reporting_date,
            commission_rate,
            NULL AS commission_amount,  -- Set to NULL for missing in payment
            validation_status,
            validation_description,
            validation_datetime,
            -- Partition columns at the end
            common_affiliate_name,
            transaction_type,
            datamonth
        FROM accrual_missing_in_payment
        WHERE '{transaction_type}' = 'payment') final_result
                                                ORDER BY booking_id
"""