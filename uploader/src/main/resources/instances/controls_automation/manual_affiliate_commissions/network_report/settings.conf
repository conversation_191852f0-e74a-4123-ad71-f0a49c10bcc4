include "/instances/controls_automation/manual_affiliate_commissions/generic_settings.conf"

app-name = "conaut_affiliate_commission_network_uploader"
oozie.yarn-queue-name = production
hadoop.spark {
  executor {
    count = 15
    cores-per-executor = 6
    memory-per-executor = "10GB"
  }
}

configUploader{
    jobFrequency: Monthly
    dateFormat = "yyyyMMdd"
    filePath = /user/${hadoop.hdfs.user}/apps/${project-name}/${app-name}/
    queryInfos = [
        {
          queryName = "Network_Query"
          include "query_network.conf"
          fileName= "{partner_name}_network_affiliate_commission_{transaction_type}_{year}{month}{day}",
          fileType = "csv",
          filePathPrefix = ${conaut_sharepoint_site_url}"/"${destination_path}"/",
          filePathSuffix = ".csv"${sharepoint_file_path_suffix}
        }
    ]
   destinationsConfig = [
       {
           reportParameters={"partner_name": "rakuten", "transaction_type":${transaction_type}, "db_name":${db_name}},
           destinations={"sharepoint":["SharepointUploaderConfig"]},
           instanceName=conaut_affiliate_commission_rakuten_${transaction_type},
       },
       {
           reportParameters={"partner_name": "cj", "transaction_type":${transaction_type}, "db_name":${db_name}},
           destinations={"sharepoint":["SharepointUploaderConfig"]},
           instanceName=conaut_affiliate_commission_cj_${transaction_type},
       }
   ]
   SharepointUploaderConfig {
        siteName = "ControlsAutomation"
        destinationPath = ${destination_path}
        vaultSecretLocation = "hadoop_cluster/uploader/MicrosoftGraph/graph_api_key"
        hdfsBackupCredentialLocation = /apps/microsoft-graph/microsoft_graph_credential.json
        retryCount = 3
   }

    saveFile{
        checkEmptyFile = false
        csvConf = {
            needHeader = true
        }
    }

    deleteHdfsFiles = true

    trackingStatus {
        monthLookbackPeriod = 3
        trackingTable = ${db_name}.uploader_status
    }
}

result {
    mail {
        alert-sender = "<EMAIL> (Uploader)"
        alert-recipients = "<EMAIL>"
    }
}
