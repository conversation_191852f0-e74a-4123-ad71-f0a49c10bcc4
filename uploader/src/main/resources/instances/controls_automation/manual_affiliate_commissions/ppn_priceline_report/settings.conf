include "/instances/controls_automation/manual_affiliate_commissions/generic_settings.conf"

app-name = "conaut_affiliate_ppn_priceline_report_uploader"
oozie.yarn-queue-name = production
hadoop.spark {
  executor {
    count = 15
    cores-per-executor = 6
    memory-per-executor = "10GB"
  }
}

configUploader{
    jobFrequency: Monthly
    dateFormat = "yyyyMMdd"
    filePath = /user/${hadoop.hdfs.user}/apps/${project-name}/${app-name}/

     enrichDFConfig{
               dropColumns  = ["file_name"]
    }
     queryInfos = [
        {
          queryName = "PPN_Priceline_query_estimate",
          include "query_estimated.conf",
          fileName= "ppn_priceline_Check_in_Estimate_in_{grouping_key}_{AID}",
          fileType = "csv",
          groupingKey = [ "file_name"],
          filePathPrefix = ${conaut_sharepoint_site_url}"/"${destination_path}"/",
          filePathSuffix = ".csv"${sharepoint_file_path_suffix}
        },
        {
          queryName = "PPN_Priceline_query_final",
          include "query_finalized.conf",
          fileName= "ppn_priceline_Check_in_Finalized_in_{grouping_key}_{AID}",
          fileType = "csv",
          groupingKey = [ "file_name"],
          filePathPrefix = ${conaut_sharepoint_site_url}"/"${destination_path}"/",
          filePathSuffix = ".csv"${sharepoint_file_path_suffix}
        },
     ]
     destinationsConfig = [
         {
             reportParameters={ "AID": "116517", "db_name":${db_name}, "transaction_type":${transaction_type}}
             destinations={"sharepoint":["SharepointUploaderConfig"]},
             instanceName="conaut_affiliate_ppn_priceline_116517",
         },
         {
             reportParameters={ "AID": "130200", "db_name":${db_name}, "transaction_type":${transaction_type}}
             destinations={"sharepoint":["SharepointUploaderConfig"]},
             instanceName="conaut_affiliate_ppn_priceline_130200",
         },
         {
             reportParameters={ "AID": "228890", "db_name":${db_name}, "transaction_type":${transaction_type}}
             destinations={"sharepoint":["SharepointUploaderConfig"]},
             instanceName="conaut_affiliate_ppn_priceline_228890",
         },
         {
             reportParameters={ "AID": "202739", "db_name":${db_name}, "transaction_type":${transaction_type}}
             destinations={"sharepoint":["SharepointUploaderConfig"]},
             instanceName="conaut_affiliate_ppn_priceline_202739",
         }
     ]

     SharepointUploaderConfig {
         siteName = "ControlsAutomation"
         destinationPath = ${destination_path}
         vaultSecretLocation = "hadoop_cluster/uploader/MicrosoftGraph/graph_api_key"
         hdfsBackupCredentialLocation = /apps/microsoft-graph/microsoft_graph_credential.json
         retryCount = 3
     }

    saveFile{
        checkEmptyFile = false
        csvConf = {
            needHeader = true
        }
    }

    deleteHdfsFiles = true

    trackingStatus {
        monthLookbackPeriod = 3
        trackingTable = ${db_name}.uploader_status
    }
}

result {
    mail {
        alert-sender = "<EMAIL> (Uploader)"
        alert-recipients = "<EMAIL>"
    }
}
