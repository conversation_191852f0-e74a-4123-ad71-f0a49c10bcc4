include "/instances/controls_automation/manual_affiliate_commissions/generic_settings.conf"

app-name = "conaut_affiliate_commission_pah_uploader"
oozie.yarn-queue-name = production
hadoop.spark {
  executor {
    count = 15
    cores-per-executor = 6
    memory-per-executor = "10GB"
  }
}

configUploader{
    jobFrequency: Monthly
    dateFormat = "yyyyMMdd"
    filePath = /user/${hadoop.hdfs.user}/apps/${project-name}/${app-name}/
     destinationsConfig = [
           {
               reportParameters={"partner_name": "tiket pah", "transaction_type":${transaction_type}, "db_name":${db_name}},
               destinations={"sharepoint":["SharepointUploaderConfig"]},
               instanceName=conaut_affiliate_commission_tiket_pah_${transaction_type},
           },
           {
               reportParameters={"partner_name": "ixigo pah", "transaction_type":${transaction_type}, "db_name":${db_name}},
               destinations={"sharepoint":["SharepointUploaderConfig"]},
               instanceName=conaut_affiliate_commission_ixigo_pah_${transaction_type},
           },
           {
               reportParameters={"partner_name": "rakuten pah", "transaction_type":${transaction_type}, "db_name":${db_name}},
               destinations={"sharepoint":["SharepointUploaderConfig"]},
               instanceName=conaut_affiliate_commission_rakuten_pah_${transaction_type},
           },
           {
               reportParameters={"partner_name": "dida pah", "transaction_type":${transaction_type}, "db_name":${db_name}},
               destinations={"sharepoint":["SharepointUploaderConfig"]},
               instanceName=conaut_affiliate_commission_dida_pah_${transaction_type},
           }
     ]
     SharepointUploaderConfig {
        siteName = "ControlsAutomation"
        destinationPath = ${destination_path}
        vaultSecretLocation = "hadoop_cluster/uploader/MicrosoftGraph/graph_api_key"
        hdfsBackupCredentialLocation = /apps/microsoft-graph/microsoft_graph_credential.json
        retryCount = 3
     }

    saveFile{
        checkEmptyFile = false
        csvConf = {
            needHeader = true
        }
    }

    deleteHdfsFiles = true

    trackingStatus {
        monthLookbackPeriod = 3
        trackingTable = ${db_name}.uploader_status
    }
}

result {
    mail {
        alert-sender = "<EMAIL> (Uploader)"
        alert-recipients = "<EMAIL>"
    }
}
