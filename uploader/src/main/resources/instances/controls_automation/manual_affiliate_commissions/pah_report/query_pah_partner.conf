query = """
WITH latest_validation AS (
    SELECT
        booking_id,
        datamonth,
        transaction_type,
        description,
        validation_status,
        validation_datetime,
        ROW_NUMBER() OVER (
            PARTITION BY booking_id, datamonth, transaction_type
            ORDER BY
                CASE WHEN validation_datetime IS NULL THEN 1 ELSE 0 END,
                validation_datetime DESC
        ) AS rn
    FROM {db_name}.affiliate_invalid_transactions
    WHERE datamonth = CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT)
      AND common_affiliate_name = '{partner_name}'
)
SELECT
    pah_t.booking_id,
    pah_t.affiliate_id as aid,
    pah_t.affiliate_name as affiliate,
    pah_t.site_id,
    pah_t.payment_method as paymentmodel,
    pah_t.tag,
    pah_t.booking_date,
    pah_t.checkin_date,
    pah_t.checkout_date,
    pah_t.country_name as destination,
    regexp_replace(regexp_replace(pah_t.hotel_name, "''", '"'), '"', '') as hotelname,
    pah_t.booking_status as status,
    pah_t.guest_nationality_country_name as guest_nationality_country_name,
    pah_t.origin_country_name as origin_country_name,
    pah_t.booking_external_reference,
    pah_t.supplier_name,
    pah_t.margin,
    pah_t.commission_amount as commission
FROM {db_name}.pah_partner_transactions pah_t
LEFT JOIN {db_name}.pah_reports pr
    ON pah_t.booking_id = pr.booking_id
LEFT JOIN latest_validation lv
    ON pah_t.booking_id = lv.booking_id
    AND pah_t.transaction_type = lv.transaction_type
    AND pah_t.datamonth = lv.datamonth
    AND lv.rn = 1
WHERE pah_t.datamonth = CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT)
  AND pah_t.transaction_type = '{transaction_type}'
  AND pah_t.common_affiliate_name = '{partner_name}'
  AND (lv.booking_id IS NULL OR lv.validation_status = 'PASS')
"""