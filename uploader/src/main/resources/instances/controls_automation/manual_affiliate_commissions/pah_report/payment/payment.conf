transaction_type="payment"
configUploader {
    queryInfos = [
        {
          queryName = "PAH_Query"
          include "/instances/controls_automation/manual_affiliate_commissions/pah_report/query_pah_internal.conf"
          fileName= "{partner_name}_affiliate_commission_{transaction_type}_{year}{month}{day}",
          fileType = "csv",
          filePathPrefix = ${conaut_sharepoint_site_url}"/"${destination_path}"/",
          filePathSuffix = ".csv"${sharepoint_file_path_suffix}
        },
        {
          queryName = "SharedReport_Query"
          include "/instances/controls_automation/manual_affiliate_commissions/pah_report/query_pah_partner.conf"
          fileName= "partner_reports/{partner_name}_affiliate_commission_partner_{transaction_type}_{year}{month}{day}",
          fileType = "csv",
          filePathPrefix = ${conaut_sharepoint_site_url}"/"${destination_path}"/",
          filePathSuffix = ".csv"${sharepoint_file_path_suffix}
        }
    ]
}