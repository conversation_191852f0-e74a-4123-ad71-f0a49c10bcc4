query = """
WITH latest_validation AS (
    SELECT
        booking_id,
        datamonth,
        transaction_type,
        description,
        validation_status,
        validation_datetime,
        ROW_NUMBER() OVER (
            PARTITION BY booking_id, datamonth, transaction_type
            ORDER BY
                CASE WHEN validation_datetime IS NULL THEN 1 ELSE 0 END,
                validation_datetime DESC
        ) AS rn
    FROM {db_name}.affiliate_invalid_transactions
    WHERE datamonth = CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT)
      AND common_affiliate_name = '{partner_name}'
)
, joined_transactions AS (
    SELECT
        pah_t.*,
        pr.booking_status AS booking_status_b2b_report,
        pr.commission_amount AS commission_amount_b2b_report,
        pr.margin AS margin_b2b_report,
        COALESCE(lv.validation_status, 'pass') AS validation_status,
        COALESCE(lv.description, 'No validation issues found') AS validation_description,
        lv.validation_datetime
    FROM {db_name}.pah_partner_transactions pah_t
    LEFT JOIN {db_name}.pah_reports pr
        ON pah_t.booking_id = pr.booking_id
    LEFT JOIN latest_validation lv
        ON pah_t.booking_id = lv.booking_id
        AND pah_t.transaction_type = lv.transaction_type
        AND pah_t.datamonth = lv.datamonth
        AND lv.rn = 1
    WHERE pah_t.datamonth = CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT)
      AND pah_t.transaction_type = '{transaction_type}'
      AND pah_t.common_affiliate_name = '{partner_name}'
)
, accrual_missing_in_payment AS (
    -- Accruals that are missing in payment (for payment run)
    SELECT
        pah_t.*,
        pr.booking_status AS booking_status_b2b_report,
        pr.commission_amount AS commission_amount_b2b_report,
        pr.margin AS margin_b2b_report,
        COALESCE(lv.validation_status, 'pass') AS validation_status,
        COALESCE(lv.description, 'No validation issues found') AS validation_description,
        lv.validation_datetime
    FROM {db_name}.pah_partner_transactions pah_t
    LEFT JOIN {db_name}.pah_reports pr
        ON pah_t.booking_id = pr.booking_id
    JOIN latest_validation lv
        ON pah_t.booking_id = lv.booking_id
        AND pah_t.datamonth = lv.datamonth
        AND lv.transaction_type = 'payment'
        AND lv.description LIKE '%Missing in payment but existing in accrual%'
    WHERE pah_t.datamonth = CAST(date_format(to_date('{date}','yyyyMMdd'),'yyyyMM') AS INT)
      AND pah_t.transaction_type = 'accrual'
      AND pah_t.common_affiliate_name = '{partner_name}'
)
SELECT
    booking_id,
    booking_status,
    affiliate_id,
    affiliate_name,
    site_id,
    site_name,
    origin_country_name,
    country_name,
    hotel_id,
    regexp_replace(regexp_replace(hotel_name, "''", '"'), '"', '') as hotel_name,
    booking_date,
    checkin_date,
    checkout_date,
    payment_method,
    margin,
    supplier_id,
    supplier_name,
    selling_amount,
    tag,
    cancellation_date,
    ctabeforetaxafterdiscount,
    guest_nationality_country_name,
    booking_external_reference,
    commission_base,
    commission_amount,
    accounting_date,
    reporting_date,
    commission_rate,
    datamonth,
    transaction_type,
    booking_status_b2b_report,
    commission_amount_b2b_report,
    margin_b2b_report,
    validation_status,
    validation_description,
    validation_datetime,
    partner_currency,
    commission_base_partner_currency,
    commission_amount_partner_currency
FROM joined_transactions

UNION ALL

SELECT
    booking_id,
    booking_status,
    affiliate_id,
    affiliate_name,
    site_id,
    site_name,
    origin_country_name,
    country_name,
    hotel_id,
    regexp_replace(regexp_replace(hotel_name, "''", '"'), '"', '') as hotel_name,
    booking_date,
    checkin_date,
    checkout_date,
    payment_method,
    margin,
    supplier_id,
    supplier_name,
    selling_amount,
    tag,
    cancellation_date,
    ctabeforetaxafterdiscount,
    guest_nationality_country_name,
    booking_external_reference,
    commission_base,
    NULL AS commission_amount,  -- Set to NULL for missing in payment
    accounting_date,
    reporting_date,
    commission_rate,
    datamonth,
    transaction_type,
    booking_status_b2b_report,
    commission_amount_b2b_report,
    margin_b2b_report,
    validation_status,
    validation_description,
    validation_datetime,
    partner_currency,
    commission_base_partner_currency,
    NULL AS commission_amount_partner_currency  -- Set to NULL for missing in payment
FROM accrual_missing_in_payment
WHERE '{transaction_type}' = 'payment'
"""