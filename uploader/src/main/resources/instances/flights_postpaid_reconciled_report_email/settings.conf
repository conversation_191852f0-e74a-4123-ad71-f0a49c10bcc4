app-name = "flights_postpaid_reconciled_report_email"
database = "finance_uploader_staging"
hadoop.spark {
	executor {
		count = 15
		cores-per-executor = 6
		memory-per-executor = 20GB
	}
}
configUploader {
	jobFrequency: Daily
	dateFormat = "yyyyMMdd"
	filePath = "s3a://"${hadoop.hdfs.user}/apps/${project-name}/${app-name}/

	queryInfos = [
		{
			queryName = "Reconcile",
			include "query.conf",
			fileName = "{supplier_name}_flight_postpaid_reconciled_{date}",
			fileType = "xlsx"
		}
	]

	destinationsConfig = [
		{
			reportParameters = {"report_schema": "finance_uploader_staging", "supplier_id": "30038", "supplier_name": "JejuAir"},
			destinations = {"email": ["jejuEmailConfig"]},
			instanceName = "30038",
		},
		{
			reportParameters = {"report_schema": "finance_uploader_staging", "supplier_id": "30018", "supplier_name": "PKFare"},
			destinations = {"email": ["pkfareEmailConfig"]},
			instanceName = "30018",
		},
		{
			reportParameters = {"report_schema": "finance_uploader_staging", "supplier_id": "30065", "supplier_name": "ScootNDC"},
			destinations = {"email": ["scootndcEmailConfig"]},
			instanceName = "30065",
		},
		{
			reportParameters = {"report_schema": "finance_uploader_staging", "supplier_id": "30027", "supplier_name": "Scoot"},
			destinations = {"email": ["scootEmailConfig"]},
			instanceName = "30027",
		},
		{
			reportParameters = {"report_schema": "finance_uploader_staging", "supplier_id": "30085", "supplier_name": "Unififi"},
			destinations = {"email": ["unififiEmailConfig"]},
			instanceName = "30085",
		},
		{
			reportParameters = {"report_schema": "finance_uploader_staging", "supplier_id": "30013", "supplier_name": "Airtrip"},
			destinations = {"email": ["airtripEmailConfig"]},
			instanceName = "30013",
		},
		{
			reportParameters = {"report_schema": "finance_uploader_staging", "supplier_id": "30086", "supplier_name": "Flightroutes24"},
			destinations = {"email": ["fr24EmailConfig"]},
			instanceName = "30086",
		},
		{
			reportParameters = {"report_schema": "finance_uploader_staging", "supplier_id": "30091", "supplier_name": "DidaDC"},
			destinations = {"email": ["didaEmailConfig"]},
			instanceName = "30091",
		}
	]

	enrichDFConfig {
		dropColumns = ["domestic/international"] //drop column is_india_domestic
	}


	saveFile {
		checkEmptyFile = true
		xlsxConf = {
			needHeader = true
			footer = {
				0: { type: "STATIC", value: "Count transaction"},
				2: { type: "COUNT_DISTINCT", column: "PNR"},
				5: { type: "STATIC", value: "Total payment"},
				6: { type: "SUM", column: "Gross Amount"},
				7: { type: "SUM", column: "Gross Amount USD"},
				8: { type: "SUM", column: "Commission"},
				9: { type: "SUM", column: "Commission USD"},
				10: { type: "SUM", column: "Fee"},
				11: { type: "SUM", column: "Fee USD"},
				12: { type: "SUM", column: "Net Amount"},
				13: { type: "SUM", column: "Net Amount USD"},
			}
		}
	}

	jejuEmailConfig = {
		subjectName = "Jeju bi-weekly reconciled report"
		htmlBody = """<p><h1>JejuAir bi-weekly reconciled report</h1></p>"""
		sender = "<EMAIL>"
		recipients = "<EMAIL>"
		mimeType = "text/xlsx"
		hasTable = true
	}

	pkfareEmailConfig = {
		subjectName = "PKFare reconciled report"
		htmlBody = """<p><h1>PKFare reconciled report</h1></p>"""
		sender = "<EMAIL>"
		recipients = "<EMAIL>"
		mimeType = "text/xlsx"
		hasTable = true
	}

	scootndcEmailConfig = {
		subjectName = "ScootNDC weekly reconciled report"
		htmlBody = """<p><h1>ScootNDC weekly reconciled report</h1></p>"""
		sender = "<EMAIL>"
		recipients = "<EMAIL>"
		mimeType = "text/xlsx"
		hasTable = true
	}

	scootEmailConfig = {
		subjectName = "Scoot weekly reconciled report"
		htmlBody = """<p><h1>Scoot weekly reconciled report</h1></p>"""
		sender = "<EMAIL>"
		recipients = "<EMAIL>"
		mimeType = "text/xlsx"
		hasTable = true
	}

	unififiEmailConfig = {
		subjectName = "Unififi weekly reconciled report"
		htmlBody = """<p><h1>Unififi weekly reconciled report</h1></p>"""
		sender = "<EMAIL>"
		recipients = "<EMAIL>"
		mimeType = "text/xlsx"
		hasTable = true
	}

	airtripEmailConfig = {
		subjectName = "Airtrip weekly reconciled report"
		htmlBody = """<p><h1>Airtrip weekly reconciled report</h1></p>"""
		sender = "<EMAIL>"
		recipients = "<EMAIL>"
		mimeType = "text/xlsx"
		hasTable = true
	}

	fr24EmailConfig = {
		subjectName = "Flightroutes24 weekly reconciled report"
		htmlBody = """<p><h1>Flightroutes24 weekly reconciled report</h1></p>"""
		sender = "<EMAIL>"
		recipients = "<EMAIL>"
		mimeType = "text/xlsx"
		hasTable = true
	}

	didaEmailConfig = {
		subjectName = "Dida reconciled report"
		htmlBody = """<p><h1>Dida reconciled report</h1></p>"""
		sender = "<EMAIL>"
		recipients = "<EMAIL>"
		mimeType = "text/xlsx"
		hasTable = true
	}

	deleteHdfsFiles = false
}

result {
	mail {
		alert-sender = "<EMAIL> (<EMAIL>)"
		alert-recipients = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
	}
}