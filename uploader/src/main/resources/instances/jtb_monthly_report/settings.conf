app-name = "jtb_monthly_report"
hadoop.spark {
  executor {
    count = 15
    cores-per-executor = 6
    memory-per-executor = 10GB
  }
}

configUploader {
    jobFrequency: Monthly
    dateFormat = "yyyyMMdd"
    filePath = /user/${hadoop.hdfs.user}/apps/${project-name}/${app-name}/

    queryInfos = [
            {
                queryName = "JTBMonthlyReport",
                include "query.conf",
                fileName= "JTB SOW1 Monthly Report check in {displayDate}",
                fileType = "csv"
                historicalTracking = {
                  tableName = ${database}.${app-name}
                  partitionColumns = ["reportdate"]
                }
            },
        ]

    destinationsConfig = [
        {
            reportParameters={},
            destinations={"email":["jtbEmailConfig"]},
            instanceName="jtb_monthly_report",
        }
    ]

   saveFile{
      checkEmptyFile = false
      csvConf = {
       needHeader = true
      }
    }

   displayDateConfig = {
      dateFormat="MMM-yy",
      offset="-1",
      offsetType="month"
   }

   jtbEmailConfig {
       subjectName = "JTB SOW1 Monthly Report check in {displayDate}"
       htmlBody = """<p><h1>JTB SOW1 Monthly Report</h1></p><p><a href='https://metabase.agodadev.io/dashboard/18718-monthly-report'>Metabase Link</a></p>"""
       sender = "<EMAIL>"
       recipients = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
       mimeType = "text/csv"
       hasTable = true
   }

   deleteHdfsFiles = false
}

result {
  mail {
    alert-sender = "<EMAIL> (<EMAIL>)"
    alert-recipients = "<EMAIL>"
  }
}


ag-vault {
  http {
    url = "https://hk.vault.agoda.local:8200"
    auth {
      method = "approle"
    }
  }
}

booking-query-client {
  client-info {
    client-id: "myClientid",
    api-key: "myApiKey",
  }
}

ag-http-client.mesh.services {
  booking_query {
    discovery {
      method = "static"
      static {
        hosts = ["booking-query-default.privatecloud.hk.agoda.is:443"]
      }
    }
  }
}
