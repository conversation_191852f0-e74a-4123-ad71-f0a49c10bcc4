app-name = "mi_hilton_priceline_agoda_report"

configUploader{
    jobFrequency: Monthly
    dateFormat = "dd-MM-yyyy"
    queryInfos = [
        {
            queryName = "MiHiltonPricelineAgodaFile",
            include "query.conf",
            fileName= "MI_Hilton_priceline_Agoda_file.{date}",
            fileType = "txt"
            historicalTracking = {
                tableName = ${database}.${app-name}
                partitionColumns = ["datadate"]
            }
        }
    ]


    filePath = /user/${hadoop.hdfs.user}/apps/${project-name}/${app-name}/


    saveFile{
      checkEmptyFile = false
      textConf = {
            needHeader = false
            customDelimiter = "|"
        }

    }

    deleteHdfsFiles = true
}



result {
  mail {
    alert-sender = "<EMAIL> (<EMAIL>)"
    alert-recipients = ""
  }
}

hadoop.spark {
   executor {
       count = 8
       cores-per-executor = 6
       memory-per-executor = 10GB
   }
   driver {
        memory = "20g"
        memory-overhead = 16384
   }
}

booking-query-client {
  client-info {
    client-id: "myClientid",
    api-key: "myApi<PERSON>ey",
  }
}

ag-http-client.mesh.services {
  booking_query {
    discovery {
      method = "static"
      static {
        hosts = ["booking-query-default.privatecloud.hk.agoda.is:443"]
      }
    }
  }
}

