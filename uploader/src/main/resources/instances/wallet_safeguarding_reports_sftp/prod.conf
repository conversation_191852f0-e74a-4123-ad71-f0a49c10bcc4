include classpath("_generic_prod.conf")
include "settings.conf"


hadoop {
  hdfs.user = "hk-fin-wal-svc"
  knox.enabled = true
  hive.username = "hk-fin-wal-svc"
  credentials = "hadoop/hk-fin-wal-svc/credentials"
}

database = "finance_wallet"

configUploader{
    dateFormat = "yyyyMMdd"
    filePath = "s3a://"${hadoop.hdfs.user}/reports/${project-name}/safeguarding_reports/uk/

    enrichDFConfig{
        dropColumns  = ["wallet_entity", "datadate"]
    }


    saveFile{
        checkEmptyFile = false // TODO what should this be ?
        csvConf = {
            needHeader = true
            customDelimiter = ","
        }
    }

    deleteHdfsFiles = false
}

result {
  mail {
    alert-sender = "<EMAIL> (<EMAIL>)"
    alert-recipients = "<EMAIL>"
  }
}

current_env = "prod"
