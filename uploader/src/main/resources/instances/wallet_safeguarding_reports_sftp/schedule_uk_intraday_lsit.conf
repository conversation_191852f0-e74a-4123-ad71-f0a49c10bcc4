include "lsit.conf"

configUploader{

  queryInfos = [
    {
      queryName = "intra_day_report",
      include "query_intra_day.conf",
      fileName= "BHFS_UK_SAFEGUARDING_INTRA_DAY_REPORT_{date}",
      fileType = "csv"
      historicalTracking = {
        tableName = ${database}.safeguarding_intraday
        partitionColumns = ["datadate", "wallet_entity"]
      }
    },
    {
      queryName = "abridged_intraday_report",
      include "query_abridged_intraday.conf",
      fileName= "BHFS_UK_SAFEGUARDING_ABRIDGED_INTRA_DAY_REPORT_{date}",
      fileType = "csv"
      historicalTracking = {
        tableName = ${database}.safeguarding_abridged_intraday
        partitionColumns = ["datadate", "wallet_entity"]
      }
    }
  ]

  destinationsConfig = [
    {
      reportParameters={"finance_wallet_db": "finance_wallet_lsit_cashflow", "wallet_entity": "BHFS_UK"},
      destinations={"sftp":["BHFSSftpConfig"]},
      instanceName="wallet_safeguarding_reports_sftp"
    }
  ]


  // TODO to update later
  BHFSSftpConfig {
    address = "sftp://sftpconnect.agoda.local:22"
    destinationPath = "/OracleWallet/Outbound/IntercoRefundMapping/UK/dev/"
    vaultSecretLocation = "dataproject/hk-fin-wal-svc/uploader"
    #hdfsBackupCredentialLocation = /apps/${project-name}/backupCredential/${app-name}/bhfs_sftp_credentials.json
    retryCount = 3
  }

}