query="""
SELECT
    wallet_account_id AS `Wallet Holder`,
    cast(opening_cash_balance as decimal(18,2)) AS `Opening Cash Balance in CMA`,
    cast(cit_received as decimal(18,2)) AS `CIT Received (exclude chargeback)`,
    cast(chargeback_received as decimal(18,2)) AS `Chargeback Received`,
    cast(refund_received as decimal(18,2)) AS `Refunds Received in CMA`,
    cast(total_cash_received_into_cma as decimal(18,2)) AS `Total Cash Received into CMA`,
    cast(cash_adjustment as decimal(18,2)) AS `Safeguarding Adjustment Processed`,
    cast(expected_ending_cash_balance as decimal(18,2)) AS `Net Cash Position`,
    cast(balance_to_safeguard as decimal(18,2)) AS `Balance to be Safeguarded`,
    cast(wallet_liability_end_balance as decimal(18,2)) AS `Ending Wallet liability balance on Prior Day`,
    cast(actual_ending_cash_balance as decimal(18,2)) AS `Actual Ending Cash Balance in CMA`,
    cast(safeguarding_adjustment_amount as decimal(18,2)) AS `Safeguarding Adjustment Required`,
    cast(end_of_day_discrepancy as decimal(18,2)) AS `End of Day Discrepancy`,
    wallet_entity,
    datadate
FROM {finance_wallet_db}.cashflow_report
where datadate = cast(date_format(to_date('{date}','yyyyMMdd'), 'yyyyMMdd') as int)
and report = 'end_of_day'
and wallet_entity = '{wallet_entity}'

UNION ALL

SELECT
    'TOTAL' AS `Wallet Holder`,
    cast(SUM(opening_cash_balance) as decimal(18,2)) AS `Opening Cash Balance in CMA`,
    cast(SUM(cit_received) as decimal(18,2)) AS `CIT Received (exclude chargeback)`,
    cast(SUM(chargeback_received) as decimal(18,2)) AS `Chargeback Received`,
    cast(SUM(refund_received) as decimal(18,2)) AS `Refunds Received in CMA`,
    cast(SUM(total_cash_received_into_cma) as decimal(18,2)) AS `Total Cash Received into CMA`,
    cast(SUM(cash_adjustment) as decimal(18,2)) AS `Safeguarding Adjustment Processed`,
    cast(SUM(expected_ending_cash_balance) as decimal(18,2)) AS `Net Cash Position`,
    cast(SUM(balance_to_safeguard) as decimal(18,2)) AS `Balance to be Safeguarded`,
    cast(SUM(wallet_liability_end_balance) as decimal(18,2)) AS `Ending Wallet liability balance on Prior Day`,
    cast(SUM(actual_ending_cash_balance) as decimal(18,2)) AS `Actual Ending Cash Balance in CMA`,
    cast(SUM(safeguarding_adjustment_amount) as decimal(18,2)) AS `Safeguarding Adjustment Required`,
    cast(SUM(end_of_day_discrepancy) as decimal(18,2)) AS `End of Day Discrepancy`,
    MAX(wallet_entity) AS wallet_entity,
    MAX(datadate) AS datadate
FROM {finance_wallet_db}.cashflow_report
WHERE datadate = cast(date_format(to_date('{date}','yyyyMMdd'), 'yyyyMMdd') as int)
  AND report = 'end_of_day'
  AND wallet_entity = '{wallet_entity}'

ORDER BY CASE WHEN `Wallet Holder` = 'TOTAL' THEN 1 ELSE 0 END

"""
