include classpath("_generic_staging.conf")
include "settings.conf"


app.adpMessaging.appName: "WALLET_SAFEGUARDING_REPORTS_SFTP_Uploader.lsit.processor"


hadoop {
  hdfs.user = "hk-fin-wal-svc--dev"
  knox.enabled = true
  hive.username = "hk-fin-wal-svc--dev"
  credentials = "hadoop/hk-fin-wal-svc--dev/credentials"
  credentials_from_env = true
  use-smart-table-loader = true
}

database = "finance_wallet_lsit_cashflow"

configUploader{
    dateFormat = "yyyyMMdd"
    filePath = "s3a://"${hadoop.hdfs.user}/reports/${project-name}/safeguarding_reports/uk/lsit/

    enrichDFConfig{
      dropColumns  = ["wallet_entity", "datadate"]
    }

    saveFile{
      checkEmptyFile = false // TODO what should this be ?
      csvConf = {
        needHeader = true
        customDelimiter = ","
      }
    }

    deleteHdfsFiles = false
}

result {
  mail {
    alert-sender = "<EMAIL> (Uploader)"
    alert-recipients = "<EMAIL>"
  }
}

current_env = "lsit"

ag-vault {
  http {
    url = "https://hk.qa-vault.agoda.local:8200"
    auth {
      method = "approle"
    }
  }
}
