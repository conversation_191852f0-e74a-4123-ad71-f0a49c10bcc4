query="""
SELECT
    wallet_account_id AS `Wallet Holder`,
    cast(opening_cash_balance as decimal(18,2)) AS `Opening Cash Balance in CMA`,
    cast(cit_pending_before_obligation as decimal(18,2)) AS `CIT Pending (less than or equal to 5 days) (exclude chargeback)`,
    cast(cit_pending_after_obligation as decimal(18,2)) AS `CIT Pending (greater than 5 days) (exclude chargeback)`,
    cast(cit_adjustment as decimal(18,2)) AS `CIT Adjustment (Pre Funding Required)`,
    cast(cit_received as decimal(18,2)) AS `CIT Received in CMA (exclude chargeback)`,
    cast(chargeback_pending as decimal(18,2)) AS `Chargeback Pending`,
    cast(chargeback_received as decimal(18,2)) AS `Chargeback Received`,
    cast(refund_pending as decimal(18,2)) AS `Refunds Pending`,
    cast(refund_received as decimal(18,2)) AS `Refunds Received in CMA`,
    cast(total_cash_received_into_cma_intra_day as decimal(18,2)) AS `Total Cash Received into CMA`,
    cast(intraday_cash_balance as decimal(18,2)) AS `Intraday Cash Balance in CMA`,
    cast(utilization as decimal(18,2)) AS `Utilization`,
    cast(withdrawal as decimal(18,2)) AS `Withdrawal`,
    cast(cumulative_pending_payment_from_cma as decimal(18,2)) AS `Cumulative Pending Payment from CMA`,
    cast(expected_ending_cash_balance as decimal(18,2)) AS `Net Cash Position`,
    cast(balance_to_safeguard as decimal(18,2)) AS `Balance to be Safeguarded`,
    cast(wallet_liability_end_balance as decimal(18,2)) AS `Ending Wallet liability balance on Prior Day`,
    cast(cash_discrepancy as decimal(18,2)) AS `Cash Discrepancy`,
    cast(expected_safeguarding_adjustment_amount as decimal(18,2)) AS `Expected Safeguarding Adjustment`,
    cast(safeguarding_adjustment_amount as decimal(18,2)) AS `Actual Safeguarding Adjustment Required`,
    wallet_entity,
    datadate
FROM {finance_wallet_db}.cashflow_report
where datadate = cast(date_format(to_date('{date}','yyyyMMdd'), 'yyyyMMdd') as int)
and report = 'intra_day'
and wallet_entity = '{wallet_entity}'

UNION ALL

SELECT
    'TOTAL' AS `Wallet Holder`,
    cast(SUM(opening_cash_balance) as decimal(18,2)) AS `Opening Cash Balance in CMA`,
    cast(SUM(cit_pending_before_obligation) as decimal(18,2)) AS `CIT Pending (less than or equal to 5 days) (exclude chargeback)`,
    cast(SUM(cit_pending_after_obligation) as decimal(18,2)) AS `CIT Pending (greater than 5 days) (exclude chargeback)`,
    cast(SUM(cit_adjustment) as decimal(18,2)) AS `CIT Adjustment (Pre Funding Required)`,
    cast(SUM(cit_received) as decimal(18,2)) AS `CIT Received in CMA (exclude chargeback)`,
    cast(SUM(chargeback_pending) as decimal(18,2)) AS `Chargeback Pending`,
    cast(SUM(chargeback_received) as decimal(18,2)) AS `Chargeback Received`,
    cast(SUM(refund_pending) as decimal(18,2)) AS `Refunds Pending`,
    cast(SUM(refund_received) as decimal(18,2)) AS `Refunds Received in CMA`,
    cast(SUM(total_cash_received_into_cma_intra_day) as decimal(18,2)) AS `Total Cash Received into CMA`,
    cast(SUM(intraday_cash_balance) as decimal(18,2)) AS `Intraday Cash Balance in CMA`,
    cast(SUM(utilization) as decimal(18,2)) AS `Utilization`,
    cast(SUM(withdrawal) as decimal(18,2)) AS `Withdrawal`,
    cast(SUM(cumulative_pending_payment_from_cma) as decimal(18,2)) AS `Cumulative Pending Payment from CMA`,
    cast(SUM(expected_ending_cash_balance) as decimal(18,2)) AS `Net Cash Position`,
    cast(SUM(balance_to_safeguard) as decimal(18,2)) AS `Balance to be Safeguarded`,
    cast(SUM(wallet_liability_end_balance) as decimal(18,2)) AS `Ending Wallet liability balance on Prior Day`,
    cast(SUM(cash_discrepancy) as decimal(18,2)) AS `Cash Discrepancy`,
    cast(SUM(expected_safeguarding_adjustment_amount) as decimal(18,2)) AS `Expected Safeguarding Adjustment`,
    cast(SUM(safeguarding_adjustment_amount) as decimal(18,2)) AS `Actual Safeguarding Adjustment Required`,
    MAX(wallet_entity) AS wallet_entity,
    MAX(datadate) AS datadate
FROM {finance_wallet_db}.cashflow_report
WHERE datadate = cast(date_format(to_date('{date}','yyyyMMdd'), 'yyyyMMdd') as int)
  AND report = 'intra_day'
  AND wallet_entity = '{wallet_entity}'

ORDER BY CASE WHEN `Wallet Holder` = 'TOTAL' THEN 1 ELSE 0 END

"""
