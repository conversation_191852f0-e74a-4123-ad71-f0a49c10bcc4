query="""
SELECT
    wallet_account_id AS `Wallet Holder`,
    cast(wallet_liability_opening_balance as decimal(18,2)) AS `E-Wallet Opening Balance`,
    cast(wallet_liability_top_up as decimal(18,2)) AS `E-Wallet Top Up`,
    cast(wallet_liability_utilization as decimal(18,2)) AS `E-Wallet Utilization`,
    cast(wallet_liability_withdrawal as decimal(18,2)) AS `E-Wallet Withdrawal`,
    cast(wallet_liability_refund as decimal(18,2)) AS `E-Wallet Refund`,
    cast(wallet_liability_end_balance as decimal(18,2)) AS `E-Wallet Closing Balance`,
    cast(opening_cash_balance as decimal(18,2)) AS `Cash Position Opening Balance`,
    cast(cit_received as decimal(18,2)) AS `Cash Position Top Up`,
    cast(chargeback_received as decimal(18,2)) AS `Cash Position Chargebacks`,
    cast(utilization as decimal(18,2)) AS `Cash Position Utilization`,
    cast(withdrawal as decimal(18,2)) AS `Cash Position Withdrawal`,
    cast(refund_received as decimal(18,2)) AS `Cash Position Refund`,
    cast(expected_ending_cash_balance as decimal(18,2)) AS `Cash Position Closing Balance`,
    cast(cit_pending_before_obligation as decimal(18,2)) AS `Safeguarding CIT (less than or equal to 5 days)`,
    cast(cit_pending_after_obligation as decimal(18,2)) AS `Safeguarding CIT (greater than 5 days)`,
    cast(balance_to_safeguard as decimal(18,2)) AS `Safeguarding Balance to be Safeguarded`,
    cast(intraday_cash_balance as decimal(18,2)) AS `Safeguarding Actual Cash Balance in CMA`,
    cast(safeguarding_adjustment_amount as decimal(18,2)) AS `Safeguarding Adjustment Required`,
    wallet_entity,
    datadate
FROM {finance_wallet_db}.cashflow_report
where datadate = cast(date_format(to_date('{date}','yyyyMMdd'), 'yyyyMMdd') as int)
and report = 'intra_day'
and wallet_entity = '{wallet_entity}'

UNION ALL

SELECT
    'TOTAL' AS `Wallet Holder`,
    cast(SUM(wallet_liability_opening_balance) as decimal(18,2)) AS `E-Wallet Opening Balance`,
    cast(SUM(wallet_liability_top_up) as decimal(18,2)) AS `E-Wallet Top Up`,
    cast(SUM(wallet_liability_utilization) as decimal(18,2)) AS `E-Wallet Utilization`,
    cast(SUM(wallet_liability_withdrawal) as decimal(18,2)) AS `E-Wallet Withdrawal`,
    cast(SUM(wallet_liability_refund) as decimal(18,2)) AS `E-Wallet Refund`,
    cast(SUM(wallet_liability_end_balance) as decimal(18,2)) AS `E-Wallet Closing Balance`,
    cast(SUM(opening_cash_balance) as decimal(18,2)) AS `Cash Position Opening Balance`,
    cast(SUM(cit_received) as decimal(18,2)) AS `Cash Position Top Up`,
    cast(SUM(chargeback_received) as decimal(18,2)) AS `Cash Position Chargebacks`,
    cast(SUM(utilization) as decimal(18,2)) AS `Cash Position Utilization`,
    cast(SUM(withdrawal) as decimal(18,2)) AS `Cash Position Withdrawal`,
    cast(SUM(refund_received) as decimal(18,2)) AS `Cash Position Refund`,
    cast(SUM(expected_ending_cash_balance) as decimal(18,2)) AS `Cash Position Closing Balance`,
    cast(SUM(cit_pending_before_obligation) as decimal(18,2)) AS `Safeguarding CIT (less than or equal to 5 days)`,
    cast(SUM(cit_pending_after_obligation) as decimal(18,2)) AS `Safeguarding CIT (greater than 5 days)`,
    cast(SUM(balance_to_safeguard) as decimal(18,2)) AS `Safeguarding Balance to be Safeguarded`,
    cast(SUM(intraday_cash_balance) as decimal(18,2)) AS `Safeguarding Actual Cash Balance in CMA`,
    cast(SUM(safeguarding_adjustment_amount) as decimal(18,2)) AS `Safeguarding Adjustment Required`,
    MAX(wallet_entity) AS wallet_entity,
    MAX(datadate) AS datadate
FROM {finance_wallet_db}.cashflow_report
WHERE datadate = cast(date_format(to_date('{date}','yyyyMMdd'), 'yyyyMMdd') as int)
  AND report = 'intra_day'
  AND wallet_entity = '{wallet_entity}'

ORDER BY CASE WHEN `Wallet Holder` = 'TOTAL' THEN 1 ELSE 0 END
"""
