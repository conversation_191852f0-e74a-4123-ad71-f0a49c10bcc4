include "lsit.conf"

configUploader{

  queryInfos = [
    {
      queryName = "end_of_day_report",
      include "query_end_of_day.conf",
      fileName= "BHFS_UK_SAFEGUARDING_END_OF_DAY_REPORT_{date}",
      fileType = "csv"
      historicalTracking = {
        tableName = ${database}.safeguarding_end_of_day
        partitionColumns = ["datadate", "wallet_entity"]
      }
    }
  ]

  destinationsConfig = [
    {
      reportParameters={"finance_wallet_db": "finance_wallet_lsit_cashflow", "wallet_entity": "BHFS_UK"},
      destinations={"sftp":["BHFSSftpConfig"]},
      instanceName="wallet_safeguarding_reports_sftp"
    }
  ]


  // TODO to update later
  BHFSSftpConfig {
    address = "sftp://sftpconnect.agoda.local:22"
    destinationPath = "/OracleWallet/Outbound/IntercoRefundMapping/UK/dev/"
    vaultSecretLocation = "dataproject/hk-fin-wal-svc/uploader"
    hdfsBackupCredentialLocation = /apps/${project-name}/backupCredential/${app-name}/bhfs_sftp_credentials.json
    retryCount = 3
  }

}