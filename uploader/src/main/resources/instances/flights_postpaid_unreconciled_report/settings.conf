app-name = "flights_postpaid_unreconciled_report"
database = "finance_uploader_staging"
hadoop.spark {
	executor {
		count = 15
		cores-per-executor = 6
		memory-per-executor = 20GB
	}
}
configUploader {
	jobFrequency: Daily
	dateFormat = "yyyyMMdd"
	filePath = "s3a://"${hadoop.hdfs.user}/apps/${project-name}/${app-name}/

	queryInfos = [
		{
			queryName = "Reconcile",
			include "query.conf",
			fileName = "{supplier_name}_flight_postpaid_unreconciled_{date}",
			fileType = "",
			historicalTracking = {
				tableName = ${database}.${app-name}
				partitionColumns = ["supplier_id", "report_date"]
			}
		}
	]

	destinationsConfig = [
		{
			reportParameters = {"supplier_id": "30038", "supplier_name": "JejuAir", "cycle": "biweekly"},
			destinations = {},
			instanceName = "30038",
		},
		{
			reportParameters = {"supplier_id": "30011", "supplier_name": "Travelboutiqueonline", "cycle": "weekly"},
			destinations = {},
			instanceName = "30011",
		},
		{
			reportParameters = {"supplier_id": "30084", "supplier_name": "EMT", "cycle": "weekly"},
			destinations = {},
			instanceName = "30084",
		},
		{
			reportParameters = {"supplier_id": "30065", "supplier_name": "ScootNDC", "cycle": "weekly"},
			destinations = {},
			instanceName = "30065",
		},
		{
			reportParameters = {"supplier_id": "30027", "supplier_name": "Scoot", "cycle": "weekly"},
			destinations = {},
			instanceName = "30027",
		},
		{
			reportParameters = {"supplier_id": "30018", "supplier_name": "PKFare", "cycle": "mon_wed_fri"},
			destinations = {},
			instanceName = "30018",
		},
		{
			reportParameters = {"supplier_id": "30085", "supplier_name": "Unififi", "cycle": "weekly"},
			destinations = {},
			instanceName = "30085",
		},
		{
			reportParameters = {"supplier_id": "30013", "supplier_name": "Airtrip", "cycle": "3_13_23"},
			destinations = {},
			instanceName = "30013",
		},
		{
			reportParameters = {"supplier_id": "30086", "supplier_name": "Flightroutes24", "cycle": "weekly"},
			destinations = {},
			instanceName = "30086",
		},
		{
			reportParameters = {"supplier_id": "30091", "supplier_name": "DidaDC", "cycle": "mon_wed"},
			destinations = {},
			instanceName = "30091",
		}
	]

	saveFile {
		checkEmptyFile = false
	}
	deleteHdfsFiles = false
}

result {
	mail {
		alert-sender = "<EMAIL> (<EMAIL>)"
		alert-recipients = "<EMAIL>"
	}
}