package com.agoda.finance.config

import com.agoda.finance.common.utils.{CommandLineUtils, DateUtils}
import com.agoda.finance.constant.Mode._
import com.agoda.finance.model.Arguments
import org.joda.time.DateTime

import scala.util.{Success, Try}

object ArgumentInfo {
  def process(args: String): Arguments = {

    val argsArr = args.split(" +")

    val usage = "Usage : " +
      "[[--date|date]=yyyy-MM-dd|yyyyMMdd] : default is current date [use mode=reprocess to activate this param] " +
      "[[--hour|hour]=\"\"] : processing hour, pass as {hour} to query " +
      "[[--mail|mail]=\"\"] : override the recipient email " +
      "[[--mode|mode]=reprocess|prod] : default is prod " +
      "[[--instance|instance]=\"\"] : run on specific destination instance"

    val setArgs = CommandLineUtils.getOpts(argsArr, usage)

    val today = DateTime.now.toString("yyyy-MM-dd")
    val date = Try(
      DateUtils
        .parseDateFromAcceptedFormats(setArgs("date"), List("yyyy-MM-dd", "yyyyMMdd"))
        .map(DateUtils.localDateToStringInFormat("yyyy-MM-dd"))
    ) match {
      case Success(Some(date)) => date
      case _                   => today
    }

    val hour = Try(setArgs("hour")).getOrElse("")
    val mail = Try(setArgs("mail")).getOrElse("")
    val mode = Try(
      setArgs("mode") match {
        case "reprocess" => Reprocess
        case _           => Prod
      }
    ).getOrElse(Prod)
    val instance = Try(setArgs("instance")).toOption.filter(_ != "_")
    Arguments(mail, date, mode, hour, instance)
  }
}
