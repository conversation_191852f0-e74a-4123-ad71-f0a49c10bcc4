package com.agoda.finance.processors.prepareDataframe.piiData

import com.agoda.booking.query.client.{GraphQLRequest, QueryBuilder}
import com.agoda.commons.vault.client.VaultClient
import com.agoda.commons.vault.client.scala.VaultSecret
import com.agoda.finance.common.utils.DataFrameUtils.explodeStructToColumns
import com.agoda.finance.constant.TrackingStatus.{GetPIIData, GetPIIDataFailed}
import com.agoda.finance.model.BapiPIIData.{BapiData, PIIDFModel}
import com.agoda.finance.model.ExecutionInfo
import com.agoda.finance.model.prepareDataframe.{DataframeInformation, DataframeInformationWithTracker, PrepareDFResult}
import com.agoda.finance.model.vault.BapiApiKey
import com.agoda.finance.utils.CommonDeserializer.BapiDataDeserializer
import com.agoda.finance.utils.RetryUtil.retry
import com.agoda.finance.utils.TrackingUtil.createTracking
import com.typesafe.config.Config
import com.typesafe.scalalogging.LazyLogging
import org.apache.commons.io.IOUtils
import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.spark.sql.functions.{coalesce, col, struct}
import org.apache.spark.sql.{functions, DataFrame, SQLContext}
import org.json4s.DefaultFormats
import org.json4s.jackson.JsonMethods

import java.nio.charset.StandardCharsets.UTF_8
import scala.jdk.CollectionConverters.asScalaBufferConverter
import scala.util.Try

class AddGuestInfoPIIProcessor(bapiQ: BapiQClientGenerator)(implicit
    vaultClient: VaultClient,
    sqlContext: SQLContext,
    hdfs: FileSystem
) extends LazyLogging {

  def apply(prepareDFResults: Seq[PrepareDFResult], piiConfig: Config): Seq[PrepareDFResult] = {
    val bapiApiKey = getBapiApiKey(piiConfig)
    prepareDFResults.map { prepareDFResult =>
      prepareDFResult.dataframeInfos match {
        case Some(seqDF) if seqDF.nonEmpty && prepareDFResult.executionInfo.shouldContinue =>
          addPIIToDFs(prepareDFResult.executionInfo, seqDF, piiConfig, bapiApiKey)
        case _ =>
          val executionInfo = prepareDFResult.executionInfo.copy(shouldContinue = false)
          PrepareDFResult(executionInfo, None)
      }
    }
  }

  private def addPIIToDFs(
      executionInfo: ExecutionInfo,
      dataframeInformations: Seq[DataframeInformation],
      piiConfig: Config,
      bapiApiKey: Option[BapiApiKey]
  ): PrepareDFResult = {
    val transformedResults = dataframeInformations.map { dfInfo =>
      addPiiData(executionInfo, dfInfo, piiConfig, bapiApiKey)
    }

    val trackingResults = transformedResults.map(_.uploaderTracker)
    val shouldContinue  = !trackingResults.map(track => track.status).contains(GetPIIDataFailed)
    val dfInfos = shouldContinue match {
      case true  => Some(transformedResults.map(_.dataframeInformation))
      case false => None
    }

    val newExecutionInfo =
      executionInfo.copy(status = executionInfo.status ++ trackingResults, shouldContinue = shouldContinue)
    PrepareDFResult(newExecutionInfo, dfInfos)
  }

  private def bapiDataToPIIDFModel(bapiData: BapiData, bookingList: Set[Int]): Seq[PIIDFModel] =
    bapiData.itineraryDetailsByBookingIds.propertyDetails.flatMap { detail =>
      val properties  = detail.propertyDetails.properties.filter(property => bookingList.contains(property.bookingId))
      val memberEmail = detail.member.flatMap(_.email)
      val propertiesPii = properties.map { property =>
        val bookingId     = property.bookingId
        val guests        = Try(property.enigmaAPI.guests).toOption.getOrElse(Seq.empty)
        val firstGuest    = guests.find(guest => guest.guestNo == 1)
        val email         = memberEmail.orElse(firstGuest.flatMap(_.email))
        val propertyPhone = property.propertyAPI.property.headOption.flatMap(_.info.contact.phone)
        firstGuest match {
          case Some(guest) =>
            PIIDFModel(bookingId, guest.firstName, guest.lastName, email, propertyPhone)
          case _ =>
            PIIDFModel(bookingId, None, None, email, propertyPhone)
        }
      }

      val nonPropertiesPii = detail.bookings
        .filter(booking => bookingList.contains(booking.bookingId))                        //filter only the bookings that are in bookingList
        .filterNot(booking => propertiesPii.map(_.booking_id).contains(booking.bookingId)) //filter out the bookings that are already in propertiesPii
        .map(booking => PIIDFModel(booking.bookingId, None, None, memberEmail, None))
      nonPropertiesPii ++ propertiesPii // concat non-property pii with property pii
    }.distinct

  private def partitionBookingIds(bookingIds: Seq[Int], partitionSize: Int): Seq[Seq[Int]] =
    bookingIds.grouped(partitionSize).toSeq

  private[piiData] def substituteQueryString(bookingIdsStr: String, queryColumns: String): String =
    s"""{
       |  ItineraryDetailsByBookingIds(bookingIds: [$bookingIdsStr]){
       |    member {
       |      email
       |    }
       |    bookings {
       |      bookingId
       |    }
       |    propertyDetails{
       |      properties{
       |        bookingId
       |        enigmaAPI{
       |          guests{
       |            guestNo,$queryColumns
       |          }
       |        }
       |        propertyAPI {
       |          property {
       |            info {
       |              contact {
       |                phone
       |              }
       |            }
       |          }
       |        }
       |      }
       |    }
       |  }
       |}""".stripMargin

  private def deduplicateByBookingID(result: DataFrame): DataFrame = {
    val piiDFColWithoutBookingId =
      functions.max(struct(result.columns.filter(col => col != "booking_id").map(col): _*)).as("max_struct")
    val dedupPiiDF = explodeStructToColumns("max_struct")(
      result
        .groupBy("booking_id")
        .agg(
          piiDFColWithoutBookingId
        )
    )
    dedupPiiDF
  }

  private[piiData] def generatePIIDF(
      bookingIds: Seq[Int],
      bapiApiKey: BapiApiKey,
      selectedColumns: Seq[String],
      partitionSize: Int
  ): DataFrame = {
    import sqlContext.implicits._
    if (bookingIds.isEmpty) {
      Seq.empty[PIIDFModel].toDF.select(selectedColumns.map(col) :+ col("booking_id"): _*)
    } else {
      val client                = bapiQ.instantiateBapiClient(bapiApiKey)
      val partitionedBookingIds = partitionBookingIds(bookingIds, partitionSize)
      val queryColumns          = convertToBapiFormat(selectedColumns)
      val result = partitionedBookingIds
        .flatMap { bookingList =>
          val bookingIdsStr = bookingList.mkString(",")
          val queryString   = substituteQueryString(bookingIdsStr, queryColumns)
          val queryResult = QueryBuilder.withQueryString(queryString).build match {
            case Right(value) =>
              val req      = GraphQLRequest(value, headers = Map(("include-archive", "true")))
              val bapiData = retry(3)(client.executeQuery[BapiData](req))
              bapiDataToPIIDFModel(bapiData, bookingList.toSet)
            case Left(value) =>
              throw new Exception(value)
          }
          queryResult
        }
        .toDF
        .select(selectedColumns.map(col) :+ col("booking_id"): _*)
      deduplicateByBookingID(result)
    }
  }

  private[piiData] def addPiiData(
      executionInfo: ExecutionInfo,
      dfInfo: DataframeInformation,
      piiConfig: Config,
      bapiApiKey: Option[BapiApiKey],
      partitionSize: Int = 20
  ): DataframeInformationWithTracker =
    bapiApiKey match {
      case Some(bapiApiKey) =>
        try {
          val df = dfInfo.dataframe.getOrElse(sqlContext.emptyDataFrame)
          val bookingIds =
            df
              .select("agoda_booking_id")
              .distinct()
              .na
              .drop()
              .collect()
              .map(row => row.getAs[Int]("agoda_booking_id"))
              .distinct
          val piiColumn    = piiConfig.getStringList("piiColumns").asScala
          val piiDF        = generatePIIDF(bookingIds, bapiApiKey, piiColumn, partitionSize).cache
          val commonColumn = df.columns intersect piiDF.columns
          val uncommon     = df.columns diff commonColumn
          val uncommonPii  = piiDF.columns diff commonColumn
          val finalDF = df
            .join(piiDF, piiDF("booking_id") === df("agoda_booking_id"), "left")
            .select(commonColumn.map(c => coalesce(piiDF(c), df(c)).as(c)) ++ uncommon.map(col) ++ uncommonPii.map(col): _*)
            .drop("booking_id")
          val trackingResult = createTracking(
            GetPIIData,
            executionInfo,
            detail = s"Get Guest PII Data (${piiColumn.mkString(",")}) Successfully",
            fileName = dfInfo.getFullPath
          )
          DataframeInformationWithTracker(
            trackingResult,
            dfInfo.copy(dataframe = Some(finalDF))
          )
        } catch {
          case e: Exception =>
            logger.error(s"Error while getting PII data: ${e.getMessage}", e)
            val trackingResult = createTracking(
              GetPIIDataFailed,
              executionInfo,
              detail = s"Get Pii Data failed: ${e.toString}",
              fileName = dfInfo.getFullPath
            )

            DataframeInformationWithTracker(
              trackingResult,
              dfInfo.copy(dataframe = None)
            )
        }

      case None => updatePrepareResultWithNoneKey(executionInfo, dfInfo)
    }

  private def convertToBapiFormat(piiColumns: Seq[String]): String =
    piiColumns
      .collect {
        case "first_name" => "firstName"
        case "last_name"  => "lastName"
        case "email"      => "email"
      }
      .mkString(",")

  private def updatePrepareResultWithNoneKey(
      executionInfo: ExecutionInfo,
      dfInfo: DataframeInformation
  ): DataframeInformationWithTracker = {
    val trackingResult = createTracking(
      GetPIIDataFailed,
      executionInfo,
      detail = "GetPIIData failed: BapiKey is missing",
      fileName = dfInfo.getFullPath
    )

    DataframeInformationWithTracker(
      trackingResult,
      dfInfo.copy(dataframe = None)
    )

  }

  private[piiData] def getBapiApiKey(piiConfig: Config): Option[BapiApiKey] =
    Try {
      getBapiKeyFromVault(piiConfig)
    }.getOrElse(Try(getBapiKeyFromBackUp(piiConfig)).getOrElse(None))

  private[piiData] def getBapiKeyFromVault(piiConfig: Config): Option[BapiApiKey] = {
    // did not test
    val vaultSecretLocations = piiConfig.getString("vaultSecretLocation")
    val bapiApiKey           = VaultSecret[BapiApiKey](vaultSecretLocations, vaultClient).get
    Some(bapiApiKey.get())
  }

  private def getBapiKeyFromBackUp(piiConfig: Config): Option[BapiApiKey] = {
    val pathToFile       = piiConfig.getString("backupCredentialLocation")
    val fsPath           = hdfs.getWorkingDirectory.toUri.getPath
    val jsonFile         = IOUtils.toString(hdfs.open(new Path(fsPath + pathToFile)), UTF_8)
    implicit val formats = DefaultFormats
    Some(JsonMethods.parse(jsonFile).extract[BapiApiKey])
  }

}
