package com.agoda.finance.utils

import com.agoda.finance.common.utils.HadoopUtils
import com.agoda.finance.constant.SenderType.{EMAIL, HTTP, SFTP, SHAREPOINT}
import com.agoda.finance.constant.TrackingStatus
import com.agoda.finance.constant.TrackingStatus.{SendEmail, UploadHTTP, UploadSFTP, UploadSharepoint}
import com.agoda.finance.model.{ExecutionInfo, TrackingConf, UploaderTracker}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{DataFrame, SQLContext}
import org.joda.time.DateTime

import java.sql.Timestamp

object TrackingUtil extends LazyLogging {
  def createTracking(
      status: TrackingStatus.Value,
      executionInfo: ExecutionInfo,
      detail: String,
      count: Option[Long] = None,
      timestamp: Timestamp = new Timestamp(System.currentTimeMillis()),
      fileName: String
  ): UploaderTracker =
    UploaderTracker(
      status,
      timestamp,
      detail,
      executionInfo.instanceName,
      fileName,
      executionInfo.filePath,
      count
    )

  def removeProcessedExecutionInfos(
      executionInfos: Seq[ExecutionInfo],
      trackingConf: TrackingConf,
      currentDatetime: DateTime = DateTime.now()
  )(implicit sqlContext: SQLContext): Seq[ExecutionInfo] =
    if (sqlContext.sparkSession.catalog.tableExists(trackingConf.trackingTable)) {
      val fileNamesTobeProcessed =
        executionInfos.flatMap(exec => exec.queryInformations.map(qi => qi.getFullPath))
      val trackingStatus = convertDestinationTypeToSendSuccessStatus(
        executionInfos.flatMap(exec => exec.destinations.keys).distinct
      )
      val startCheckingDate = currentDatetime
        .minusMonths(trackingConf.monthLookbackPeriod)
        .toString("yyyyMM")
      val fileNameWithStatus = sqlContext
        .table(trackingConf.trackingTable)
        .filter(
          col("status").isin(trackingStatus: _*) && col("file_name").isin(
            fileNamesTobeProcessed: _*
          )
            && col("datamonth") >= startCheckingDate.toInt
        )
        .select("file_name")
        .collect()
        .map(row => row.getAs[String]("file_name"))
      val unprocessedExecutionInfo =
        executionInfos.filter(exec => !exec.queryInformations.exists(queryInfo => fileNameWithStatus.contains(queryInfo.getFullPath)))
      val processedExecutionInfos = executionInfos
        .filter(exec => exec.queryInformations.exists(queryInfo => fileNameWithStatus.contains(queryInfo.getFullPath)))
        .map(_.instanceName)
        .mkString(",")
      logger.info(s"processed instances $processedExecutionInfos")
      unprocessedExecutionInfo
    } else {
      executionInfos
    }

  def convertDestinationTypeToSendSuccessStatus(
      destinationTypes: Seq[String]
  ): Seq[String] =
    destinationTypes
      .map {
        case SFTP       => UploadSFTP.toString
        case EMAIL      => SendEmail.toString
        case HTTP       => UploadHTTP.toString
        case SHAREPOINT => UploadSharepoint.toString
      }

  def saveTrackingResult(
      executionInfos: Seq[ExecutionInfo],
      trackingConfOption: Option[TrackingConf],
      currentDateTime: DateTime = DateTime.now()
  )(implicit sqlContext: SQLContext): Unit =
    trackingConfOption match {
      case Some(trackingConf: TrackingConf) =>
        val allStatus =
          convertToStatusToDF(executionInfos.flatMap(_.status)).withColumn(
            "datamonth",
            lit(currentDateTime.toString("yyyyMM").toInt)
          )
        HadoopUtils.writeToExternalTable(
          allStatus,
          trackingConf.trackingTable,
          "append",
          Seq("datamonth")
        )
      case _ => logger.info("Tracking Data is not saved")
    }

  def convertToStatusToDF(
      status: Seq[UploaderTracker]
  )(implicit sqlContext: SQLContext): DataFrame = {
    import sqlContext.implicits._
    status
      .map { stat =>
        (
          stat.status.toString,
          stat.logTimeStamp,
          stat.detail,
          stat.instanceName,
          stat.fileName,
          stat.filepath,
          stat.count
        )
      }
      .toDF(
        "status",
        "log_timestamp",
        "detail",
        "instance_name",
        "file_name",
        "file_path",
        "count"
      )
  }

  def transformHeaders(df: DataFrame): DataFrame = {
    val transformedColumns = df.columns.map { colName =>
      val transformedColName = colName.trim
        .replaceAll("\\s+", "_")
        .replaceAll("[-+.^:,()]", "")
      col(s"`$colName`").as(transformedColName)
    }

    df.select(transformedColumns: _*)
  }

}
