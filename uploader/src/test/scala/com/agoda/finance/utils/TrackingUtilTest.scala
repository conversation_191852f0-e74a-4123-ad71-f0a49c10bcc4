package com.agoda.finance.utils

import com.agoda.finance.common.utils.HadoopUtils
import com.agoda.finance.constant.TrackingStatus.{SendEmail, SendEmailFailed, Transformed, UploadHTTP, UploadSFTP, UploadSharepoint}
import com.agoda.finance.model.{ExecutionInfo, QueryInformation, TrackingConf, UploaderTracker}
import com.agoda.finance.utils.TrackingUtil.createTracking
import com.agoda.ml.spark.DataFrameMatchers.beEqualTo
import com.agoda.ml.spark.services.etl.{DataFrameETLExtensionLocalTest, SparkETLLocalTest}
import com.typesafe.config.ConfigFactory
import org.joda.time.DateTime

import java.sql.Timestamp

class TrackingUtilTest extends SparkETLLocalTest with DataFrameETLExtensionLocalTest {
  setupAll {
    sqlContext.sql(s"CREATE DATABASE IF NOT EXISTS finance_uploader")
  }
  teardown {
    sqlContext.sql(s"DROP TABLE IF EXISTS finance_uploader")
  }

  test("should successfully create uploaderTracker") {

    val queryInfos = Seq(
      QueryInformation(
        "df1",
        s"new_file_name",
        s"select hello from finance_supplier where product_type = vehicle and datadate = 20210101 and reconentity = commission",
        "csv"
      )
    )

    val executionInfo = ExecutionInfo(
      queryInfos,
      Map(),
      "fakepath/hello",
      "first_one",
      Map("email" -> Seq("email_config_1"), "sftp" -> Seq("sftp_config_1")),
      false,
      Seq(),
      "20220101"
    )
    val timestamp = new Timestamp(System.currentTimeMillis())
    val actual =
      createTracking(
        Transformed,
        executionInfo,
        timestamp = timestamp,
        detail = "detail",
        fileName = "new_file_name"
      )
    val expected = UploaderTracker(
      status = Transformed,
      logTimeStamp = timestamp,
      detail = "detail",
      instanceName = "first_one",
      fileName = "new_file_name",
      filepath = "fakepath/hello"
    )
    assert(actual == expected)
  }
  test(
    "removeProcessedExecutionInfos (remove the executionInfo even only one file is success out my many files inside an executionInfo)"
  ) {
    import sqlContext.implicits._
    val queryInformations = Seq(
      QueryInformation(
        "FireflyRefundReport",
        s"select long_col,string_col from testDB.test_1 where int_col = 12",
        s"new_file_name",
        "csv"
      ),
      QueryInformation(
        "FireflyRefundReport2",
        s"select long_col,string_col from testDB.test_1 where int_col = 12",
        s"new_file_name2",
        "csv"
      )
    )

    val secondExecutionInfo = ExecutionInfo(
      Seq(
        QueryInformation(
          "FireflyRefundReport2",
          s"select long_col,string_col from testDB.test_1 where int_col = 12",
          s"new_file_namel",
          "csv"
        )
      ),
      Map(),
      "fakepath/hello",
      "first_one",
      Map("email" -> Seq("email_config_1")),
      true,
      Seq(),
      "20220101"
    )

    val executionInfos = Seq(
      ExecutionInfo(
        queryInformations,
        Map(),
        "fakepath/hello",
        "first_one",
        Map("email" -> Seq("email_config_1")),
        true,
        Seq(),
        "20220101"
      ),
      secondExecutionInfo
    )
    val trackingDF = Seq(("new_file_name2", "SendEmail", 202201)).toDF(
      "file_name",
      "status",
      "datamonth"
    )
    HadoopUtils.writeToExternalTable(
      trackingDF,
      "finance_uploader.test",
      "append",
      Seq("datamonth")
    )
    val trackingConf = TrackingConf("finance_uploader.test", 3)
    val actual = TrackingUtil.removeProcessedExecutionInfos(
      executionInfos,
      trackingConf,
      new DateTime(2022, 3, 26, 12, 0, 0, 0)
    )
    val expected = Seq(secondExecutionInfo)
    assert(actual == expected)
  }
  test(
    "removeProcessedExecutionInfos (remove the executionInfo even only one protocol is success out my many protocol inside an executionInfo)"
  ) {
    import sqlContext.implicits._
    val queryInformations = Seq(
      QueryInformation(
        "FireflyRefundReport2",
        s"select long_col,string_col from testDB.test_1 where int_col = 12",
        s"new_file_name2",
        "csv"
      )
    )

    val secondExecutionInfo = ExecutionInfo(
      Seq(
        QueryInformation(
          "FireflyRefundReport2",
          s"select long_col,string_col from testDB.test_1 where int_col = 12",
          s"new_file_namel",
          "csv"
        )
      ),
      Map(),
      "fakepath/hello",
      "first_one",
      Map("email" -> Seq("email_config_1")),
      true,
      Seq(),
      "20220101"
    )

    val executionInfos = Seq(
      ExecutionInfo(
        queryInformations,
        Map(),
        "fakepath/hello",
        "first_one",
        Map("email" -> Seq("email_config_1"), "sftp" -> Seq("hello")),
        true,
        Seq(),
        "20220101"
      ),
      secondExecutionInfo
    )
    val trackingDF = Seq(("new_file_name2", "SendEmail", 202201)).toDF(
      "file_name",
      "status",
      "datamonth"
    )
    HadoopUtils.writeToExternalTable(
      trackingDF,
      "finance_uploader.test2",
      "append",
      Seq("datamonth")
    )
    val trackingConf = TrackingConf("finance_uploader.test2", 3)
    val actual = TrackingUtil.removeProcessedExecutionInfos(
      executionInfos,
      trackingConf,
      new DateTime(2022, 3, 26, 12, 0, 0, 0)
    )
    val expected = Seq(secondExecutionInfo)
    assert(actual == expected)
  }

  test(
    "removeProcessedExecutionInfos (do not remove anything because tracking table does not exist)"
  ) {
    import sqlContext.implicits._
    val queryInformations = Seq(
      QueryInformation(
        "FireflyRefundReport2",
        s"select long_col,string_col from testDB.test_1 where int_col = 12",
        s"new_file_name2",
        "csv"
      )
    )

    val executionInfos = Seq(
      ExecutionInfo(
        queryInformations,
        Map(),
        "fakepath/hello",
        "first_one",
        Map("email" -> Seq("email_config_1"), "sftp" -> Seq("hello")),
        true,
        Seq(),
        "20220101"
      )
    )
    val trackingDF = Seq(("new_file_name2", "SendEmail", 202201)).toDF(
      "file_name",
      "status",
      "datamonth"
    )
    val trackingConf = TrackingConf("finance_uploader.test7", 3)
    val actual = TrackingUtil.removeProcessedExecutionInfos(
      executionInfos,
      trackingConf,
      new DateTime(2022, 3, 26, 12, 0, 0, 0)
    )
    assert(actual == executionInfos)
  }

  test(
    "removeProcessedExecutionInfos (do not remove anything because success status already pass the lookback period)"
  ) {
    import sqlContext.implicits._
    val queryInformations = Seq(
      QueryInformation(
        "FireflyRefundReport2",
        s"select long_col,string_col from testDB.test_1 where int_col = 12",
        s"new_file_name2",
        "csv"
      )
    )

    val executionInfos = Seq(
      ExecutionInfo(
        queryInformations,
        Map(),
        "fakepath/hello",
        "first_one",
        Map("email" -> Seq("email_config_1"), "sftp" -> Seq("hello")),
        true,
        Seq(),
        "20220101"
      )
    )
    val trackingDF = Seq(("new_file_name2", "SendEmail", 202001)).toDF(
      "file_name",
      "status",
      "datamonth"
    )
    HadoopUtils.writeToExternalTable(
      trackingDF,
      "finance_uploader.test6",
      "append",
      Seq("datamonth")
    )
    val trackingConf = TrackingConf("finance_uploader.test6", 3)
    val actual = TrackingUtil.removeProcessedExecutionInfos(
      executionInfos,
      trackingConf,
      new DateTime(2022, 3, 26, 12, 0, 0, 0)
    )
    assert(actual == executionInfos)
  }
  test("convertDestinationTypeToSendSuccessStatus successfully") {
    val input    = Seq("sftp", "email", "http", "sharepoint")
    val actual   = TrackingUtil.convertDestinationTypeToSendSuccessStatus(input)
    val expected = Seq(UploadSFTP.toString, SendEmail.toString, UploadHTTP.toString, UploadSharepoint.toString)
    assert(actual == expected)
  }

  test("convertDestinationTypeToSendSuccessStatus failed unexpected protocol") {
    val input = Seq("sftp", "email", "test")
    assertThrows[MatchError] {
      TrackingUtil.convertDestinationTypeToSendSuccessStatus(input)
    }
  }
  test("saveTrackingResult should be done successfully") {
    import sqlContext.implicits._
    val queryInformations = Seq(
      QueryInformation(
        "FireflyRefundReport2",
        s"select long_col,string_col from testDB.test_1 where int_col = 12",
        s"new_file_name2",
        "csv"
      )
    )
    val timestamp = Timestamp.valueOf("2021-01-03 23:00:00")

    val executionInfos = Seq(
      ExecutionInfo(
        queryInformations,
        Map(),
        "fakepath/hello",
        "first_one",
        Map("email" -> Seq("email_config_1"), "sftp" -> Seq("hello")),
        true,
        Seq(
          UploaderTracker(
            status = Transformed,
            logTimeStamp = timestamp,
            detail = "detail",
            instanceName = "first_one",
            fileName = "new_file_name",
            filepath = "fakepath/hello"
          ),
          UploaderTracker(
            status = SendEmail,
            logTimeStamp = timestamp,
            detail = "detail",
            instanceName = "first_one",
            fileName = "new_file_name",
            filepath = "fakepath/hello"
          )
        ),
        "20220101"
      ),
      ExecutionInfo(
        queryInformations,
        Map(),
        "fakepath/hello",
        "first_one",
        Map("email" -> Seq("email_config_1"), "sftp" -> Seq("hello")),
        true,
        Seq(
          UploaderTracker(
            status = Transformed,
            logTimeStamp = timestamp,
            detail = "detail",
            instanceName = "first_one",
            fileName = "new_file_name",
            filepath = "fakepath/hello"
          ),
          UploaderTracker(
            status = SendEmailFailed,
            logTimeStamp = timestamp,
            detail = "detail",
            instanceName = "first_one",
            fileName = "new_file_name",
            filepath = "fakepath/hello",
            count = Some(10)
          )
        ),
        "20220101"
      )
    )
    val trackingConf = Some(TrackingConf("finance_uploader.test10", 3))
    TrackingUtil.saveTrackingResult(
      executionInfos,
      trackingConf,
      new DateTime(2022, 3, 26, 12, 0, 0, 0)
    )
    val expected = Seq(
      (
        "SendEmailFailed",
        timestamp,
        "detail",
        "first_one",
        "new_file_name",
        "fakepath/hello",
        Some(10.toLong),
        202203
      ),
      (
        "SendEmail",
        timestamp,
        "detail",
        "first_one",
        "new_file_name",
        "fakepath/hello",
        None,
        202203
      ),
      (
        "Transformed",
        timestamp,
        "detail",
        "first_one",
        "new_file_name",
        "fakepath/hello",
        None,
        202203
      ),
      (
        "Transformed",
        timestamp,
        "detail",
        "first_one",
        "new_file_name",
        "fakepath/hello",
        None,
        202203
      )
    ).toDF(
      "status",
      "log_timestamp",
      "detail",
      "instance_name",
      "file_name",
      "file_path",
      "count",
      "datamonth"
    )
    val actual = sqlContext.table("finance_uploader.test10")
    actual should beEqualTo(expected)
  }
  test(
    "saveTrackingResult should not have any problem when trackingConf is None"
  ) {
    val trackingConf = None
    val executionInfos = Seq(
      ExecutionInfo(
        Seq(
          QueryInformation(
            "FireflyRefundReport2",
            s"select long_col,string_col from testDB.test_1 where int_col = 12",
            s"new_file_name2",
            "csv"
          )
        ),
        Map(),
        "fakepath/hello",
        "first_one",
        Map("email" -> Seq("email_config_1"), "sftp" -> Seq("hello")),
        true,
        Seq(),
        "20220101"
      )
    )
    TrackingUtil.saveTrackingResult(
      executionInfos,
      trackingConf
    )
  }

  test("transformHeaders") {
    import sqlContext.implicits._
    val rows = Seq(
      ("a", "b", "c", "d", "e", "k"),
      ("f", "g", "h", "i", "j", "l")
    )
    val df = rows.toDF(
      "Producer Code",
      "Policy No.",
      "Test Col 1",
      "Test Col+ 2 ",
      " Test Col-+.    3",
      " Test Col-+.()    4"
    )

    val expected = rows.toDF(
      "Producer_Code",
      "Policy_No",
      "Test_Col_1",
      "Test_Col_2",
      "Test_Col_3",
      "Test_Col_4"
    )
    val actual = TrackingUtil.transformHeaders(df)

    actual shouldEqual expected
  }

  test(
    "removeProcessedExecutionInfos should use fullpath if present, otherwise fallback to fileName"
  ) {
    import sqlContext.implicits._
    val queryInformations = Seq(
      QueryInformation(
        "FireflyRefundReport",
        s"select long_col,string_col from testDB.test_1 where int_col = 12",
        s"new_file_name",
        "csv",
        filePathPrefix = Some("custom_"),
        filePathSuffix = Some("_file_name")
      ),
      QueryInformation(
        "FireflyRefundReport2",
        s"select long_col,string_col from testDB.test_1 where int_col = 12",
        s"new_file_name2",
        "csv"
        // No prefix/suffix, fallback to fileName
      )
    )

    val executionInfos = Seq(
      ExecutionInfo(
        queryInformations,
        Map(),
        "fakepath/hello",
        "first_one",
        Map("email" -> Seq("email_config_1")),
        true,
        Seq(),
        "20220101"
      )
    )

    // The tracking table contains both the custom and fallback file names
    val trackingDF = Seq(
      ("custom_new_file_name_file_name", "SendEmail", 202201),
      ("new_file_name2", "SendEmail", 202201)
    ).toDF("file_name", "status", "datamonth")

    HadoopUtils.writeToExternalTable(
      trackingDF,
      "finance_uploader.test_fallback",
      "append",
      Seq("datamonth")
    )
    val trackingConf = TrackingConf("finance_uploader.test_fallback", 3)
    val actual = TrackingUtil.removeProcessedExecutionInfos(
      executionInfos,
      trackingConf,
      new DateTime(2022, 3, 26, 12, 0, 0, 0)
    )
    // Both QueryInformation entries are considered processed, so the result should be empty
    val expected = Seq.empty[ExecutionInfo]
    assert(actual == expected)
  }
}
