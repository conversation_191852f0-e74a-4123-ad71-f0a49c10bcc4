package com.agoda.finance.config

import com.agoda.finance.model.Arguments
import org.joda.time.DateTime
import org.scalatest.FunSuite

class ArgumentInfoTest extends FunSuite {

  val currentDate: String = DateTime.now.toString("yyyy-MM-dd")
  test("get email and date yyyy-MM-dd correctly") {
    assert(
      ArgumentInfo.process("mail=<EMAIL> date=2022-01-01") == Arguments(
        mail = "<EMAIL>",
        date = "2022-01-01"
      )
    )
  }

  test("get date yyyyMMdd correctly") {
    assert(
      ArgumentInfo.process("date=20220101") == Arguments(
        date = "2022-01-01"
      )
    )
  }

  test("get hour correctly") {
    assert(
      ArgumentInfo.process("hour=12 date=2022-02-02") == Arguments(
        hour = "12",
        date = "2022-02-02"
      )
    )
  }

  test("get instance correctly") {
    assert(
      ArgumentInfo.process("instance=pkfare date=2022-02-02") == Arguments(
        instance = Some("pkfare"),
        date = "2022-02-02"
      )
    )
  }

  test("get empty instance correctly") {
    assert(
      ArgumentInfo.process("instance=_ date=2022-02-02") == Arguments(
        date = "2022-02-02"
      )
    )
  }
}
