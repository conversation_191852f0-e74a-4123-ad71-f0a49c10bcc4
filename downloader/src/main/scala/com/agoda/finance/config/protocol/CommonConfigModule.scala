package com.agoda.finance.config.protocol

import com.agoda.finance.constant.Encryption.{Encryption, PGPEncryption}
import com.agoda.finance.constant.FileType.isBspType
import com.agoda.finance.constant.{FileType, ProcessMode, SaveMode}
import com.agoda.finance.model._
import com.agoda.finance.utils.VaultDecryptProvider
import com.typesafe.config._
import org.apache.spark.sql.types._

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Map.Entry
import scala.jdk.CollectionConverters.{asScalaBufferConverter, asScalaSetConverter}
import scala.util.Try

trait ConfigModule

trait CommonConfigModule extends ConfigModule {

  val process: Arguments
  val conf: Config
  val argDateFormat: String
  val fileList: String

  var isReprocess        = false
  val file: Config       = conf.getConfig(fileList)
  val offsetDate: Int    = Try(file.getInt("file.offset-date")).getOrElse(0)
  val dateFormat: String = file.getString("file.date-file-name-format")
  val isPIIData: Boolean = Try(file.getBoolean("is-pii-data")).getOrElse(false)

  private val processDate: LocalDate = {
    //only used for mode=dateWithPartitionDate, date, mirror, prod
    lazy val fileDateLocal = LocalDate
      .parse(
        process.fileDateName,
        DateTimeFormatter.ofPattern(argDateFormat)
      )
    //only used for mode=fileWithPartitionDate, file
    lazy val fileDateLocalFromFileName = {
      val regex =
        (".*(\\d{4}-\\d{2}-\\d{2}|\\d{4}/\\d{2}/\\d{2}|\\d{4}\\d{2}\\d{2}|\\d{4}[.]\\d{2}[.]\\d{2}|" +
          "\\d{2}-\\d{2}-\\d{4}|\\d{2}/\\d{2}/\\d{4}|\\d{2}\\d{2}\\d{4}|\\d{2}[.]\\d{2}[.]\\d{4}).*").r

      process.fileDateName match {
        case regex(date) => Try(LocalDate.parse(date, DateTimeFormatter.ofPattern(dateFormat))).getOrElse(LocalDate.now)
        case _           => LocalDate.now
      }
    }

    process.processStatus match {
      case ProcessMode.prod                                     => LocalDate.now.plusDays(offsetDate)
      case ProcessMode.mirror                                   => fileDateLocal.plusDays(offsetDate)
      case ProcessMode.file | ProcessMode.fileWithPartitionDate => fileDateLocalFromFileName
      case _                                                    => fileDateLocal
    }
  }

  val formattedDate: String = processDate.format(DateTimeFormatter.ofPattern(dateFormat))
  val year: String          = processDate.getYear.toString

  val VaultDecryptionHelper: VaultDecryptProvider
  lazy val passPhrase: String                = VaultDecryptionHelper.getPassPhrase()
  lazy val decryptPrivateKey: String         = VaultDecryptionHelper.getPrivateKey()
  lazy val publicKey: String                 = VaultDecryptionHelper.getPublicKey()
  lazy val previousPassphrase: String        = VaultDecryptionHelper.getPassPhrase(previous = true)
  lazy val previousDecryptPrivateKey: String = VaultDecryptionHelper.getPrivateKey(previous = true)
  lazy val previousPublicKey: String         = VaultDecryptionHelper.getPublicKey(previous = true)

  // https://gitlab.agodadev.io/FinancePlatform/reconciliation-platform/external-data-pipeline/-/merge_requests/1386
  // TODO - This is part of a larger effort to mask PII data
  val usingIOStream: Boolean = Try(conf.getBoolean("using-io-stream")).getOrElse(false)

  val proxy: String                  = Try(conf.getString("proxy")).getOrElse("")
  val generateUuidColumn: Boolean    = Try(conf.getBoolean("generate-uuid-column")).getOrElse(false)
  val fileType: String               = file.getString("file.type")
  val excelInferSchema: Boolean      = Try(file.getBoolean("file.excel-infer-schema")).getOrElse(false)
  val excelSheetIndex: Option[Int]   = Try(file.getInt("file.excel-sheet-index")).toOption
  val excelSheetName: Option[String] = Try(file.getString("file.excel-sheet-name")).toOption
  val jsonReadOption: Option[Map[String, String]] = Try(file.getConfig("file.json-read-option"))
    .map(
      _.entrySet().asScala
        .map(e => e.getKey -> e.getValue.unwrapped().toString)
        .toMap
    )
    .toOption
  val jsonColumnToBeExploded: Option[String] = Try(file.getString("file.json-column-to-be-exploded")).toOption
  val jsonColumnToBeString: Option[Boolean]  = Try(file.getBoolean("file.json-column-to-be-string")).toOption

  val delimiter: String = Try(file.getString("file.delimiter")).getOrElse(
    if (FileType.isExcelType(fileType)) {
      ";"
    } else {
      ",(?=(?:[^\"]*\"[^\"]*\")*(?![^\"]*\"))"
    }
  )
  val hasColumnHeader: Boolean = Try(file.getBoolean("file.has-column-header")).getOrElse(true)
  val fileEncryption: Option[Encryption] = Try(file.getString("file.encryption")).toOption match {
    case Some(t) if t.toLowerCase() == PGPEncryption.name.toLowerCase() => Some(PGPEncryption)
    case _                                                              => None
  }
  val isDeleted: Boolean    = Try(file.getBoolean("delete-file-after-downloaded")).getOrElse(false)
  val isZippedFile: Boolean = Try(file.getBoolean("is_compressed_file")).getOrElse(false)
  val tableName: String = if (process.processStatus == ProcessMode.mirror) {
    val schema = ConfigFactory.load().getString("schema")
    file
      .getString("table-name")
      .replace(
        schema,
        s"$schema${if (process.commitHash.equals("")) "" else s"_downloader_test_${process.commitHash.replaceAll("_true|_false", "")}"}"
      )
  } else {
    file.getString("table-name")
  }
  val trackingColumns: Option[List[String]] = Try(file.getStringList("tracking-columns").asScala.toList).toOption

  val saveMode: String = if (process.saveMode._1) {
    process.saveMode._2
  } else {
    Try(file.getString("save-mode")).getOrElse(SaveMode.APPEND)
  }

  // Supports partition keys for existing columns in the source and can auto-generate one of the two partition columns (`datadate` or `datamonth`).
  // Please specify only one auto-generate partition column and provide `partition-format` to define the desired format.
  val partKey: Seq[String] = file.getStringList("partition.key").asScala
  val partValue: String =
    if (process.processStatus == ProcessMode.dateWithPartitionDate || process.processStatus == ProcessMode.fileWithPartitionDate) {
      "partitiondate"
    } else {
      Try(file.getString("partition.value")).getOrElse("0")
    }
  val fileMapper: String      = if (file.hasPath("mapper")) file.getString("mapper") else null
  val isSkipChecking: Boolean = Try(file.getBoolean("is-skip-checking")).getOrElse(false) || process.isIgnoreChecking
  val fixLineBreak: Boolean   = Try(file.getBoolean("file.fix-line-break")).getOrElse(false)
  val fixUnicode: Boolean     = Try(file.getBoolean("file.fix-unicode")).getOrElse(false)
  val headerColumns: Seq[String] =
    Try(file.getStringList("enable-filter.header-columns").asScala).getOrElse(Seq[String]())

  val fileNamePatterns: Seq[String]     = file.getStringList("file.name").asScala
  val originalFileNameList: Seq[String] = fileNamePatterns.map(formatFileName).distinct //immutable - the file name as set in the config
  var fileNameList: Seq[String]         = fileNamePatterns.map(formatFileName).distinct //mutable - this might be changed to include lookback date
  val reqPayload: Option[String]        = None
  var payloadList: Seq[Option[String]]  = Seq(None)

  val fixedPositionIndex: Boolean = Try(file.getBoolean("file.fixed-position-index")).getOrElse(false)

  val loadedSheetName: Option[String] = Try(file.getString("file.sheet-name")).toOption

  val mappers: Map[String, List[MappingConfiguration]] =
    if (conf.hasPath("mappers")) {
      conf
        .getConfig("mappers")
        .entrySet
        .asScala
        .map { (f: Entry[String, ConfigValue]) =>
          val mappingList = conf
            .getConfig("mappers")
            .getConfigList(f.getKey)
            .asScala
            .toList
            .map { (element: Config) =>
              val mappingField = MappingField(
                element.getString("name"),
                DataTypeMapper.getDataType(element),
                element.getBoolean("isNullable"),
                Try(element.getString("alias")).getOrElse(element.getString("name")),
                Try(element.getBoolean("shouldTrim")).getOrElse(true),
                Try(element.getString("forbidRegex")).getOrElse(""),
                Try(element.getBoolean("isColumnExists")).getOrElse(true),
                Try(Option(element.getString("hardCodeValue"))).getOrElse(None)
              )
              val fixedColumnSpecification = if (fixedPositionIndex) {
                Some(FixedColumnSpecification(element.getInt("beginIndex"), element.getInt("endIndex")))
              } else {
                None
              }
              val fieldSpec = FieldSpecification(
                dropColumn = Try(element.getBoolean("dropColumn")).getOrElse(false),
                fixedColumn = fixedColumnSpecification
              )
              MappingConfiguration(mappingField, Some(fieldSpec))
            }
          f.getKey -> mappingList
        }(collection.breakOut)
    } else {
      Map.empty[String, List[MappingConfiguration]]
    }

  val bspMapper: Option[Map[String, List[BspFieldIdentifier]]] = if (isBspType(fileType)) {
    Option(
      conf
        .getConfig("bsp_file_mapper")
        .entrySet()
        .asScala
        .map { entry =>
          val key = entry.getKey
          val value = entry.getValue
            .asInstanceOf[ConfigList]
            .asScala
            .toList
            .map {
              case obj: ConfigObject => obj.toConfig
              case other             => throw new Exception(s"Expected ConfigObject, got: $other")
            }
          key -> {
            value.map { v =>
              BspFieldIdentifier(
                v.getString("name"),
                v.getString("dataType"),
                v.getBoolean("isNullable"),
                v.getString("alias"),
                v.getInt("beginIndex"),
                v.getInt("endIndex")
              )
            }
          }
        }
        .toMap
    )
  } else {
    None
  }

  val timeStampColFormat: String             = file.getString("timestamp-column-format.type")
  val FilterFooterEmptyRows: Boolean         = Try(file.getBoolean("enable-filter.filter-footer-empty-rows")).getOrElse(false)
  val FilterHeader: Option[Int]              = Try(Some(file.getInt("enable-filter.header-line-count"))).getOrElse(None)
  val FilterFooter: Option[Int]              = Try(Some(file.getInt("enable-filter.footer-line-count"))).getOrElse(None)
  val FilterLeftCol: Option[Int]             = Try(Some(file.getInt("enable-filter.left-column-count"))).getOrElse(None)
  val FilterRightCol: Option[Int]            = Try(Some(file.getInt("enable-filter.right-column-count"))).getOrElse(None)
  val filterEmptyFileContent: Option[String] = Try(Some(file.getString("enable-filter.empty-file-content"))).getOrElse(None)

  val LineCountLocationZone: Option[String] =
    Try(Some(file.getString("enable-filter.all-line-count.location-zone"))).getOrElse(None)
  val LineCountLocationPoint: Array[Option[Int]] = Array(
    Try(Some(file.getInt("enable-filter.all-line-count.row-location-point"))).getOrElse(None),
    Try(Some(file.getInt("enable-filter.all-line-count.column-location-point"))).getOrElse(None)
  )
  val LineCountMismatchAction: Option[String] =
    Try(Some(file.getString("enable-filter.all-line-count.mismatch-action"))).getOrElse(None)
  val partitionFormat: String =
    Try(file.getString("partition-format")).getOrElse("yyyyMMdd")
  val ignoreFileRegs: Boolean =
    Try(file.getBoolean("ignore-file-regs")).getOrElse(false)
  val hdfsDestination: String   = file.getString("destination-path") + s"$year/"
  val temporaryLocation: String = file.getString("destination-path") + s".temp/"

  val dynamicDownloadPath: Option[Boolean] = Try(file.getBoolean("dynamic-download-path")).toOption
  val actualDownloadPath: String = dynamicDownloadPath match {
    case Some(true) => generateDynamicDownloadPathName(file.getString("download-path"))
    case _          => file.getString("download-path")
  }
  val compressedFileType: Option[String] = Try(file.getString("compressed-file-type")).toOption

  private def generateDynamicDownloadPathName(downloadPath: String): String = {
    val month = processDate.getMonth.getValue.toString
    val day   = processDate.getDayOfMonth.toString

    downloadPath
      .replace("{date}", formattedDate)
      .replace("{year}", year)
      .replace("{month}", month)
      .replace("{day}", day)
  }

  protected def formatFileName(format: String): String =
    if (process.processStatus == ProcessMode.prod) {
      format.replace("{date}", formattedDate) + s".$fileType"
    } else if (
      Seq(ProcessMode.dateWithPartitionDate, ProcessMode.date, ProcessMode.mirror).contains(
        process.processStatus
      )
    ) {
      isReprocess = true
      format.replace("{date}", formattedDate) + s".$fileType"
    } else if (Seq(ProcessMode.fileWithPartitionDate, ProcessMode.file).contains(process.processStatus)) {
      isReprocess = true
      process.fileDateName
    } else {
      ""
    }

  def toOutputBatchingKey: OutputBatchingKey = OutputBatchingKey(
    tableName,
    saveMode,
    partKey,
    trackingColumns,
    isPIIData
  )
}

object DataTypeMapper {

  def getDataType(element: Config): DataType =
    element.getString("dataType") match {
      case "StringType"    => StringType
      case "IntegerType"   => IntegerType
      case "TimestampType" => TimestampType
      case "ShortType"     => ShortType
      case "LongType"      => LongType
      case "DoubleType"    => DoubleType
      case "DecimalType" =>
        DecimalType(
          if (element.hasPath("precision")) element.getInt("precision") else 10,
          if (element.hasPath("scale")) element.getInt("scale") else 0
        )
      case "BooleanType" => BooleanType
    }
}

case class OutputBatchingKey(
    tableName: String,
    saveMode: String,
    partKey: Seq[String],
    trackingColumns: Option[List[String]],
    isPIIData: Boolean
)
