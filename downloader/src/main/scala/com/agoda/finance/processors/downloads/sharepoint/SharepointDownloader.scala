package com.agoda.finance.processors.downloads.sharepoint

import com.typesafe.scalalogging.LazyLogging
import com.agoda.finance.common.utility.microsoft.sharepoint.MicrosoftGraphSharepoint
import com.agoda.finance.config.protocol.SharepointDownloadConfigModule
import com.agoda.finance.constant.TrackingStatus.TrackingStatus
import com.agoda.finance.constant.{FailedReason, RetryConstant, TrackingStatus}
import com.agoda.finance.handler.TaskRetry
import com.agoda.finance.model.{DownloadResult, Status}
import com.agoda.finance.services.Tracker
import com.agoda.finance.utils.{TrackerUtils, TrackingInitData}
import com.agoda.hadoop.fs.FileSystemOperation
import monix.eval.Task
import org.apache.hadoop.fs.Path
import org.apache.spark.sql.SQLContext
import resource.{managed, ManagedResource}

import scala.concurrent.{Await, Future}
import scala.concurrent.duration.{Duration, DurationInt}
import scala.util.{Failure, Success, Try}
import monix.execution.Scheduler.Implicits.global
import java.io.InputStream

/** Class responsible for downloading files from SharePoint and saving them to HDFS.
  *
  * This class interacts with Microsoft SharePoint to download specified files and stores them
  * in the Hadoop File System (HDFS). It handles retries, error tracking, and status updates
  * throughout the download process.
  *
  * @param sharepointConfig The configuration module containing SharePoint and download settings.
  * @param retries          Number of retry attempts for downloading files. Default is `RetryConstant.RetryNumber`.
  * @param retrySeconds     Number of seconds to wait between retries. Default is `RetryConstant.RetrySecond`.
  * @param timeoutSeconds   Optional timeout in seconds for the download operation.
  */
class SharepointDownloader(sharepointConfig: SharepointDownloadConfigModule)(
    retries: Int = RetryConstant.RetryNumber,
    retrySeconds: Int = RetryConstant.RetrySecond,
    timeoutSeconds: Option[Int] = None
)(implicit
    trackingData: TrackingInitData,
    sqlContext: SQLContext,
    tracker: Tracker
) extends LazyLogging {

  /** Entry point for downloading files from SharePoint.
    *
    * This method retrieves the list of files to download, checks their existence on SharePoint,
    * and processes them accordingly. It handles both successful downloads and failures, updating
    * the tracking status for each file.
    *
    * @return A sequence of [[DownloadResult]] containing information about the downloaded files.
    */
  def apply(): Seq[DownloadResult] = {

    logger.info(s"List of all files to download: ${sharepointConfig.fileNameList.mkString(",")} from: ${sharepointConfig.actualDownloadPath}")
    val destinationFolder = new Path(sharepointConfig.hdfsDestination)

    val siteName = sharepointConfig.siteName

    val actualFileNameList = sharepointConfig.fileNameList.map(fileName => sharepointConfig.actualDownloadPath + fileName)

    sharepointConfig.fileClient match {
      case Some(client) =>
        val successLists         = TrackerUtils.getSuccessList(actualFileNameList, TrackingStatus.Transformed)
        val unprocessedListFiles = getUnprocessList(actualFileNameList, successLists)
        logger.info("Final files list to download : " + unprocessedListFiles.mkString(","))

        val (existedFilesInSharepoint, notExistedFilesInSharepoint) =
          unprocessedListFiles.partition(item => checkFileExist(item, siteName, client))
        logger.info("List of all files from sharepoint : " + existedFilesInSharepoint.mkString(","))
        tracker.track(
          notExistedFilesInSharepoint.flatMap(item => handleFailedStatusWithSpecificReason(item, FailedReason.noFile))
        )

        existedFilesInSharepoint.flatMap(item => saveFromSharepointToHDFS(item, siteName, client, destinationFolder, sharepointConfig.isReprocess))

      case _ =>
        tracker.track(
          sharepointConfig.fileNameList.flatMap(item => handleFailedStatusWithSpecificReason(item, FailedReason.noClient))
        )
        Seq()
    }
  }

  /** Checks if a file exists on SharePoint.
    *
    * @param filePath The path to the file on SharePoint.
    * @param siteName The name of the SharePoint site.
    * @param client   The SharePoint client used for communication.
    * @return `true` if the file exists, `false` otherwise.
    */
  private[sharepoint] def checkFileExist(filePath: String, siteName: String, client: MicrosoftGraphSharepoint): Boolean =
    Try(client.checkFileExist(siteName, filePath)) match {
      case Success(Right(isExist)) =>
        logger.info(s"Is file: $filePath exists in SharePoint site $siteName : $isExist")
        isExist
      case Success(Left(exception)) =>
        logger.error(s"Error checking file existence for $filePath: ${exception.getMessage}")
        false
      case Failure(exception) =>
        logger.error(s"Unexpected error checking file existence for $filePath: ${exception.getMessage}")
        false
    }

  /** Downloads a file from SharePoint and saves it to HDFS.
    *
    * @param filePath         The path to the file on SharePoint.
    * @param siteName         The name of the SharePoint site.
    * @param client           The SharePoint client used for communication.
    * @param destinationFolder The HDFS path where the file should be saved.
    * @param isReprocess      Indicates if the download is part of a reprocessing job.
    * @return An optional [[DownloadResult]] containing information about the downloaded file.
    */
  private[sharepoint] def saveFromSharepointToHDFS(
      filePath: String,
      siteName: String,
      client: MicrosoftGraphSharepoint,
      destinationFolder: Path,
      isReprocess: Boolean
  ): Option[DownloadResult] =
    Try {
      val task = TaskRetry.constant(
        Task {
          logger.info(s"start downloading $filePath")
          val getStream = Future {
            client.getFile(siteName, filePath) match {
              case Right(inputStream) => inputStream
              case Left(exception) =>
                logger.error(s"Failed to get input stream: ${exception.getMessage}")
                throw exception
            }
          }
          Await.result(getStream, timeoutSeconds.map(_.seconds).getOrElse(Duration.Inf))
        },
        retries,
        retrySeconds.seconds,
        isReprocess,
        filePath,
        sharepointConfig.fileList
      )

      val stream: ManagedResource[InputStream] = managed(
        Await.result(task.runAsync, ((retries + 1) * (retrySeconds + timeoutSeconds.getOrElse(0)) + 1).seconds)
      )

      val uploadPath = new Path(destinationFolder, filePath)

      if (sharepointConfig.usingIOStream) {
        downloadSuccessCase(filePath)
        DownloadResult(uploadPath, filePath, dataStream = Some(stream))
      } else {

        stream.foreach(uploadFileToHDFS(uploadPath, _))

        downloadSuccessCase(filePath)

        DownloadResult(uploadPath, filePath)
      }

    } match {
      case Success(result) => Some(result)
      case Failure(e) =>
        downloadFailedCase(filePath, e.toString)
        None
    }

  private[sharepoint] def uploadFileToHDFS(
      uploadPath: Path,
      inputStream: InputStream,
      fs: FileSystemOperation = FileSystemOperation.getOrCreate()
  ): Unit =
    try fs.uploadStream(uploadPath, inputStream)
    catch {
      case e: Exception =>
        logger.error(s"Failed to upload file to HDFS at $uploadPath: ${e.getMessage}")
        throw new RuntimeException(s"Failed to upload file to HDFS at $uploadPath", e)
    }

  private[sharepoint] def downloadSuccessCase(filePath: String): () => Future[Unit] = { () =>
    val fileName = filePath

    Future {
      tracker.track(
        Seq(
          Status(
            fileName,
            sharepointConfig.actualDownloadPath + fileName,
            sharepointConfig.fileList,
            TrackingStatus.Downloaded,
            s"Download success : $fileName",
            sharepointConfig.isReprocess
          )
        )
      )
    }
  }

  private[sharepoint] def downloadFailedCase(filePath: String, errorMsg: String): () => Future[Unit] = { () =>
    val fileName    = filePath
    val path        = Try(sharepointConfig.actualDownloadPath).getOrElse("")
    val isReprocess = Try(sharepointConfig.isReprocess).getOrElse(false)

    Future {
      tracker.track(
        Seq(
          Status(
            fileName,
            path + fileName,
            sharepointConfig.fileList,
            TrackingStatus.DownloadFailed,
            s"$errorMsg : $fileName",
            isReprocess
          )
        )
      )
    }
  }

  private[sharepoint] def handleFailedStatusWithSpecificReason(filePath: String, reason: String): Seq[Status] = {

    def createStatusWithMessage(status: TrackingStatus, message: String): Status =
      createStatus(filePath, status, message)

    def cannotConnectStatus: Status = createStatusWithMessage(
      TrackingStatus.NotReady,
      s"Cannot connect to url for file (invalid sharepoint Client): $filePath"
    )

    def connectSuccessStatus: Status = createStatusWithMessage(
      TrackingStatus.Ready,
      s"Connect to url for file $filePath successfully"
    )

    def downloadFailedStatus: Status = createStatusWithMessage(
      TrackingStatus.DownloadFailed,
      s"Cannot find in Sharepoint : $filePath"
    )

    def alreadyProcessedStatus: Status = createStatusWithMessage(
      TrackingStatus.Downloaded,
      s"already processed : $filePath"
    )

    reason match {
      case FailedReason.noClient =>
        Seq(cannotConnectStatus)
      case FailedReason.noFile =>
        Seq(connectSuccessStatus, downloadFailedStatus)
      case FailedReason.processedFile =>
        Seq(connectSuccessStatus, alreadyProcessedStatus)
    }
  }

  private[sharepoint] def getUnprocessList(
      fileNameList: Seq[String],
      successLists: Seq[String]
  ): Seq[String] =
    sharepointConfig.isSkipChecking match {
      case false =>
        tracker.track(
          successLists
            .flatMap(item => handleFailedStatusWithSpecificReason(item, FailedReason.processedFile))
        )
        fileNameList.filter(item => !successLists.contains(item))
      case _ => fileNameList
    }

  private def createStatus(filePath: String, trackingStatus: TrackingStatus, message: String): Status = Status(
    fileName = filePath,
    filePath,
    sharepointConfig.fileList,
    trackingStatus,
    message,
    sharepointConfig.isReprocess
  )
}
