package com.agoda.finance.processors.zip

import com.agoda.finance.config.protocol.CommonConfigModule
import com.agoda.finance.constant.TrackingStatus
import com.agoda.finance.model.{DownloadResult, Status}
import com.agoda.finance.services.Tracker
import com.agoda.finance.utils.TrackingInitData
import com.typesafe.scalalogging.LazyLogging
import org.apache.commons.io.IOUtils
import org.apache.hadoop.conf.Configuration
import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.spark.sql.SQLContext
import resource.managed

import java.io.{ByteArrayInputStream, File}
import java.util.zip.{GZIPInputStream, ZipEntry, ZipInputStream}

class UnZipFileProcessor(fileConfig: CommonConfigModule)(implicit sqlContext: SQLContext, trackingData: TrackingInitData, tracker: Tracker)
    extends LazyLogging {

  def apply(downloadResult: DownloadResult): Option[DownloadResult] =
    if (fileConfig.compressedFileType.isDefined) {
      val hdfs     = getHdfsFileSystem()
      val fileType = downloadResult.path.getName.split("\\.").last
      fileType match {
        case "zip" => unzipFile(hdfs, downloadResult, downloadResult.path)
        case "gz"  => decompressGzip(hdfs, downloadResult)
        case _     => throw new UnsupportedOperationException(s"decompress method is not supported for this file type: $fileType")
      }

    } else {
      Some(downloadResult)
    }

  private def decompressGzip(hdfs: FileSystem, downloadResult: DownloadResult): Option[DownloadResult] = {
    val source = downloadResult.path
    val target = new Path(downloadResult.path.toString.replace(".gz", ""))

    if (fileConfig.usingIOStream && downloadResult.dataStream.isDefined) {
      // Use InputStream mode - return result with dataStream instead of writing to HDFS
      downloadResult.dataStream.get.acquireAndGet { inputStream =>
        try {
          val gis                = new GZIPInputStream(inputStream)
          val decompressedData   = IOUtils.toByteArray(gis)
          val decompressedStream = new ByteArrayInputStream(decompressedData)

          trackSuccess(downloadResult)
          Some(
            downloadResult.copy(
              path = target,
              dataStream = Some(resource.managed(decompressedStream))
            )
          )
        } catch {
          case e: Exception =>
            trackFailure(downloadResult, e)
            None
        }
      }
    } else if (fileConfig.usingIOStream && downloadResult.dataStream.isEmpty) {
      trackFailure(downloadResult, new Exception("dataStream is empty"))
      None
    } else {
      // Use HDFS mode - existing logic
      val gis    = new GZIPInputStream(hdfs.open(source))
      val output = hdfs.create(target)
      try {
        val buffer = new Array[Byte](1024)
        var len    = gis.read(buffer)
        while (len > 0) {
          output.write(buffer, 0, len)
          len = gis.read(buffer)
        }
        trackSuccess(downloadResult)
        Some(downloadResult.copy(path = target))
      } catch {
        case e: Exception =>
          trackFailure(downloadResult, e)
          None
      } finally {
        gis.close()
        output.close()
      }
    }
  }

  private def unzipFile(
      hdfs: FileSystem,
      downloadResult: DownloadResult,
      inputPath: Path
  ): Option[DownloadResult] = {
    val outputDir = new Path(downloadResult.path.toString.replace(".zip", ""))

    if (fileConfig.usingIOStream && downloadResult.dataStream.isDefined) {
      // Use InputStream mode - return result with dataStream instead of writing to HDFS
      downloadResult.dataStream.get.acquireAndGet { inputStream =>
        try {
          val zipInputStream = new ZipInputStream(inputStream)
          val allEntries     = scala.collection.mutable.ListBuffer[Array[Byte]]()

          Stream.continually(zipInputStream.getNextEntry).takeWhile(_ != null).foreach { entry =>
            if (!entry.isDirectory) {
              val entryData = IOUtils.toByteArray(zipInputStream)
              allEntries += entryData
            }
          }

          val combinedData   = allEntries.flatten.toArray
          val combinedStream = new ByteArrayInputStream(combinedData)

          trackSuccess(downloadResult)
          Some(
            downloadResult.copy(
              path = outputDir,
              dataStream = Some(resource.managed(combinedStream))
            )
          )
        } catch {
          case e: Exception =>
            trackFailure(downloadResult, e)
            None
        }
      }
    } else if (fileConfig.usingIOStream && downloadResult.dataStream.isEmpty) {
      // Handle missing dataStream in IOStream mode
      trackFailure(downloadResult, new Exception("dataStream is empty"))
      None
    } else {
      // Use HDFS mode - existing logic
      createOutputDirectory(hdfs, outputDir)
      val zipInputStream = new ZipInputStream(hdfs.open(inputPath))
      try {
        Stream.continually(zipInputStream.getNextEntry).takeWhile(_ != null).foreach { entry =>
          if (!entry.isDirectory) {
            /*
            If file is within a folder entry==folder/fileName,outputDir=folder, entrypath=parent+entry to avoid nested folder
            else entry=fileName outputDir=folder , entryPath=parent+outPutDir+entry
             */
            val entryPath = if (isFileInFolder(entry)) {
              new Path(
                outputDir.getParent
                  + File.separator + entry.getName
              )
            } else {
              new Path(outputDir.getParent + File.separator + outputDir.getName + File.separator + entry.getName)
            }
            val entryData       = IOUtils.toByteArray(zipInputStream)
            val entryDataStream = new ByteArrayInputStream(entryData)
            val output          = hdfs.create(entryPath)
            try IOUtils.copy(entryDataStream, output, 8192)
            finally {
              output.close()
              entryDataStream.close()
            }
          }
        }
        trackSuccess(downloadResult)
        Some(downloadResult.copy(path = outputDir))
      } catch {
        case e: Exception =>
          trackFailure(downloadResult, e)
          None
      } finally zipInputStream.close()
    }
  }

  private def trackSuccess(downloadResult: DownloadResult): Unit = {
    val msg = "Unzip File Success " + downloadResult.originalFileName
    tracker.track(
      Seq(
        Status(
          downloadResult.path.getName,
          downloadResult.path.toString,
          fileConfig.fileList,
          TrackingStatus.Unzipped,
          msg,
          fileConfig.isReprocess,
          originalFileName = downloadResult.originalFileName
        )
      )
    )
    logger.info(msg)
  }

  private def trackFailure(downloadResult: DownloadResult, exception: Exception): Unit = {
    val msg = s"Unzip File failed ${downloadResult.originalFileName} exception: ${exception.getMessage}"
    tracker.track(
      Seq(
        Status(
          downloadResult.path.getName,
          downloadResult.path.toString,
          fileConfig.fileList,
          TrackingStatus.UnzippedFailed,
          msg,
          fileConfig.isReprocess,
          originalFileName = downloadResult.originalFileName
        )
      )
    )
    logger.error(msg)
  }

  private[zip] def getHdfsFileSystem(): FileSystem = {
    val conf = new Configuration()
    FileSystem.get(conf)
  }

  private def createOutputDirectory(hdfs: FileSystem, outputDir: Path) = {
    if (hdfs.exists(outputDir)) {
      hdfs.delete(outputDir, true)
    }
    hdfs.mkdirs(outputDir)
  }

  /** Check if file is within a folder
    *
    * @param entry
    * @return
    */

  private def isFileInFolder(entry: ZipEntry): Boolean = entry.getName.contains("/")
}
