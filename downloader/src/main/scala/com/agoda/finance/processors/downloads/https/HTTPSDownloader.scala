package com.agoda.finance.processors.downloads.https

import com.typesafe.scalalogging.LazyLogging
import com.agoda.finance.config.protocol.HTTPSDownloadConfigModule
import com.agoda.finance.constant.{RetryConstant, TrackingStatus}
import com.agoda.finance.handler.TaskRetry
import com.agoda.finance.model.{DownloadResult, HTTPSPayload, Status}
import com.agoda.finance.services.Tracker
import com.agoda.finance.utils.{HTTPSDownloaderUtils, TrackerUtils, TrackingInitData}
import com.agoda.hadoop.fs.FileSystemOperation
import monix.eval.Task
import org.apache.hadoop.fs.Path
import org.apache.spark.sql.SQLContext
import resource.ManagedResource

import java.io.InputStream
import scala.concurrent.{Await, Future}
import scala.concurrent.duration.{Duration, DurationInt}
import scala.util.{Failure, Success, Try}
import monix.execution.Scheduler.Implicits.global

class HTTPSDownloader(httpsConfig: HTTPSDownloadConfigModule, httpsDownloaderUtils: HTTPSDownloaderUtils)(
    retries: Int = RetryConstant.RetryNumber,
    retrySeconds: Int = RetryConstant.RetrySecond,
    timeoutSeconds: Option[Int] = None
)(implicit
    trackingData: TrackingInitData,
    sqlContext: SQLContext,
    tracker: Tracker
) extends LazyLogging {

  private def writeTrackingDownloadPassStatus(filePath: Path, isReprocess: Boolean): Unit = {
    val downloadPassStatus = Seq(
      Status(
        filePath.getName,
        filePath.toString,
        httpsConfig.fileList,
        TrackingStatus.Ready,
        s"Connect to url for file ${filePath.toString} successfully",
        isReprocess
      ),
      Status(
        filePath.getName,
        filePath.toString,
        httpsConfig.fileList,
        TrackingStatus.Downloaded,
        s"Download success : ${filePath.getName}",
        isReprocess
      )
    )
    tracker.track(downloadPassStatus)
  }

  private[https] def writeTrackingDownloadFailedStatus(filePath: Path, exception: Throwable, isReprocess: Boolean): Unit = {
    val downloadFailedStatus = Seq(
      Status(
        filePath.getName,
        filePath.toString,
        httpsConfig.fileList,
        TrackingStatus.NotReady,
        s"Unable to connect to ${httpsConfig.downloadLink}",
        isReprocess
      ),
      Status(
        filePath.getName,
        filePath.toString,
        httpsConfig.fileList,
        TrackingStatus.DownloadFailed,
        s"Failure ${exception.toString}",
        isReprocess
      )
    )
    tracker.track(downloadFailedStatus)
  }

  private def uploadToHDFS(stream: ManagedResource[InputStream], uploadPath: Path, filePath: Path, isReprocess: Boolean): Unit =
    stream.foreach { s =>
      FileSystemOperation.getOrCreate().uploadStream(uploadPath, s)
      writeTrackingDownloadPassStatus(filePath, isReprocess)
    }

  def saveFileToHDFS(
      stream: ManagedResource[InputStream],
      filePath: Path
  ): DownloadResult = {
    val destinationFolder = new Path(httpsConfig.hdfsDestination)
    FileSystemOperation.getOrCreate().createDirectoryIfDoesNotExist(destinationFolder)
    val uploadPath = new Path(destinationFolder, filePath.getName)

    if (httpsConfig.usingIOStream) {
      writeTrackingDownloadPassStatus(filePath, httpsConfig.isReprocess)
      DownloadResult(uploadPath, filePath.getName, dataStream = Some(stream))
    } else {
      uploadToHDFS(stream, uploadPath, filePath, httpsConfig.isReprocess)
      DownloadResult(uploadPath, filePath.getName)
    }

  }

  def apply(): Seq[DownloadResult] = {

    logger.info("List of all files to download : " + httpsConfig.fileNameList.mkString(","))
    logger.info("List of all payload to download : " + httpsConfig.payloadList.mkString(","))

    val httpPayloadList = httpsDownloaderUtils.buildHttpPayload(httpsConfig.fileNameList, httpsConfig.payloadList)
    val filteredHttpPayloadList = if (httpsConfig.isSkipChecking) {
      httpPayloadList
    } else {
      val successLists = TrackerUtils.getSuccessList(httpPayloadList.map(_.filePath.getName), TrackingStatus.Transformed)
      logger.info("Downloaded success lists : " + successLists.mkString(","))
      httpPayloadList.filter(httpPayload => !successLists.contains(httpPayload.filePath.getName))
    }

    logger.info(s"httpMethod: ${httpsConfig.httpMethod}, filteredHttpPayloadList: $filteredHttpPayloadList")

    if (httpsConfig.isParallel) {
      filteredHttpPayloadList.par.flatMap(payload => processHttpPayload(payload)).seq
    } else {
      filteredHttpPayloadList.flatMap(payload => processHttpPayload(payload))
    }
  }

  private def processHttpPayload(httpPayload: HTTPSPayload): Option[DownloadResult] =
    Try {
      val httpsTask = Task {
        val getStream = Future {
          resource.managed(httpsDownloaderUtils.getInputStream(httpPayload))
        }
        Await.result(getStream, timeoutSeconds.map(_.seconds).getOrElse(Duration.Inf))
      }
      val retryTask =
        TaskRetry.constant(httpsTask, retries, retrySeconds.seconds, httpsConfig.isReprocess, httpPayload.filePath.toString, httpsConfig.fileList)

      val stream = Await.result(retryTask.runAsync, ((retries + 1) * (retrySeconds + timeoutSeconds.getOrElse(0)) + 1).seconds)
      saveFileToHDFS(stream, httpPayload.filePath)
    } match {
      case Success(stream) => Some(stream)
      case Failure(e) =>
        logger.error(e.getMessage, e)
        writeTrackingDownloadFailedStatus(httpPayload.filePath, e, httpsConfig.isReprocess)
        None
    }
}
