package com.agoda.finance.processors.downloads.smb

import com.agoda.common.fileclient.service.smb.SmbFileClient
import com.typesafe.scalalogging.LazyLogging
import com.agoda.finance.config.protocol.SMBDownloadConfigModule
import com.agoda.finance.constant.TrackingStatus.TrackingStatus
import com.agoda.finance.constant.{FailedReason, RetryConstant, TrackingStatus}
import com.agoda.finance.handler.TaskRetry
import com.agoda.finance.model.{DownloadResult, Status}
import com.agoda.finance.services.Tracker
import com.agoda.finance.utils.{TrackerUtils, TrackingInitData}
import com.agoda.hadoop.fs.FileSystemOperation
import monix.eval.Task
import monix.execution.Scheduler.Implicits.global
import org.apache.commons.io.IOUtils.toBufferedInputStream
import org.apache.hadoop.fs.Path
import org.apache.spark.sql.SQLContext

import java.io.{FileInputStream, InputStream}
import scala.concurrent.{Await, Future}
import scala.util.{Failure, Success, Try}

class SMBDownloader(smbConfig: SMBDownloadConfigModule)(
    retries: Int = RetryConstant.RetryNumber,
    retrySeconds: Int = RetryConstant.RetrySecond,
    timeoutSeconds: Option[Int] = None
)(implicit
    sqlContext: SQLContext,
    trackingData: TrackingInitData,
    tracker: Tracker
) extends LazyLogging {
  import scala.concurrent.duration._

  def apply(): Seq[DownloadResult] = {

    logger.info(
      "List of all files to download : " + smbConfig.fileNameList.mkString(",") + s" in ${smbConfig.actualDownloadPath}"
    )
    val destinationFolder = new Path(smbConfig.hdfsDestination)
    FileSystemOperation.getOrCreate().createDirectoryIfDoesNotExist(destinationFolder)

    val listedFilesToDownloaded = smbConfig.fileNameList.map { filePath =>
      val fullPath = new Path(filePath)
      fullPath
    }

    smbConfig.fileClient match {
      case Some(client) =>
        val unprocessedListFiles = smbConfig.isSkipChecking match {
          case false =>
            val successLists =
              TrackerUtils.getSuccessList(listedFilesToDownloaded.map(_.getName), TrackingStatus.Transformed)
            tracker.track(
              successLists
                .map(filePath => new Path(filePath))
                .flatMap(item => handleFailedStatusWithSpecificReason(item, FailedReason.processedFile))
            )

            listedFilesToDownloaded.filter(item => !successLists.contains(item.getName))
          case _ => listedFilesToDownloaded
        }

        logger.info("Final files list to download : " + unprocessedListFiles.map(_.getName).mkString(","))

        val (existedFilesInSMB, notExistedFilesInSMB) =
          unprocessedListFiles.partition(item => checkFileExist(item, client))
        logger.info("List of all files from smb : " + existedFilesInSMB.map(_.getName).mkString(","))
        tracker.track(
          notExistedFilesInSMB.flatMap(item => handleFailedStatusWithSpecificReason(item, FailedReason.noFile))
        )

        existedFilesInSMB.flatMap(item => saveFileToHDFS(item, destinationFolder, client))
      case _ =>
        tracker.track(
          listedFilesToDownloaded.flatMap(item => handleFailedStatusWithSpecificReason(item, FailedReason.noClient))
        )
        Seq()
    }
  }

  private[smb] def checkFileExist(filePath: Path, client: SmbFileClient): Boolean =
    Try(client.fileExists(filePath.getName)) match {
      case Success(result) => result
      case _               => false
    }

  private[smb] def saveFileToHDFS(
      filePath: Path,
      destinationFolder: Path,
      client: SmbFileClient
  ): Option[DownloadResult] =
    Try {
      val task = TaskRetry.constant(
        Task {
          logger.info(s"start downloading ${filePath.getName}")
          val file = client.download(s"${filePath.getName}")
          logger.info(s"after downloading ${file.getName}")
          val getStream = Future {
            val fileInputStream = new FileInputStream(file)
            toBufferedInputStream(fileInputStream)
          }
          Await.result(getStream, timeoutSeconds.map(_.seconds).getOrElse(Duration.Inf))
        },
        retries,
        retrySeconds.seconds,
        smbConfig.isReprocess,
        filePath.toString,
        smbConfig.fileList
      )
      import resource._

      val stream: ManagedResource[InputStream] = managed(
        Await.result(task.runAsync, ((retries + 1) * (retrySeconds + timeoutSeconds.getOrElse(0)) + 1).seconds)
      )

      val uploadPath = new Path(destinationFolder, filePath.getName)

      val downloadPass = Seq(
        Status(
          filePath.getName,
          filePath.toString,
          smbConfig.fileList,
          TrackingStatus.Ready,
          s"Connect to url for file ${filePath.toString} successfully",
          smbConfig.isReprocess
        ),
        Status(
          filePath.getName,
          filePath.toString,
          smbConfig.fileList,
          TrackingStatus.Downloaded,
          s"Download success : ${filePath.getName}",
          smbConfig.isReprocess
        )
      )

      if (smbConfig.usingIOStream) {
        tracker.track(downloadPass)
        DownloadResult(uploadPath, filePath.getName, dataStream = Some(stream))
      } else {
        stream.foreach { s =>
          FileSystemOperation.getOrCreate().uploadStream(uploadPath, s)
          tracker.track(downloadPass)
        }
        DownloadResult(uploadPath, filePath.getName)
      }

    } match {
      case Success(result) => Some(result)
      case Failure(e) =>
        val downloadFailed = Seq(
          Status(
            filePath.getName,
            filePath.toString,
            smbConfig.fileList,
            TrackingStatus.NotReady,
            s"Unable to connect to url for file ${filePath.toString}",
            smbConfig.isReprocess
          ),
          Status(
            filePath.getName,
            filePath.toString,
            smbConfig.fileList,
            TrackingStatus.DownloadFailed,
            s"Failure ${e.toString}",
            smbConfig.isReprocess
          )
        )
        tracker.track(downloadFailed)
        None
    }

  private def handleFailedStatusWithSpecificReason(filePath: Path, reason: String): Seq[Status] = reason match {
    case FailedReason.noClient =>
      Seq(
        createStatus(
          filePath,
          TrackingStatus.NotReady,
          s"Cannot connect to url for file (invalid smbclient): ${filePath.toString}"
        )
      )
    case FailedReason.noFile =>
      Seq(
        createStatus(
          filePath,
          TrackingStatus.Ready,
          s"Connect to url for file ${filePath.toString} successfully"
        ),
        createStatus(
          filePath,
          TrackingStatus.DownloadFailed,
          s"Cannot find in SMB : ${filePath.getName}"
        )
      )
    case FailedReason.processedFile =>
      Seq(
        createStatus(
          filePath,
          TrackingStatus.Ready,
          s"Connect to url for file ${filePath.toString} successfully"
        ),
        createStatus(
          filePath,
          TrackingStatus.Downloaded,
          s"already processed : ${filePath.getName}"
        )
      )
  }

  private def createStatus(filePath: Path, trackingStatus: TrackingStatus, message: String): Status = Status(
    filePath.getName,
    filePath.toString,
    smbConfig.fileList,
    trackingStatus,
    message,
    smbConfig.isReprocess
  )

}
