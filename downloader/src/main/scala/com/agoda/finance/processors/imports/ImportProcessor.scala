package com.agoda.finance.processors.imports

import com.typesafe.scalalogging.LazyLogging
import com.agoda.finance.config.protocol.CommonConfigModule
import com.agoda.finance.constant.FileType.isBspType
import com.agoda.finance.constant.{FileType, TrackingStatus}
import com.agoda.finance.model.{DownloadResult, MappingConfiguration, Status}
import com.agoda.finance.services.Tracker
import com.agoda.finance.utils.{DataFrameResult, DataFrameUtils, JsonToDfUtils, TrackingInitData}
import org.apache.hadoop.conf.Configuration
import org.apache.hadoop.fs.{FileStatus, FileSystem, Path}
import org.apache.spark.sql.{DataFrame, SQLContext}

import java.io.FileNotFoundException
import scala.util.{Failure, Success, Try}

case class ResultImporter(importDF: Option[DataFrame], processingStatus: TrackingStatus.Value)

class ImportProcessor(config: CommonConfigModule)(implicit sqlContext: SQLContext, trackingData: TrackingInitData, tracker: Tracker)
    extends LazyLogging {

  def apply(downloadDetail: DownloadResult): ResultImporter = {
    val hdfs = getHdfsFileSystem()
    if (hdfs.exists(downloadDetail.path)) {
      Try {
        val fieldList = if (config.hasColumnHeader && !config.fixedPositionIndex) {
          None
        } else {
          config.mappers.get(config.fileMapper)
        }

        val folderPath = downloadDetail.path
        if (hdfs.getFileStatus(folderPath).isDirectory) {
          processFolder(folderPath, hdfs, fieldList)

        } else {
          if (isBspType(config.fileType)) {
            processBspFile(downloadDetail)
          } else {
            processFile(downloadDetail, fieldList)
          }
        }

      } match {
        case Success(result: DataFrameResult) =>
          val msg = if (result.lineCountMatched) {
            "Import File success"
          } else {
            "Import File success : LineCount in footer/header doesn't equal [Warning]"
          }

          tracker.track(
            Seq(
              Status(
                downloadDetail.originalFileName,
                downloadDetail.path.toString,
                config.fileList,
                TrackingStatus.Imported,
                msg,
                config.isReprocess,
                lineCount = result.df.count,
                originalFileName = downloadDetail.originalFileName
              )
            )
          )
          logger.info(msg)
          ResultImporter(Some(result.df), TrackingStatus.Imported)
        case Failure(e) =>
          tracker.track(
            Seq(
              Status(
                downloadDetail.path.getName,
                downloadDetail.path.toString,
                config.fileList,
                TrackingStatus.ImportFailed,
                e.toString,
                config.isReprocess,
                originalFileName = downloadDetail.originalFileName
              )
            )
          )
          logger.error("Import File Error", e)
          ResultImporter(None, TrackingStatus.ImportFailed)
      }
    } else {
      tracker.track(
        Seq(
          Status(
            downloadDetail.path.getName,
            downloadDetail.path.toString,
            config.fileList,
            TrackingStatus.ImportFailed,
            s"Path definition file/folder not found on the classpath: ${downloadDetail.path.toString}",
            config.isReprocess,
            originalFileName = downloadDetail.originalFileName
          )
        )
      )
      logger.error("Import File Error", new FileNotFoundException("There is no file/folder found in HDFS"))
      ResultImporter(None, TrackingStatus.ImportFailed)
    }
  }

  private def importExcelToDF(downloadPath: String): DataFrameResult =
    DataFrameUtils.excelToDF(
      downloadPath,
      config
    )

  private def importFlatFileToDF(downloadPath: String, fieldList: Option[List[MappingConfiguration]]): DataFrameResult =
    DataFrameUtils.flatToDF(downloadPath, fieldList.orNull, config.FilterHeader, config.FilterFooter)

  private def importJsonToDF(downloadPath: String): DataFrameResult =
    DataFrameResult(JsonToDfUtils.jsonToDF(downloadPath, config).transform(DataFrameUtils.createHashingAndLineDF), true)

  private def importCsvToDF(downloadPath: String, fieldList: Option[List[MappingConfiguration]]): DataFrameResult =
    DataFrameUtils
      .csvToDF(
        downloadPath,
        config.FilterHeader,
        config.FilterFooter,
        config.LineCountLocationZone,
        config.LineCountLocationPoint,
        config.LineCountMismatchAction,
        config.delimiter,
        fieldList.map(_.map(_.mappingField)),
        config.fixLineBreak,
        config.fixUnicode,
        config.headerColumns
      )

  private def importHtmlToDF(downloadPath: String): DataFrameResult =
    DataFrameUtils.htmlSpreadsheetToDF(downloadPath)

  private def importXMLSpreadsheetToDF(downloadPath: String): DataFrameResult =
    DataFrameUtils.xmlSpreadsheetToDF(
      downloadPath,
      config.loadedSheetName
    )

  private def importBspToDf(downloadPath: String): DataFrameResult =
    if (isBspType(config.fileType)) {
      logger.info(s"Importing BSP file from path: $downloadPath")
      val bspMapper = config.bspMapper

      if (bspMapper.isDefined) {
        DataFrameUtils.bspToDf(
          downloadPath,
          bspMapper.get
        )
      } else {
        throw new IllegalArgumentException("BSP mapper is not defined in the configuration")
      }

    } else {
      throw new IllegalArgumentException(s"Unsupported file type for BSP import: ${config.fileType}")
    }

  private def getDataFramesForCompressed(
      files: Array[FileStatus],
      config: CommonConfigModule,
      fieldList: Option[List[MappingConfiguration]]
  ): DataFrameResult =
    files
      .map(file =>
        config.compressedFileType
          .map {
            case fType if FileType.isExcelType(fType) =>
              importExcelToDF(file.getPath.toString)
            case fType if FileType.isCsvType(fType) =>
              importCsvToDF(file.getPath.toString, fieldList)
            case fType if FileType.isHtmlType(fType) =>
              importHtmlToDF(file.getPath.toString)
            case FileType.JSON =>
              importJsonToDF(file.getPath.toString)
            case _ => importFlatFileToDF(file.getPath.toString, fieldList)
          }
          .getOrElse(throw new Exception("Compressed file type not specified"))
      )
      .reduce((d1, d2) => DataFrameResult(d1.df.unionByName(d2.df), d1.lineCountMatched && d2.lineCountMatched))

  private def getHdfsFileSystem(): FileSystem = {
    val conf = new Configuration()
    FileSystem.get(conf)
  }

  private def processFolder(
      folderPath: Path,
      hdfs: FileSystem,
      fieldList: Option[List[MappingConfiguration]]
  ): DataFrameResult =
    getDataFramesForCompressed(hdfs.listStatus(folderPath), config, fieldList)

  private def processFile(downloadDetail: DownloadResult, fieldList: Option[List[MappingConfiguration]]) =
    if (FileType.isExcelType(config.fileType)) {
      importExcelToDF(downloadDetail.path.toString)
    } else if (FileType.isXMLType(config.fileType)) {
      importXMLSpreadsheetToDF(downloadDetail.path.toString)
    } else if (FileType.isHtmlType(config.fileType)) {
      importHtmlToDF(downloadDetail.path.toString)
    } // TODO Remove jsonReadOption check when we move cashback_wise_file to import by json
    else if (config.fileType == FileType.JSON && config.jsonReadOption.isDefined) {
      importJsonToDF(downloadDetail.path.toString)
    } else if (config.fixedPositionIndex) {
      importFlatFileToDF(downloadDetail.path.toString, fieldList)
    } else {
      importCsvToDF(downloadDetail.path.toString, fieldList)
    }

  private def processBspFile(downloadDetail: DownloadResult): DataFrameResult =
    if (isBspType(config.fileType)) {
      importBspToDf(downloadDetail.path.toString)
    } else {
      throw new IllegalArgumentException(s"Unsupported file type for BSP import: ${config.fileType}")
    }
}
