package com.agoda.finance.processors.transforms

import com.typesafe.scalalogging.LazyLogging
import com.agoda.finance.common.utils.{DataTypeUtils, DateUtils}
import com.agoda.finance.config.protocol.CommonConfigModule
import com.agoda.finance.constant.FileType.isBspType
import com.agoda.finance.constant.TrackingStatus
import com.agoda.finance.model.{DownloadResult, MappingField, Status}
import com.agoda.finance.services.Tracker
import com.agoda.finance.utils.TrackingInitData
import com.agoda.hadoop.fs.FileSystemOperation
import com.google.common.base.Charsets
import com.google.common.hash.Hashing
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import org.apache.spark.sql.{Column, DataFrame, SQLContext}
import org.joda.time.DateTime

import scala.annotation.tailrec
import scala.util.{Failure, Success, Try}

case class TransformResult(status: Seq[Status], transformedDf: Option[DataFrame], partitionDate: Int)

class TransformProcessor(conf: CommonConfigModule)(implicit sqlContext: SQLContext, tracker: Tracker) extends LazyLogging {

  def apply(df: Option[DataFrame], downloadDetail: DownloadResult)(implicit
      trackingData: TrackingInitData
  ): TransformResult = {
    val filePath = downloadDetail.encryptedPath.getOrElse(downloadDetail.path)

    val partitionDate = getFormattedDatePartitionValue(conf.partValue, conf.partitionFormat, trackingData.date)

    val transformResult = df match {
      case Some(df) =>
        Try {
          val mapper: Option[List[MappingField]] = conf.mappers
            .get(conf.fileMapper)
            .map(_.filter(!_.fieldSpecification.exists(_.dropColumn)).map(_.mappingField))

          if (
            conf.filterEmptyFileContent.nonEmpty
            && df.columns.toSet == Set("line_number", "hash_key", conf.filterEmptyFileContent.getOrElse(""))
            && df.count() == 0
          ) {
            val transformedStatus = Seq(
              Status(
                filePath.getName,
                filePath.toString,
                conf.fileList,
                TrackingStatus.Transformed,
                s"File is empty with ${conf.filterEmptyFileContent.getOrElse("")} column. Nothing to be written",
                conf.isReprocess,
                lineCount = df.cache.count,
                originalFileName = downloadDetail.originalFileName
              )
            )
            logger.info("Skipping empty file transformation for file: " + filePath.toString)
            TransformResult(transformedStatus, Some(df), partitionDate)
          } else {
            val transformed = if (isBspType(conf.fileType)) {
              transformBspColumns(df, filePath.getName, partitionDate)
            } else {
              transformColumns(df, mapper, filePath.getName, partitionDate)
            }
            val transformedPendingToBeWrittenStatus = Seq(
              Status(
                filePath.getName,
                filePath.toString,
                conf.fileList,
                TrackingStatus.TransformedPendingToBeWritten,
                s"File is successfully transformed to mapper DataFrame. Pending to be written",
                conf.isReprocess,
                lineCount = transformed.cache.count,
                originalFileName = downloadDetail.originalFileName
              )
            )
            logger.info("Transform File success. Pending to be written")
            TransformResult(transformedPendingToBeWrittenStatus, Some(transformed), partitionDate)
          }
        } match {
          case Success(result) => result
          case Failure(e) =>
            val transformFailedStatus = Seq(
              Status(
                filePath.getName,
                filePath.toString,
                conf.fileList,
                TrackingStatus.TransformFailed,
                e.toString,
                conf.isReprocess,
                originalFileName = downloadDetail.originalFileName
              )
            )
            tracker.track(transformFailedStatus)
            logger.error("Transform File Error", e)
            TransformResult(transformFailedStatus, None, partitionDate)
        }
      case None => TransformResult(Nil, None, partitionDate)
    }

    // Delete temp decrypted file for encrypted supplier file
    Try {
      if (downloadDetail.encryptedPath.nonEmpty) {
        val fs = FileSystemOperation.getOrCreate().fileSystem
        //delete temp, keep encrypt
        logger.info("Deleting decrypted temp " + downloadDetail.path)
        fs.delete(downloadDetail.path, false)
        val deletedTemp = Seq(
          Status(
            filePath.getName,
            filePath.toString,
            conf.fileList,
            TrackingStatus.DeletedTemp,
            "Deleting decrypted temp" + downloadDetail.path,
            conf.isReprocess,
            originalFileName = downloadDetail.originalFileName
          )
        )
        tracker.track(deletedTemp)
      }
    }.recover { case e =>
      logger.error("Error delete a file", e)
      val deleteTempFailed = Seq(
        Status(
          filePath.getName,
          filePath.toString,
          conf.fileList,
          TrackingStatus.DeleteTempFailed,
          e.toString,
          conf.isReprocess,
          originalFileName = downloadDetail.originalFileName
        )
      )
      tracker.track(deleteTempFailed)
    }
    transformResult
  }

  private[transforms] def isStructPathExists(df: DataFrame, field: String): Boolean = {
    val parts = field.split("\\.").dropRight(1)

    @tailrec
    def checkStructPath(schema: StructType, path: Seq[String]): Boolean = path match {
      case Nil => true
      case head +: tail =>
        schema.fields.find(_.name == head) match {
          case Some(StructField(_, s: StructType, _, _)) => checkStructPath(s, tail)
          case _                                         => false
        }
    }

    checkStructPath(df.schema, parts)
  }

  private[transforms] def transformBspColumns(df: DataFrame, fileName: String = "", partitionDate: Int = DateUtils.getCurrentDate.toInt)(implicit
      trackingData: TrackingInitData
  ): DataFrame =
    transformDownloaderColumns(df, fileName, trackingData, partitionDate)

  private[transforms] def transformColumns(
      df: DataFrame,
      mapper: Option[List[MappingField]],
      fileName: String = "",
      partitionDate: Int = DateUtils.getCurrentDate.toInt
  )(implicit
      trackingData: TrackingInitData
  ): DataFrame = {

    var processDF = df
    var error     = ""

    mapper match {
      case Some(map) =>
        val columnsToAdd: List[MappingField] = map.filterNot(_.isColumnExists)

        columnsToAdd.foreach { col =>
          logger.info(s"Column to add: Name = ${col.name}, DataType = ${col.dataType}, Nullable = ${col.nullable}")
        }

        val dfWithMissingColumns = columnsToAdd.foldLeft(df) { (tempDF, col) =>
          tempDF.withColumn(col.name, lit(null).cast(col.dataType))
        }

        val selectCols: Seq[Column] = map.filter(_.hardCodeValue.isEmpty).map { c =>
          if (c.name.contains(".")) {
            if (isStructPathExists(dfWithMissingColumns, c.name)) {
              col(c.name).as(c.name)
            } else {
              lit(null).as(c.name)
            }
          } else {
            col(c.name).as(c.name)
          }
        } ++ Seq(col("line_number"), col("hash_key"))

        val dfWithMapperColumns =
          dfWithMissingColumns.select(
            selectCols: _*
          )

        val dfReplaceStructToString = dfWithMapperColumns.select(dfWithMapperColumns.columns.map { colName =>
          val isColumnStruct = dfWithMapperColumns.schema(colName).dataType.typeName == "struct"

          if (isColumnStruct) {
            to_json(col(s"`$colName`")).cast("string").alias(colName)
          } else {
            col(s"`$colName`")
          }
        }: _*)

        val dfReplacedEmptyStringToNull =
          dfReplaceStructToString.select(dfReplaceStructToString.columns.map { colName =>
            when(col(s"`$colName`") === "", null).otherwise(col(s"`$colName`")).alias(colName)
          }: _*)
        val dfDroppedAllNullColRow =
          Try(dfReplacedEmptyStringToNull.na.drop("all", map.map(_.name)))
            .getOrElse(dfReplacedEmptyStringToNull)
        val dfReplacedNullToEmptyString = dfDroppedAllNullColRow.na.fill("")

        processDF = dfReplacedNullToEmptyString

        map.foreach { col =>
          val ver = castColumnTo(
            processDF,
            col.name,
            col.dataType,
            col.nullable,
            col.alias,
            col.shouldTrim,
            col.forbidRegex,
            col.hardCodeValue
          )
          processDF = ver._1
          error = error + ver._2
        }

        if (error == "") {
          transformDownloaderColumns(processDF, fileName, trackingData, partitionDate)
        } else {
          throw new Exception("There are some data fields fail to convert/null value/empty string/format | " + error)
        }
      case None => throw new Exception("There is no map setting to transform and verify data type for this file.")
    }
  }

  private[transforms] def castColumnTo(
      df: DataFrame,
      columnName: String,
      dataType: DataType,
      isNullable: Boolean,
      aliasName: String,
      shouldTrim: Boolean,
      forbidRegex: String,
      hardCodeValue: Option[String]
  ): (DataFrame, String) = {

    implicit val castOption: CastOption  = CastOption(isNullable, shouldTrim, forbidRegex)
    implicit val timeStampFormat: String = conf.timeStampColFormat
    val castStrategy: CastStrategy       = CastStrategyResolver.resolve(dataType)

    val castDF: DataFrame = {
      val addedHardCodeValueDF =
        if (hardCodeValue.nonEmpty) {
          df.withColumn(columnName, lit(hardCodeValue.getOrElse("")))
        } else {
          df
        }
      val trimmedDF: DataFrame =
        if (shouldTrim) {
          addedHardCodeValueDF.withColumn(columnName, trim(col(s"`$columnName`")))
        } else {
          addedHardCodeValueDF
        }
      val targetColumn: Column = trimmedDF(s"`$columnName`")
      trimmedDF.withColumn(columnName, castStrategy.cast(targetColumn)).withColumnRenamed(columnName, aliasName)

    }

    CastValidationProcessor(castDF, aliasName)
  }

  private[transforms] def transformDownloaderColumns(
      df: DataFrame,
      fileName: String,
      trackingData: TrackingInitData,
      partitionDate: Int
  ): DataFrame = {
    val fileId = getFileId(fileName)
    val dfWithMetadata = df
      .withColumn("file_id", lit(fileId))
      .withColumn("accounting_date", lit(DataTypeUtils.castDateToTimeStamp(trackingData.date)))
      .withColumn("reporting_date", current_timestamp)
      .withColumn("file_name", lit(fileName))
      .transform(df => addUuidColumn(df, conf.generateUuidColumn))

    val missingPartitionsInDF = conf.partKey.filterNot(dfWithMetadata.columns.contains)

    val dfWithDatePartition = {
      val additionalColumns = missingPartitionsInDF.collect {
        case "datamonth" if !dfWithMetadata.columns.contains("datamonth") => lit(partitionDate).as("datamonth")
        case "datadate" if !dfWithMetadata.columns.contains("datadate")   => lit(partitionDate).as("datadate")
      }

      dfWithMetadata.select(dfWithMetadata.columns.map(col) ++ additionalColumns: _*)
    }

    dfWithDatePartition
  }

  private[transforms] def getFileId(fileName: String) =
    Hashing.murmur3_128().newHasher().putString(fileName, Charsets.UTF_8).hash().asLong()

  private[transforms] def getFormattedDatePartitionValue(partValue: String, dateFormat: String, trackingDate: String): Int =
    partValue match {
      case "partitiondate" =>
        DataTypeUtils.castDataDateStringToInt(trackingDate, strNewFormat = dateFormat)
      case date =>
        DateTime.now.plusDays(date.toInt).toString(dateFormat).toInt
    }

  private[transforms] def addUuidColumn(df: DataFrame, generateUuidColumn: Boolean): DataFrame =
    if (generateUuidColumn) {
      df.withColumn("uuid", expr("uuid()"))
    } else {
      df
    }
}
