package com.agoda.finance.processors.downloads.sftp

import com.typesafe.scalalogging.LazyLogging
import com.agoda.finance.config.protocol.SFTPDownloadConfigModule
import com.agoda.finance.constant.{RetryConstant, TrackingStatus}
import com.agoda.finance.handler.TaskRetry
import com.agoda.finance.model.{DownloadResult, Status}
import com.agoda.finance.processors.downloads.sftp.SFTPHandler.Implicits._
import com.agoda.finance.services.Tracker

import scala.jdk.CollectionConverters._
import com.agoda.finance.utils.TrackingInitData
import com.agoda.hadoop.fs.FileSystemOperation
import com.jcraft.jsch.{ChannelSftp, SftpATTRS}
import monix.eval.Task
import monix.execution.Scheduler.Implicits.global
import org.apache.hadoop.fs.Path
import org.apache.spark.sql.SQLContext
import resource._

import java.io.InputStream
import java.util.concurrent.ConcurrentLinkedQueue
import scala.collection.convert.ImplicitConversions.`collection AsScalaIterable`
import scala.collection.mutable.ListBuffer
import scala.concurrent.{Await, Future}
import scala.jdk.CollectionConverters.asJavaIterableConverter
import scala.util.matching.Regex
import scala.util.{Failure, Success, Try}

case class SFTPFileEntry(rootPath: String, folder: String, file: ChannelSftp.LsEntry)
case class SFTPFileStatus(sftpFileEntry: SFTPFileEntry, originalFileName: String)
case class ResultFiles(
    listSFTPFileEntry: Seq[SFTPFileStatus],
    processingStatus: TrackingStatus.Value,
    throwsMsg: String
)

class SFTPDownloader(sftpConfig: SFTPDownloadConfigModule)(
    retries: Int = RetryConstant.RetryNumber,
    retrySeconds: Int = RetryConstant.RetrySecond,
    timeoutSeconds: Option[Int] = None,
    downloadedFileNames: Seq[String]
)(implicit
    sqlContext: SQLContext,
    trackingData: TrackingInitData,
    tracker: Tracker
) extends LazyLogging {

  import scala.concurrent.duration._

  type LsEntry = ChannelSftp.LsEntry

  def apply(): Seq[DownloadResult] = {
    val sftp              = SFTPHandler(sftpConfig)
    val destinationFolder = new Path(sftpConfig.hdfsDestination)
    FileSystemOperation.getOrCreate().createDirectoryIfDoesNotExist(destinationFolder)

    val fileSuccessList =
      if (!sftpConfig.isSkipChecking) {
        downloadedFileNames
      } else {
        Seq.empty
      }

    val ignoreSftpFileList = sftpConfig.ignoreProcessedFiles && sftpConfig.ignoreMissingFiles
    val allFiles: Option[ResultFiles] = if (ignoreSftpFileList) {
      logger.info("Skipping SFTP file listing. Using fileNameList directly.")
      None
    } else {
      Some(getFiles(sftp, sftpConfig.actualDownloadPath))
    }

    val saveFiles = allFiles match {
      case Some(files) =>
        logger.info("List of all files from sftp : " + files.listSFTPFileEntry.mkString(","))

        if (!sftpConfig.ignoreProcessedFiles) {
          val processedFileList = files.listSFTPFileEntry.filter(f =>
            fileSuccessList.contains(f.originalFileName) && validatePatterns(sftpConfig.originalFileNameList, f.originalFileName)
          )
          if (processedFileList.nonEmpty) {
            logger.info("List of processed files : " + processedFileList.mkString(","))
            processedFileCase(processedFileList, sftp.sftpModule.server)
          }
        }

        if (!sftpConfig.ignoreMissingFiles) {
          val fileNotFoundList = getFileNotFoundList(
            sftpConfig.fileNameList.filterNot(fileSuccessList.contains),
            files.listSFTPFileEntry.map(_.originalFileName)
          )
          if (fileNotFoundList.nonEmpty) {
            logger.info("List of files not found : " + fileNotFoundList.mkString(","))
            fileNotFoundCase(fileNotFoundList, sftp.sftpModule.server)
          }
        }

        if (files.throwsMsg.nonEmpty) {
          sftpConnectionIssueCase(files.throwsMsg)
        }

        val listToDownload = files.listSFTPFileEntry.filter(f => f.originalFileName.nonEmpty && !fileSuccessList.contains(f.originalFileName))
        logger.info("List of all files to download : " + listToDownload.mkString(","))

        listToDownload.flatMap { entry =>
          downloadFile(
            sftp = sftp,
            remoteDirectory = entry.sftpFileEntry.rootPath,
            fileName = entry.sftpFileEntry.file.getFilename,
            destinationFolder = destinationFolder,
            folder = entry.sftpFileEntry.folder,
            isReprocess = sftpConfig.isReprocess,
            fileAttrs = Option(entry.sftpFileEntry.file.getAttrs),
            originalFileName = Some(entry.originalFileName)
          )
        }

      case None =>
        val fileNames = sftpConfig.fileNameList.filterNot(fileSuccessList.contains)
        logger.info("List of files to download directly: " + fileNames.mkString(","))
        fileNames.flatMap { fileName =>
          downloadFile(
            sftp = sftp,
            remoteDirectory = sftpConfig.actualDownloadPath,
            fileName = fileName,
            destinationFolder = destinationFolder,
            isReprocess = sftpConfig.isReprocess,
            fileAttrs = None,
            originalFileName = Some(fileName)
          )
        }
    }

    logger.info(s"List of all files that was saved to HDFS : " + saveFiles.mkString(","))
    saveFiles
  }

  def getFileList: Seq[String] = sftpConfig.fileNameList

  private[sftp] def filterToDownloadFile(sftpFileNameList: Seq[String], originalFileName: String): Seq[String] =
    sftpFileNameList.flatMap { sftpFileName =>
      ("^" + sftpFileName + "$").r findFirstIn originalFileName match {
        case Some(_) => Some(originalFileName)
        case _       => None
      }
    }

  private[sftp] def getFiles(sftp: SFTPHandler, root: String): ResultFiles =
    Try(getFilesEntryList(sftp, root, sftpConfig.fileNameList.head, sftpConfig.isReprocess)) match {
      case Success(list) =>
        val allSftpFiles = list
          .map((item: SFTPFileEntry) =>
            SFTPFileStatus(
              item,
              filterToDownloadFile(sftpConfig.fileNameList, item.file.getFilename).headOption.getOrElse("")
            )
          )
          .filter(_.originalFileName.nonEmpty)

        ResultFiles(allSftpFiles, TrackingStatus.Ready, "")
      case Failure(e) =>
        ResultFiles(Nil, TrackingStatus.NotReady, e.toString)
    }

  private[sftp] def getFilesEntryList(
      sftp: SFTPHandler,
      root: String,
      fileName: String,
      isReprocess: Boolean,
      dir: String = ""
  ): Seq[SFTPFileEntry] = {

    val task = TaskRetry.constant(
      Task {
        sftp.getFileList(root + dir, root)
      },
      retries,
      retrySeconds.seconds,
      isReprocess,
      root + fileName,
      sftpConfig.fileList
    )

    val files = Await.result(task.runAsync, ((retries + 1) * retrySeconds + 1).seconds)

    files.flatMap { f =>
      if ((f.getAttrs.isRegs || sftpConfig.ignoreFileRegs) && !f.getAttrs.isDir) {
        Seq(SFTPFileEntry(root, dir, f))
      } else {
        Nil
      }
    }
  }

  private[sftp] def delFiles(sftp: SFTPHandler, path: Path, originalFileName: String): Unit =
    // Delete file(s) after downloaded
    try if (path.getName.nonEmpty) {
      sftp.deleteFile(sftpConfig.actualDownloadPath + path.getName)
      logger.info(s"Deleted file on SFTP: ${path.getName}")

      tracker.track(
        Seq(
          Status(
            path.getName,
            sftpConfig.actualDownloadPath + path.getName,
            sftpConfig.fileList,
            TrackingStatus.RemoteDeleted,
            "Deleted file on SFTP - file name : " + path.getName,
            sftpConfig.isReprocess,
            originalFileName = originalFileName
          )
        )
      )
    } catch {
      case e: Exception =>
        logger.error(s"Unable to delete file on SFTP: ${path.getName}", e)
        tracker.track(
          Seq(
            Status(
              path.getName,
              sftpConfig.actualDownloadPath + path.getName,
              sftpConfig.fileList,
              TrackingStatus.RemoteDeleteFailed,
              "Unable to deleted file on SFTP - file name : " + path.getName,
              sftpConfig.isReprocess,
              originalFileName = originalFileName
            )
          )
        )
    }

  private[sftp] def downloadFile(
      sftp: SFTPHandler,
      remoteDirectory: String,
      fileName: String,
      destinationFolder: Path,
      folder: String = "",
      isReprocess: Boolean,
      fileAttrs: Option[SftpATTRS] = None,
      originalFileName: Option[String] = None
  ): Option[DownloadResult] = {
    val filepath                  = remoteDirectory + folder + fileName
    val effectiveOriginalFileName = originalFileName.getOrElse(fileName)
    val serverName                = Try(sftp.sftpModule.server).getOrElse("")

    Try {
      val task = TaskRetry.constant(
        Task {
          logger.info(s"start downloading $fileName")
          val getStream = Future(sftp.getFileStream(filepath))
          Await.result(getStream, timeoutSeconds.map(_.seconds).getOrElse(Duration.Inf))
        },
        retries,
        retrySeconds.seconds,
        isReprocess,
        filepath,
        sftpConfig.fileList
      )

      val stream: ManagedResource[InputStream] = managed(
        Await.result(task.runAsync, ((retries + 1) * (retrySeconds + timeoutSeconds.getOrElse(0)) + 1).seconds)
      )

      val uploadPath =
        if (folder != "") {
          new Path(destinationFolder, new Path(folder, fileName))
        } else {
          new Path(destinationFolder, fileName)
        }

      if (sftpConfig.usingIOStream) {
        trackDownloadSuccess(fileName, folder, serverName, effectiveOriginalFileName, 0)
        DownloadResult(uploadPath, effectiveOriginalFileName, dataStream = Some(stream))
      } else {

        stream.foreach(FileSystemOperation.getOrCreate().uploadStream(uploadPath, _))

        // Track download success
        val mTime = fileAttrs.map(_.getMTime).getOrElse(0)
        trackDownloadSuccess(fileName, folder, serverName, effectiveOriginalFileName, mTime)

        if (sftpConfig.deleteFileAfterDownloaded) {
          delFiles(sftp, uploadPath, effectiveOriginalFileName)
        }

        DownloadResult(uploadPath, effectiveOriginalFileName)
      }

    } match {
      case Success(result) => Some(result)
      case Failure(e) =>
        trackDownloadFailure(fileName, folder, serverName, e.toString, effectiveOriginalFileName, fileAttrs.map(_.getMTime).getOrElse(0))
        None
    }
  }

  private[sftp] def trackDownloadSuccess(
      fileName: String,
      folder: String,
      serverName: String,
      originalFileName: String,
      mTime: Int
  ): Unit = {
    val fullPath = sftpConfig.actualDownloadPath + folder + fileName
    tracker.track(
      Seq(
        Status(
          fileName,
          fullPath,
          sftpConfig.fileList,
          TrackingStatus.Ready,
          s"SFTP Server is ready : $serverName",
          sftpConfig.isReprocess,
          originalFileName = originalFileName
        ),
        Status(
          fileName,
          fullPath,
          sftpConfig.fileList,
          TrackingStatus.Downloaded,
          s"Download success : $fileName",
          sftpConfig.isReprocess,
          mTime,
          originalFileName = originalFileName
        )
      )
    )
  }

  private[sftp] def trackDownloadFailure(
      fileName: String,
      folder: String,
      serverName: String,
      errorMsg: String,
      originalFileName: String,
      mTime: Int
  ): Unit = {
    val fullPath    = sftpConfig.actualDownloadPath + folder + fileName
    val isReprocess = Try(sftpConfig.isReprocess).getOrElse(false)
    tracker.track(
      Seq(
        Status(
          fileName,
          fullPath,
          sftpConfig.fileList,
          TrackingStatus.Ready,
          s"SFTP Server is ready : $serverName",
          isReprocess,
          originalFileName = originalFileName
        ),
        Status(
          fileName,
          fullPath,
          sftpConfig.fileList,
          TrackingStatus.DownloadFailed,
          s"$errorMsg : $fileName",
          isReprocess,
          mTime,
          originalFileName = originalFileName
        )
      )
    )
  }

  private[sftp] def processedFileCase(processedFileList: Seq[SFTPFileStatus], serverName: String): Unit =
    tracker.track(
      processedFileList.flatMap { file =>
        val fileName = file.sftpFileEntry.file.getFilename
        Seq(
          Status(
            fileName,
            sftpConfig.actualDownloadPath + fileName,
            sftpConfig.fileList,
            TrackingStatus.Ready,
            s"SFTP Server is ready : $serverName",
            sftpConfig.isReprocess,
            originalFileName = file.originalFileName
          ),
          Status(
            fileName,
            sftpConfig.actualDownloadPath + fileName,
            sftpConfig.fileList,
            TrackingStatus.AlreadyProcessed,
            "This file is already processed. File name : " + fileName,
            sftpConfig.isReprocess,
            originalFileName = file.originalFileName
          )
        )
      }
    )

  private[sftp] def fileNotFoundCase(fileNotFoundList: Seq[String], serverName: String): Unit =
    tracker.track(
      fileNotFoundList.flatMap(fileName =>
        Seq(
          Status(
            fileName,
            sftpConfig.actualDownloadPath + fileName,
            sftpConfig.fileList,
            TrackingStatus.Ready,
            s"SFTP Server is ready : $serverName",
            sftpConfig.isReprocess
          ),
          Status(
            fileName,
            sftpConfig.actualDownloadPath + fileName,
            sftpConfig.fileList,
            TrackingStatus.DownloadFailed,
            s"Unable to find the file in ${sftpConfig.actualDownloadPath} from $serverName",
            sftpConfig.isReprocess
          )
        )
      )
    )

  private[sftp] def sftpConnectionIssueCase(errorMsg: String): Unit =
    tracker.track(
      sftpConfig.fileNameList.map(fileName =>
        Status(
          fileName,
          sftpConfig.actualDownloadPath + fileName,
          sftpConfig.fileList,
          TrackingStatus.NotReady,
          errorMsg,
          sftpConfig.isReprocess
        )
      )
    )

  private[sftp] def getFileNotFoundList(fileNameList: Seq[String], availableFiles: Seq[String]): Seq[String] =
    fileNameList.filter(name => !availableFiles.exists(n => validatePatterns(Seq(name), n)))

  private[sftp] def validatePatterns(patterns: Seq[String], fileName: String): Boolean = {
    val regexes = patterns.map(pattern => new Regex(pattern))
    regexes.exists(regex => regex.findFirstIn(fileName).isDefined)
  }
}
