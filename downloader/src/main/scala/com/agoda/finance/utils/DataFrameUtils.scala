package com.agoda.finance.utils

import com.agoda.finance.config.protocol.CommonConfigModule
import com.agoda.finance.constant.FileType
import com.agoda.finance.model.{BspFieldIdentifier, MappingConfiguration, MappingField}
import com.agoda.finance.utils.BspFileUtils.alignAndUnionDataFrames
import com.agoda.hadoop.fs.FileSystemOperation
import com.typesafe.scalalogging.LazyLogging
import org.apache.hadoop.fs.Path
import org.apache.spark.rdd.RDD
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{LongType, StringType}
import org.apache.spark.sql.{DataFrame, Row, SQLContext}

case class DataFrameResult(df: DataFrame, lineCountMatched: Boolean)

object DataFrameUtils extends LazyLogging {

  def flatToDF(
      hdfsPath: String,
      mapping: List[MappingConfiguration],
      filterHeader: Option[Int],
      filterFooter: Option[Int]
  )(implicit sqlContext: SQLContext): DataFrameResult = {
    import sqlContext.implicits._
    val file: RDD[String] = sqlContext.sparkSession.sparkContext.textFile(hdfsPath)
    val withLineNumber = file.zipWithIndex
      .map { case (row, line_number) => (row, line_number + 1) }
    val totalLines = withLineNumber.count
    val result = withLineNumber
      .filter { case (_, line_number) =>
        line_number > filterHeader.getOrElse(0) &&
          line_number <= totalLines - filterFooter.getOrElse(0)
      }
      .toDF("value", "line_number")
      .transform(createHashing)
      .transform(createColumn(mapping))

    DataFrameResult(result, true)
  }

  def excelToDF(
      hdfsPath: String,
      config: CommonConfigModule
  )(implicit sqlContext: SQLContext): DataFrameResult = {
    val headerLineCount  = config.FilterHeader
    val footerLineCount  = config.FilterFooter
    val locationZone     = Some(config.LineCountLocationZone.getOrElse("header"))
    val locationPoint    = config.LineCountLocationPoint
    val mismatchAction   = Some(config.LineCountMismatchAction.getOrElse("warning"))
    val delimiter        = config.delimiter
    val headerColumns    = config.headerColumns
    val fileType         = config.compressedFileType.getOrElse(config.fileType)
    val excelSheetIdx    = config.excelSheetIndex
    val excelSheetName   = config.excelSheetName
    val excelInferSchema = config.excelInferSchema
    val readCellAddress  = ExcelUtils.getReadCellAddressFromFilter(hdfsPath, config)

    // Check if sheet name is provided, otherwise use sheet index or default to the first sheet
    val dataAddress = if (excelSheetName.exists(_.nonEmpty)) {
      s"${excelSheetName.get}!${readCellAddress.fromCell}:${readCellAddress.toCell}"
    } else {
      s"${excelSheetIdx.getOrElse(0)}!${readCellAddress.fromCell}:${readCellAddress.toCell}"
    }

    val loadedDf: DataFrame = sqlContext.sparkSession.read
      .format("com.crealytics.spark.excel")
      .option("header", "true")
      .option("dataAddress", dataAddress)
      .option("inferSchema", s"${excelInferSchema.toString}")
      .load(hdfsPath)

    postReadProcessing(
      loadedDf,
      headerLineCount,
      footerLineCount,
      locationZone,
      locationPoint,
      mismatchAction,
      delimiter,
      headerColumns,
      fileType
    )
  }

  private def postReadProcessing(
      df: DataFrame,
      headerLineCount: Option[Int] = None,
      footerLineCount: Option[Int] = None,
      locationZone: Option[String] = Some("header"),
      locationPoint: Array[Option[Int]] = Array(None, None),
      mismatchAction: Option[String] = Some("warning"),
      delimiter: String = ",(?=(?:[^\"]*\"[^\"]*\")*(?![^\"]*\"))",
      headerColumns: Seq[String] = Seq(),
      fileType: String = FileType.CSV
  )(implicit sqlContext: SQLContext): DataFrameResult = {
    var dfWithLineNumber =
      if (FileType.isExcelType(fileType) || FileType.isXMLType(fileType) || FileType.isHtmlType(fileType)) {
        convertExcelDFToDFWithValueAndLineNumber(df, delimiter)
      } else {
        convertToDFWithValueAndLineNumber(df)
      }

    val headersMap = if (headerColumns.nonEmpty) {
      val headerKeys = dfWithLineNumber.first.getString(0).split("\\" + delimiter)
      val headerValues =
        dfWithLineNumber.filter(col("line_number") === 2).collect()(0).getAs[String]("value").split("\\" + delimiter)
      val result = headerKeys.zipWithIndex.map { case (key, count) =>
        (key, headerValues(count))
      }.toMap
      result.filter(tuple => headerColumns.contains(tuple._1))
    } else {
      Map[String, String]()
    }

    var footerData = sqlContext.emptyDataFrame
    var headerData = sqlContext.emptyDataFrame

    if (fileType.contains(FileType.CSV)) {
      footerLineCount match {
        case Some(lCount) =>
          val mainDf = dfWithLineNumber.count
          footerData = dfWithLineNumber
            .filter(col("line_number") > mainDf - lCount)
            .drop("line_number")
            .withColumn("value", convertToArray(delimiter)(col("value")))
          dfWithLineNumber = dfWithLineNumber.filter(col("line_number") <= mainDf - lCount)
          logger.info(s"There is footer setting for this file with $lCount lines")
        case _ => logger.info("There is no footer setting for this file.")
      }
      headerLineCount match {
        case Some(lCount) =>
          headerData = dfWithLineNumber
            .filter(col("line_number") <= lCount)
            .drop("line_number")
            .withColumn("value", convertToArray(delimiter)(col("value")))
          dfWithLineNumber = dfWithLineNumber.filter(col("line_number") > lCount)
          logger.info(s"There is header setting for this file with $lCount lines")
        case _ => logger.info("There is no header setting for this file.")
      }
    }

    val headerCol = dfWithLineNumber.first.getString(0).split("\\" + delimiter)
    val manipulatedDF = dfWithLineNumber
      .transform(createHashing)
      .withColumn("value", convertToArray(delimiter)(col("value")))
      .filter(col("value") =!= headerCol.map(x => x.replaceAll("^\"|\"$", "")))
      .cache

    val csvDF              = setArrayToCol(manipulatedDF, headerCol, "value").drop("value")
    val csvDFAppendHeaders = appendHeadersToDF(csvDF, headersMap).cache
    logger.info(s"count csv to DF ${csvDFAppendHeaders.count}")

    var selectDF = sqlContext.emptyDataFrame

    locationPoint match {
      case Array(Some(row), Some(col)) =>
        locationZone.getOrElse("header") match {
          case "footer" => selectDF = footerData
          case "header" => selectDF = headerData
        }

        getLineCount(selectDF, row, col) match {
          case Some(x) =>
            if (csvDFAppendHeaders.count == x) {
              DataFrameResult(csvDFAppendHeaders, true)
            } else {
              mismatch(
                mismatchAction,
                csvDFAppendHeaders,
                "Number of Lines on footer/header are not equal to the real data",
                new Throwable("Number of Lines are not equal")
              )
            }
          case None =>
            mismatch(
              mismatchAction,
              csvDFAppendHeaders,
              "Unable to get locationPoint of LineCount from header/footer",
              new Throwable("fail to get locationPoint")
            )
        }
      case _ => DataFrameResult(csvDFAppendHeaders, true)
    }
  }

  def csvToDF(
      hdfsPath: String,
      headerLineCount: Option[Int] = None,
      footerLineCount: Option[Int] = None,
      locationZone: Option[String] = Some("header"),
      locationPoint: Array[Option[Int]] = Array(None, None),
      mismatchAction: Option[String] = Some("warning"),
      delimiter: String = ",(?=(?:[^\"]*\"[^\"]*\")*(?![^\"]*\"))",
      mappingFields: Option[List[MappingField]] = None,
      fixBreakingLine: Boolean = false,
      fixUnicode: Boolean = false,
      headerColumns: Seq[String] = Seq()
  )(implicit sqlContext: SQLContext): DataFrameResult = {

    import sqlContext.implicits._
    // MappingFields as none means csv file has a header column.
    // If no header column, program will use 'mappingFields' to create a new file .modified extension with header row

    val df = {
      (mappingFields, fixBreakingLine, fixUnicode) match {
        case (None, false, false) => sqlContext.read.text(hdfsPath)
        case (_, _, _) =>
          val raw =
            FileSystemOperation.getOrCreate().readFileContents(new Path(hdfsPath))
          // Remove UTF BOM
          val bomStripedContent =
            if (fixUnicode) raw.replace("\uFEFF", "")
            else raw
          //to fix line break between double quotes
          val allLines = bomStripedContent.split("\r?\n").toList
          val cleanedContent =
            if (fixBreakingLine) DataFrameUtils.fixBreakingLine(allLines)
            else allLines
          val content = mappingFields match {
            case None => cleanedContent
            case Some(x) =>
              val delimiterReconstruct =
                if (delimiter == ",(?=(?:[^\"]*\"[^\"]*\")*(?![^\"]*\"))") ","
                else delimiter
              val header = x
                .map { p =>
                  p.name
                }
                .mkString(delimiterReconstruct)
              header +: cleanedContent
          }
          content.toDF("value")
      }
    }

    postReadProcessing(
      df,
      headerLineCount,
      footerLineCount,
      locationZone,
      locationPoint,
      mismatchAction,
      delimiter,
      headerColumns
    )
  }

  def xmlSpreadsheetToDF(hdfsPath: String, sheetName: Option[String])(implicit sqlContext: SQLContext): DataFrameResult = {
    val sName = sheetName.getOrElse("Sheet1")

    val loadedDf: DataFrame = XmlWorksheetUtils.loadXmlWorksheet(hdfsPath)

    val transformDf = XmlWorksheetUtils.transformLoadedXml(loadedDf, sName)

    postReadProcessing(transformDf, delimiter = ";", fileType = FileType.XML)
  }

  def htmlSpreadsheetToDF(hdfsPath: String)(implicit sqlContext: SQLContext): DataFrameResult = {
    val df: DataFrame = HtmlWorksheetUtils.readHtmlWorksheet(hdfsPath)
    postReadProcessing(df, delimiter = ";", fileType = FileType.HTML)
  }

  private[utils] def convertToArray(delimiter: String): UserDefinedFunction = udf { str: String =>
    str
      .split("\\" + delimiter)
      .map(x => x.replaceAll("^\"|\"$", ""))
  }

  private[utils] def fixBreakingLine(originalContent: Seq[String]): Seq[String] = {
    var result       = Seq[String]()
    val oIterator    = originalContent.iterator
    var concatenator = ""
    while (oIterator.hasNext) {
      val value  = concatenator + oIterator.next()
      val quotes = value.replaceAll("[^\"]", "").length
      if (quotes % 2 == 0) {
        concatenator = ""
        result = result :+ value
      } else {
        concatenator = value.replaceAll("\\n", "")
      }
    }
    result
  }

  private[utils] def setArrayToCol(df: DataFrame, colsName: Seq[String], rootCol: String): DataFrame = {
    var newDF = df
    for ((colName, index) <- colsName.zipWithIndex)
      newDF = newDF.withColumn(colName.trim, col(rootCol)(index))
    newDF
  }

  private[utils] def appendHeadersToDF(df: DataFrame, headersMap: Map[String, String]): DataFrame = {
    val columns = df.columns ++ headersMap.keys
    val select = columns.map { column =>
      if (headersMap.keySet.contains(column)) {
        lit(headersMap(column)).as(column)
      } else {
        df(column)
      }
    }
    df.select(select: _*)
  }

  private[utils] def getLineCount(df: DataFrame, row: Int, Col: Int): Option[Int] =
    try Some(df.collect()(row).getList(0).get(Col).toString.toInt)
    catch {
      case _: Exception => None
    }

  private[utils] def mismatch(
      mismatchAction: Option[String],
      df: DataFrame,
      msg: String,
      throwable: Throwable
  ): DataFrameResult =
    mismatchAction.getOrElse("warning") match {
      case "error" =>
        logger.error(
          s"$msg - set to send error msg!! and not to write dataframe",
          throwable
        )
        throw new Exception(s"${throwable.getMessage} : Conf was set to error")
      case "warning" =>
        logger.warn(
          s"$msg - set to send warning msg!! and continue to write dataframe"
        )
        DataFrameResult(df, false)
    }

  private[utils] def createColumn(mapping: List[MappingConfiguration])(flatDf: DataFrame): DataFrame =
    flatDf.select(mapping.flatMap { config =>
      if (config.fieldSpecification.exists(_.dropColumn)) {
        None
      } else {
        val beginIndex: Int = config.fieldSpecification.flatMap(_.fixedColumn.map(_.beginIndex)).getOrElse(0)
        val length: Int     = config.fieldSpecification.flatMap(_.fixedColumn.map(_.endIndex)).getOrElse(0) - beginIndex + 1
        Some(col("value").substr(beginIndex + 1, length).alias(config.mappingField.name))
      }

    } ++ List(col("line_number"), col("hash_key")): _*)

  protected[utils] def createHashing(df: DataFrame): DataFrame =
    df.withColumn("hash_key", sha1(col("value")))

  private[utils] def convertExcelDFToDFWithValueAndLineNumber(df: DataFrame, delimiter: String)(implicit sqlContext: SQLContext): DataFrame = {
    import sqlContext.implicits._

    val cols      = df.columns
    val columnsDf = Seq((cols.mkString(delimiter), 1.toLong)).toDF("value", "line_number")
    val dataDf = df.rdd
      .map { row =>
        val sanitizedRow = row.toSeq.map {
          case null  => ""
          case value => value.toString
        }
        sanitizedRow.mkString(delimiter)
      }
      .zipWithIndex
      .map { case (row, line_number) =>
        (row, (line_number + 2).toLong)
      }
      .toDF("value", "line_number")
    columnsDf.union(dataDf)
  }

  private[utils] def convertToDFWithValueAndLineNumber(df: DataFrame)(implicit sqlContext: SQLContext): DataFrame = {
    import sqlContext.implicits._
    df.rdd
      .map(x => x.getString(0))
      .zipWithIndex
      .map { case (row, line_number) =>
        (row, line_number + 1)
      }
      .toDF("value", "line_number")
  }

  def createHashingAndLineDF(df: DataFrame)(implicit sqlContext: SQLContext): DataFrame =
    sqlContext
      .createDataFrame(
        df.rdd.zipWithIndex
          .map { case (row, line_number) =>
            Row.fromSeq(row.toSeq :+ row.mkString :+ (line_number + 1))
          },
        df.schema
          .add("value", StringType, false)
          .add("line_number", LongType, false)
      )
      .transform(createHashing)
      .drop("value")

  def bspToDf(hdfsPath: String, mappers: Map[String, List[BspFieldIdentifier]])(implicit sqlContext: SQLContext): DataFrameResult = {
    import sqlContext.implicits._

    val file: RDD[String] = sqlContext.sparkSession.sparkContext.textFile(hdfsPath)

    val withLineNumber = file.zipWithIndex
      .map { case (row, line_number) => (row, line_number + 1) }
      .toDF("value", "line_number")

    val hashedDf = withLineNumber
      .transform(createHashing)

    val manipulatedDf = hashedDf.withColumn(
      "bsp_transaction_type",
      concat(col("value").substr(1, 3), col("value").substr(12, 2))
    )

    val dfsByTransactionType = mappers.map { mapper =>
      val transactionType        = mapper._1
      val bspFieldIdentifierList = mapper._2

      val specificTypeDf = manipulatedDf.filter(col("bsp_transaction_type") === transactionType)

      val dfByTransactionType = specificTypeDf
        .select(bspFieldIdentifierList.flatMap { fieldIdentifier =>
          val beginIndex: Int = fieldIdentifier.beginIndex
          val endIndex: Int   = fieldIdentifier.endIndex
          val length: Int     = endIndex - beginIndex + 1
          val colValue        = trim(col("value").substr(beginIndex + 1, length))
          val castedCol       = BspFileUtils.castField(fieldIdentifier.dataType, colValue).alias(fieldIdentifier.alias)
          Some(castedCol)
        } ++ Seq(col("value").alias("raw_data"), col("line_number"), col("hash_key")): _*)
        .withColumn("bsp_transaction_type", lit(transactionType))

      dfByTransactionType
    }.toSeq

    val unionDF = alignAndUnionDataFrames(
      dfsByTransactionType,
      mappers
    )

    DataFrameResult(unionDF, true)
  }
}
