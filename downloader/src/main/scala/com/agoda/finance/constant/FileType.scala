package com.agoda.finance.constant

object FileType {
  final val CSV  = "csv"
  final val JSON = "json"
  final val XLSX = "xlsx"
  final val XLS  = "xls"
  final val XML  = "xml"
  final val HTML = "html"
  final val HTM  = "htm"
  final val BSP  = "bsp"
  final val DAT  = "dat"

  def isExcelType(fileType: String): Boolean =
    fileType.contains(XLS) || fileType.contains(XLSX)

  def isCsvType(fileType: String): Boolean =
    fileType.contains(CSV)

  def isXMLType(fileType: String): Boolean =
    fileType == XML

  def isHtmlType(fileType: String): Boolean =
    Seq(HTML, HTM).contains(fileType)

  def isBspType(fileType: String): Boolean =
    Seq(BSP, DAT).contains(fileType.toLowerCase)
}
