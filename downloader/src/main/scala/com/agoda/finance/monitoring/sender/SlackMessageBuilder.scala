package com.agoda.finance.monitoring.sender

import com.agoda.finance.externaldatapipeline.core.utils.StringUtil.escapedJsonCtlChar

object SlackMessageBuilder {

  def build(headerMsg: String, bodyString: String = "", badgeColor: String): String = {
    val jsonPayload =
      s"""
         | {
         |     "username": "Downloader alert",
         |     "icon_emoji": ":chicken:",
         |     "text": "${escapedJsonCtlChar(headerMsg)}",
         |     "response_type": "in_channel",
         |     "attachments": [
         |		    {
         |			    "text": "${escapedJsonCtlChar(bodyString)}",
         |			    "color": "$badgeColor",
         |			    "attachment_type": "default"
         |       }
         |     ]
         | }
      """.stripMargin

    jsonPayload
  }
}
