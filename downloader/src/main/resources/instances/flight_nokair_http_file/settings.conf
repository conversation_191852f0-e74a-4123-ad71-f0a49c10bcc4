include "payload.conf"

instance-name = "flight_nokair_http_file"

app-name = "FlightnokairdirectfileDownloader"
instance-type = "individual"
protocol = "https"

config-downloader {
  team-owner: "@it-recon-devs"
  job-frequency: Daily
  monitoring: {
    status-verification: {
      check-ready-state: true
      check-download-state: false
    }
    slack: {
      enable-status-verification: true
      enable-check-historical-files: false
      warning-alert: {
        check-empty-file: true
      }
    }
    check-lookback-state: true
  }
  downloader_server_nokair {

    include "mapping.conf"

    look-back-date = 7

    file-list: ["nokair_daily_file"]

    nokair_daily_file {
      mapper: "mapper_nokair_daily_file"
      file: {
        name: ["nokair_daily_{date}"]
        type: "xlsx"
        offset-date: -2
        date-file-name-format: "yyyyMMdd"
        excel-sheet-index: 0
      }

      req-payload: ${nokair-sales-payload}
      http-method: "POST"
      http-header: {
          vault-secret-http-auth-header-location: "hadoop_cluster/downloader/api_key/ui_downloader"
          Authorization: "Bearer {token}"
      }
      download-path: "https://ui-downloader-prod.privatecloud.hk.agoda.is/download"

      destination-path: ${destination-root-path}"/nokair_daily_file/"
      table-name: ${schema}".nokair_daily"
      partition {
        key: ["datadate"]
        value: "0" // "[-t,..,0,..,+t]" (default is 0 (current date))) or "partitiondate"
      }
      timestamp-column-format:{
        type:"dd/MM/yyyy HH:mm:ss"

      }
      delete-file-after-downloaded : "false"
      enable-filter: {
        header-line-count: 11
        footer-line-count: 3
        left-column-count: 2
      }
    }
  }

  retries = 2
  retry-seconds = 30
  timeout-seconds = 360
  server = "downloader_server_nokair"
  arguments-date-format = "yyyy-MM-dd"
}

tracking-status {
  table-name: ${schema}".downloader_status"
}

result {
  mail {
    alert-sender = "<EMAIL> (Downloader)"
    alert-recipients = "<EMAIL>,<EMAIL>,<EMAIL>, <EMAIL>"
  }
}
