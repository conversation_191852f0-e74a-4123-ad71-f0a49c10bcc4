instance-name = "activity_bemyguest_cm_file"

app-name = "BemyguestCmSFTPDownloader"
instance-type = "individual"
protocol = "sftp"

config-downloader {

  team-owner: "@it-recon-devs"
  job-frequency: Daily
  monitoring: {
    status-verification: {
      check-ready-state: true
      check-download-state: false
    }
    check-historical-files: {
        schedule-pattern: "0 0 0 ? * MON"
    }
    slack: {
      enable-status-verification: true
      enable-check-historical-files: true
    }
    filename-pattern: ["bemyguest_cm_report_monthly_.+.csv.pgp"]
  }
  downloader_server_bemyguest_cm{

    include "mapping.conf"

    address : "sftp://sftpconnect.agoda.local:22"
    vault-secret-location: "hadoop_cluster/SFTP/activity_bemyguest_file"
    vault-decrypt-location: "hadoop_cluster/decrypt/activity_bemyguest_file"
    look-back-date = 31

    file-list : ["bemyguest_cm_daily_file"]

    bemyguest_cm_daily_file {
      mapper: "Map_BemyguestCm_File"
      file:{
        name: ["bemyguest_cm_report_monthly_{date}"]
        type: "csv.pgp"
        date-file-name-format: "ddMMyyyy"
        offset-date: -1
        encryption: "pgp"
      }

      download-path: "/upload/cm/" // Should end with "/"
      destination-path: ${destination-root-path}"/BemyguestCm_Daily_File/"  // Should be ended with "/"
      table-name: ${schema}".bemyguest_cm_daily_report"
      partition{
        key:["datadate"]
        value:"0" // "[-t,..,0,..,+t]" (default is 0 (current date))) or "partitiondate"
      }
      timestamp-column-format:{
        type: "yyyy-MM-dd'T'HH:mm:ssX"
      }
    }

  }

  retries = 3
  retry-seconds = 30
  server = ${server}
  arguments-date-format = "yyyy-MM-dd"
}

tracking-status{
  table-name: ${schema}".downloader_status"

}

result {
  mail {
    alert-sender = "<EMAIL> (Downloader)"
    alert-recipients = "<EMAIL>,<EMAIL>"
  }
}
