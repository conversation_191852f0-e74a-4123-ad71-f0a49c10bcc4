mappers: {
  Map_BemyguestCm_File: [
    {name: "agoda_booking_id", dataType: "LongType", isNullable: false, alias: "agoda_booking_id"},
    {name: "agoda_transaction_id", dataType: "LongType", isNullable: false, alias: "agoda_transaction_id"},
    {name: "supplier_transaction_id", dataType: "StringType", isNullable: false, alias: "supplier_transaction_id"},
    {name: "supplier_booking_id", dataType: "StringType", isNullable: false, alias: "supplier_booking_id"},
    {name: "operator_ref_id", dataType: "StringType", isNullable: false, alias: "operator_ref_id"},
    {name: "activity_name", dataType: "StringType", isNullable: false, alias: "activity_name"},
    {name: "operator_name", dataType: "StringType", isNullable: false, alias: "operator_name"},
    {name: "transaction_type", dataType: "StringType", isNullable: false, alias: "transaction_type"},
    {name: "local_currency", dataType: "StringType", isNullable: false, alias: "local_currency"},
    {name: "cost_local", dataType: "DoubleType", isNullable: false, alias: "cost_local"},
    {name: "channel_manager_fee_local", dataType: "DoubleType", isNullable: false, alias: "channel_manager_fee_local"},
    {name: "tax_amount_local", dataType: "DoubleType", isNullable: false, alias: "tax_amount_local"},
    {name: "commission", dataType: "DoubleType", isNullable: true, alias: "commission"},
    {name: "total_amount", dataType: "DoubleType", isNullable: false, alias: "total_amount"},
    {name: "booking_date_utc", dataType: "TimestampType", isNullable: false, alias: "booking_date_utc"},
    {name: "transaction_date_utc", dataType: "TimestampType", isNullable: false, alias: "transaction_date_utc"}
  ]
}

