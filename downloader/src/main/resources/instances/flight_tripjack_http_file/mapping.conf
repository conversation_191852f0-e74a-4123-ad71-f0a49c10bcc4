mappers: {
  mapper_tripjack_daily_file: [
    {name: "Booking Id", dataType: "StringType", isNullable: false, alias: "booking_id"}
    {name: "Booking Type", dataType: "StringType", isNullable: false, alias: "booking_type"}
    {name: "Booking Date", dataType: "TimestampType", isNullable: false, alias: "booking_date"}
    {name: "Reseller Id", dataType: "StringType", isNullable: true, alias: "reseller_id"}
    {name: "Reseller Name", dataType: "StringType", isNullable: true, alias: "reseller_name"}
    {name: "Reseller Email", dataType: "StringType", isNullable: true, alias: "reseller_email"}
    {name: "Reseller State", dataType: "StringType", isNullable: true, alias: "reseller_state"}
    {name: "Reseller Country", dataType: "StringType", isNullable: true, alias: "reseller_country"}
    {name: "Description", dataType: "StringType", isNullable: true, alias: "description"}
    {name: "AmendmentId", dataType: "StringType", isNullable: true, alias: "amendmentid"}
    {name: "Payment Medium", dataType: "StringType", isNullable: true, alias: "payment_medium"}
    {name: "Payment Status", dataType: "StringType", isNullable: true, alias: "payment_status"}
    {name: "Amendment Type", dataType: "StringType", isNullable: true, alias: "amendment_type"}
    {name: "Amendment Date", dataType: "StringType", isNullable: true, alias: "amendment_date"}
    {name: "Product Name", dataType: "StringType", isNullable: true, alias: "product_name"}
    {name: "Destination", dataType: "StringType", isNullable: true, alias: "destination"}
    {name: "Origin", dataType: "StringType", isNullable: true, alias: "origin"}
    {name: "Arrival Date", dataType: "StringType", isNullable: true, alias: "arrival_date"}
    {name: "Arrival Time", dataType: "StringType", isNullable: true, alias: "arrival_time"}
    {name: "Departure Date", dataType: "StringType", isNullable: true, alias: "departure_date"}
    {name: "Departure Time", dataType: "StringType", isNullable: true, alias: "departure_time"}
    {name: "Days to travel", dataType: "StringType", isNullable: true, alias: "days_to_travel"}
    {name: "Status", dataType: "StringType", isNullable: true, alias: "status"}
    {name: "Cart Generation Date", dataType: "StringType", isNullable: true, alias: "cart_generation_date"}
    {name: "System invoice id", dataType: "StringType", isNullable: true, alias: "system_invoice_id"}
    {name: "Flight Number", dataType: "StringType", isNullable: true, alias: "flight_number"}
    {name: "Passenger Name", dataType: "StringType", isNullable: true, alias: "passenger_name"}
    {name: "Pax Type", dataType: "StringType", isNullable: true, alias: "pax_type"}
    {name: "Gds Pnr", dataType: "StringType", isNullable: true, alias: "gds_pnr"}
    {name: "Pnr", dataType: "StringType", isNullable: true, alias: "pnr"}
    {name: "Ticket Number", dataType: "StringType", isNullable: true, alias: "ticket_number"}
    {name: "Total Fare", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "total_fare"}
    {name: "Base Fare", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "base_fare"}
    {name: "YQ", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "yq"}
    {name: "YR", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "yr"}
    {name: "Taxable", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "taxable"}
    {name: "TAF", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "taf"}
    {name: "Other Taxes", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "other_taxes"}
    {name: "CGST", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "cgst"}
    {name: "SGST", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "sgst"}
    {name: "IGST", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "igst"}
    {name: "UGST", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "ugst"}
    {name: "RCF", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "rcf"}
    {name: "PSF", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "psf"}
    {name: "UDF", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "udf"}
    {name: "JN", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "jn"}
    {name: "Airline Gst", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "airline_gst"}
    {name: "OB", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "ob"}
    {name: "OC", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "oc"}
    {name: "Airline Sbc", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "airline_sbc"}
    {name: "Airline Kkc", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "airline_kkc"}
    {name: "Gross Fare", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "gross_fare"}
    {name: "To Gst", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "to_gst"}
    {name: "From Gst", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "from_gst"}
    {name: "Customer Gst", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "customer_gst"}
    {name: "Management Fee", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "management_fee"}
    {name: "Gross Discount", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "gross_discount"}
    {name: "TDS", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "tds"}
    {name: "Net Discount", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "net_discount"}
    {name: "Points Discount", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "points_discount"}
    {name: "Net Fare", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "net_fare"}
    {name: "Booking Class", dataType: "StringType", isNullable: true, alias: "booking_class"}
    {name: "Net Fare With Markup", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "net_fare_with_markup"}
    {name: "Supplier Amendment Fee", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "supplier_amendment_fee"}
    {name: "Reseller Amendment Fee", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "reseller_amendment_fee"}
    {name: "Booking Time", dataType: "StringType", isNullable: true, alias: "booking_time"}
    {name: "Cart Generation Time", dataType: "StringType", isNullable: true, alias: "cart_generation_time"}
    {name: "Reseller City", dataType: "StringType", isNullable: true, alias: "reseller_city"}
    {name: "Trip Type", dataType: "StringType", isNullable: true, alias: "trip_type"}
    {name: "Destination Country", dataType: "StringType", isNullable: true, alias: "destination_country"}
    {name: "Airline Code", dataType: "StringType", isNullable: true, alias: "airline_code"}
    {name: "Cabin Class", dataType: "StringType", isNullable: true, alias: "cabin_class"}
    {name: "Payment Fee", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "payment_fee"}
    {name: "Credit Shell Amount", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "credit_shell_amount"}
    {name: "Refundable Booking Amount", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "refundable_booking_amount"}
    {name: "Total Refundable Booking Charges", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "total_refundable_booking_charges"}
    {name: "Channel Type", dataType: "StringType", isNullable: true, alias: "channel_type"}
    {name: "Document Id", dataType: "StringType", isNullable: true, alias: "document_id"}
    {name: "Fare Type", dataType: "StringType", isNullable: true, alias: "fare_type"}
    {name: "Emd Number", dataType: "StringType", isNullable: true, alias: "emd_number"}
    {name: "O-D", dataType: "StringType", isNullable: true, alias: "o_d"}
    {name: "Meal Price", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "meal_price"}
    {name: "Seat Price", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "seat_price"}
    {name: "Baggage Price", dataType: "DecimalType", precision: 18, scale: 2, isNullable: true, alias: "baggage_price"}
  ]
}