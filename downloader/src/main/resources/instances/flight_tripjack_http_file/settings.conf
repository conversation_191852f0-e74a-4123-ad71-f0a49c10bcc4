include "payload.conf"

instance-name = "flight_tripjack_http_file"

app-name = "FlighttripjackfileDownloader"
instance-type = "individual"
protocol = "https"

config-downloader {

  team-owner: "@it-recon-devs"
  job-frequency: Daily
  monitoring: {
    status-verification: {
      check-ready-state: true
      check-download-state: true
    }
    slack: {
      enable-status-verification: true
      enable-check-historical-files: false
      warning-alert: {
        check-empty-file: true
      }
    }
  }
  downloader_server_tripjack {

    include "mapping.conf"

    look-back-date = 7

    file-list : ["tripjack_daily_file"]

    tripjack_daily_file {
      mapper: "mapper_tripjack_daily_file"
      file:{
        name: ["tripjack_daily_{date}"]
        type: "csv"
        offset-date: -2
        delimiter: ","

        date-file-name-format: "yyyyMMdd"
        fix-unicode: true
      }

      req-payload: ${tripjack_daily_file_payload}
      http-method: "POST"
      http-header: {
        vault-secret-http-auth-header-location: "hadoop_cluster/downloader/api_key/ui_downloader"
        Authorization: "Bearer {token}"
      }
      download-path: "https://ui-downloader-prod.privatecloud.hk.agoda.is/download"

      destination-path: ${destination-root-path}"/tripjack_daily_file/"
      table-name: ${schema}".tripjack_daily"
      partition{
        key:["datadate"]
        value:"0" // "[-t,..,0,..,+t]" (default is 0 (current date))) or "partitiondate"
      }
      timestamp-column-format:{
        type: "yyyy-MM-dd"
      }

      delete-file-after-downloaded : "false"

    }
  }

  retries = 2
  retry-seconds = 30
  timeout-seconds = 360
  server = "downloader_server_tripjack"
  arguments-date-format = "yyyy-MM-dd"
}

tracking-status{
  table-name: ${schema}".downloader_status"

}

result {
  mail {
    alert-sender = "<EMAIL> (Downloader)"
    alert-recipients = "<EMAIL>,<EMAIL>,<EMAIL>"
  }
}
