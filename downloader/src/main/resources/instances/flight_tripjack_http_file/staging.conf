include classpath("_generic_staging.conf")
include "settings.conf"

#schema= "finance_sftp_downloader_qa" uncomment this line to get data in qa
schema= "finance_sftp_downloader_staging"
current_env="staging"
server = "downloader_server_tripjack"
app.adpMessaging.appName: "downloader.staging"
host-download-path = "/upload/tripjack/dev/daily/"

hadoop {
  hdfs.user = "hk-fin-supl-prod-svc--dev"
  knox.enabled = true
  hive.username = "hk-fin-supl-prod-svc--dev"
  credentials = "hadoop/hk-fin-supl-prod-svc--dev/credentials"
  credentials_from_env = true
  use-smart-table-loader = true
}

result {
  mail {
    alert-sender = "<EMAIL> (Downloader)"
    alert-recipients = "<EMAIL>"
  }
}

interactive-slackbot {
    request-group = "Okta-Global_Apps_Admins"
    approve-group = "Okta-Global_Apps_Admins"
    slack-channel = "CM86MMHV4"
}