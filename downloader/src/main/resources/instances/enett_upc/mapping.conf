mappers: {
Enett_UPC: [
    {name: "reconcile_txn_id",         dataType: "LongType",       isNullable: true,   alias: reconcile_txn_id},
    {name: "hotel_id",                 dataType: "LongType",       isNullable: true,   alias: hotel_id},
    {name: "Account_id",               dataType: "LongType",       isNullable: true,   alias: account_id},
    {name: "LinkedAuthorizations",     dataType: "LongType",       isNullable: true,   alias: linked_authorizations},
    {name: "dmc_id",                   dataType: "LongType",       isNullable: true,   alias: dmc_id},
    {name: "batch_id",                 dataType: "StringType",     isNullable: true,   alias: batch_id},
    {name: "booking_id",               dataType: "LongType",       isNullable: true,   alias: booking_id},
    {name: "Trans_ID",                 dataType: "LongType",       isNullable: true,   alias: ttrans_id},
    {name: "trans_currency_code",      dataType: "StringType",     isNullable: true,   alias: trans_currency_code},
    {name: "trans_amount",             dataType: "DoubleType",     isNullable: true,   alias: trans_amount},
    {name: "postdate",                 dataType: "StringType",     isNullable: true,   alias: postdate},
    {name: "transaction_date",         dataType: "StringType",     isNullable: true,   alias: transaction_date},
    {name: "settlement_date",          dataType: "StringType",     isNullable: true,   alias: settlement_date},
    {name: "Activity_Type",            dataType: "StringType",     isNullable: true,   alias: activity_type},
    {name: "post_currency_code",       dataType: "StringType",     isNullable: true,   alias: post_currency_code},
    {name: "post_amount",              dataType: "DoubleType",     isNullable: true,   alias: post_amount},
    {name: "Reconciliation_currency",  dataType: "StringType",     isNullable: true,   alias: reconciliation_currency},
    {name: "Reconciliation_amount",    dataType: "DoubleType",     isNullable: true,   alias: reconciliation_ammount},
    {name: "Merch_ID",                 dataType: "LongType",       isNullable: true,   alias: merch_id},
    {name: "MCC_code",                 dataType: "LongType",       isNullable: true,   alias: merchant_category_code},
    {name: "merchant_code",            dataType: "LongType",       isNullable: true,   alias: merchant_code},
    {name: "authorization_no",         dataType: "LongType",       isNullable: true,   alias: authorization_no},
    {name: "merchant_name",            dataType: "StringType",     isNullable: true,   alias: merchant_name},
    {name: "merchant_country",         dataType: "StringType",     isNullable: true,   alias: merchant_country},
    {name: "Merch_Address",            dataType: "StringType",     isNullable: true,   alias: merchant_address},
    {name: "Merch_State",              dataType: "StringType",     isNullable: true,   alias: merch_state},
    {name: "Merch_City",               dataType: "StringType",     isNullable: true,   alias: merchant_city},
    {name: "Merch_PostCode",           dataType: "StringType",     isNullable: true,   alias: merchant_postcode},
    {name: "pcardno",                  dataType: "StringType",     isNullable: true,   alias: pcardno},
    {name: "IntegratorReference",      dataType: "StringType",     isNullable: true,   alias: integrator_reference}
  ]
}
