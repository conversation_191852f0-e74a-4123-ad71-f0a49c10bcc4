instance-name = "flight_dida_file"

app-name = "FlightdidafileDownloader"
instance-type = "individual"
protocol = "sftp"

config-downloader {

  team-owner: "@it-recon-devs"
  job-frequency: Daily
  monitoring: {
    status-verification: {
      check-ready-state: true
      check-download-state: false
    }
    slack: {
      enable-status-verification: true
      enable-check-historical-files: false
      warning-alert: {
        check-empty-file: true
      }
    }
  }
  downloader_server_dida {

    include "mapping.conf"

    address : "sftp://sftpconnect.agoda.local:22"
    vault-secret-location: "hadoop_cluster/SFTP/flight_dida_file"
    look-back-date = 8

    file-list : ["dida_daily_file"]

    dida_daily_file {
      mapper: "mapper_dida_daily_file"
      file:{
        name: ["HFSTM.*__{date}"]
        type: "xlsx"
        delimiter: ","
        offset-date: -1

        date-file-name-format: "yyMMdd"
        fix-unicode: true
      }

      download-path: ${host-download-path}
      destination-path: ${destination-root-path}"/dida_daily_file/"
      table-name: ${schema}".dida_daily"
      partition{
        key:["datadate"]
        value:"0" // "[-t,..,0,..,+t]" (default is 0 (current date))) or "partitiondate"
      }
      timestamp-column-format:{
        type: "dd/MM/yyyy HH:mm"
      }

      delete-file-after-downloaded : "false"

    }
  }

  retries = 3
  retry-seconds = 30
  server = "downloader_server_dida"
  arguments-date-format = "yyyy-MM-dd"
}

tracking-status{
  table-name: ${schema}".downloader_status"

}

result {
  mail {
    alert-sender = "<EMAIL> (Downloader)"
    alert-recipients = "<EMAIL>,<EMAIL>,<EMAIL>"
  }
} 