mappers: {
  mapper_dida_daily_file: [
    {name: "TransactionType", dataType: "StringType", isNullable: true, alias: "transaction_type"},
    {name: "TransactionDate", dataType: "TimestampType", isNullable: true, alias: "transaction_date"},
    {name: "BaseFare", dataType: "DecimalType", precision:18, scale:2, isNullable: true, alias: "base_fare"},
    {name: "Tax", dataType: "DecimalType", precision:18, scale:2, isNullable: true, alias: "tax"},
    {name: "Penalty", dataType: "DecimalType", precision:18, scale:2, isNullable: true, alias: "penalty"},
    {name: "AdminFee", dataType: "DecimalType", precision:18, scale:2, isNullable: true, alias: "admin_fee"},
    {name: "Commission", dataType: "DecimalType", precision:18, scale:2, isNullable: true, alias: "commission"},
    {name: "Ticketing<PERSON>ee", dataType: "DecimalType", precision:18, scale:2, isNullable: true, alias: "ticketing_fee"},
    {name: "CancellationCharge", dataType: "DecimalType", precision:18, scale:2, isNullable: true, alias: "cancellation_charge"},
    {name: "BaggageAncillaries", dataType: "DecimalType", precision:18, scale:2, isNullable: true, alias: "baggage_ancillaries"},
    {name: "SeatsAncillaries", dataType: "DecimalType", precision:18, scale:2, isNullable: true, alias: "seats_ancillaries"},
    {name: "MealAncillaries", dataType: "DecimalType", precision:18, scale:2, isNullable: true, alias: "meal_ancillaries"},
    {name: "TotalAmount", dataType: "DecimalType", precision:18, scale:2, isNullable: true, alias: "total_amount"},
    {name: "ClientCurrency", dataType: "StringType", isNullable: true, alias: "client_currency"},
    {name: "GDSPNR", dataType: "StringType", isNullable: true, alias: "gds_pnr"},
    {name: "AirLinePNR", dataType: "StringType", isNullable: true, alias: "airline_pnr"},
    {name: "Ticket Number", dataType: "StringType", isNullable: true, alias: "ticket_no"},
    {name: "PassengerName", dataType: "StringType", isNullable: true, alias: "passenger_name"},
    {name: "OldSupplierReference", dataType: "StringType", isNullable: true, alias: "old_supplier_reference"},
    {name: "OldTicket Number", dataType: "StringType", isNullable: true, alias: "old_ticket_no"},
    {name: "AgentMemo", dataType: "StringType", isNullable: true, alias: "agent_memo"},
    {name: "EMDNum", dataType: "StringType", isNullable: true, alias: "emd_num"},
    {name: "EMDType", dataType: "StringType", isNullable: true, alias: "emd_type"},
    {name: "Invoice Number", dataType: "StringType", isNullable: true, alias: "invoice_no"},
    {name: "TicketIssueDate", dataType: "StringType", isNullable: true, alias: "ticket_issue_date"},
    {name: "CreatedOn", dataType: "StringType", isNullable: true, alias: "created_on"},
    {name: "Segments", dataType: "StringType", isNullable: true, alias: "segments"},
    {name: "Airline", dataType: "StringType", isNullable: true, alias: "airline"}
  ]
} 