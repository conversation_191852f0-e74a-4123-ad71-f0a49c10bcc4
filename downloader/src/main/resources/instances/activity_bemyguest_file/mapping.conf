mappers: {
  Map_Bemyguest_File: [
    {name: "agoda_booking_id", dataType: "LongType", isNullable: false, alias: "agoda_booking_id"},
    {name: "agoda_transaction_id", dataType: "LongType", isNullable: false, alias: "agoda_transaction_id"},
    {name: "supplier_transaction_id", dataType: "StringType", isNullable: false, alias: "supplier_transaction_id"},
    {name: "supplier_booking_id", dataType: "StringType", isNullable: false, alias: "supplier_booking_id"},
    {name: "transaction_type", dataType: "StringType", isNullable: false, alias: "transaction_type"},
    {name: "currency", dataType: "StringType", isNullable: false, alias: "currency"},
    {name: "cost", dataType: "DoubleType", isNullable: false, alias: "cost"},
    {name: "commission", dataType: "DoubleType", isNullable: true, alias: "commission"},
    {name: "total_amount", dataType: "DoubleType", isNullable: false, alias: "total_amount"},
    {name: "booking_date_utc", dataType: "TimestampType", isNullable: false, alias: "booking_date_utc"},
    {name: "transaction_date_utc", dataType: "TimestampType", isNullable: false, alias: "transaction_date_utc"},
    {name: "cost_usd", dataType: "DoubleType", isNullable: false, alias: "cost_usd"},
    {name: "commission_usd", dataType: "DoubleType", isNullable: true, alias: "commission_usd"},
    {name: "total_amount_usd", dataType: "DoubleType", isNullable: false, alias: "total_amount_usd"},
    {name: "gst_amount", dataType: "DoubleType", isNullable: false, alias: "gst_amount"},
    {name: "invoice_number", dataType: "StringType", isNullable: true, alias: "invoice_number"}
  ]
}

