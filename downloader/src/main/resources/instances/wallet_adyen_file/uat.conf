include classpath("_generic_staging.conf")
include "settings.conf"


schema="finance_wallet_uat"
current_env="uat"
app.adpMessaging.appName: "downloader.uat"

server = "downloader_server_wallet_adyen"

config-downloader.downloader_server_wallet_adyen.address = "sftp://sftp-test.adyen.com:5892"

slack-config.channel-id = "o_fintech-wallet-uat"

hadoop {
  hdfs.user = "hk-fin-wal-svc"
  knox.enabled = true
  hive.username = "hk-fin-wal-svc"
  credentials = "hadoop/hk-fin-wal-svc/credentials"
  credentials_from_env = true
  use-smart-table-loader = true

}

config-downloader{
  downloader_server_wallet_adyen:{
    vault-secret-location: "dataproject/hk-fin-wal-svc/downloader/adyen_sftp_uat"
    wallet_adyen_uk_daily_file{
      // TODO update download-path to actual path for uat
      download-path: "/AgodaTH_Wallet_UAT_UK_TEST/" // Should end with "/"
      destination-path: ${destination-root-path}"/Wallet_Adyen_UAT_UK_Daily_File/"  // Should be ended with "/"
    }
  }
}


result {
  mail {
    alert-sender = "<EMAIL> (Downloader)"
    alert-recipients = ""
  }
}

interactive-slackbot {
  request-group = "Okta-Global_Apps_Admins"
  approve-group = "Okta-Global_Apps_Admins"
  slack-channel = "C07HQNRL16C" # FINWAL-507 to be change later
}

ag-vault {
  http {
      url = "https://hk.vault.agoda.local:8200"
    auth {
      method = "approle"
    }
  }
}
