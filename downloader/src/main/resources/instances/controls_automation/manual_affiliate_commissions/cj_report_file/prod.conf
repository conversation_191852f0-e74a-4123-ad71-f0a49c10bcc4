include classpath("_generic_prod.conf")
include "settings.conf"

schema="affiliate_commission_accrual_control"
env_path="accrual-for-affiliate-commission"
current_env="prod"
server = "downloader_server"
app.adpMessaging.appName: "downloader.prod"

hadoop {
  hdfs.user = "hk-conaut-svc"
  knox.enabled = true
  hive.username = "hk-conaut-svc"
  credentials = "hadoop/hk-conaut-svc/credentials"
  credentials_from_env = true
  use-smart-table-loader = true
}

config-downloader {
  downloader_server {
    vault-fallback-location = backupCredential/microsoft-graph/microsoft_graph_credential.json
  }
}

result {
  mail {
    alert-sender = "<EMAIL> (Downloader)"
    alert-recipients = "<EMAIL>"
  }
}

slack-config.channel-id = "orchestration-platform-support"