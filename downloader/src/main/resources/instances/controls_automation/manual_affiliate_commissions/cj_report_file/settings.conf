instance-name = "cj_report_file"

app-name = "cj_report_file_downloader"
instance-type = "individual"
protocol = "sharepoint"

config-downloader {
  monitoring: {
    status-verification: {
      check-ready-state: true
      check-download-state: true
    }
    slack: {
      enable-status-verification: true
      enable-check-historical-files: false
    }
  }

  downloader_server {

    include "mapping.conf"
    look-back-date = 1
    file-list : [
      "us-report",
      "eu-report",
      "uk-report"
    ]

    site-name: "controlsautomation"

    us-report {
      mapper: "Report_File_Mapper"
        file:{
          name: ["261856-Commission Junction-US-{date}"]
          type: "xlsx"
          date-file-name-format: "yyyyMM"
          has-column-header:"true"
          excel-sheet-name: "US"
        }
      download-path: ${env_path}"/network-partner-reports/" // Should be ended with "/"
      destination-path: ${destination-root-path}"/cj-us-report/"  // Should be ended with "/"
      table-name: ${schema}".cj_reports_staging"
      timestamp-column-format:{
        type:"dd/MM/yyyy"
      }
      partition{
       key:["datamonth", "currency"]
       value:"partitiondate" // "[-t,..,0,..,+t]" (default is 0 (current date))) or "partitiondate"
     }
      enable-filter: {
          filter-footer-empty-rows: true
        }
      is-skip-checking : "true"
      save-mode: "overwrite"
      partition-format: "yyyyMM"
    }

    eu-report {
      mapper: "Report_File_Mapper"
        file:{
          name: ["261856-Commission Junction-EU-{date}"]
          type: "xlsx"
          date-file-name-format: "yyyyMM"
          has-column-header:"true"
          excel-sheet-name: "EU"
        }
      download-path: ${env_path}"/network-partner-reports/" // Should be ended with "/"
      destination-path: ${destination-root-path}"/cj-eu-report/"  // Should be ended with "/"
      table-name: ${schema}".cj_reports_staging"
      timestamp-column-format:{
        type:"dd/MM/yyyy"
      }
      partition{
        key:["datamonth", "currency"]
        value:"partitiondate" // "[-t,..,0,..,+t]" (default is 0 (current date))) or "partitiondate"
      }
      enable-filter: {
          filter-footer-empty-rows: true
        }
      is-skip-checking : "true"
      save-mode: "overwrite"
      partition-format: "yyyyMM"
    }

    uk-report {
      mapper: "Report_File_Mapper"
        file:{
          name: ["261856-Commission Junction-EU-{date}"]
          type: "xlsx"
          date-file-name-format: "yyyyMM"
          has-column-header:"true"
          excel-sheet-name: "UK"
        }
      download-path: ${env_path}"/network-partner-reports/" // Should be ended with "/"
      destination-path: ${destination-root-path}"/cj-uk-report/"  // Should be ended with "/"
      table-name: ${schema}".cj_reports_staging"
      timestamp-column-format:{
        type:"dd/MM/yyyy"
      }
      partition{
       key:["datamonth", "currency"]
       value:"partitiondate" // "[-t,..,0,..,+t]" (default is 0 (current date))) or "partitiondate"
      }
      enable-filter: {
          filter-footer-empty-rows: false
        }
      is-skip-checking : "true"
      save-mode: "overwrite"
      partition-format: "yyyyMM"
    }

  }



  retries = 3
  retry-seconds = 30
  server = ${server}
  arguments-date-format = "yyyy-MM-dd"
}

hadoop.spark {
  driver {
    memory = "20g"
    memory-overhead = 16384
  }
}

tracking-status {
  table-name: ${schema}".downloader_status"
}