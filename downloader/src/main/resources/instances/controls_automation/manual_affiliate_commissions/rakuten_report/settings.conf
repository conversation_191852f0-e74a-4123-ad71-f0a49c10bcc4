instance-name = "rakuten_report"

app-name = "rakuten_report"
instance-type = "individual"
protocol = "sharepoint"
server = "downloader_server"

config-downloader {
  monitoring: {
    status-verification: {
      check-ready-state: true
      check-download-state: true
    }
    slack: {
      enable-status-verification: true
      enable-check-historical-files: false
    }
  }

  downloader_server {
    
    include "mapping.conf"

    vault-fallback-location = backupCredential/microsoft-graph/microsoft_graph_credential.json

    file-list : [
      "rakuten_report",
    ]

    site-name: "controlsautomation"

    rakuten_report {
      mapper: "rakuten_report_file"
      file:{
        name: ["217645-Rakuten-{date}"]
        type: "xlsx"
        date-file-name-format: "yyyyMM"
        has-column-header:"true"
      }
      download-path: ${file_directory}/network-partner-reports/
      destination-path: ${destination-root-path}/${file_directory}/network-partner-reports/
      save-mode: "overwrite"
      table-name: ${schema}".rakuten_report_staging"
      timestamp-column-format:{
        type:"dd/MM/yyyy"
      }
      enable-filter: {
        header-line-count: 4 # start at row 5
      }
      partition{
        key:["datamonth"]
        value:"partitiondate" // "[-t,..,0,..,+t]" (default is 0 (current date))) or "partitiondate"
      }
      partition-format: "yyyyMM"

      is-skip-checking : "true"
    },

  }

  retries = 3
  retry-seconds = 30
  server = ${server}
  arguments-date-format = "yyyy-MM-dd"
}

hadoop.spark {
  driver {
    memory = "20g"
    memory-overhead = 16384
  }
}

tracking-status {
  table-name: ${schema}".downloader_status"
}