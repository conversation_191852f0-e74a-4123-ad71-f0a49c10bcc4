instance-name = "pah_b2b_report_file"

app-name = "pah_b2b_report_file_downloader"
instance-type = "individual"
protocol = "sharepoint"

config-downloader {
  monitoring: {
    status-verification: {
      check-ready-state: true
      check-download-state: true
    }
    slack: {
      enable-status-verification: true
      enable-check-historical-files: false
    }
  }

  downloader_server {
    include "mapping.conf"

    look-back-date = 1
    file-list : [
      "rakuten_pah",
      "ixigo_pah",
      "dida_pah",
      "bcom_pah",
      "tiket_pah"
    ]

    site-name: "controlsautomation"

    rakuten_pah {
      mapper: "rakuten_pah_mapper"
        file:{
          name: ["282665-rakuten pay at hotel-{date}"]
          type: "xlsx"
          date-file-name-format: "yyyyMM"
          has-column-header:"true"
        }
      download-path: ${env_path}"/pah-reports/" // Should be ended with "/"
      destination-path: ${destination-root-path}"/rakuten-pah-report/"  // Should be ended with "/"
      table-name: ${schema}".pah_reports_staging"
      timestamp-column-format:{
        type:"dd/MM/yyyy"
      }
      partition{
        key:["datamonth"]
        value:"partitiondate" // "[-t,..,0,..,+t]" (default is 0 (current date))) or "partitiondate"
      }
      enable-filter: {
          filter-footer-empty-rows: true
        }
      is-skip-checking : "true"
      save-mode: "overwrite" // only added the overwrite config to this report, as this report is the first to be processed
      partition-format: "yyyyMM"
   }

   ixigo_pah {
     mapper: "ixigo_pah_mapper"
       file:{
         name: ["299836-ixigo pay at hotel-{date}"]
         type: "xlsx"
         date-file-name-format: "yyyyMM"
         has-column-header:"true"
       }
     download-path: ${env_path}"/pah-reports/" // Should be ended with "/"
     destination-path: ${destination-root-path}"/ixigo-pah-report/"  // Should be ended with "/"
     table-name: ${schema}".pah_reports_staging"
     timestamp-column-format:{
       type:"dd/MM/yyyy"
     }
     partition{
       key:["datamonth"]
       value:"partitiondate" // "[-t,..,0,..,+t]" (default is 0 (current date))) or "partitiondate"
     }
     enable-filter: {
         filter-footer-empty-rows: true
         header-line-count: 1 # start at row 2
       }
     is-skip-checking : "true"
     partition-format: "yyyyMM"
  }

  dida_pah {
     mapper: "dida_pah_mapper"
       file:{
         name: ["300099-dida pay at hotel-{date}"]
         type: "xlsx"
         date-file-name-format: "yyyyMM"
         has-column-header:"true"
         excel-sheet-name: "Sheet1"
       }
     download-path: ${env_path}"/pah-reports/" // Should be ended with "/"
     destination-path: ${destination-root-path}"/dida-pah-report/"  // Should be ended with "/"
     table-name: ${schema}".pah_reports_staging"
     timestamp-column-format:{
       type:"dd/MM/yyyy"
     }
     partition{
       key:["datamonth"]
       value:"partitiondate" // "[-t,..,0,..,+t]" (default is 0 (current date))) or "partitiondate"
     }
     enable-filter: {
         filter-footer-empty-rows: true
       }
     is-skip-checking : "true"
     partition-format: "yyyyMM"
  }

  bcom_pah {
     mapper: "bcom_pah_mapper"
       file:{
         name: ["243996-tiket pay at hotel (Bcom Inventory)-{date}"]
         type: "xlsx"
         date-file-name-format: "yyyyMM"
         has-column-header:"true"
         excel-sheet-name: "Booking_List"
       }
     download-path: ${env_path}"/pah-reports/" // Should be ended with "/"
     destination-path: ${destination-root-path}"/bcom-pah-report/"  // Should be ended with "/"
     table-name: ${schema}".pah_reports_staging"
     timestamp-column-format:{
       type:"dd/MM/yyyy"
     }
     partition{
       key:["datamonth"]
       value:"partitiondate" // "[-t,..,0,..,+t]" (default is 0 (current date))) or "partitiondate"
     }
     enable-filter: {
         filter-footer-empty-rows: true
       }
     is-skip-checking : "true"
     partition-format: "yyyyMM"
  }

  tiket_pah {
     mapper: "tiket_pah_mapper"
       file:{
         name: ["243996-tiket pay at hotel-{date}"]
         type: "xlsx"
         date-file-name-format: "yyyyMM"
         has-column-header:"true"
         excel-sheet-name: "Booking_List"
       }
     download-path: ${env_path}"/pah-reports/" // Should be ended with "/"
     destination-path: ${destination-root-path}"/tiket-pah-report/"  // Should be ended with "/"
     table-name: ${schema}".pah_reports_staging"
     timestamp-column-format:{
       type:"dd/MM/yyyy"
     }
     partition{
       key:["datamonth"]
       value:"partitiondate" // "[-t,..,0,..,+t]" (default is 0 (current date))) or "partitiondate"
     }
     enable-filter: {
         filter-footer-empty-rows: true
       }
     is-skip-checking : "true"
     partition-format: "yyyyMM"
  }
}



  retries = 3
  retry-seconds = 30
  server = ${server}
  arguments-date-format = "yyyy-MM-dd"
}

hadoop.spark {
  driver {
    memory = "20g"
    memory-overhead = 16384
  }
}

tracking-status {
  table-name: ${schema}".downloader_status"
}