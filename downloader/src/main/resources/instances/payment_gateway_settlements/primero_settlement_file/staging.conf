# Configuration for STAGING environment
include classpath("instances/payment_gateway_settlements/_common_staging.conf")
include "settings.conf"

server = "downloader_server_primero_settlement"
file_type = "primero_settlement_file"

config-downloader {
  downloader_server_primero_settlement_data {
    address: "sftp://sftp.dlocal.com:22"
    proxy: "http://sisproxy.hkg.agoda.local:3128"
    vault-secret-location: "dataproject/hk-it-pymntstlmt-svc/edp/primero_settlement_file"
    look-back-date = 10
  }
}