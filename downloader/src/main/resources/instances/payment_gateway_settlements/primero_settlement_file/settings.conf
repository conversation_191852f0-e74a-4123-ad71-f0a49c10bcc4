instance-name = "primero_settlement_file"
app-name = "PaymentPrimeroSettlementDownloader"

config-downloader {
  downloader_server_primero_settlement_data {
    include "mapping.conf"
    file-list: [${file_type}]

    primero_settlement_file {
      mapper: "primero_settlement_report"
      file: {
        name: ["Primero_Agoda_Settlement_{date}"]
        date-file-name-format: "yyyyMMdd"
        type: "csv"
        delimiter: ","
      }
      save-mode: "overwrite"
      offset-date: -1
      download-path: "/"
      table-name: ${schema}."raw_"${file_type}
      destination-path: ${destination-root-path}"/"${file_type}"/"
      partition {
        key: ["datadate", "file_id"]
        value: "0"
      }
      timestamp-column-format: {
        type: "yyyy-MM-dd'T'HH:mm:ss"
      }
      enable-filter: {
        footer-line-count: 1
      }
    }
  }

  server = "downloader_server_primero_settlement_data"
}
