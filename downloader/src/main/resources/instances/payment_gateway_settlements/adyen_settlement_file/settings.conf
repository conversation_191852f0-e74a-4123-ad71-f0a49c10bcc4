instance-name = "adyen_settlement_file"
app-name = ""
protocol = "https"  # Override default SFTP

config-downloader {
  downloader_server_adyen_settlement_data {
    include "mapping.conf"
    proxy : "http://sisproxy.hkg.agoda.local:3128"

    look-back-date = 7
    generate-uuid-column: true
    file-list: [${file_type}]
    
    adyen_settlement_report {
      mapper: "Adyen_Settlement_Report"
      file: {
        name: ["settlement_detail_report_batch_*_{date}"]
        type: "csv"
        date-file-name-format: "yyyyMMdd"
        offset-date: -1
      }
      http-header: {
        vault-secret-http-auth-header-location: "dataproject/hk-it-pymntstlmt-svc/edp/adyen_settlement_file"
        Authorization: "Basic {token}"
      }
      http-method: "GET"
      delete-file-after-downloaded: "false"
      download-path: "https://ca-live.adyen.com/reports/download/MerchantAccount/"
      url-postfix-for-extract-html-links = ["AgodaCOM", "AgodaUS", "AgodaSG", "AgodaSG_RM", "AgodaUS_RM", "AgodaCOM_FLT", "AgodaUS_FLT","AgodaSG_FLT",
          "AgodaCOM_RM", "AgodaSG_XB", "AgodaHK_XB", "Agoda_PCLN", "AgodaSG_XB_FLT", "AgodaHK_XB_FLT", "AgodaSG_XB_MYR", "AgodaSG_XB_FLT_MYR",
          "AgodaCOM_APMs", "AgodaAU_RM", "AgodaAU", "AgodaMY", "AgodaMY_FLT", "AgodaUS_PCLN", "AgodaHK", "AgodaJP", "AgodaCOM_AP"]
      extract-html-links: "true"
      table-name: ${schema}."raw_"${file_type}
      destination-path: ${destination-root-path}"/"${file_type}"/"
      is-skip-checking: "true"
      partition {
        key: ["datadate", "file_id"]
        value: "0"
      }
      timestamp-column-format: {
        type: "yyyy-MM-dd'T'HH:mm:ss"
      }
    }
  }

  server = "downloader_server_adyen_settlement_data"
}
