instance-name = "alipay_settlement_file"
app-name = "AlipaySettlementDownloader"

config-downloader {
  downloader_server_alipay_settlement {
    include "mapping.conf"
    look-back-date = 1
    file-list: [${file_type}]

    alipay_osp_settlement {
      mapper: "alipay_osp_mapper"
      file: {
        name: ["settlementItems_.*"]
        date-file-name-format: "yyyyMMdd"
        type: "csv"
        delimiter: ","
        offset-date: -1
      }
      save-mode: "overwrite"
      destination-path: ${destination-root-path}"/"${file_type}"/"
      table-name: ${schema}."raw_"${file_type}
      partition {
        key: ["datadate", "file_id"]
        value: "0"
      }
      timestamp-column-format: {
        type: "yyyy-MM-dd'T'HH:mm:ss"
      }
      enable-filter: {
        footer-line-count: 1
      }
      delete-file-after-downloaded: "false"
    }
  }

  server = "downloader_server_alipay_settlement"
}