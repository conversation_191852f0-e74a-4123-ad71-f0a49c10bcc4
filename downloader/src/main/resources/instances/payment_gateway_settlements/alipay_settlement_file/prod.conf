include classpath("instances/payment_gateway_settlements/_common_prod.conf")
include "settings.conf"
# Alipay-specific configurations
server = "downloader_server_alipay_settlement"
file_type = "alipay_osp_settlement"

config-downloader {
  downloader_server_alipay_settlement {
    address: "sftp://sftp.alipay.com:22"
    vault-secret-location: "dataproject/hk-it-pymntstlmt-svc/edp/alipay_settlement_file"
    proxy: "http://sisproxy.hkg.agoda.local:3128"
    
    alipay_osp_settlement {
      download-path: "/v1/settlements/2188120003296895/{date}/"
      dynamic-download-path: "true"
    }

    look-back-date = 1
  }
}