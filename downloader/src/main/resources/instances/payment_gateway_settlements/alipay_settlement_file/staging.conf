# Configuration for STAGING environment
include classpath("instances/payment_gateway_settlements/_common_payment_gateway_settlements_staging.conf")
include "settings.conf"

server = "downloader_server_alipay_settlement"
file_type = "alipay_osp_settlement"

config-downloader {
  downloader_server_alipay_settlement {
    address: "sftp://sftpconnect.agoda.local:22"
    vault-secret-location: "dataproject/hk-it-pymntstlmt-svc/edp/staging/agoda_sftp"

    alipay_osp_settlement {
      download-path: "/upload/"
    }
  }
}

# Alipay-specific interactive slackbot configuration
interactive-slackbot {
    request-group = "Okta-Global_Apps_Admins"
    approve-group = "Okta-Global_Apps_Admins"
    slack-channel = "C08SESS0L59"
}

