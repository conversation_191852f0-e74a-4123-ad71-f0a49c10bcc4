include classpath("instances/payment_gateway_settlements/_common_prod.conf")
include "settings.conf"
# NTT-specific configurations
server = "downloader_server_ntt_settlement_data"
file_type = "ntt_settlement_file"

# Override Hadoop username for NTT
hadoop {
  hive.username = "hk-it-pymntstlmt-svc--dev"
  credentials = "hadoop/hk-it-pymntstlmt-svc--dev/credentials"
}
slack-config.channel-id = "edp-settlement-processing"

config-downloader {
  downloader_server_ntt_settlement_data {
    address: "sftp://sftp-sin-prod.ndhkpay.com:2222"
    proxy: "http://sisproxy.hkg.agoda.local:3128"
    vault-secret-location: "dataproject/hk-it-pymntstlmt-svc/edp/ntt_settlement_file"
    look-back-date = 10
  }
}