instance-name = "ntt_settlement_file"
app-name = "PaymentNttSettlementDownloader"

config-downloader {
  downloader_server_ntt_settlement_data {
    include "mapping.conf"
    file-list: [${file_type}]

    ntt_settlement_file {
      mapper: "ntt_settlement_report"
      file: {
        name: ["Dailyreport{date}"]
        date-file-name-format: "yyyyMMdd"
        type: "csv"
        delimiter: ","
      }
      save-mode: "overwrite"
      offset-date: -1
      download-path: "/agodarpt/"
      table-name: ${schema}."raw_"${file_type}
      destination-path: ${destination-root-path}"/"${file_type}"/"
      partition {
        key: ["datadate", "file_id"]
        value: "0"
      }
      timestamp-column-format: {
        type: "yyyy-MM-dd'T'HH:mm:ss"
      }
      enable-filter: {
        footer-line-count: 1
      }
    }
  }

  server = "downloader_server_ntt_settlement_data"
}
