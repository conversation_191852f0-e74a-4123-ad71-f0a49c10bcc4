# Configuration for STAGING environment
include classpath("instances/payment_gateway_settlements/_common_staging.conf")
include "settings.conf"

server = "downloader_server_ntt_settlement_data"
file_type = "ntt_settlement_file"

config-downloader {
  downloader_server_ntt_settlement_data {
    address: "sftp://sftp-sin-prod.ndhkpay.com:2222"
    proxy: "http://sisproxy.hkg.agoda.local:3128"
    vault-secret-location: "dataproject/hk-it-pymntstlmt-svc/edp/ntt_settlement_file"
    look-back-date = 10
  }
}