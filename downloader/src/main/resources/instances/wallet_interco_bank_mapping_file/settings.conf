instance-name = "wallet_interco_bank_mapping_file"

app-name = "WalletIntercoBankMappingSFTPDownloader"
instance-type = "individual"
protocol = "sftp"
config-downloader {
  team-owner: "@team-fintech-wallet"
  job-frequency: Daily
  monitoring: {
    status-verification: {
      check-ready-state: true
      check-download-state: true
    }
    slack: {
      enable-status-verification: true
      enable-check-historical-files: false
    }
  }
  downloader_server_wallet_interco_bank_mapping: {
    include "mapping.conf"

    vault-secret-location: "dataproject/hk-fin-wal-svc/downloader/agoda_sftp_oracle_wallet"
    generate-uuid-column: true
    look-back-date = 7
    address: "sftp://sftpconnect.agoda.local:22"
    file-list : ["wallet_interco_bank_mapping_uk_daily_file"]

    wallet_interco_bank_mapping_uk_daily_file {
      mapper: "Map_Wallet_Interco_Bank_File"
      file:{
        name: ["interco_refund_bank_uk_mapping_{date}"]
        type: "csv"
        date-file-name-format: "yyyyMMdd"
        offset-date: 0
      }
      download-path: "/OracleWallet/Outbound/IntercoRefundMapping/UK/" // Should end with "/"
      destination-path: ${destination-root-path}"/Wallet_Interco_Bank_UK_Mapping_Daily_File/"  // Should be ended with "/"
      table-name: ${schema}".wallet_interco_bank_mapping"
      partition{
        key:["datadate", "business_unit"]
        value:"0" // "[-t,..,0,..,+t]" (default is 0 (current date))) or "partitiondate"
      }
      timestamp-column-format:{
        type: "dd-MM-yyyy HH:mm:ss"
      }
    }
  }

  retries = 3
  retry-seconds = 30
  server = ${server}
  arguments-date-format = "yyyy-MM-dd"
}

tracking-status{
  table-name: ${schema}".wallet_interco_bank_mapping_downloader_status"
}

result {
  mail {
    alert-sender = "<EMAIL> (Downloader)"
    alert-recipients = "<EMAIL>"
  }
}

ag-vault {
  http {
    url = "https://hk.qa-vault.agoda.local:8200"
    auth {
      method = "approle"
    }
  }
}

