mappers: {
  Map_Wallet_Interco_Bank_File: [
    {name: "payment_id",                      dataType: "StringType",                               isNullable: true,      alias: "payment_id"},
    {name: "business_unit",                   dataType: "StringType",                               isNullable: true,      alias: "business_unit"},
    {name: "payment_status",                  dataType: "StringType",                               isNullable: true,      alias: "payment_status"},
    {name: "source_label",                    dataType: "StringType",                               isNullable: true,      alias: "source_label"},
    {name: "payment_number",                  dataType: "StringType",                               isNullable: true,      alias: "payment_number"},
    {name: "amount",                          dataType: "DecimalType", precision: 24, scale: 8,     isNullable: true,      alias: "amount"},
    {name: "currency",                        dataType: "StringType",                               isNullable: true,      alias: "currency"},
    {name: "creation_time",                   dataType: "TimestampType",                            isNullable: true,      alias: "creation_time"},
    {name: "transmitted_time",                dataType: "TimestampType",                            isNullable: true,      alias: "transmitted_time"},
    {name: "last_update_time",                dataType: "TimestampType",                            isNullable: true,      alias: "last_update_time"},
    {name: "key",                             dataType: "StringType",                               isNullable: true,      alias: "key"}
  ]
}
