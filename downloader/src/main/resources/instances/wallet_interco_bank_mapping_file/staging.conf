include classpath("_generic_staging.conf")
include "settings.conf"

schema="finance_wallet_staging"
current_env="staging"
app.adpMessaging.appName: "downloader.staging"

server = "downloader_server_wallet_interco_bank_mapping"

config-downloader.downloader_server_wallet_interco_bank_mapping.wallet_interco_bank_mapping_uk_daily_file.download-path = "/OracleWallet/Outbound/IntercoRefundMapping/UK/dev/"

slack-config.channel-id = "o_fintech-wallet-dev"

hadoop {
   hdfs.user = "hk-fin-wal-svc--dev"
   knox.enabled = true
   hive.username = "hk-fin-wal-svc--dev"
   credentials = "hadoop/hk-fin-wal-svc--dev/credentials"
   credentials_from_env = true
   use-smart-table-loader = true
}

result {
  mail {
    alert-sender = "<EMAIL> (Downloader)"
    alert-recipients = ""
  }
}

interactive-slackbot {
    request-group = "Okta-Global_Apps_Admins"
    approve-group = "Okta-Global_Apps_Admins"
    slack-channel = "C07HQNRL16C" # FINWAL-507 to be change later
}