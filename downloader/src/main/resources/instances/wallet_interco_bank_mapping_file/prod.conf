include classpath("_generic_prod.conf")
include "settings.conf"

schema="finance_wallet"
current_env="prod"
app.adpMessaging.appName: "downloader.prod"
server = "downloader_server_wallet_interco_bank_mapping"

config-downloader.downloader_server_wallet_interco_bank_mapping.wallet_interco_bank_mapping_uk_daily_file.download-path = "/OracleWallet/Outbound/IntercoRefundMapping/UK/prod/"

slack-config.channel-id = "o_fintech-wallet"

hadoop {
   hdfs.user = "hk-fin-wal-svc"
   knox.enabled = true
   hive.username = "hk-fin-wal-svc"
   credentials = "hadoop/hk-fin-wal-svc/credentials"
}

interactive-slackbot {
    request-group = "finance-backoffice-wallet"
    approve-group = "finance-backoffice-wallet"
    slack-channel = "C07HQNRL16C" // fintech-wallet
}

ag-vault {
  http {
    url = "https://hk.vault.agoda.local:8200"
    auth {
      method = "approle"
    }
  }
}