include classpath("_generic_staging.conf")
include "settings.conf"


schema="finance_wallet_uat"
current_env="uat"
server = "downloader_server_wallet_wise"
app.adpMessaging.appName: "downloader.uat"

hadoop.spark.dependency-files = ["hdfs:///user/"${hadoop.hdfs.user}/apps/${project-name}"/wallet_wise/config/log4j_adp.properties"]

slack-config.channel-id = "o_fintech-wallet-uat"

hadoop {
  hdfs.user = "hk-fin-wal-svc"
  knox.enabled = true
  hive.username = "hk-fin-wal-svc"
  credentials = "hadoop/hk-fin-wal-svc/credentials"
  credentials_from_env = true
  use-smart-table-loader = true

}

config-downloader {
  downloader_server_wallet_wise {
    wallet_uk_wise {
      http-header: {
        vault-secret-http-auth-header-location: "cashout-api/psp/wise/bhfsGBP-sandbox"
        Authorization: "Bearer {token}"
      }
      download-path: "https://api.sandbox.transferwise.tech/v1/profiles/28721608/balance-statements/260825/statement.json"
    }
  }
}

result {
  mail {
    alert-sender = "<EMAIL> (Downloader)"
    alert-recipients = "<EMAIL>"
  }
}

interactive-slackbot {
  request-group = "Okta-Global_Apps_Admins"
  approve-group = "Okta-Global_Apps_Admins"
  slack-channel = "C07HQNRL16C" # FINWAL-507 to be change later
}

ag-vault {
  http {
    url = "https://hk.vault.agoda.local:8200"
    auth {
      method = "approle"
    }
  }
}

