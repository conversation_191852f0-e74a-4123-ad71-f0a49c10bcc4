include classpath("_generic_staging.conf")
include "settings.conf"


schema="finance_wallet_staging"
current_env="qa"
server = "downloader_server_wallet_wise"
app.adpMessaging.appName: "downloader.staging"

hadoop.spark.dependency-files = ["hdfs:///user/"${hadoop.hdfs.user}/apps/${project-name}"/wallet_wise/config/log4j_adp.properties"]

slack-config.channel-id = "o_fintech-wallet-dev"

hadoop {
   hdfs.user = "hk-fin-wal-svc--dev"
   knox.enabled = true
   hive.username = "hk-fin-wal-svc--dev"
   credentials = "hadoop/hk-fin-wal-svc--dev/credentials"
   credentials_from_env = true
   use-smart-table-loader = true

}


result {
  mail {
    alert-sender = "<EMAIL> (Downloader)"
    alert-recipients = "<EMAIL>"
  }
}

interactive-slackbot {
    request-group = "Okta-Global_Apps_Admins"
    approve-group = "Okta-Global_Apps_Admins"
    slack-channel = "C07HQNRL16C" # FINWAL-507 to be change later
}

ag-vault {
  http {
    url = "https://hk.qa-vault.agoda.local:8200"
    auth {
      method = "approle"
    }
  }
}

