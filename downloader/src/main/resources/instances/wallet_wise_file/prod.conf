include classpath("_generic_prod.conf")
include "settings.conf"


schema="finance_wallet"
current_env="prod"
server = "downloader_server_wallet_wise"
app.adpMessaging.appName: "downloader.prod"

slack-config.channel-id = "o_fintech-wallet"

hadoop {
   hdfs.user = "hk-fin-wal-svc"
   knox.enabled = true
   hive.username = "hk-fin-wal-svc"
   credentials = "hadoop/hk-fin-wal-svc/credentials"
}


interactive-slackbot {
    request-group = "finance-backoffice-wallet"
    approve-group = "finance-backoffice-wallet"
    slack-channel = "C07HQNRL16C" // fintech-wallet
}

ag-vault {
  http {
    url = "https://hk.qa-vault.agoda.local:8200" // TODO update url once credentials are available in prod
    auth {
      method = "approle"
    }
  }
}
