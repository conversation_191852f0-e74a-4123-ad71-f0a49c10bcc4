mappers: {
  Map_MarriottSettlement_File: [
        {name: "PaidIATA", dataType: "IntegerType", isNullable: false, alias: "paid_iata", beginIndex: 0, endIndex: 7 },
        {name: "StatementDate", dataType: "TimestampType", isNullable: false, alias: "statement_date", beginIndex: 8, endIndex: 17 },
        {name: "RecordType", dataType: "StringType", isNullable: false, alias: "record_type", beginIndex: 18, endIndex: 18 },
        {name: "BookingIATA", dataType: "IntegerType", isNullable: false, alias: "booking_iata", beginIndex: 19, endIndex: 26 },
        {name: "PropCode", dataType: "StringType", isNullable: false, alias: "prop_code", beginIndex: 27, endIndex: 31 },
        {name: "PropName", dataType: "StringType", isNullable: false, alias: "prop_name", beginIndex: 32, endIndex: 61 },
        {name: "City", dataType: "StringType", isNullable: true, alias: "city", beginIndex: 62, endIndex: 79 },
        {name: "State", dataType: "StringType", isNullable: true, alias: "state", beginIndex: 80, endIndex: 81 },
        {name: "Brand", dataType: "StringType", isNullable: false, alias: "brand", beginIndex: 82, endIndex: 83 },
        {name: "CountryCode", dataType: "StringType", isNullable: false, alias: "country_code", beginIndex: 84, endIndex: 85 },
        {name: "CurrencyCode", dataType: "StringType", isNullable: true, alias: "currency_code", beginIndex: 86, endIndex: 88 },
        {name: "GuestName", dataType: "StringType", isNullable: false, alias: "guest_name", beginIndex: 89, endIndex: 100, dropColumn: true },
        {name: "ConfoNumber", dataType: "StringType", isNullable: true, alias: "confo_number", beginIndex: 101, endIndex: 110 },
        {name: "ArrivalDate", dataType: "TimestampType", isNullable: false, alias: "arrival_date", beginIndex: 111, endIndex: 120 },
        {name: "CommRateNum", dataType: "DecimalType", precision: 5, scale: 2, isNullable: false, alias: "comm_rate_num", beginIndex: 121, endIndex: 127 },
        {name: "RnNumber", dataType: "IntegerType", isNullable: false, alias: "rn_number", beginIndex: 128, endIndex: 132 },
        {name: "RoomRateCTACNum", dataType: "DecimalType", precision: 15, scale: 5, isNullable: false, alias: "room_rate_ctac_num", beginIndex: 133, endIndex: 149 },
        {name: "CommRevCTACNum", dataType: "DecimalType", precision: 15, scale: 5, isNullable: false, alias: "comm_rev_ctac_num", beginIndex: 150, endIndex: 166 },
        {name: "CommAmtCTACNum", dataType: "DecimalType", precision: 15, scale: 5, isNullable: false, alias: "comm_amt_ctac_num", beginIndex: 167, endIndex: 183 },
        {name: "TaxAmtCTACNum", dataType: "DecimalType", precision: 15, scale: 5, isNullable: false, alias: "tax_amt_ctac_num", beginIndex: 184, endIndex: 200 },
        {name: "RoomRateUSDNum", dataType: "DecimalType", precision: 7, scale: 2, isNullable: false, alias: "room_rate_usd_num", beginIndex: 201, endIndex: 209 },
        {name: "CommRevUSDNum", dataType: "DecimalType", precision: 11, scale: 2, isNullable: false, alias: "comm_rev_usd_num", beginIndex: 210, endIndex: 222 },
        {name: "CommAmtUSDNum", dataType: "DecimalType", precision: 7, scale: 2, isNullable: false, alias: "comm_amt_usd_num", beginIndex: 223, endIndex: 231 },
        {name: "TaxAmtUSDNum", dataType: "DecimalType", precision: 7, scale: 2, isNullable: false, alias: "tax_amt_usd_num", beginIndex: 232, endIndex: 240 },
        {name: "TAPreferredCurr", dataType: "StringType", isNullable: true, alias: "ta_preferred_curr", beginIndex: 241, endIndex: 243 },
        {name: "TAPrefCurrDesc", dataType: "StringType", isNullable: true, alias: "ta_pref_curr_desc", beginIndex: 244, endIndex: 257 },
        {name: "CommAmtPayNum", dataType: "DecimalType", precision: 15, scale: 5, isNullable: false, alias: "comm_amt_pay_num", beginIndex: 258, endIndex: 280 },
        {name: "TaxAmtPayNum", dataType: "DecimalType", precision: 15, scale: 5, isNullable: false, alias: "tax_amt_pay_num", beginIndex: 281, endIndex: 297 },
        {name: "CheckNumber", dataType: "StringType", isNullable: true, alias: "check_number", beginIndex: 298, endIndex: 308 },
        {name: "CheckDate", dataType: "TimestampType", isNullable: true, alias: "check_date", beginIndex: 309, endIndex: 323 },
        {name: "ActionCodeDesc", dataType: "DecimalType", precision: 15, scale: 5, isNullable: false, alias: "action_code_desc", beginIndex: 324, endIndex: 339 },
        {name: "ResReasonCode", dataType: "StringType", isNullable: true, alias: "res_reason_code", beginIndex: 340, endIndex: 343 },
        {name: "ResReasonCodeDesc", dataType: "StringType", isNullable: true, alias: "res_reason_code_desc", beginIndex: 344, endIndex: 427 },
        {name: "PropPhone", dataType: "StringType", isNullable: false, alias: "prop_phone", beginIndex: 428, endIndex: 452 },
        {name: "Filler", dataType: "StringType", isNullable: false, alias: "filler", beginIndex: 453, endIndex: 453 }
  ]
}
