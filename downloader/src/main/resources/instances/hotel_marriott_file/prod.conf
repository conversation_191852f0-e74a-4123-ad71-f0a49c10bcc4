include classpath("_generic_prod.conf")
include "settings.conf"

coordinator.freq = "0 07 *"

schema="finance_sftp_downloader"
current_env="prod"

server = "downloader_server_sftp_agoda_local"
app.adpMessaging.appName: "downloader.prod"

slack-config.channel-id = "fintech-ap-recon"

hadoop {
  hdfs.user = "hk-fin-supl-prod-svc"
  knox.enabled = true
  hive.username = "hk-fin-supl-prod-svc"
  credentials = "hadoop/hk-fin-supl-prod-svc/credentials"
}

config-downloader.downloader_server_sftp_agoda_local.marriott_settlement_file.download-path = "/upload/"

interactive-slackbot {
    request-group = "finance-backoffice-blackline,finance-backoffice-funnels"
    approve-group = "finance-backoffice-funnels"
    slack-channel = "CEBHXHYAK" // it-morpheus-blackline
}
