instance-name = "settlement_tutuka_file"

app-name = "TutukaSettlementFileDownloader"
instance-type = "individual"
protocol = "sftp"
output-table = "tutuka_settlement"

config-downloader {
  monitoring: {
    status-verification: {
      check-ready-state: true
      check-download-state: true
    }
    slack: {
      enable-status-verification: true
      enable-check-historical-files: false
    }
  }
  downloader_server {
    include "mapping.conf"
    address: "sftp://sftp.voucherengine.com:2122"
    proxy: "http://sisproxy.hkg.agoda.local:3128"
    vault-secret-location: "it-payout/sftp/vcard"
    vault-fallback-location: "paymentology"
    look-back-date = 0

    file-list: []
  }

  retries = 3
  retry-seconds = 15
  server = ${server}
  arguments-date-format = "yyyy-MM-dd"
}

tracking-status {
  table-name: ${schema}".tutuka_settlement_status"
  save-mode: "append"
  partition-keys: ["datadate", "instance_name", "file_name"]
  write-retry-attempts: 3
}

result {
  mail {
    alert-sender = "<EMAIL> (Downloader)"
    alert-recipients = "<EMAIL>,<EMAIL>"
  }
}

deploy {
  target-folder = /user/${hadoop.hdfs.user}/apps/${project-name}/_deploy/${current_env}
}