mappers: {
  Map_Hotel_Hyatt_Onyx_File: [
    {name: "Version",                       dataType: "StringType",         isNullable: false,      alias: "version"},
    {name: "PaymentCycleID",                dataType: "IntegerType",        isNullable: false,      alias: "payment_cycle_id"},
    {name: "PaymentCycleDate",              dataType: "StringType",         isNullable: false,      alias: "payment_cycle_date"},
    {name: "UniqueBookingID",               dataType: "IntegerType",        isNullable: false,      alias: "unique_booking_id"},
    {name: "RecoverProTransactionID",       dataType: "IntegerType",        isNullable: false,      alias: "recover_pro_transaction_id"},
    {name: "RecoverProContractID",          dataType: "IntegerType",        isNullable: false,      alias: "recover_pro_contract_id"},
    {name: "RecoverProContractName",        dataType: "StringType",         isNullable: false,      alias: "recover_pro_contract_name"},
    {name: "RecoverProPaypointID",          dataType: "IntegerType",        isNullable: false,      alias: "recover_pro_paypoint_id"},
    {name: "RecoverProPaypointName",        dataType: "StringType",         isNullable: false,      alias: "recover_pro_paypoint_name"},
    {name: "PNR",                           dataType: "StringType",         isNullable: false,      alias: "pnr"},
    {name: "LineNo",                        dataType: "IntegerType",        isNullable: false,      alias: "line_no"},
    {name: "BookingConfirmationNo",         dataType: "StringType",         isNullable: false,      alias: "booking_confirmation_no"},
    {name: "BookingCorporateID",            dataType: "StringType",         isNullable: true,       alias: "booking_corporate_id"},
    {name: "BookingRateCode",               dataType: "StringType",         isNullable: true,       alias: "booking_rate_code"},
    {name: "BookingAccommodationType",      dataType: "StringType",         isNullable: true,       alias: "booking_accommodation_type"},
    {name: "BookingSegmentType",            dataType: "StringType",         isNullable: true,       alias: "booking_segment_type"},
    {name: "BookingStatusCode",             dataType: "StringType",         isNullable: false,      alias: "booking_status_code"},
    {name: "BookingCreateDate",             dataType: "StringType",         isNullable: false,      alias: "booking_create_date"},
    {name: "BookingModifyDate",             dataType: "StringType",         isNullable: true,       alias: "booking_modify_date"},
    {name: "AgentID",                       dataType: "StringType",         isNullable: false,      alias: "agent_id"},
    {name: "AgentOfficeID",                 dataType: "StringType",         isNullable: true,       alias: "agent_office_id"},
    {name: "AgentName",                     dataType: "StringType",         isNullable: false,      alias: "agent_name"},
    {name: "AgentRepresentativeInitials",   dataType: "StringType",         isNullable: false,      alias: "agent_representative_initials"},
    {name: "AgentRef1",                     dataType: "StringType",         isNullable: true,       alias: "agent_ref_1"},
    {name: "AgentRef2",                     dataType: "StringType",         isNullable: true,       alias: "agent_ref_2"},
    {name: "AgentRef3",                     dataType: "StringType",         isNullable: true,       alias: "agent_ref_3"},
    {name: "BookingNoOfRooms",              dataType: "IntegerType",        isNullable: false,      alias: "booking_no_of_rooms"},
    {name: "BookingNoOfNights",             dataType: "IntegerType",        isNullable: false,      alias: "booking_no_of_nights"},
    {name: "BookingDateIn",                 dataType: "StringType",         isNullable: false,      alias: "booking_date_in"},
    {name: "BookingDateOut",                dataType: "StringType",         isNullable: false,      alias: "booking_date_out"},
    {name: "BookingCurrency",               dataType: "StringType",         isNullable: false,      alias: "booking_currency"},
    {name: "BookingCostPrNight",            dataType: "DecimalType",  precision:18,  scale:2, isNullable: false,  alias: "booking_cost_per_night"},
    {name: "BookingCommissionPercent",      dataType: "DecimalType",  precision:18,  scale:2, isNullable: false,  alias: "booking_commission_percent"},
    {name: "RecoverProHotelAccountID",      dataType: "IntegerType",        isNullable: false,      alias: "recover_pro_hotel_account_id"},
    {name: "HotelPropertyID",               dataType: "StringType",         isNullable: false,      alias: "hotel_property_id"},
    {name: "HotelGDSChainCode",             dataType: "StringType",         isNullable: false,      alias: "hotel_gds_chain_code"},
    {name: "HotelName",                     dataType: "StringType",         isNullable: false,      alias: "hotel_name"},
    {name: "HotelAddress1",                 dataType: "StringType",         isNullable: false,      alias: "hotel_address_1"},
    {name: "HotelAddress2",                 dataType: "StringType",         isNullable: true,       alias: "hotel_address_2"},
    {name: "HotelZip",                      dataType: "StringType",         isNullable: false,      alias: "hotel_zip"},
    {name: "HotelCity",                     dataType: "StringType",         isNullable: false,      alias: "hotel_city"},
    {name: "HotelState",                    dataType: "StringType",         isNullable: false,      alias: "hotel_state"},
    {name: "HotelCountryCode",              dataType: "StringType",         isNullable: false,      alias: "hotel_country_code"},
    {name: "HotelAirportCityCode",          dataType: "StringType",         isNullable: false,      alias: "hotel_airport_city_code"},
    {name: "HotelPhone",                    dataType: "StringType",         isNullable: false,      alias: "hotel_phone"},
    {name: "HotelFax",                      dataType: "StringType",         isNullable: false,      alias: "hotel_fax"},
    {name: "HotelVATID",                    dataType: "StringType",         isNullable: false,      alias: "hotel_vat_id"},
    {name: "HotelLegalCountryCode",         dataType: "StringType",         isNullable: false,      alias: "hotel_legal_country_code"},
    {name: "HotelAddedTransaction",         dataType: "StringType",         isNullable: true,       alias: "hotel_added_transaction"},
    {name: "ConfConfirmationNo",            dataType: "StringType",         isNullable: false,      alias: "conf_confirmation_no"},
    {name: "ConfNoOfRooms",                 dataType: "IntegerType",        isNullable: false,      alias: "conf_no_of_rooms"},
    {name: "ConfNoOfNights",                dataType: "IntegerType",        isNullable: false,      alias: "conf_no_of_nights"},
    {name: "ConfDateIn",                    dataType: "StringType",         isNullable: false,      alias: "conf_date_in"},
    {name: "ConfDateOut",                   dataType: "StringType",         isNullable: false,      alias: "conf_date_out"},
    {name: "ConfCommissionPercent",         dataType: "DecimalType",  precision:18,  scale:2, isNullable: false,  alias: "conf_commission_percent"},
    {name: "ConfCostPrNight",               dataType: "DecimalType",  precision:18,  scale:2, isNullable: false,      alias: "conf_cost_pr_night"},
    {name: "ConfCurrency",                  dataType: "StringType",         isNullable: false,      alias: "conf_currency"},
    {name: "PaidCurrency",                  dataType: "StringType",         isNullable: false,      alias: "paid_currency"},
    {name: "PaidCommission",                dataType: "DecimalType",  precision:18,  scale:2, isNullable: false,  alias: "paid_commission"},
    {name: "PaymentSource",                 dataType: "StringType",         isNullable: false,      alias: "payment_source"},
    {name: "CommissionVATAmountTotal",      dataType: "DecimalType",  precision:18,  scale:2, isNullable: false, alias: "commission_vat_amount_total"},
    {name: "CommissionVATAmountSub1",       dataType: "DecimalType",  precision:18,  scale:2, isNullable: false, alias: "commission_vat_amount_sub_1"},
    {name: "CommissionVATAmountSub2",       dataType: "DecimalType",  precision:18,  scale:2, isNullable: true,  alias: "commission_vat_amount_sub_2"},
    {name: "VATCurrency",                   dataType: "StringType",         isNullable: false,      alias: "vat_currency"},
    {name: "VATRateTotal",                  dataType: "DecimalType",  precision:18,  scale:2, isNullable: false,  alias: "vat_rate_total"},
    {name: "VATRateSub1",                   dataType: "DecimalType",  precision:18,  scale:2, isNullable: false,  alias: "vat_rate_sub_1"},
    {name: "VATRateSub2",                   dataType: "DecimalType",  precision:18,  scale:2, isNullable: true,   alias: "vat_rate_sub_2"},
    {name: "PaymentStatusCode",             dataType: "StringType",         isNullable: false,      alias: "payment_status_code"},
    {name: "PaymentDate",                   dataType: "StringType",         isNullable: false,      alias: "payment_date"},
    {name: "PaymentReference",              dataType: "StringType",         isNullable: false,      alias: "payment_reference"},
    {name: "InvoiceNumber",                 dataType: "StringType",         isNullable: true,       alias: "invoice_number"},
    {name: "InvoiceIssueDate",              dataType: "StringType",         isNullable: true,       alias: "invoice_issue_date"},
    {name: "RecoverProFee",                 dataType: "DecimalType",  precision:18,  scale:2, isNullable: false,  alias: "recover_pro_fee"},
    {name: "RecoverProFeeCurrency",         dataType: "StringType",         isNullable: false,      alias: "recover_pro_fee_currency"},
  ]
}
