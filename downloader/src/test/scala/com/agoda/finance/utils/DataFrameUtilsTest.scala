package com.agoda.finance.utils

import com.agoda.finance.config.protocol.SFTPDownloadConfigModule
import com.agoda.finance.constant.ProcessMode
import com.agoda.finance.model._
import com.agoda.finance.utils.DataFrameUtils.createHashingAndLineDF
import com.agoda.ml.spark.DataFrameMatchers.beEqualTo
import com.agoda.ml.spark.SparkSharedLocalTest
import com.typesafe.config.{Config, ConfigFactory}
import org.apache.hadoop.fs.Path
import org.apache.spark.sql.functions.sum
import org.apache.spark.sql.types.StringType
import org.joda.time.DateTime
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar.mock
import org.scalatest.{FunSuite, Matchers}

import java.io.ByteArrayInputStream
import java.nio.file.Paths

class DataFrameUtilsTest extends FunSuite with SparkSharedLocalTest with Matchers {
  import sqlContext.implicits._

  private val currentAbsPath = Paths.get(".").toAbsolutePath.toString
  private val currentPath    = currentAbsPath.substring(0, currentAbsPath.length - 2)
  private val projectPath = currentPath.indexOf("downloader") match {
    case -1 => currentPath + "/downloader"
    case _  => currentPath
  }
  private val testDataPath = s"$projectPath/src/test/resources/test-data"

  test("Fix line breaking -- Incomplete line") {
    val input  = Seq("col1, col2, \"col3 -- long", " description\", col4")
    val output = Seq("col1, col2, \"col3 -- long description\", col4")
    val actual = DataFrameUtils.fixBreakingLine(input)

    actual should equal(output)
  }

  test("Fix line breaking -- Complete line") {
    val input  = Seq("\"col1\", \"col2\", \"col3\"", "\"col1\", \"col2\", \"col3\", \"col4\"")
    val output = Seq("\"col1\", \"col2\", \"col3\"", "\"col1\", \"col2\", \"col3\", \"col4\"")
    val actual = DataFrameUtils.fixBreakingLine(input)

    actual should equal(output)
  }

  test("Fix line breaking -- Both complete and incomplete") {
    val input = Seq(
      "\"col1\", \"col2\", \"col3\", \"col4\"",
      "col1, col2, \"col3 -- long",
      " description\", col4",
      "\"col1\", \"col2\", \"col3\", \"col4\""
    )
    val output = Seq(
      "\"col1\", \"col2\", \"col3\", \"col4\"",
      "col1, col2, \"col3 -- long description\", col4",
      "\"col1\", \"col2\", \"col3\", \"col4\""
    )
    val actual = DataFrameUtils.fixBreakingLine(input)
    actual should equal(output)
  }

  test("Fix line breaking -- 3 lines") {
    val input  = Seq("col1, col2, \"col3 -- very", " long", " description\", col4")
    val output = Seq("col1, col2, \"col3 -- very long description\", col4")
    val actual = DataFrameUtils.fixBreakingLine(input)
    actual should equal(output)
  }
  test("Convert flat file to DataFrame") {

    val path = new Path(suitePath, "import_file.csv")
    val data =
      """HEADER-should-be-filtered
        |0123456789ABCDE
        |aaa123 B00152.9
        |bbb123 B00152.9
        |ccc123 B00152.9
        |ddd123AB00152.9
        |FOOTER-should-be-filtered
        |""".stripMargin

    fs.uploadStream(path, new ByteArrayInputStream(data.getBytes))

    import sqlContext.implicits._
    val expected = Seq(
      ("012", "67", "89ABCDE", 2L, "dde677e94792ee98d99dea11316e9dbbb7953a33"),
      ("aaa", " B", "00152.9", 3L, "ed76e635feca8febc9cbd3f1fb9e4b5dae9c3620"),
      ("bbb", " B", "00152.9", 4L, "c50befba68ed1a9ade08883bc03b7d5f86a8c26c"),
      ("ccc", " B", "00152.9", 5L, "d9b6491007f7d3b7853ab2f187bf3dab7d014719"),
      ("ddd", "AB", "00152.9", 6L, "5079175bcc280bd871ca06ea649b6405a6b6099d")
    ).toDF("a", "c", "d", "line_number", "hash_key")

    val mapping = List(
      MappingConfiguration(
        MappingField("a", StringType, nullable = false, "A"),
        Some(FieldSpecification(fixedColumn = Some(FixedColumnSpecification(0, 2))))
      ),
      MappingConfiguration(
        MappingField("b", StringType, nullable = false, "B"),
        Some(FieldSpecification(fixedColumn = Some(FixedColumnSpecification(3, 5)), dropColumn = true))
      ),
      MappingConfiguration(
        MappingField("c", StringType, nullable = false, "C"),
        Some(FieldSpecification(fixedColumn = Some(FixedColumnSpecification(6, 7))))
      ),
      MappingConfiguration(
        MappingField("d", StringType, nullable = false, "D"),
        Some(FieldSpecification(fixedColumn = Some(FixedColumnSpecification(8, 14))))
      )
    )
    val result = DataFrameUtils.flatToDF(path.toString, mapping, Some(1), Some(1))
    result.df should beEqualTo(expected)
  }

  test("Vietjet Convert Excel to DF - with configuration") {
    val VaultCredentialConnector: VaultSecretProvider = mock[VaultSecretProvider]
    val VaultDecryptionHelper: VaultDecryptProvider   = mock[VaultDecryptProvider]
    when(VaultCredentialConnector.getUsername()).thenReturn("")
    when(VaultCredentialConnector.getPassword()).thenReturn("")
    when(VaultCredentialConnector.getPrivateKey()).thenReturn("")

    when(VaultDecryptionHelper.getPassPhrase()).thenReturn("")
    when(VaultDecryptionHelper.getPrivateKey()).thenReturn("")
    when(VaultDecryptionHelper.getPublicKey()).thenReturn("")
    val rootConfig: Config = ConfigFactory.load()
    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(
        DateTime.now.minusDays(1).toString("yyyy-MM-dd"),
        "",
        "",
        "",
        ProcessMode.prod
      )
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      "Vietjet",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )
    val filePath = s"$testDataPath/vietjet_daily_file_20221218_20221218.xls"
    val result   = DataFrameUtils.excelToDF(filePath, sftpConfig)
    val actual   = result.df.agg(sum("Payment Amount")).first.getDouble(0)
    actual.round shouldEqual 42054.65.round
    result.df.count() shouldEqual 615
  }

  test("Gofirst Convert Excel to DF - with configuration") {
    val VaultCredentialConnector: VaultSecretProvider = mock[VaultSecretProvider]
    val VaultDecryptionHelper: VaultDecryptProvider   = mock[VaultDecryptProvider]
    when(VaultCredentialConnector.getUsername()).thenReturn("")
    when(VaultCredentialConnector.getPassword()).thenReturn("")
    when(VaultCredentialConnector.getPrivateKey()).thenReturn("")

    when(VaultDecryptionHelper.getPassPhrase()).thenReturn("")
    when(VaultDecryptionHelper.getPrivateKey()).thenReturn("")
    when(VaultDecryptionHelper.getPublicKey()).thenReturn("")
    val rootConfig: Config = ConfigFactory.load()
    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(
        DateTime.now.minusDays(1).toString("yyyy-MM-dd"),
        "",
        "",
        "",
        ProcessMode.prod
      )
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      "Gofirst",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )
    val filePath = s"$testDataPath/gofirst_daily_file_20221211_20221211.xlsx"
    val result   = DataFrameUtils.excelToDF(filePath, sftpConfig)
    val actual   = result.df.agg(sum("BookingAmount")).first.getDouble(0)
    actual.round shouldEqual 46977
    result.df.count() shouldEqual 10
  }

  test("Indigo Convert Excel to DF - with configuration") {
    val VaultCredentialConnector: VaultSecretProvider = mock[VaultSecretProvider]
    val VaultDecryptionHelper: VaultDecryptProvider   = mock[VaultDecryptProvider]
    when(VaultCredentialConnector.getUsername()).thenReturn("")
    when(VaultCredentialConnector.getPassword()).thenReturn("")
    when(VaultCredentialConnector.getPrivateKey()).thenReturn("")

    when(VaultDecryptionHelper.getPassPhrase()).thenReturn("")
    when(VaultDecryptionHelper.getPrivateKey()).thenReturn("")
    when(VaultDecryptionHelper.getPublicKey()).thenReturn("")
    val rootConfig: Config = ConfigFactory.load()
    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(
        DateTime.now.minusDays(1).toString("yyyy-MM-dd"),
        "",
        "",
        "",
        ProcessMode.prod
      )
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      "Indigo",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )
    val filePath = s"$testDataPath/DividePNR_FUSOTI67_15Jan2023_06Mar2023_20230308115017.xlsx"
    val result   = DataFrameUtils.excelToDF(filePath, sftpConfig)
    val actual   = result.df.agg(sum("PaymentAmount")).first.getDouble(0)
    actual.round shouldEqual 288901
    result.df.count() shouldEqual 72
  }

  test("ThaiVietjet Convert Excel to DF - with configuration") {
    val VaultCredentialConnector: VaultSecretProvider = mock[VaultSecretProvider]
    val VaultDecryptionHelper: VaultDecryptProvider   = mock[VaultDecryptProvider]
    when(VaultCredentialConnector.getUsername()).thenReturn("")
    when(VaultCredentialConnector.getPassword()).thenReturn("")
    when(VaultCredentialConnector.getPrivateKey()).thenReturn("")

    when(VaultDecryptionHelper.getPassPhrase()).thenReturn("")
    when(VaultDecryptionHelper.getPrivateKey()).thenReturn("")
    when(VaultDecryptionHelper.getPublicKey()).thenReturn("")
    val rootConfig: Config = ConfigFactory.load()
    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(
        DateTime.now.minusDays(1).toString("yyyy-MM-dd"),
        "",
        "",
        "",
        ProcessMode.prod
      )
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      "ThaiVietjet",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )
    val filePath = s"$testDataPath/thaivietjet_daily_file_20230613_20230613.xls"
    val result   = DataFrameUtils.excelToDF(filePath, sftpConfig)
    val actual   = result.df.agg(sum("Payment Amount")).first.getDouble(0)
    actual.round shouldEqual 132004
    result.df.count() shouldEqual 102
  }

  test("Convert XMLSpreadsheet to DF - simple case no null") {
    val currentAbsPath = Paths.get(".").toAbsolutePath.toString
    val currentPath    = currentAbsPath.substring(0, currentAbsPath.length - 2)
    val projectPath = currentPath.indexOf("downloader") match {
      case -1 => currentPath + "/downloader"
      case _  => currentPath
    }
    val testDataPath = s"$projectPath/src/test/resources/test-data/xml-spreadsheet-test-data"
    val filePath     = s"$testDataPath/sample1.xml"

    val expected = Seq(("aaaaa", "1"), ("bbbbb", "2")).toDF("name", "age")

    val result = DataFrameUtils.xmlSpreadsheetToDF(filePath, Some("Sheet1"))
    result.df.drop("line_number", "hash_key") should beEqualTo(expected)
  }

  test("Convert XMLSpreadsheet to DF - multiple tables with null columns") {
    val currentAbsPath = Paths.get(".").toAbsolutePath.toString
    val currentPath    = currentAbsPath.substring(0, currentAbsPath.length - 2)
    val projectPath = currentPath.indexOf("downloader") match {
      case -1 => currentPath + "/downloader"
      case _  => currentPath
    }
    val testDataPath = s"$projectPath/src/test/resources/test-data/xml-spreadsheet-test-data"
    val filePath     = s"$testDataPath/sample3.xml"

    val expected =
      Seq[(String, String, String, String, String, String)](
        ("G", "HILTON", "HAMPTON INN YORK, UNITED KINGDOM", "TOFT GREEN", "", "YORK"),
        ("G", "HILTON", "BEST HOTEL EVER", "COCONUT", "KHAOCHONG", "THAILAND")
      )
        .toDF("Record Type", "Hotel Group Code", "Property Name", "Property Addr1", "Property Addr2", "Property City")

    val result = DataFrameUtils.xmlSpreadsheetToDF(filePath, Some("Transactions"))

    result.df.drop("line_number", "hash_key") should beEqualTo(expected)
  }

  test("createHashingAndLineDF Should create correctly") {
    import sqlContext.implicits._
    val expected = Seq(
      ("GYDUSD", BigDecimal("0.00477783"), Struct(1, "a"), Map("a" -> "a", "b" -> "1"), Seq(1, 2), "3aff5bd070ac4e806b4c3a621c06ea45087f878e", 1L),
      ("SLLUSD", BigDecimal("0.00004789"), Struct(2, "b"), Map("a" -> "b", "b" -> "2"), Seq(3), "ee60b27b0bc055f7b7b227070ee3674f62c8407a", 2L),
      ("ADPUSD", BigDecimal("0.00662556"), Struct(3, "c"), Map("a" -> "c", "b" -> "3"), Seq(4), "6a51c9a3757a834091fa55dcc973bcaba509b4bb", 3L),
      ("AEDUSD", BigDecimal("0.27224219"), Struct(4, "d"), Map("a" -> "d", "b" -> "4"), Seq(5), "9575b5709787b0756b828f2b568b9b21cb2d0fde", 4L),
      ("AFNUSD", BigDecimal("0.01173234"), Struct(5, "e"), Map("a" -> "e", "b" -> "5"), Seq(6), "b7d05ed02b609edf4e19e450ff8cae1c64ebdd61", 5L),
      ("AFNUSD", BigDecimal("0.01173234"), Struct(5, "e"), Map("a" -> "e", "b" -> "5"), Seq(6), "b7d05ed02b609edf4e19e450ff8cae1c64ebdd61", 6L)
    ).toDF(
      "string",
      "decimal",
      "struct",
      "map",
      "array",
      "hash_key",
      "line_number"
    )

    val actual = expected.drop("hash_key", "line_number").transform(createHashingAndLineDF)
    actual should beEqualTo(expected)
  }

  test("IATA - Holiday Tour BSP Thailand") {
    val VaultCredentialConnector: VaultSecretProvider = mock[VaultSecretProvider]
    val VaultDecryptionHelper: VaultDecryptProvider   = mock[VaultDecryptProvider]
    when(VaultCredentialConnector.getUsername()).thenReturn("")
    when(VaultCredentialConnector.getPassword()).thenReturn("")
    when(VaultCredentialConnector.getPrivateKey()).thenReturn("")

    when(VaultDecryptionHelper.getPassPhrase()).thenReturn("")
    when(VaultDecryptionHelper.getPrivateKey()).thenReturn("")
    when(VaultDecryptionHelper.getPublicKey()).thenReturn("")

    val rootConfig: Config = ConfigFactory.parseResources("configs/HolidayTourIata_settings.conf").resolve()
    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(
        DateTime.now.minusDays(1).toString("yyyy-MM-dd"),
        "",
        "",
        "",
        ProcessMode.prod
      )
    )(
      rootConfig.getConfig("config-downloader.downloader_server_holiday_tour_iata"),
      "yyyy-MM-dd",
      "bsp_thailand_file",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    val filePath = s"$testDataPath/BSP_Thailand.DAT"
    val result   = DataFrameUtils.bspToDf(filePath, sftpConfig.bspMapper.get)

    // Verify that the DataFrame was created successfully
    result.df should not be null

    // Verify that we have data rows (should be more than 0)
    val rowCount = result.df.count()
    rowCount should be > 0L

    // Verify that required columns exist in the DataFrame
    val columns = result.df.columns.toSet
    columns should contain("bsp_transaction_type")
    columns should contain("line_number")
    columns should contain("hash_key")

    // Verify that we have different transaction types from the BSP file
    val transactionTypes = result.df.select("bsp_transaction_type").distinct().collect().map(_.getString(0)).toSet
    transactionTypes should contain("BFH01") // File Header Record
    transactionTypes should contain("BCH02") // Billing Cycle Header
    transactionTypes should contain("BOH03") // Office Header
    transactionTypes should contain("BKT06") // Transaction Header Record
    transactionTypes should contain("BKS24") // Ticket/Document Identifier Record

    // Verify specific data from the first BFH record (File Header)
    val bfhRecords = result.df.filter($"bsp_transaction_type" === "BFH01")
    bfhRecords.count() should be > 0L

    val firstBfhRecord = bfhRecords.first()
    firstBfhRecord.getAs[String]("standard_message_identifier") shouldBe "BFH"
    firstBfhRecord.getAs[Int]("sequence_number") shouldBe 1
    firstBfhRecord.getAs[String]("bsp_identifier") shouldBe "BKK"
    firstBfhRecord.getAs[String]("iso_country_code") shouldBe "TH"

    // Verify specific data from BOH record (Office Header)
    val bohRecords = result.df.filter($"bsp_transaction_type" === "BOH03")
    bohRecords.count() should be > 0L

    val firstBohRecord = bohRecords.first()
    firstBohRecord.getAs[String]("standard_message_identifier") shouldBe "BOH"
    firstBohRecord.getAs[String]("currency_type") shouldBe "THB2"

    // Verify that hash_key column contains valid hash values (should be non-empty strings)
    val hashKeys = result.df.select("hash_key").collect().map(_.getString(0))
    hashKeys should not contain null
    hashKeys.foreach { hash =>
      hash should not be empty
      hash.length should be > 10 // Hash should be reasonably long
    }

    // Print some sample data for verification (can be removed in production)
    println(s"Total records processed: $rowCount")
    println("Transaction types found:")
    transactionTypes.foreach(txnType => println(s"  - $txnType"))

    // Show first few records for manual verification
    println("Sample records:")
    result.df
      .select("bsp_transaction_type", "standard_message_identifier", "sequence_number", "line_number")
      .orderBy("line_number")
      .limit(5)
      .show(truncate = false)
  }
}

case class Struct(a: Int, b: String)
