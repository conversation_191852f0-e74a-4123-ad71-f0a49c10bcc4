package com.agoda.finance.monitoring.sender

import com.agoda.commons.vault.client.VaultClient
import com.agoda.finance.common.services.slack.{CommonSlackerTrait, SlackPayload, SlackerTrait}
import com.agoda.finance.config.protocol.{CommonConfigModule, SFTPDownloadConfigModule}
import com.agoda.finance.config.{CommonInfo, ExecutionInfo, SlackConfig}
import com.agoda.finance.constant.TrackingStatus.{DownloadFailed, Downloaded, Imported}
import com.agoda.finance.constant.WarningAlert.CheckEmptyFile
import com.agoda.finance.constant.{ProcessMode, TrackingStatus, WarningAlert}
import com.agoda.finance.model._
import com.agoda.finance.monitoring.resolver.DownloaderStatusResolver
import com.agoda.finance.testutils.StatusUtil.generateTracking
import com.agoda.finance.utils.{VaultDecryptProvider, VaultSecretProvider}
import com.agoda.ml.spark.SparkSharedLocalTest
import com.typesafe.config.{Config, ConfigFactory}
import org.apache.hadoop.fs.{FileSystem, Path}
import org.mockito.ArgumentMatchers.{any, anyString}
import org.mockito.Mockito.{never, spy, verify, when}
import org.mockito.{ArgumentCaptor, ArgumentMatchers, Mockito}
import org.scalatest.FunSuite
import org.scalatest.mockito.MockitoSugar.mock
import org.json4s._
import org.json4s.jackson.JsonMethods._

import java.net.URI
import java.sql.Timestamp

class DownloaderSlackerTest extends FunSuite with SparkSharedLocalTest {

  lazy implicit val vaultClient: Option[VaultClient] = None
  lazy val sparkApplicationId                        = sqlContext.sparkContext.applicationId
  val tokenPath                                      = "fakepath/token-path"
  val slackConfig                                    = SlackConfig(None, Some(tokenPath), "", "", Some(""))

  setupAll {
    val fs     = FileSystem.get(new URI(tokenPath), spark.sparkContext.hadoopConfiguration)
    val stream = fs.create(new Path(tokenPath))
    stream.write("test".getBytes())
    stream.close()
  }

  test("Slacker will send status when errorMsg is present") {
    val mockSlacker         = mock[CommonSlackerTrait]
    val mockCommonInfo      = mock[CommonInfo]
    val downloaderSlack     = spy(new DownloaderSlacker(mockCommonInfo, mockSlacker, slackConfig))
    val status              = Seq()
    val failureNotification = FailureNotification(Some("test"), Seq.empty, Seq.empty, Seq.empty)
    downloaderSlack.sendAlert(status, failureNotification)
    verify(mockSlacker).sendSlackMessageUsingToken[String](
      ArgumentMatchers.eq(
        SlackMessageBuilder.build(
          " :mega: *Downloader Failed to Process Instance Name : null* ",
          s"*Message*: test \n*Please see more details in <https://hadoop-knox-sgprod.agoda.local:8442/gateway/agoda/yarn/cluster/app/$sparkApplicationId|hadoop cluster log>*",
          "#E33A4F"
        )
      ),
      any()
    )
  }
  test("Slacker will send status when failureList is present with reprocess link if slackbot setting enabled") {
    val mockSlacker    = mock[CommonSlackerTrait]
    val mockCommonInfo = mock[CommonInfo]
    val monitoringInfo =
      MonitoringInfo(Seq(), None, SlackInfo(enableStatusVerification = true, enableCheckHistoricalFiles = false), Seq(), checkLookBack = true, 0)
    when(mockCommonInfo.monitoringInfo).thenReturn(monitoringInfo)
    val downloaderSlack = spy(new DownloaderSlacker(mockCommonInfo, mockSlacker, slackConfig) {
      override protected val isReprocessSlackBotEnabled = true
    })
    val status              = Seq(generateTracking(DownloadFailed, "test1"), generateTracking(DownloadFailed, "test2"))
    val failureNotification = FailureNotification(None, status, Seq.empty, Seq.empty)
    downloaderSlack.sendAlert(status, failureNotification)
    verify(mockSlacker).sendSlackMessageUsingToken[String](
      ArgumentMatchers.eq(
        SlackMessageBuilder.build(
          s" :mega: *Downloader(null) Failed to Process :* \n :page_facing_up: *test2* <https://financebackoffice.agodadev.io/financeRequest/downloader?instance=test&file_type=test&save_mode=append&file_name=test2|↻>\n" +
            " :page_facing_up: *test1* <https://financebackoffice.agodadev.io/financeRequest/downloader?instance=test&file_type=test&save_mode=append&file_name=test1|↻> ",
          s" :right-arrow-5:  DownloadFailed: failed \n\n*Please see more details in notification mail or <https://hadoop-knox-sgprod.agoda.local:8442/gateway/agoda/yarn/cluster/app/$sparkApplicationId|hadoop cluster log>*",
          "#E33A4F"
        )
      ),
      any()
    )
  }

  test("Slacker will send status when failureList is present and SlackInfo enableStatusVerification is true") {
    val mockSlacker    = mock[CommonSlackerTrait]
    val mockCommonInfo = mock[CommonInfo]
    val monitoringInfo =
      MonitoringInfo(Seq(), None, SlackInfo(enableStatusVerification = true, enableCheckHistoricalFiles = false), Seq(), checkLookBack = true, 0)
    when(mockCommonInfo.monitoringInfo).thenReturn(monitoringInfo)
    val downloaderSlack     = spy(new DownloaderSlacker(mockCommonInfo, mockSlacker, slackConfig))
    val status              = Seq(generateTracking(DownloadFailed, "test1"), generateTracking(DownloadFailed, "test2"))
    val failureNotification = FailureNotification(None, status, Seq.empty, Seq.empty)
    downloaderSlack.sendAlert(status, failureNotification)
    verify(mockSlacker).sendSlackMessageUsingToken[String](
      ArgumentMatchers.eq(
        SlackMessageBuilder.build(
          s" :mega: *Downloader(null) Failed to Process :* \n :page_facing_up: *test2*\n :page_facing_up: *test1* ",
          s" :right-arrow-5:  DownloadFailed: failed \n\n*Please see more details in notification mail or <https://hadoop-knox-sgprod.agoda.local:8442/gateway/agoda/yarn/cluster/app/$sparkApplicationId|hadoop cluster log>*",
          "#E33A4F"
        )
      ),
      any()
    )
  }

  test("Slacker will send warningstatus when SlackInfo slackWarningList is not empty and imported file is empty") {
    val mockSlacker    = mock[CommonSlackerTrait]
    val mockCommonInfo = mock[CommonInfo]
    val monitoringInfo =
      MonitoringInfo(
        Seq(),
        None,
        SlackInfo(enableStatusVerification = false, enableCheckHistoricalFiles = false, slackWarningList = Seq(WarningAlert.CheckEmptyFile)),
        Seq(),
        checkLookBack = true,
        0
      )
    when(mockCommonInfo.monitoringInfo).thenReturn(monitoringInfo)
    val mockResolver        = new DownloaderStatusResolver(mockCommonInfo)
    val status              = Seq(generateTracking(Imported, "test1"), generateTracking(Imported, "test2"))
    val downloaderSlack     = spy(new DownloaderSlacker(mockCommonInfo, mockSlacker, slackConfig))
    val mockStatusResult    = mockResolver.resolveStatus(status)
    val mockWarningStatus   = mockStatusResult.slackWarningList
    val failureNotification = FailureNotification(None, Seq.empty, mockWarningStatus, Seq.empty)
    downloaderSlack.sendAlert(status, failureNotification)
    verify(mockSlacker).sendSlackMessageUsingToken[String](
      ArgumentMatchers.eq(
        SlackMessageBuilder.build(
          s" :mega: *Downloader(null) Warning Alert for: test1* ",
          s" :right-arrow-5:  ImportFailed: Warning: Imported file is empty \n\n*Please see more details in notification mail or <https://hadoop-knox-sgprod.agoda.local:8442/gateway/agoda/yarn/cluster/app/$sparkApplicationId|hadoop cluster log>*",
          "#F2DA93"
        )
      ),
      any()
    )
    verify(mockSlacker).sendSlackMessageUsingToken[String](
      ArgumentMatchers.eq(
        SlackMessageBuilder.build(
          s" :mega: *Downloader(null) Warning Alert for: test2* ",
          s" :right-arrow-5:  ImportFailed: Warning: Imported file is empty \n\n*Please see more details in notification mail or <https://hadoop-knox-sgprod.agoda.local:8442/gateway/agoda/yarn/cluster/app/$sparkApplicationId|hadoop cluster log>*",
          "#F2DA93"
        )
      ),
      any()
    )
  }

  test("Slacker will send warningstatus and failstatus") {
    val mockSlacker    = mock[CommonSlackerTrait]
    val mockCommonInfo = mock[CommonInfo]
    val slackInfo = SlackInfo(
      enableStatusVerification = true,
      enableCheckHistoricalFiles = false,
      slackWarningList = Seq(CheckEmptyFile)
    )
    val monitoringInfo =
      MonitoringInfo(
        Seq(),
        None,
        slackInfo,
        Seq(".*"),
        checkLookBack = true,
        0
      )
    when(mockCommonInfo.monitoringInfo).thenReturn(monitoringInfo)
    val downloaderSlack = spy(new DownloaderSlacker(mockCommonInfo, mockSlacker, slackConfig))

    val mockResolver        = new DownloaderStatusResolver(mockCommonInfo)
    val status              = Seq(generateTracking(Imported, "test1"), generateTracking(DownloadFailed, "test2"))
    val mockStatusResult    = mockResolver.resolveStatus(status)
    val mockWarningStatus   = mockStatusResult.slackWarningList
    val mockFailingStatus   = mockStatusResult.failList
    val failureNotification = FailureNotification(None, mockFailingStatus, mockWarningStatus, Seq.empty)
    downloaderSlack.sendAlert(status, failureNotification)
    verify(mockSlacker).sendSlackMessageUsingToken[String](
      ArgumentMatchers.eq(
        SlackMessageBuilder.build(
          s" :mega: *Downloader(null) Warning Alert for: test1* ",
          s" :right-arrow-5:  ImportFailed: Warning: Imported file is empty \n\n*Please see more details in notification mail or <https://hadoop-knox-sgprod.agoda.local:8442/gateway/agoda/yarn/cluster/app/$sparkApplicationId|hadoop cluster log>*",
          "#F2DA93"
        )
      ),
      any()
    )
    verify(mockSlacker).sendSlackMessageUsingToken[String](
      ArgumentMatchers.eq(
        SlackMessageBuilder.build(
          s" :mega: *Downloader(null) Failed to Process :* \n :page_facing_up: *test2* ",
          s" :right-arrow-5:  DownloadFailed: failed \n\n*Please see more details in notification mail or <https://hadoop-knox-sgprod.agoda.local:8442/gateway/agoda/yarn/cluster/app/$sparkApplicationId|hadoop cluster log>*",
          "#E33A4F"
        )
      ),
      any()
    )
  }

  test("Slacker will send failStatus and historicalFileFailure") {
    val mockSlacker    = mock[CommonSlackerTrait]
    val mockCommonInfo = mock[CommonInfo]
    val monitoringInfo =
      MonitoringInfo(Seq(), None, SlackInfo(enableStatusVerification = true, enableCheckHistoricalFiles = true), Seq(), checkLookBack = true, 0)
    when(mockCommonInfo.monitoringInfo).thenReturn(monitoringInfo)
    val downloaderSlack = spy(new DownloaderSlacker(mockCommonInfo, mockSlacker, slackConfig))
    val status          = Seq(generateTracking(DownloadFailed, "test1"), generateTracking(DownloadFailed, "test2"))
    val historicalFileFailure = Seq(
      HistoricalFileFailure("testOne", List("testOne_not_download_20240201.csv")),
      HistoricalFileFailure("testTwo", List("testTwo_not_download_20240201.csv", "testTwo_not_download_second_20240201.csv"))
    )
    val failureNotification = FailureNotification(None, status, Seq.empty, historicalFileFailure)
    downloaderSlack.sendAlert(status, failureNotification)
    verify(mockSlacker).sendSlackMessageUsingToken[String](
      ArgumentMatchers.eq(
        SlackMessageBuilder.build(
          s" :mega: *Downloader(null) Failed to Process :* \n :page_facing_up: *test2*\n :page_facing_up: *test1* ",
          s" :right-arrow-5:  DownloadFailed: failed \n\n*Please see more details in notification mail or <https://hadoop-knox-sgprod.agoda.local:8442/gateway/agoda/yarn/cluster/app/$sparkApplicationId|hadoop cluster log>*",
          "#E33A4F"
        )
      ),
      any()
    )
    verify(mockSlacker).sendSlackMessageUsingToken[String](
      ArgumentMatchers.eq(
        SlackMessageBuilder.build(
          s" :mega: *Downloader(null) list of files that should have been downloaded",
          "fileModule: testOne \n fileList: testOne_not_download_20240201.csv\nfileModule: testTwo \n fileList: testTwo_not_download_20240201.csv,testTwo_not_download_second_20240201.csv",
          "#E33A4F"
        )
      ),
      any()
    )
  }
  test("Slacker will not send failStatus and historicalFileFailure when enableStatusVerification and enableCheckHistoricalFiles are false") {
    val mockSlacker    = mock[CommonSlackerTrait]
    val mockCommonInfo = mock[CommonInfo]
    val monitoringInfo =
      MonitoringInfo(Seq(), None, SlackInfo(enableStatusVerification = false, enableCheckHistoricalFiles = false), Seq(), checkLookBack = true, 0)
    when(mockCommonInfo.monitoringInfo).thenReturn(monitoringInfo)
    val downloaderSlack = spy(new DownloaderSlacker(mockCommonInfo, mockSlacker, slackConfig))
    val status          = Seq(generateTracking(DownloadFailed, "test1"), generateTracking(DownloadFailed, "test2"))
    val historicalFileFailure = Seq(
      HistoricalFileFailure("testOne", List("testOne_not_download_20240201.csv")),
      HistoricalFileFailure("testTwo", List("testTwo_not_download_20240201.csv", "testTwo_not_download_second_20240201.csv"))
    )
    val failureNotification = FailureNotification(None, status, Seq.empty, historicalFileFailure)
    downloaderSlack.sendAlert(status, failureNotification)
    verify(mockSlacker, never()).sendSlackMessageUsingToken[String](any[String], any())
  }
  test("Slacker will not send status when everything is good") {
    val mockSlacker    = mock[CommonSlackerTrait]
    val mockCommonInfo = mock[CommonInfo]
    val monitoringInfo =
      MonitoringInfo(Seq(), None, SlackInfo(enableStatusVerification = true, enableCheckHistoricalFiles = true), Seq(), checkLookBack = true, 0)
    when(mockCommonInfo.monitoringInfo).thenReturn(monitoringInfo)
    val downloaderSlack     = spy(new DownloaderSlacker(mockCommonInfo, mockSlacker, slackConfig))
    val status              = Seq(generateTracking(Downloaded, "test1"))
    val failureNotification = FailureNotification(None, Seq.empty, Seq.empty, Seq.empty)
    downloaderSlack.sendAlert(status, failureNotification)
    verify(mockSlacker, never()).sendSlackMessageUsingToken[String](any[String], any())

  }

  test("downloaderSlack sendWarningMessage will get call when we need to checkLookback and lookbackData is not zero") {
    val mockSlacker    = mock[CommonSlackerTrait]
    val slackInfo      = SlackInfo(true, true)
    val monitoringInfo = MonitoringInfo(Seq(), None, slackInfo, Seq(), true, 0)

    val executionInfo = mock[ExecutionInfo]
    when(executionInfo.monitoringInfo).thenReturn(monitoringInfo)
    when(executionInfo.lookBackData).thenReturn(1)

    val downloaderSlack     = spy(new DownloaderSlacker(executionInfo, mockSlacker, slackConfig))
    val status              = Seq(generateTracking(DownloadFailed, "test1"))
    val failureNotification = FailureNotification(None, Seq.empty, Seq.empty, Seq.empty)
    downloaderSlack.sendAlert(status, failureNotification)
    verify(downloaderSlack).sendWarningMessage(any(), any())
  }

  test("Slack will send warning when all conditions are met") {
    val status = Seq(
      Tracking(
        1L,
        123,
        "original_file_name_1.csv",
        "file_name",
        "file_path",
        "file_list_type",
        TrackingStatus.DownloadFailed.id,
        "Nothing",
        "This is the real details",
        false,
        Some(Timestamp.valueOf("2021-01-01 23:00:00")),
        Some(100),
        Timestamp.valueOf("2021-01-01 23:00:00"),
        22012,
        "test",
        "6a5b7cc9-d51d-455d-b888-6b1b5cec1f10"
      ),
      Tracking(
        1L,
        123,
        "original_file_name_2.csv",
        "file_name",
        "file_path",
        "file_list_type",
        TrackingStatus.DownloadFailed.id,
        "Nothing",
        "This is the real details",
        false,
        Some(Timestamp.valueOf("2021-01-01 23:00:00")),
        Some(100),
        Timestamp.valueOf("2021-01-01 23:00:00"),
        22012,
        "test",
        "6a5b7cc9-d51d-455d-b888-6b1b5cec1f10"
      )
    )
    val arg    = Arguments("2022-01-01", "", "", "", ProcessMode.prod)
    val config = ConfigFactory.load("configs/SFTP_settings.conf").getConfig("config-downloader")
    val commonConfigModule = new SFTPDownloadConfigModule(arg)(
      config.getConfig("downloader_server_tester"),
      "any",
      "File_1",
      VaultSecretProvider("test", "test"),
      VaultDecryptProvider("test", "test")
    ) {
      fileNameList = Seq("original_file_name_1.csv")
    }
    val executionInfo = new ExecutionInfo(arg) {
      override val modules: List[CommonConfigModule] = List(commonConfigModule)
    }
    val mockSlacker     = mock[CommonSlackerTrait]
    val downloaderSlack = new DownloaderSlacker(executionInfo, mockSlacker, slackConfig)
    downloaderSlack.sendWarningMessage(executionInfo, status)
    val capture: ArgumentCaptor[String] = ArgumentCaptor.forClass(classOf[String])
    verify(mockSlacker).sendSlackMessageUsingToken[String](capture.capture(), any())
    assert(capture.getValue.contains("has not been downloaded"))
  }
  test("Slack will send 2 warning when all conditions are met") {
    val status = Seq(
      Tracking(
        1L,
        123,
        "original_file_name_1.csv",
        "file_name",
        "file_path",
        "file_list_type",
        TrackingStatus.DownloadFailed.id,
        "Nothing",
        "This is the real details",
        false,
        Some(Timestamp.valueOf("2021-01-01 23:00:00")),
        Some(100),
        Timestamp.valueOf("2021-01-01 23:00:00"),
        22012,
        "test",
        "6a5b7cc9-d51d-455d-b888-6b1b5cec1f10"
      ),
      Tracking(
        1L,
        123,
        "original_file_name_2.csv",
        "file_name",
        "file_path",
        "file_list_type",
        TrackingStatus.DownloadFailed.id,
        "Nothing",
        "This is the real details",
        false,
        Some(Timestamp.valueOf("2021-01-01 23:00:00")),
        Some(100),
        Timestamp.valueOf("2021-01-01 23:00:00"),
        22012,
        "test",
        "6a5b7cc9-d51d-455d-b888-6b1b5cec1f10"
      )
    )
    val arg    = Arguments("2022-01-01", "", "", "", ProcessMode.prod)
    val config = ConfigFactory.load("configs/SFTP_settings.conf").getConfig("config-downloader")
    val commonConfigModuleOne = new SFTPDownloadConfigModule(arg)(
      config.getConfig("downloader_server_tester"),
      "any",
      "File_1",
      VaultSecretProvider("test", "test"),
      VaultDecryptProvider("test", "test")
    ) {
      fileNameList = Seq("original_file_name_1.csv")
    }
    val commonConfigModuleTwo = new SFTPDownloadConfigModule(arg)(
      config.getConfig("downloader_server_tester"),
      "any",
      "File_1",
      VaultSecretProvider("test", "test"),
      VaultDecryptProvider("test", "test")
    ) {
      fileNameList = Seq("original_file_name_2.csv")
    }
    val executionInfo = new ExecutionInfo(arg) {
      override val modules: List[CommonConfigModule] = List(commonConfigModuleOne, commonConfigModuleTwo)
    }

    val mockSlacker     = mock[CommonSlackerTrait]
    val downloaderSlack = new DownloaderSlacker(executionInfo, mockSlacker, slackConfig)
    downloaderSlack.sendWarningMessage(executionInfo, status)
    verify(mockSlacker, Mockito.times(2)).sendSlackMessageUsingToken[String](anyString(), any())
  }
  test("Slack will not send warning when some file is able to successfully transformed") {
    val status = Seq(
      Tracking(
        1L,
        123,
        "original_file_name_1.csv",
        "file_name",
        "file_path",
        "file_list_type",
        TrackingStatus.DownloadFailed.id,
        "Nothing",
        "This is the real details",
        false,
        Some(Timestamp.valueOf("2021-01-01 23:00:00")),
        Some(100),
        Timestamp.valueOf("2021-01-01 23:00:00"),
        22012,
        "test",
        "6a5b7cc9-d51d-455d-b888-6b1b5cec1f10"
      ),
      Tracking(
        1L,
        123,
        "original_file_name_2.csv",
        "file_name",
        "file_path",
        "file_list_type",
        TrackingStatus.Transformed.id,
        "Nothing",
        "This is the real details",
        false,
        Some(Timestamp.valueOf("2021-01-01 23:00:00")),
        Some(100),
        Timestamp.valueOf("2021-01-01 23:00:00"),
        22012,
        "test",
        "6a5b7cc9-d51d-455d-b888-6b1b5cec1f10"
      )
    )
    val arg    = Arguments("2022-01-01", "", "", "", ProcessMode.prod)
    val config = ConfigFactory.load("configs/SFTP_settings.conf").getConfig("config-downloader")
    val commonConfigModule = new SFTPDownloadConfigModule(arg)(
      config.getConfig("downloader_server_tester"),
      "any",
      "File_1",
      VaultSecretProvider("test", "test"),
      VaultDecryptProvider("test", "test")
    ) {
      fileNameList = Seq("original_file_name_1.csv", "original_file_name_2.csv")
    }
    val executionInfo = new ExecutionInfo(arg) {
      override val modules: List[CommonConfigModule] = List(commonConfigModule)
    }
    val mockSlacker     = mock[CommonSlackerTrait]
    val downloaderSlack = new DownloaderSlacker(executionInfo, mockSlacker, slackConfig)
    downloaderSlack.sendWarningMessage(executionInfo, status)
    verify(mockSlacker, never()).sendSlackMessageUsingToken[String](anyString(), any())
  }

  test("Slack will not send warning when fileName list != fileName list in tracking") {
    val status = Seq(
      Tracking(
        1L,
        123,
        "original_file_name_1.csv",
        "file_name",
        "file_path",
        "file_list_type",
        TrackingStatus.DownloadFailed.id,
        "Nothing",
        "This is the real details",
        false,
        Some(Timestamp.valueOf("2021-01-01 23:00:00")),
        Some(100),
        Timestamp.valueOf("2021-01-01 23:00:00"),
        22012,
        "test",
        "6a5b7cc9-d51d-455d-b888-6b1b5cec1f10"
      )
    )
    val arg    = Arguments("2022-01-01", "", "", "", ProcessMode.prod)
    val config = ConfigFactory.load("configs/SFTP_settings.conf").getConfig("config-downloader")
    val commonConfigModule = new SFTPDownloadConfigModule(arg)(
      config.getConfig("downloader_server_tester"),
      "any",
      "File_1",
      VaultSecretProvider("test", "test"),
      VaultDecryptProvider("test", "test")
    ) {
      fileNameList = Seq("original_file_name_1.csv", "original_file_name_2.csv")
    }
    val executionInfo = new ExecutionInfo(arg) {
      override val modules: List[CommonConfigModule] = List(commonConfigModule)
    }
    val mockSlacker     = mock[CommonSlackerTrait]
    val downloaderSlack = new DownloaderSlacker(executionInfo, mockSlacker, slackConfig)
    downloaderSlack.sendWarningMessage(executionInfo, status)
    verify(mockSlacker, never()).sendSlackMessageUsingToken[String](anyString(), any())
  }
  test("Slack will not send warning when file does not satisfy regex") {
    val status = Seq(
      Tracking(
        1L,
        123,
        "original_file_name_1.csv",
        "file_name",
        "file_path",
        "file_list_type",
        TrackingStatus.DownloadFailed.id,
        "Nothing",
        "This is the real details",
        false,
        Some(Timestamp.valueOf("2021-01-01 23:00:00")),
        Some(100),
        Timestamp.valueOf("2021-01-01 23:00:00"),
        22012,
        "test",
        "6a5b7cc9-d51d-455d-b888-6b1b5cec1f10"
      )
    )
    val arg    = Arguments("2022-01-01", "", "", "", ProcessMode.prod)
    val config = ConfigFactory.load("configs/SFTP_settings.conf").getConfig("config-downloader")
    val commonConfigModule = new SFTPDownloadConfigModule(arg)(
      config.getConfig("downloader_server_tester"),
      "any",
      "File_1",
      VaultSecretProvider("test", "test"),
      VaultDecryptProvider("test", "test")
    ) {
      fileNameList = Seq("original_file_name_1.csv")
    }
    val slackInfo = SlackInfo(true, true)
    val executionInfo = new ExecutionInfo(arg) {
      override val modules: List[CommonConfigModule] = List(commonConfigModule)
      override val monitoringInfo                    = MonitoringInfo(Seq(), None, slackInfo, Seq("chubb_daily_.+.csv"), true, 0)
    }

    val mockSlacker     = mock[CommonSlackerTrait]
    val downloaderSlack = new DownloaderSlacker(executionInfo, mockSlacker, slackConfig)
    downloaderSlack.sendWarningMessage(executionInfo, status)
    verify(mockSlacker, never()).sendSlackMessageUsingToken[String](anyString(), any())
  }

  test("getReprocessLink for supplier with from-to pattern") {
    val arg = Arguments("2022-01-01", "", "", "", ProcessMode.prod)
    val configFile =
      """
        |server = "downloader_server_airasia"
        |config-downloader {
        |  downloader_server_airasia {
        |    file-list: ["airasia_daily_file_thb","airasia_daily_file_myr","airasia_daily_file_sgd","airasia_daily_file_php"]
        |    airasia_daily_file_thb {
        |      mapper: "mapper_airasia_daily_file"
        |      file: {
        |        name: ["sale-report-thb-{date}-{date}"]
        |        type: "csv"
        |        offset-date: -2
        |        date-file-name-format: "yyyyMMdd"
        |      }
        |    }
        |  }
        |}
        |""".stripMargin
    val testConfig         = ConfigFactory.parseString(configFile)
    val commonConfigModule = mock[SFTPDownloadConfigModule]
    val executionInfo = new ExecutionInfo(arg) {
      override val modules: List[CommonConfigModule] = List(commonConfigModule)
    }
    val downloaderSlack = new DownloaderSlacker(executionInfo, mock[CommonSlackerTrait], slackConfig) {
      override val isReprocessSlackBotEnabled = true
      override val config                     = testConfig
    }
    val actual = downloaderSlack.getReprocessLink(
      "flight_airasia_http_file",
      "airasia_daily_file_thb",
      "sale-report-thb-20250402-20250402.csv"
    )
    val expected = " <https://financebackoffice.agodadev.io/financeRequest/downloader" +
      "?instance=flight_airasia_http_file" +
      "&file_type=airasia_daily_file_thb" +
      "&save_mode=append" +
      "&file_date=20250402|↻>"
    assert(actual == expected)
  }

  test("SlackMessageBuilder escaped function should handle tab characters correctly") {
    // format: off
    val jsonWithTab = """{"field": "value with	tab"}"""
    // format: on
    val slackMessage = SlackMessageBuilder.build("Test", jsonWithTab, "#color")
    try parse(slackMessage)
    catch {
      case ex: Exception => fail(s"Method should not have thrown an parsing error exception ${ex.getMessage}")
    }
    assert(slackMessage.contains("\\t"))
  }

  test("SlackMessageBuilder escaped function should handle newlines correctly") {
    // format: off
    val jsonWithNewline = """{"field": "value with
newline"}"""
    // format: on
    val slackMessage = SlackMessageBuilder.build("Test", jsonWithNewline, "#color")
    try parse(slackMessage)
    catch {
      case ex: Exception => fail(s"Method should not have thrown an parsing error exception ${ex.getMessage}")
    }
    assert(slackMessage.contains("\\n"))
  }

  test("SlackMessageBuilder escaped function should remove control characters") {
    val jsonWithControlChars = "{\u0001\"field\": \"value\u0002\"}"
    val slackMessage         = SlackMessageBuilder.build("Test", jsonWithControlChars, "#color")
    try parse(slackMessage)
    catch {
      case ex: Exception => fail(s"Method should not have thrown an parsing error exception ${ex.getMessage}")
    }
    assert(!slackMessage.contains("\u0001"))
    assert(!slackMessage.contains("\u0002"))
  }

}
