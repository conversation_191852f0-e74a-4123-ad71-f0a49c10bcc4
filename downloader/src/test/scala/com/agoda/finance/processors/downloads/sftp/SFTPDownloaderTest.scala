package com.agoda.finance.processors.downloads.sftp

import com.agoda.commons.vault.client.VaultClient
import com.agoda.finance.config.ExecutionInfo
import com.agoda.finance.config.protocol.SFTPDownloadConfigModule
import com.agoda.finance.constant.{ProcessMode, TrackingStatus}
import com.agoda.finance.model.{Arguments, DownloadResult, Status}
import com.agoda.finance.processors.downloads.sftp.SFTPHandler.Implicits._
import com.agoda.finance.services.Tracker
import com.agoda.finance.utils.{TrackingInitData, VaultDecryptProvider, VaultSecretProvider}
import com.agoda.hadoop.fs.FileSystemOperation
import com.agoda.ml.spark.HiveSupport
import com.agoda.ml.spark.SparkHive._
import com.agoda.ml.spark.services.etl.{DataFrameETLExtensionLocalTest, SparkETLLocalTest}
import com.jcraft.jsch.{ChannelSftp, SftpATTRS}
import com.typesafe.config.{Config, ConfigFactory}
import org.apache.commons.io.IOUtils
import org.apache.hadoop.fs.Path
import org.joda.time.DateTime
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers.{any, anyString}
import org.mockito.Mockito.{never, spy, verify, when}
import org.scalatest.mockito.MockitoSugar.mock

import java.io.InputStream
import java.sql.Timestamp
import scala.util.Random

class SFTPDownloaderTest extends SparkETLLocalTest with DataFrameETLExtensionLocalTest with HiveSupport {

  implicit val tracker: Tracker = new Tracker()

  setupAll {
    spark.sql("CREATE DATABASE IF NOT EXISTS finance_downloader")
    spark.sql("CREATE DATABASE IF NOT EXISTS testdb")
    System.setProperty("config.resource", "configs/SFTP_settings.conf")
    ConfigFactory.invalidateCaches()
  }
  teardownAll {
    spark.sql("DROP DATABASE IF EXISTS finance_downloader CASCADE")
    spark.sql("DROP DATABASE IF EXISTS testdb CASCADE")
    System.setProperty("config.resource", "application.conf")
    ConfigFactory.invalidateCaches()
  }

  type LsEntry = ChannelSftp.LsEntry

  val lsRoot = List(
    mockLsEntry("file1", isDir = false, isReg = true),
    mockLsEntry("file2", isDir = false, isReg = true)
  )

  val lsRootFileEntryList = List(
    SFTPFileEntry(suitePath.toString + "/", "", mockLsEntry("file_Save.csv", isDir = false, isReg = true)),
    SFTPFileEntry("/root/not/exist/", "", mockLsEntry("file_Not_save.csv", isDir = false, isReg = true))
  )

  val file: InputStream = IOUtils.toInputStream(
    """
      |a,b,c
      |1,2,3
      |4,5,6
    """.stripMargin,
    "UTF-8"
  )

  val rootConfig: Config = ConfigFactory.load()

  val mockSftpConfig = mock[SFTPDownloadConfigModule]
  when(mockSftpConfig.fileList).thenReturn("test_file")

  val VaultCredentialConnector: VaultSecretProvider = mock[VaultSecretProvider]
  val VaultDecryptionHelper: VaultDecryptProvider   = mock[VaultDecryptProvider]
  when(VaultCredentialConnector.getUsername()).thenReturn("")
  when(VaultCredentialConnector.getPassword()).thenReturn("")
  when(VaultCredentialConnector.getPrivateKey()).thenReturn("")
  when(VaultCredentialConnector.getPassPhrase()).thenReturn("")
  when(VaultCredentialConnector.getFingerprint()).thenReturn("")

  when(VaultDecryptionHelper.getPassPhrase()).thenReturn("")
  when(VaultDecryptionHelper.getPrivateKey()).thenReturn("")
  when(VaultDecryptionHelper.getPublicKey()).thenReturn("")

  implicit val trackingData: TrackingInitData =
    TrackingInitData(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), System.currentTimeMillis, "")

  implicit val vaultClient: Option[VaultClient] = None

  def mockLsEntry(fileName: String, isDir: Boolean, isReg: Boolean): LsEntry = {
    val attrs = mock[SftpATTRS]
    when(attrs.isDir).thenReturn(isDir)
    when(attrs.getFlags).thenReturn(-1) // all 1s - no restriction

    if (isReg) {
      when(attrs.getPermissions).thenReturn(S_IFREG)
    } else {
      when(attrs.getPermissions).thenReturn(-1)
    }

    val entry = mock[LsEntry]
    when(entry.getFilename).thenReturn(fileName)
    when(entry.getAttrs).thenReturn(attrs)
    entry
  }

  test("getFileList should return fileNameList from config") {
    val fileNames = Seq("fileA.csv", "fileB.csv", "fileC.csv")
    val config    = mock[SFTPDownloadConfigModule]
    when(config.fileNameList).thenReturn(fileNames)

    val downloader = new SFTPDownloader(config)(3, 2, None, Seq.empty[String])
    downloader.getFileList shouldBe fileNames
  }

  //Unit test : Try to get file List from SFTP.
  test("Try to get file List from SFTP.") {

    // mocking folder structure
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    // act
    val actual = new SFTPDownloader(mockSftpConfig)(5, 2, None, Seq.empty[String]).getFilesEntryList(sftp, "root/", "", isReprocess = false)

    actual should contain theSameElementsAs Seq(
      SFTPFileEntry("root/", "", lsRoot.head),
      SFTPFileEntry("root/", "", lsRoot(1))
    )
  }

  //Unit test : Try to get file List from SFTP, It was failed on first 3 retry and recover back to get in next retry (equal max retry setting)
  test("Try to get file List from SFTP, It was failed on first 3 retry and recover back to get in max retry") {

    val exception = new RuntimeException(new com.jcraft.jsch.JSchException())
    // mocking folder structure
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenThrow(exception) // retry 1
      .thenThrow(exception) // retry 2
      .thenThrow(exception) // retry 3
      .thenThrow(exception) // retry 4
      .thenReturn(lsRoot)

    // act
    val actuals = new SFTPDownloader(mockSftpConfig)(4, 2, None, Seq.empty[String]).getFilesEntryList(sftp, "root/", "", isReprocess = false)

    actuals should contain theSameElementsAs Seq(
      SFTPFileEntry("root/", "", lsRoot.head),
      SFTPFileEntry("root/", "", lsRoot(1))
    )

  }

  //Unit test : Try to get file List from SFTP, It was failed on first 3 retry and recover back to get in next retry (not exceed max retry setting)
  test("Try to get file List from SFTP, It was failed on first 3 retry and recover back to get in next retry") {

    val exception = new RuntimeException(new com.jcraft.jsch.JSchException())

    // mocking folder structure
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenThrow(exception) // retry 1
      .thenThrow(exception) // retry 2
      .thenThrow(exception) // retry 3
      .thenThrow(exception) // retry 4
      .thenReturn(lsRoot)

    // act
    val actual = new SFTPDownloader(mockSftpConfig)(5, 2, None, Seq.empty[String]).getFilesEntryList(sftp, "root/", "", isReprocess = false)

    actual should contain theSameElementsAs Seq(
      SFTPFileEntry("root/", "", lsRoot.head),
      SFTPFileEntry("root/", "", lsRoot(1))
    )

  }

  //Unit test : Try to get file List from SFTP with empty file in path
  test("Try to get file List from SFTP with empty file in path") {

    // mocking folder structure
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/empty/path/", "root/empty/path/"))
      .thenReturn(Nil)

    // act
    val actual =
      new SFTPDownloader(mockSftpConfig)(5, 2, None, Seq.empty[String]).getFilesEntryList(sftp, "root/empty/path/", "", isReprocess = false)

    actual should contain theSameElementsAs Nil
  }

  //Unit test : Try to save file
  test("Try to save file") {

    // mocking folder structure
    val sftp = mock[SFTPHandler]

    val fileSave = lsRootFileEntryList.head

    val desPath       = new Path(suitePath.toString)
    val fullPathFile1 = fileSave.rootPath + fileSave.folder + fileSave.file.getFilename

    when(sftp.getFileStream(fullPathFile1))
      .thenReturn(file)

    val sftpConfig_1 = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_MultiFile"),
      "yyyy-MM-dd",
      "File_1",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    // act
    val actual = new SFTPDownloader(sftpConfig_1)(3, 2, None, Seq.empty[String]).downloadFile(
      sftp,
      remoteDirectory = fileSave.rootPath,
      fileName = fileSave.file.getFilename,
      destinationFolder = desPath,
      folder = fileSave.folder,
      isReprocess = false,
      fileAttrs = Some(fileSave.file.getAttrs),
      originalFileName = Some(fileSave.file.getFilename)
    )

    actual shouldBe Some(DownloadResult(new Path(suitePath, fullPathFile1), fileSave.file.getFilename))
  }

  //Unit test : Try to save file, but failed
  test("Try to save file, but failed") {
    // mocking folder structure
    val sftp = mock[SFTPHandler]
    val fs   = mock[FileSystemOperation]

    val fileNotSave = lsRootFileEntryList(1)

    val desPath = new Path(fileNotSave.rootPath)

    // act
    val actual = new SFTPDownloader(mockSftpConfig)(3, 2, None, Seq.empty[String]).downloadFile(
      sftp,
      remoteDirectory = fileNotSave.rootPath,
      fileName = fileNotSave.file.getFilename,
      destinationFolder = desPath,
      folder = fileNotSave.folder,
      isReprocess = false,
      fileAttrs = Some(fileNotSave.file.getAttrs),
      originalFileName = Some(fileNotSave.file.getFilename)
    )

    actual shouldBe None
  }

  //Unit test : Try to get file List from real SFTP with some file in the path in multiple filelist in the setting filelist : ["File_1","File_2"]
  ignore("Try to get file List from real SFTP with some file in the path in multiple filelist") {

    val sftpConfig_1 = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_MultiFile"),
      "yyyy-MM-dd",
      "File_1",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    val sftpConfig_2 = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_MultiFile"),
      "yyyy-MM-dd",
      "File_2",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    val sftpCon_1 = SFTPHandler(sftpConfig_1)
    val sftpCon_2 = SFTPHandler(sftpConfig_2)

    val actual_1 =
      new SFTPDownloader(sftpConfig_1)(5, 2, None, Seq.empty[String]).getFilesEntryList(
        sftpCon_1,
        "/inbound/Relocation/",
        "",
        isReprocess = false
      )
    val actual_2 =
      new SFTPDownloader(sftpConfig_2)(5, 2, None, Seq.empty[String]).getFilesEntryList(
        sftpCon_2,
        "/inbound/Relocation/",
        "",
        isReprocess = false
      )
    assert(actual_1.nonEmpty)
    assert(actual_2.nonEmpty)

  }

  //Unit test : tried to play with lookback file in case of no yesterday file and it will be picked up today
  test("Tried to play with lookback file in case of no yesterday file and it will be picked up today") {
    import sqlContext.implicits._
    val status = Seq(
      (
        402001L,
        2,
        s"PCLN_FIN_BOOKING_ANOTHER_${DateTime.now.minusDays(2).toString("MMddyyyy")}.csv",
        "/test/",
        "Downloaded",
        "Download Completed",
        false,
        Timestamp.valueOf("2019-01-01 19:15:00.0"),
        10L,
        "2019-05-05",
        Timestamp.valueOf("2019-01-01 19:15:00.0"),
        201908
      )
    ).toDF(
      "file_id",
      "status_id",
      "file_name",
      "file_path",
      "status_detail",
      "details",
      "is_reprocess",
      "last_modify",
      "line_count",
      "accounting_date",
      "reporting_date",
      "datadate"
    )

    status.insertIntoTable("finance_downloader.common_downloader_status", overwrite = false, Seq("datadate"))

    val arg      = Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    val execInfo = new ExecutionInfo(arg)

    val filename: Seq[String] = execInfo.modules.flatMap(_.asInstanceOf[SFTPDownloadConfigModule].fileNameList)

    val expected = Seq(
      s"PCLN_FIN_BOOKING_${DateTime.now.minusDays(1).toString("MMddyyyy")}.csv",
      s"PCLN_FIN_BOOKING_${DateTime.now.minusDays(2).toString("MMddyyyy")}.csv",
      s"PCLN_FIN_BOOKING_${DateTime.now.minusDays(3).toString("MMddyyyy")}.csv",
      s"PCLN_FIN_2_BOOKING_${DateTime.now.toString("MMddyyyy")}.csv",
      s"PCLN_FIN_2_BOOKING_${DateTime.now.minusDays(1).toString("MMddyyyy")}.csv",
      s"PCLN_FIN_2_BOOKING_${DateTime.now.minusDays(2).toString("MMddyyyy")}.csv"
    ).sorted

    filename.length shouldBe 6 //(2 mains + 2 each of lookback)
    filename.sorted should equal(expected)
  }

  test("Download actual connection") {
    val formatName = "TaiwanEGUI_IG_Transmit_ACK"
    //        val lsRoot = List(mockLsEntry("IG_1.8_97162640_ECI2C_"+ DateTime.now.toString("yyyyMMdd") +"_001_ACK.txt", isDir = false, isReg = true))
    //        val sftp = mock[SFTPHandler]
    //
    //        when(sftp.getFileList("root/", "root/"))
    //        .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.apply()
    result.map(p => println(p))
    //        val result = dl.getFilesEntryList(sftp, "root/","",false)
    //
    //        result should contain theSameElementsAs Seq(
    //            SFTPFileEntry("root/", "", lsRoot.head)
    //        )
  }

  test("Download delete file") {
    val formatName = "TaiwanEGUI_IG_Transmit_ACK"
    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )
    val dl   = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val sftp = SFTPHandler(sftpConfig)
    val path = new Path(suitePath, "aaa/aaa.txt")
    dl.delFiles(sftp, path, "aaa.txt")
  }

  test("SFTP with pass_phrase and fingerprint") {
    // Mock VaultSecretProvider with passPhrase and fingerprint
    val mockVaultProvider = mock[VaultSecretProvider]
    when(mockVaultProvider.getUsername()).thenReturn("testuser")
    when(mockVaultProvider.getPassword()).thenReturn("testpass")
    when(mockVaultProvider.getPrivateKey()).thenReturn("testkey")
    when(mockVaultProvider.getPassPhrase()).thenReturn("testphrase")
    when(mockVaultProvider.getFingerprint()).thenReturn("testfingerprint")

    // Create a mock SFTPDownloadConfigModule
    val mockConfig = mock[SFTPDownloadConfigModule]
    when(mockConfig.username).thenReturn("testuser")
    when(mockConfig.password).thenReturn("testpass")
    when(mockConfig.privateKey).thenReturn("testkey")
    when(mockConfig.sftpPassPhrase).thenReturn("testphrase")
    when(mockConfig.fingerprint).thenReturn("testfingerprint")
    when(mockConfig.proxy).thenReturn("")
    when(mockConfig.server).thenReturn("sftp://test.server.com:22")
    when(mockConfig.fileList).thenReturn("test_file")

    val sftp = SFTPHandler(mockConfig)

    assert(sftp != null)

    // We can't actually test the connection without a real server,
    // but we can verify that the configuration is passed correctly
    assert(sftp.sftpModule.username == "testuser")
    assert(sftp.sftpModule.password == "testpass")
    assert(sftp.sftpModule.privateKey == "testkey")
    assert(sftp.sftpModule.sftpPassPhrase == "testphrase")
    assert(sftp.sftpModule.fingerprint == "testfingerprint")
  }

  test("Download IG_Transmit") {
    val formatName = "TaiwanEGUI_IG_Transmit_ACK"
    val fileName   = "IG_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_ACK.txt"

    val lsRoot = List(
      mockLsEntry(
        fileName,
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry should contain theSameElementsAs Seq(
      SFTPFileStatus(SFTPFileEntry("root/", "", lsRoot.head), fileName)
    )
  }

  test("Regex file name should still be in file not found list if name pattern is not matched") {
    val sftpConfig                = mock[SFTPDownloadConfigModule]
    val fileNameList: Seq[String] = Seq(".*_test_111_.*")
    val fileEntryNameList = Seq(
      "not_related_file1.xlsx",
      "not_related_file2.xlsx"
    )
    val dl       = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val actual   = dl.getFileNotFoundList(fileNameList, fileEntryNameList)
    val expected = Seq(".*_test_111_.*")
    assert(actual == expected)
  }

  test(
    "Regex file name should not be in file not found list if the pattern is matched"
  ) {
    val sftpConfig                = mock[SFTPDownloadConfigModule]
    val fileNameList: Seq[String] = Seq(".*_test_222_.*")
    val fileEntryNameList = Seq(
      "aaa_test_222_aaa.xlsx",
      "bbb_test_222_bbb.xlsx"
    )
    val dl       = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val actual   = dl.getFileNotFoundList(fileNameList, fileEntryNameList)
    val expected = Seq()
    assert(actual == expected)
  }

  test(
    "getFileNotFoundList should still be applicable to file names that are not Regex pattern"
  ) {
    val sftpConfig                = mock[SFTPDownloadConfigModule]
    val fileNameList: Seq[String] = Seq("test_111.xlsx", "test_222.xls", "not_matched.xlsx")
    val fileEntryNameList = Seq(
      "test_111.xlsx",
      "test_222.xls"
    )
    val dl       = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val actual   = dl.getFileNotFoundList(fileNameList, fileEntryNameList)
    val expected = Seq("not_matched.xlsx")
    assert(actual == expected)
  }

  test("Download IG_Transmit missing") {
    val formatName = "TaiwanEGUI_IG_Transmit_ACK"
    val lsRoot = List(
      mockLsEntry(
        "XIG_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_ACK.txt",
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry shouldNot contain theSameElementsAs Seq(
      SFTPFileEntry("root/", "", lsRoot.head)
    )
  }

  test("Download IH_Transmit") {
    val formatName = "TaiwanEGUI_IH_Transmit_ACK"
    val fileName   = "IH_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_ACK.txt"

    val lsRoot = List(
      mockLsEntry(
        fileName,
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry should contain theSameElementsAs Seq(
      SFTPFileStatus(SFTPFileEntry("root/", "", lsRoot.head), fileName)
    )
  }

  test("Download IH_Transmit missing") {
    val formatName = "TaiwanEGUI_IH_Transmit_ACK"
    val lsRoot = List(
      mockLsEntry(
        "IHH_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_ACK.txt",
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry shouldNot contain theSameElementsAs Seq(
      SFTPFileEntry("root/", "", lsRoot.head)
    )
  }

  test("Download VOID") {
    val formatName = "TaiwanEGUI_VOID_ACK"
    val fileName   = "VOID_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_ACK.txt"

    val lsRoot = List(
      mockLsEntry(
        fileName,
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry should contain theSameElementsAs Seq(
      SFTPFileStatus(SFTPFileEntry("root/", "", lsRoot.head), fileName)
    )
  }

  test("Download VOID wrong type") {
    val formatName = "TaiwanEGUI_VOID_ACK"
    val lsRoot = List(
      mockLsEntry(
        "VOID_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_ACK.png",
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry shouldNot contain theSameElementsAs Seq(
      SFTPFileEntry("root/", "", lsRoot.head)
    )
  }

  test("Download IG FILEACK") {
    val formatName = "TaiwanEGUI_IG_File_ACK"
    val fileName   = "IG_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_FILEACK.txt"

    val lsRoot = List(
      mockLsEntry(
        fileName,
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry should contain theSameElementsAs Seq(
      SFTPFileStatus(SFTPFileEntry("root/", "", lsRoot.head), fileName)
    )
  }

  test("Download IG_FILEACK wrong date") {
    val formatName = "TaiwanEGUI_IG_File_ACK"
    val lsRoot = List(
      mockLsEntry(
        "IG_1.8_97162640_ECI2C_" + DateTime.now.plusDays(1).toString("yyyyMMdd") + "_001_FILEACK.txt",
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry shouldNot contain theSameElementsAs Seq(
      SFTPFileEntry("root/", "", lsRoot.head)
    )
  }

  test("Download IH_FILEACK") {
    val formatName = "TaiwanEGUI_IH_File_ACK"
    val fileName   = "IH_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_FILEACK.txt"

    val lsRoot = List(
      mockLsEntry(
        fileName,
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry should contain theSameElementsAs Seq(
      SFTPFileStatus(SFTPFileEntry("root/", "", lsRoot.head), fileName)
    )
  }

  test("Download IH_FILEACK wrong type") {
    val formatName = "TaiwanEGUI_IH_File_ACK"
    val lsRoot = List(
      mockLsEntry(
        "IH_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_FILEACK.txt.png",
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry shouldNot contain theSameElementsAs Seq(
      SFTPFileEntry("root/", "", lsRoot.head)
    )
  }

  test("Download VOID_FILEACK") {
    val formatName = "TaiwanEGUI_VOID_File_ACK"
    val fileName   = "VOID_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_FILEACK.txt"

    val lsRoot = List(
      mockLsEntry(
        fileName,
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry should contain theSameElementsAs Seq(
      SFTPFileStatus(SFTPFileEntry("root/", "", lsRoot.head), fileName)
    )
  }

  test("Download VOID_FILEACK case sensitive") {
    val formatName = "TaiwanEGUI_VOID_File_ACK"
    val lsRoot = List(
      mockLsEntry(
        "Void_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_FILEACK.txt",
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry shouldNot contain theSameElementsAs Seq(
      SFTPFileEntry("root/", "", lsRoot.head)
    )
  }

  test("Download IG_CTL") {
    val formatName = "TaiwanEGUI_IG_CTL_ACK"
    val fileName   = "IG_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_ACK.ctl"

    val lsRoot = List(
      mockLsEntry(
        fileName,
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry should contain theSameElementsAs Seq(
      SFTPFileStatus(SFTPFileEntry("root/", "", lsRoot.head), fileName)
    )
  }

  test("Download IG_CTL version changed") {
    val formatName = "TaiwanEGUI_IG_CTL_ACK"
    val fileName   = "IG_1.999_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_ACK.ctl"

    val lsRoot = List(
      mockLsEntry(
        fileName,
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry should contain theSameElementsAs Seq(
      SFTPFileStatus(SFTPFileEntry("root/", "", lsRoot.head), fileName)
    )
  }

  test("Download IG_FILEACK_CTL") {
    val formatName = "TaiwanEGUI_IG_CTL_FILEACK"
    val fileName   = "IG_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_FILEACK.ctl"

    val lsRoot = List(
      mockLsEntry(
        fileName,
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry should contain theSameElementsAs Seq(
      SFTPFileStatus(SFTPFileEntry("root/", "", lsRoot.head), fileName)
    )
  }

  test("Download IG_FILEACK_CTL name changed") {
    val formatName = "TaiwanEGUI_IG_CTL_FILEACK"
    val fileName   = "IG_1.8_97162640_new_name_" + DateTime.now.toString("yyyyMMdd") + "_001_FILEACK.ctl"

    val lsRoot = List(
      mockLsEntry(
        fileName,
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry should contain theSameElementsAs Seq(
      SFTPFileStatus(SFTPFileEntry("root/", "", lsRoot.head), fileName)
    )
  }

  test("Download IH_CTL") {
    val formatName = "TaiwanEGUI_IH_CTL_ACK"
    val fileName   = "IH_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_ACK.ctl"

    val lsRoot = List(
      mockLsEntry(
        fileName,
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry should contain theSameElementsAs Seq(
      SFTPFileStatus(SFTPFileEntry("root/", "", lsRoot.head), fileName)
    )
  }

  test("Download IH_CTL version changed") {
    val formatName = "TaiwanEGUI_IH_CTL_ACK"
    val fileName   = "IH_2_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_ACK.ctl"

    val lsRoot = List(
      mockLsEntry(
        fileName,
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry should contain theSameElementsAs Seq(
      SFTPFileStatus(SFTPFileEntry("root/", "", lsRoot.head), fileName)
    )
  }

  test("Download IH_FILEACK_CTL") {
    val formatName = "TaiwanEGUI_IH_CTL_FILEACK"
    val fileName   = "IH_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_FILEACK.ctl"

    val lsRoot = List(
      mockLsEntry(
        fileName,
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry should contain theSameElementsAs Seq(
      SFTPFileStatus(SFTPFileEntry("root/", "", lsRoot.head), fileName)
    )
  }

  test("Download IH_FILEACK_CTL completely wrong name") {
    val formatName = "TaiwanEGUI_IH_CTL_FILEACK"
    val lsRoot     = List(mockLsEntry("xxx.ctl", isDir = false, isReg = true))
    val sftp       = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry shouldNot contain theSameElementsAs Seq(
      SFTPFileEntry("root/", "", lsRoot.head)
    )
  }

  test("Download VOID_CTL") {
    val formatName = "TaiwanEGUI_VOID_CTL_ACK"
    val fileName   = "VOID_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_ACK.ctl"

    val lsRoot = List(
      mockLsEntry(
        fileName,
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry should contain theSameElementsAs Seq(
      SFTPFileStatus(SFTPFileEntry("root/", "", lsRoot.head), fileName)
    )
  }

  test("Download VOID_CTL for case sensitive") {
    val formatName = "TaiwanEGUI_VOID_CTL_ACK"
    val lsRoot = List(
      mockLsEntry(
        "void_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_ACK.ctl",
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry shouldNot contain theSameElementsAs Seq(
      SFTPFileEntry("root/", "", lsRoot.head)
    )
  }

  test("Download VOID_FILEACK_CTL") {
    val formatName = "TaiwanEGUI_VOID_CTL_FILEACK"
    val fileName   = "VOID_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_FILEACK.ctl"

    val lsRoot = List(
      mockLsEntry(
        fileName,
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry should contain theSameElementsAs Seq(
      SFTPFileStatus(SFTPFileEntry("root/", "", lsRoot.head), fileName)
    )
  }

  test("Download VOID_FILEACK_CTL for case sensitive type") {
    val formatName = "TaiwanEGUI_VOID_CTL_FILEACK"
    val lsRoot = List(
      mockLsEntry(
        "VOID_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_FILEACK.CTL",
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry shouldNot contain theSameElementsAs Seq(
      SFTPFileEntry("root/", "", lsRoot.head)
    )
  }

  test("Download VOID_FILEACK_CTL filtering download files") {
    val formatName = "TaiwanEGUI_VOID_CTL_FILEACK"
    val lsRoot = List(
      mockLsEntry(
        "VOID_1.8_97162640_ECI2C_" + DateTime.now.toString("yyyyMMdd") + "_001_FILEACK.CTL",
        isDir = false,
        isReg = true
      ),
      mockLsEntry("VOID_1.8_97162640_ECI2C_20200101_001_FILEACK.CTL", isDir = false, isReg = true)
    )

    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry shouldNot contain theSameElementsAs Seq(
      SFTPFileEntry("root/*" + DateTime.now.toString("yyyyMMdd") + "*", "", lsRoot.head)
    )
  }

  test("Run downloader and return as Input Stream") {

    val formatName = "TaiwanEGUI_VOID_CTL_FILEACK"
    // mocking folder structure
    val sftp = mock[SFTPHandler]

    val fileSave = lsRootFileEntryList.head

    val desPath       = new Path(suitePath.toString)
    val fullPathFile1 = fileSave.rootPath + fileSave.folder + fileSave.file.getFilename

    when(sftp.getFileStream(fullPathFile1))
      .thenReturn(file)

    val sftpConfig = spy(
      new SFTPDownloadConfigModule(
        Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
      )(
        rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
        "yyyy-MM-dd",
        formatName,
        VaultCredentialConnector,
        VaultDecryptionHelper
      )
    )

    when(sftpConfig.usingIOStream).thenReturn(true)

    // act
    val actual = new SFTPDownloader(sftpConfig)(3, 2, None, Seq.empty[String]).downloadFile(
      sftp,
      remoteDirectory = fileSave.rootPath,
      fileName = fileSave.file.getFilename,
      destinationFolder = desPath,
      folder = fileSave.folder,
      isReprocess = false,
      fileAttrs = Some(fileSave.file.getAttrs),
      originalFileName = Some(fileSave.file.getFilename)
    )

    actual.map { result =>
      result.dataStream.get.foreach { stream =>
        IOUtils.contentEquals(stream, file)
      }
    }
  }

  test("Download and delete file completed") {

    val formatName = "TaiwanEGUI_VOID_CTL_FILEACK"
    // mocking folder structure
    val sftp = mock[SFTPHandler]

    val fileSave = lsRootFileEntryList.head

    val desPath       = new Path(suitePath.toString)
    val fullPathFile1 = fileSave.rootPath + fileSave.folder + fileSave.file.getFilename

    when(sftp.getFileStream(fullPathFile1))
      .thenReturn(file)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    // act
    val actual = new SFTPDownloader(sftpConfig)(3, 2, None, Seq.empty[String]).downloadFile(
      sftp,
      remoteDirectory = fileSave.rootPath,
      fileName = fileSave.file.getFilename,
      destinationFolder = desPath,
      folder = fileSave.folder,
      isReprocess = false,
      fileAttrs = Some(fileSave.file.getAttrs),
      originalFileName = Some(fileSave.file.getFilename)
    )

    actual shouldBe Some(DownloadResult(new Path(suitePath, fullPathFile1), fileSave.file.getFilename))
  }

  test("Download and delete file failed") {
    val formatName = "TaiwanEGUI_VOID_CTL_FILEACK"
    // mocking folder structure
    val sftp = mock[SFTPHandler]

    val fileSave = lsRootFileEntryList.head

    val desPath       = new Path(suitePath.toString)
    val fullPathFile1 = fileSave.rootPath + fileSave.folder + fileSave.file.getFilename

    when(sftp.getFileStream(fullPathFile1))
      .thenReturn(file)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    val exception = new RuntimeException(new com.jcraft.jsch.JSchException())
    when(sftp.deleteFile(sftpConfig.actualDownloadPath + fileSave.file.getFilename))
      .thenThrow(exception)

    // act
    val actual = new SFTPDownloader(sftpConfig)(3, 2, None, Seq.empty[String]).downloadFile(
      sftp,
      remoteDirectory = fileSave.rootPath,
      fileName = fileSave.file.getFilename,
      destinationFolder = desPath,
      folder = fileSave.folder,
      isReprocess = false,
      fileAttrs = Some(fileSave.file.getAttrs),
      originalFileName = Some(fileSave.file.getFilename)
    )

    actual shouldBe Some(DownloadResult(new Path(suitePath, fullPathFile1), fileSave.file.getFilename))
  }

  test("Download Enett_NAT - Downloaded status is expected when doing simple download") {
    // Objective: Downloaded status is expected when doing simple download

    // 1. mock up file
    val sftp           = mock[SFTPHandler]
    val sourceFileName = "WeeklyNonAuthorisedTransactions_304100_20200501_024500_00.csv"
    val fileSave       = SFTPFileEntry(suitePath.toString + "/", "", mockLsEntry(sourceFileName, isDir = false, isReg = true))
    val desPath        = new Path(suitePath.toString)
    val fullPathFile   = fileSave.rootPath + fileSave.folder + sourceFileName

    when(sftp.getFileStream(fullPathFile))
      .thenReturn(file)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments("20200501", "20200501", "", "", ProcessMode.date)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_standard"),
      "yyyyMMdd",
      "enett_nat_file_1",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    // 2. download and save
    val start = DateTime.now.toString("yyyy-MM-dd HH:mm:ss")
    val actual = new SFTPDownloader(sftpConfig)(1, 2, None, Seq.empty[String]).downloadFile(
      sftp,
      remoteDirectory = fileSave.rootPath,
      fileName = fileSave.file.getFilename,
      destinationFolder = desPath,
      folder = fileSave.folder,
      isReprocess = false,
      fileAttrs = Some(fileSave.file.getAttrs),
      originalFileName = Some(fileSave.file.getFilename)
    )
    actual shouldBe Some(DownloadResult(new Path(suitePath, fullPathFile), fileSave.file.getFilename))

  }

  test("Download Enett_NAT_Core - The last attempt of retries assigned is successful") {
    // Test case # 4 & 6 & 7
    // Objective: The last attempt of retries assigned is successful

    // 1. mock up file
    val sftp     = mock[SFTPHandler]
    val fileDir  = "root/"
    val fileName = "WeeklyNonAuthorisedTransactions_304100_20200501_024500_00.csv"
    val fileSave = mockLsEntry(fileName, isDir = false, isReg = true)
    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments("20200501", "20200501", "", "", ProcessMode.date)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_standard"),
      "yyyyMMdd",
      "enett_nat_file_1",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )
    val exception = new RuntimeException(new com.jcraft.jsch.JSchException())

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    // 2. Make retries
    when(sftp.getFileList(fileDir, fileDir))
      .thenThrow(exception) // retry 1
      .thenThrow(exception) // retry 2
      .thenThrow(exception) // retry 3
      .thenReturn(List(fileSave))

    // 3. download and save
    val actuals = new SFTPDownloader(sftpConfig)(3, 2, None, Seq.empty[String]).getFilesEntryList(sftp, fileDir, fileName, false)

    // 4. do assertion for download result
    actuals.size shouldBe 1
    actuals.head.file shouldBe fileSave
  }

  test("Download Enett_NAT_Core - Attempts exceed the maximum retries assigned with no success") {
    // Test case # 5
    // Objective: Attempts exceed the maximum retries assigned with no success

    // 1. mock up file
    val sftp     = mock[SFTPHandler]
    val fileDir  = "root/"
    val fileName = "WeeklyNonAuthorisedTransactions_304100_20200501_024500_00.csv"
    val fileSave = mockLsEntry(fileName, isDir = false, isReg = true)
    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments("20200501", "20200501", "", "", ProcessMode.date)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_standard"),
      "yyyyMMdd",
      "enett_nat_file_1",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )
    val exception = new RuntimeException(new com.jcraft.jsch.JSchException())

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    // 2. Make retries
    when(sftp.getFileList(fileDir, fileDir))
      .thenThrow(exception) // retry 1
      .thenThrow(exception) // retry 2
      .thenThrow(exception) // retry 3
      .thenThrow(exception) // retry 4
      .thenReturn(List(fileSave))

    // 3. try downloading
    val ex = intercept[RuntimeException] {
      new SFTPDownloader(sftpConfig)(3, 3, None, Seq.empty[String]).getFilesEntryList(sftp, fileDir, fileName, isReprocess = false)
    }

    // 4. do assertion for exception
    ex shouldBe exception
  }

  test("Download Hyatt File with regex successfully") {
    val formatName = "hotel_hyatt_file"
    val fileName = "StatusData_" + DateTime.now.toString("yyyy-MM-dd") + "_Customer" + getRandomAlphabet(1) + "_" +
      getRandomInteger(5) + "_" + getRandomInteger(3) + "_" + getRandomInteger(5) + "_" +
      getRandomInteger(2) + ".csv"

    val lsRoot = List(
      mockLsEntry(
        fileName,
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_hyatt_file"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry should contain theSameElementsAs Seq(
      SFTPFileStatus(SFTPFileEntry("root/", "", lsRoot.head), fileName)
    )
  }

  test("Download Hyatt File with regex failed") {
    val formatName = "hotel_hyatt_file"
    val fileName = "StatusData_" + DateTime.now.toString("yyyyMMdd") + "_Customer" + getRandomAlphabet(1) + "_" +
      getRandomInteger(5) + "_" + getRandomInteger(3) + "_" + getRandomInteger(5) + "_" +
      getRandomInteger(2) + ".csv"

    val lsRoot = List(
      mockLsEntry(
        fileName,
        isDir = false,
        isReg = true
      )
    )
    val sftp = mock[SFTPHandler]

    when(sftp.getFileList("root/", "root/"))
      .thenReturn(lsRoot)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_hyatt_file"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val dl     = new SFTPDownloader(sftpConfig)(5, 2, None, Seq.empty[String])
    val result = dl.getFiles(sftp, "root/")

    result.listSFTPFileEntry shouldNot contain theSameElementsAs Seq(
      SFTPFileEntry("root/", "", lsRoot.head)
    )
  }
  test("Download Zip File") {
    // Objective: Downloaded status is expected when doing simple download

    // 1. mock up file
    val sftp           = mock[SFTPHandler]
    val sourceFileName = "DailyVANsSettlement_304100_410032_20230121.zip"
    val fileSave       = SFTPFileEntry(suitePath.toString + "/", "", mockLsEntry(sourceFileName, isDir = true, isReg = true))
    val desPath        = new Path(suitePath.toString)
    val fullPathFile   = fileSave.rootPath + fileSave.folder + sourceFileName

    when(sftp.getFileStream(fullPathFile))
      .thenReturn(file)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments("20230121", "20230121", "", "", ProcessMode.date)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_standard"),
      "yyyyMMdd",
      "enett_upc",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    // 2. download and save
    val start = DateTime.now.toString("yyyy-MM-dd HH:mm:ss")
    val actual = new SFTPDownloader(sftpConfig)(1, 2, None, Seq.empty[String]).downloadFile(
      sftp,
      remoteDirectory = fileSave.rootPath,
      fileName = fileSave.file.getFilename,
      destinationFolder = desPath,
      folder = fileSave.folder,
      isReprocess = false,
      fileAttrs = Some(fileSave.file.getAttrs),
      originalFileName = Some(fileSave.file.getFilename)
    )
    actual shouldBe Some(DownloadResult(new Path(suitePath, fullPathFile), fileSave.file.getFilename))

  }

  test("Verify Download to HDFS with folder name") {
    val sftp           = mock[SFTPHandler]
    val sourceFileName = "DailyVANsSettlement_304100_410032_20230121.zip"
    val fileSave       = SFTPFileEntry(suitePath.toString + "/", "test/", mockLsEntry(sourceFileName, isDir = true, isReg = true))
    val desPath        = new Path(suitePath.toString)
    val fullPathFile   = fileSave.rootPath + fileSave.folder + sourceFileName

    when(sftp.getFileStream(fullPathFile))
      .thenReturn(file)

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments("20230121", "20230121", "", "", ProcessMode.date)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_standard"),
      "yyyyMMdd",
      "enett_upc",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    when(sftp.sftpModule)
      .thenReturn(sftpConfig)

    val actual = new SFTPDownloader(sftpConfig)(1, 2, None, Seq.empty[String]).downloadFile(
      sftp,
      remoteDirectory = fileSave.rootPath,
      fileName = fileSave.file.getFilename,
      destinationFolder = desPath,
      folder = fileSave.folder,
      isReprocess = false,
      fileAttrs = Some(fileSave.file.getAttrs),
      originalFileName = Some(fileSave.file.getFilename)
    )
    actual shouldBe Some(DownloadResult(new Path(suitePath, fullPathFile), fileSave.file.getFilename))
  }

  test("Download file when file.name in config is a list of Regex patterns") {
    val formatName = "loyalty_dbssg_file"

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_dbssg"),
      "yyyy-MM-dd",
      formatName,
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    val dl     = new SFTPDownloader(sftpConfig)(1, 2, None, Seq.empty[String])
    val result = dl.apply()

    assert(result.isEmpty)
  }

  test("validateRegexPatterns should return true if file name match any patterns in fileNameList") {
    val sftpConfig                = mock[SFTPDownloadConfigModule]
    val fileNameList: Seq[String] = Seq(".*_test_111_.*", ".*_test_222_.*", "match_normal_string.csv")
    val fileNames                 = Seq("match_normal_string.csv", "match_test_111_regex.csv")
    val dl                        = new SFTPDownloader(sftpConfig)(1, 1, None, Seq.empty[String])
    val actual                    = fileNames.map(name => dl.validatePatterns(fileNameList, name))
    actual should contain noElementsOf Seq(false)
  }

  test("validateRegexPatterns should return false if file name doesn't match any patterns in fileNameList") {
    val sftpConfig                = mock[SFTPDownloadConfigModule]
    val fileNameList: Seq[String] = Seq(".*_test_111_.*", ".*_test_222_.*", "flight_file_20230922.csv")
    val fileNames                 = Seq("a", "not_match", "flight_file_20230922_fixed.csv")
    val dl                        = new SFTPDownloader(sftpConfig)(1, 1, None, Seq.empty[String])
    val actual                    = fileNames.map(name => dl.validatePatterns(fileNameList, name))
    actual should contain noElementsOf Seq(true)
  }

  test("fileNotFoundCase") {
    val config = mock[SFTPDownloadConfigModule]
    when(config.actualDownloadPath).thenReturn("/home/<USER>")
    implicit val tracker: Tracker = mock[Tracker]
    val downloader                = new SFTPDownloader(config)(0, 60, None, Seq.empty[String])
    downloader.fileNotFoundCase(Seq("file1.txt", "file2.txt"), "sftpconnect.agoda.local")
    val captor: ArgumentCaptor[Seq[Status]] = ArgumentCaptor.forClass(classOf[Seq[Status]])
    verify(tracker).track(captor.capture())(any(), any())
    captor.getValue.size shouldBe 4
    captor.getValue.filter(_.status == TrackingStatus.Ready).map(_.details) shouldBe Seq(
      "SFTP Server is ready : sftpconnect.agoda.local",
      "SFTP Server is ready : sftpconnect.agoda.local"
    )
    captor.getValue.filter(_.status == TrackingStatus.DownloadFailed).map(_.details) shouldBe Seq(
      "Unable to find the file in /home/<USER>",
      "Unable to find the file in /home/<USER>"
    )
    captor.getValue.filter(_.status == TrackingStatus.DownloadFailed).map(_.fileName) shouldBe Seq(
      "file1.txt",
      "file2.txt"
    )
  }

  test("We should download files when isSkipChecking is true and suuccessList is not empty") {
    val sftp     = mock[SFTPHandler]
    val fileName = "test_file.csv"

    when(mockSftpConfig.ignoreMissingFiles).thenReturn(true)
    when(mockSftpConfig.ignoreProcessedFiles).thenReturn(true)
    when(mockSftpConfig.fileNameList).thenReturn(Seq(fileName))
    when(mockSftpConfig.actualDownloadPath).thenReturn("/test/path/")
    when(mockSftpConfig.hdfsDestination).thenReturn(suitePath.toString)
    when(mockSftpConfig.isSkipChecking).thenReturn(true)

    val dl = spy(new SFTPDownloader(mockSftpConfig)(3, 2, None, downloadedFileNames = Seq(fileName)) {
      override private[sftp] def downloadFile(
          sftp: SFTPHandler,
          remoteDirectory: String,
          fileName: String,
          destinationFolder: Path,
          folder: String,
          isReprocess: Boolean,
          fileAttrs: Option[SftpATTRS] = None,
          originalFileName: Option[String] = None
      ): Option[DownloadResult] =
        Some(DownloadResult(new Path("/test/path/test_file.csv"), fileName))
    })

    val result = dl.apply()

    verify(dl, never()).getFiles(any[SFTPHandler], anyString())
    result.length shouldBe 1
    result.head.originalFileName shouldBe fileName
  }

  test("We should not download files when isSkipChecking is false and suuccess-List is not empty") {
    val sftp     = mock[SFTPHandler]
    val fileName = "test_file.csv"

    when(mockSftpConfig.ignoreMissingFiles).thenReturn(true)
    when(mockSftpConfig.ignoreProcessedFiles).thenReturn(true)
    when(mockSftpConfig.fileNameList).thenReturn(Seq(fileName))
    when(mockSftpConfig.actualDownloadPath).thenReturn("/test/path/")
    when(mockSftpConfig.hdfsDestination).thenReturn(suitePath.toString)
    when(mockSftpConfig.isSkipChecking).thenReturn(false)

    val dl = spy(new SFTPDownloader(mockSftpConfig)(3, 2, None, downloadedFileNames = Seq(fileName)) {
      override private[sftp] def downloadFile(
          sftp: SFTPHandler,
          remoteDirectory: String,
          fileName: String,
          destinationFolder: Path,
          folder: String,
          isReprocess: Boolean,
          fileAttrs: Option[SftpATTRS] = None,
          originalFileName: Option[String] = None
      ): Option[DownloadResult] =
        Some(DownloadResult(new Path("/test/path/test_file.csv"), fileName))
    })

    val result = dl.apply()

    verify(dl, never()).getFiles(any[SFTPHandler], anyString())
    result.length shouldBe 0
  }

  private def getRandomInteger(length: Int): String =
    Random.alphanumeric.filter(_.isDigit).take(length).mkString

  private def getRandomAlphabet(length: Int): String =
    Random.alphanumeric.filter(_.isLetter).take(length).mkString

}
