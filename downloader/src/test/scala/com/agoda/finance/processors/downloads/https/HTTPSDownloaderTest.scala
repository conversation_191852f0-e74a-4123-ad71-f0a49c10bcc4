package com.agoda.finance.processors.downloads.https

import com.agoda.commons.vault.client.VaultClient
import com.agoda.finance.config.ExecutionInfo
import com.agoda.finance.config.protocol.HTTPSDownloadConfigModule
import com.agoda.finance.constant.{ProcessMode, TrackingStatus}
import com.agoda.finance.externaldatapipeline.core.schema.Schema
import com.agoda.finance.model.{Arguments, HTTPSPayload, Status}
import com.agoda.finance.services.Tracker
import com.agoda.finance.utils.{HTTPSDownloaderUtils, TrackingInitData, VaultDecryptProvider}
import com.agoda.hadoop.fs.FileSystemOperation
import com.agoda.ml.spark.{HiveSupport, SparkSharedLocalTest}
import com.typesafe.config.{Config, ConfigFactory}
import org.apache.commons.io.IOUtils
import org.apache.hadoop.fs.Path
import org.apache.spark.sql.DataFrame
import org.joda.time.DateTime
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{spy, verify, when}
import org.scalatest.FunSuite
import org.scalatest.Matchers.{convertToAnyShouldWrapper, equal}
import org.scalatest.mockito.MockitoSugar.mock

class HTTPSDownloaderTest extends FunSuite with SparkSharedLocalTest with HiveSupport {

  implicit val tracker: Tracker = new Tracker()

  val VaultDecryptionHelper: VaultDecryptProvider = mock[VaultDecryptProvider]

  when(VaultDecryptionHelper.getPassPhrase()).thenReturn("")
  when(VaultDecryptionHelper.getPrivateKey()).thenReturn("")
  when(VaultDecryptionHelper.getPublicKey()).thenReturn("")

  setupAll {
    spark.sql("CREATE DATABASE IF NOT EXISTS finance_http_downloader")
    System.setProperty("config.resource", "configs/HTTP_settings.conf")
    ConfigFactory.invalidateCaches()
  }

  teardownAll {
    spark.sql(s"DROP DATABASE IF EXISTS finance_http_downloader CASCADE")
    System.setProperty("config.resource", "application.conf")
    ConfigFactory.invalidateCaches()
  }
  val todayMinusTenDay: String = DateTime.now.minusDays(5).toString("yyyyMMdd")
  val rootConfig: Config       = ConfigFactory.load()
  implicit val trackingData: TrackingInitData =
    TrackingInitData(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), System.currentTimeMillis, "")
  implicit val vaultClient: Option[VaultClient] = None

  test("Run downloader for tutuka_Nat file and return as InputStream") {
    val httpConfig = spy(
      new HTTPSDownloadConfigModule(
        Arguments(todayMinusTenDay, todayMinusTenDay, "", "", ProcessMode.date)
      )(
        rootConfig.getConfig("config-downloader.downloader_server_standard_http"),
        "yyyyMMdd",
        "tutuka_nat_file_1",
        VaultDecryptionHelper
      ) {
        override val hdfsDestination: String = suitePath.toString

      }
    )

    when(httpConfig.usingIOStream).thenReturn(true)

    val httpsDownloaderUtils     = new HTTPSDownloaderUtils(httpConfig)
    val payloads                 = httpsDownloaderUtils.buildHttpPayload(httpConfig.fileNameList, Seq(None, None))
    val mockHTTPSDownloaderUtils = mock[HTTPSDownloaderUtils]

    val mockInputStream = IOUtils.toInputStream("some test data for my input stream", "UTF-8")

    when(mockHTTPSDownloaderUtils.buildHttpPayload(any[Seq[String]](), any[Seq[Option[String]]]()))
      .thenReturn(payloads)
    when(mockHTTPSDownloaderUtils.getInputStream(any())).thenReturn(mockInputStream)
    val HTTPSDownloader = new HTTPSDownloader(httpConfig, mockHTTPSDownloaderUtils)(1, 2)
    val downloadResult  = HTTPSDownloader.apply()

    downloadResult.map { result =>
      result.dataStream.get.foreach { stream =>
        IOUtils.contentEquals(stream, mockInputStream)
      }
    }

  }

  test("Download tutuka_Nat file and it is exist") {

    val httpConfig = spy(
      new HTTPSDownloadConfigModule(
        Arguments(todayMinusTenDay, todayMinusTenDay, "", "", ProcessMode.date)
      )(
        rootConfig.getConfig("config-downloader.downloader_server_standard_http"),
        "yyyyMMdd",
        "tutuka_nat_file_1",
        VaultDecryptionHelper
      ) {
        override val hdfsDestination: String = suitePath.toString

      }
    )
    val hdfs                     = FileSystemOperation.getOrCreate()
    val httpsDownloaderUtils     = new HTTPSDownloaderUtils(httpConfig)
    val payloads                 = httpsDownloaderUtils.buildHttpPayload(httpConfig.fileNameList, Seq(None, None))
    val mockHTTPSDownloaderUtils = mock[HTTPSDownloaderUtils]
    when(mockHTTPSDownloaderUtils.buildHttpPayload(any[Seq[String]](), any[Seq[Option[String]]]()))
      .thenReturn(payloads)
    when(mockHTTPSDownloaderUtils.getInputStream(any())).thenReturn(IOUtils.toInputStream("some test data for my input stream", "UTF-8"))
    val HTTPSDownloader = new HTTPSDownloader(httpConfig, mockHTTPSDownloaderUtils)(1, 2)
    val fileList        = HTTPSDownloader.apply()
    fileList.size shouldBe 2
    httpConfig.fileNameList.map { file =>
      hdfs.checkIfFileExists(new Path(httpConfig.hdfsDestination, new Path(file).getName)) shouldBe true
    }
  }

  test("Download tutuka_Nat file name only and it is exist") {
    val httpConfig = spy(
      new HTTPSDownloadConfigModule(
        Arguments(
          todayMinusTenDay,
          f"FEB27A97-155D-0357-1054F85C01C61280/SettlementsWithoutAuthorisations_AgodaVCNMVRHighRisk_$todayMinusTenDay.csv",
          "",
          "",
          ProcessMode.file
        )
      )(
        rootConfig.getConfig("config-downloader.downloader_server_standard_http"),
        "yyyyMMdd",
        "tutuka_nat_file_1",
        VaultDecryptionHelper
      ) {
        override val hdfsDestination: String = suitePath.toString
      }
    )

    val hdfs                     = FileSystemOperation.getOrCreate()
    val httpsDownloaderUtils     = new HTTPSDownloaderUtils(httpConfig)
    val payloads                 = httpsDownloaderUtils.buildHttpPayload(httpConfig.fileNameList, Seq())
    val mockHTTPSDownloaderUtils = mock[HTTPSDownloaderUtils]
    when(mockHTTPSDownloaderUtils.buildHttpPayload(any[Seq[String]](), any[Seq[Option[String]]]()))
      .thenReturn(payloads)
    when(mockHTTPSDownloaderUtils.getInputStream(any())).thenReturn(IOUtils.toInputStream("some test data for my input stream", "UTF-8"))

    val HTTPSDownloader = new HTTPSDownloader(httpConfig, mockHTTPSDownloaderUtils)(1, 2)
    val fileList        = HTTPSDownloader.apply()
    fileList.size shouldBe 1
    httpConfig.fileNameList.map { file =>
      hdfs.checkIfFileExists(new Path(httpConfig.hdfsDestination, new Path(file).getName)) shouldBe true
    }
  }

  test("Download tutuka_Nat file but not exist") {
    val httpConfig = spy(
      new HTTPSDownloadConfigModule(
        Arguments("20201028", "20201028", "", "", ProcessMode.date)
      )(
        rootConfig.getConfig("config-downloader.downloader_server_standard_http"),
        "yyyyMMdd",
        "tutuka_nat_file_1",
        VaultDecryptionHelper
      ) {
        override val hdfsDestination: String = suitePath.toString

      }
    )

    httpConfig.fileNameList = Seq("notexist")

    val hdfs                     = FileSystemOperation.getOrCreate()
    val httpsDownloaderUtils     = new HTTPSDownloaderUtils(httpConfig)
    val payloads                 = httpsDownloaderUtils.buildHttpPayload(httpConfig.fileNameList, Seq())
    val mockHTTPSDownloaderUtils = mock[HTTPSDownloaderUtils]
    val exception                = new RuntimeException("Unable to find file name : notexist.csv on this SFTP path")
    when(mockHTTPSDownloaderUtils.buildHttpPayload(any[Seq[String]](), any[Seq[Option[String]]]()))
      .thenReturn(payloads)
    when(mockHTTPSDownloaderUtils.getInputStream(any())).thenThrow(exception)

    val HTTPSDownloader = new HTTPSDownloader(httpConfig, mockHTTPSDownloaderUtils)(1, 2)
    val fileList        = HTTPSDownloader.apply()
    fileList.nonEmpty shouldBe false
    httpConfig.fileNameList.map { file =>
      hdfs.checkIfFileExists(new Path(httpConfig.hdfsDestination, new Path(file).getName)) shouldBe false
    }
  }

  test("Download using post protocol") {
    val httpConfig = new HTTPSDownloadConfigModule(
      Arguments("20230220", "20230220", "", "", ProcessMode.date)
    )(
      rootConfig.getConfig("config-downloader.downloader_post_http"),
      "yyyyMMdd",
      "example_post_protocol_1",
      VaultDecryptionHelper
    ) {
      override val hdfsDestination: String = suitePath.toString
      payloadList = reqPayload match {
        case Some(payload) => Seq(Some(payload.replace("{date}", formattedDate)))
        case _             => Seq(None)
      }
    }
    val mockHTTPSPayloadList = Seq(
      HTTPSPayload(new Path("divided-pnr_20230221.csv"), Some("payload_20230221")),
      HTTPSPayload(new Path("divided-pnr_20230222.csv"), Some("payload_20230222")),
      HTTPSPayload(new Path("divided-pnr_20230223.csv"), Some("payload_20230223"))
    )
    val httpsDownloaderUtils = mock[HTTPSDownloaderUtils]
    when(httpsDownloaderUtils.buildHttpPayload(any[Seq[String]](), any[Seq[Option[String]]]())).thenReturn(mockHTTPSPayloadList)
    when(httpsDownloaderUtils.getInputStream(any())).thenReturn(IOUtils.toInputStream("some test data for my input stream", "UTF-8"))
    val HTTPSDownloader = new HTTPSDownloader(httpConfig, httpsDownloaderUtils)(0, 60)
    val fileList        = HTTPSDownloader.apply()
    fileList.nonEmpty shouldBe true
  }

  test("Download using post protocol - Exception") {
    val httpConfig = new HTTPSDownloadConfigModule(
      Arguments("20230220", "20230220", "", "", ProcessMode.date)
    )(
      rootConfig.getConfig("config-downloader.downloader_post_http"),
      "yyyyMMdd",
      "example_post_protocol_1",
      VaultDecryptionHelper
    ) {
      override val hdfsDestination: String = suitePath.toString
      payloadList = reqPayload match {
        case Some(payload) => Seq(Some(payload.replace("{date}", formattedDate)))
        case _             => Seq(None)
      }
    }
    val mockHTTPSPayloadList = Seq(
      HTTPSPayload(new Path("divided-pnr_20230221.csv"), Some("payload_20230221")),
      HTTPSPayload(new Path("divided-pnr_20230222.csv"), Some("payload_20230222")),
      HTTPSPayload(new Path("divided-pnr_20230223.csv"), Some("payload_20230223"))
    )
    val mockHTTPSDownloaderUtils = mock[HTTPSDownloaderUtils]
    val exception                = new RuntimeException("Some Exception")
    when(mockHTTPSDownloaderUtils.buildHttpPayload(any[Seq[String]](), any[Seq[Option[String]]]())).thenReturn(mockHTTPSPayloadList)
    when(mockHTTPSDownloaderUtils.getInputStream(any())).thenThrow(
      exception
    )
    val HTTPSDownloader = new HTTPSDownloader(httpConfig, mockHTTPSDownloaderUtils)(0, 60)
    val fileList        = HTTPSDownloader.apply()
    fileList.nonEmpty shouldBe false
  }

  test("ExecutionInfo test") {
    val arg = Arguments(DateTime.now.minusDays(1).toString("yyyyMMdd"), "", "", "", ProcessMode.prod)

    val execInfo = new ExecutionInfo(arg)

    var filename: Seq[String] = Seq()

    for (i <- execInfo.modules.indices)
      filename = filename ++ execInfo.modules(i).asInstanceOf[HTTPSDownloadConfigModule].fileNameList

    val expected = Seq(
      s"FE7FE164-C168-6A87-E05AC933F5B28CAA/SettlementsWithoutAuthorisations_AgodaVCNTRYLowRisk_${DateTime.now.minusDays(2).toString("yyyyMMdd")}.csv",
      s"FE7FE164-C168-6A87-E05AC933F5B28CAA/SettlementsWithoutAuthorisations_AgodaVCNTRYLowRisk_${DateTime.now.minusDays(1).toString("yyyyMMdd")}.csv",
      s"FE7FE164-C168-6A87-E05AC933F5B28CAA/SettlementsWithoutAuthorisations_AgodaVCNTRYLowRisk_${DateTime.now.minusDays(0).toString("yyyyMMdd")}.csv",
      s"FEB27A97-155D-0357-1054F85C01C61280/SettlementsWithoutAuthorisations_AgodaVCNMVRHighRisk_${DateTime.now.minusDays(2).toString("yyyyMMdd")}.csv",
      s"FEB27A97-155D-0357-1054F85C01C61280/SettlementsWithoutAuthorisations_AgodaVCNMVRHighRisk_${DateTime.now.minusDays(1).toString("yyyyMMdd")}.csv",
      s"FEB27A97-155D-0357-1054F85C01C61280/SettlementsWithoutAuthorisations_AgodaVCNMVRHighRisk_${DateTime.now.minusDays(0).toString("yyyyMMdd")}.csv",
      s"FE7FE164-C168-6A87-E05AC933F5B28CAA/SettlementsWithoutAuthorisations_AgodaVCNTRYLowRisk2_${DateTime.now.minusDays(2).toString("yyyyMMdd")}.csv",
      s"FE7FE164-C168-6A87-E05AC933F5B28CAA/SettlementsWithoutAuthorisations_AgodaVCNTRYLowRisk2_${DateTime.now.minusDays(1).toString("yyyyMMdd")}.csv",
      s"FE7FE164-C168-6A87-E05AC933F5B28CAA/SettlementsWithoutAuthorisations_AgodaVCNTRYLowRisk2_${DateTime.now.minusDays(0).toString("yyyyMMdd")}.csv",
      s"FEB27A97-155D-0357-1054F85C01C61280/SettlementsWithoutAuthorisations_AgodaVCNMVRHighRisk2_${DateTime.now.minusDays(2).toString("yyyyMMdd")}.csv",
      s"FEB27A97-155D-0357-1054F85C01C61280/SettlementsWithoutAuthorisations_AgodaVCNMVRHighRisk2_${DateTime.now.minusDays(1).toString("yyyyMMdd")}.csv",
      s"FEB27A97-155D-0357-1054F85C01C61280/SettlementsWithoutAuthorisations_AgodaVCNMVRHighRisk2_${DateTime.now.minusDays(0).toString("yyyyMMdd")}.csv"
    ).sorted

    filename.sorted should equal(expected)
  }

  test("writeTrackingDownloadFailedStatus") {
    val config = mock[HTTPSDownloadConfigModule]
    when(config.downloadLink).thenReturn("agoda.com/test")
    implicit val tracker: Tracker = mock[Tracker]
    val HTTPSDownloader           = new HTTPSDownloader(config, mock[HTTPSDownloaderUtils])(0, 60)
    HTTPSDownloader.writeTrackingDownloadFailedStatus(new Path("divided-pnr_20230221.csv"), new RuntimeException("Some Exception"), false)
    val captor: ArgumentCaptor[Seq[Status]] = ArgumentCaptor.forClass(classOf[Seq[Status]])
    verify(tracker).track(captor.capture())(any(), any())
    captor.getValue.size shouldBe 2
    captor.getValue.find(_.status == TrackingStatus.NotReady).map(_.details) shouldBe Some("Unable to connect to agoda.com/test")
    captor.getValue.find(_.status == TrackingStatus.DownloadFailed).map(_.details) shouldBe Some("Failure java.lang.RuntimeException: Some Exception")
  }

}
