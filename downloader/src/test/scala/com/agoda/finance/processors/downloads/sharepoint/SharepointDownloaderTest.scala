package com.agoda.finance.processors.downloads.sharepoint

import com.agoda.commons.vault.client.VaultClient
import com.agoda.finance.common.utility.microsoft.sharepoint.MicrosoftGraphSharepoint
import com.agoda.finance.config.protocol.SharepointDownloadConfigModule
import com.agoda.finance.constant.{FailedReason, ProcessMode, TrackingStatus}
import com.agoda.finance.model.{Arguments, DownloadResult, Status}
import com.agoda.finance.services.Tracker
import com.agoda.finance.utils.{TrackingInitData, VaultDecryptProvider, VaultMicrosoftGraphKeysProvider}
import com.agoda.hadoop.fs.FileSystemOperation
import com.agoda.ml.spark.{HiveSupport, SparkSharedLocalTest}
import com.typesafe.config.{Config, ConfigFactory}
import org.apache.commons.io.IOUtils
import org.joda.time.DateTime
import org.mockito.Mockito._
import org.scalatest.FunSuite
import org.scalatest.mockito.MockitoSugar.mock
import org.apache.hadoop.fs.Path
import org.mockito.ArgumentMatchers.any
import org.scalatest.Matchers.{convertToAnyShouldWrapper, include}

import java.io.InputStream
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import scala.concurrent.Future
import scala.jdk.CollectionConverters.asScalaBufferConverter

class SharepointDownloaderTest extends FunSuite with SparkSharedLocalTest with HiveSupport {
  implicit val tracker: Tracker                                    = mock[Tracker]
  val VaultDecryptionHelper: VaultDecryptProvider                  = mock[VaultDecryptProvider]
  val VaultMicrosoftGraphProvider: VaultMicrosoftGraphKeysProvider = mock[VaultMicrosoftGraphKeysProvider]
  when(VaultMicrosoftGraphProvider.getClientId()).thenReturn("client_id")
  when(VaultMicrosoftGraphProvider.getTenantId()).thenReturn("tenant_id")
  when(VaultMicrosoftGraphProvider.getClientSecret()).thenReturn("client_secret")

  val datePattern = "yyyyMMdd"
  val todayDate   = LocalDate.now().format(DateTimeFormatter.ofPattern(datePattern))

  setupAll {
    spark.sql("CREATE DATABASE IF NOT EXISTS finance_sharepoint_downloader")
    System.setProperty("config.resource", "configs/Sharepoint_settings.conf")
    ConfigFactory.invalidateCaches()
  }

  teardownAll {
    spark.sql(s"DROP DATABASE IF EXISTS finance_sharepoint_downloader CASCADE")
    System.setProperty("config.resource", "application.conf")
    ConfigFactory.invalidateCaches()
  }

  val file: InputStream = IOUtils.toInputStream(
    """
      |a,b,c
      |1,2,3
      |4,5,6
    """.stripMargin,
    "UTF-8"
  )

  val todayMinusTenDay: String                = DateTime.now.minusDays(5).toString("yyyyMMdd")
  val rootConfig: Config                      = ConfigFactory.load()
  implicit val trackingData: TrackingInitData = TrackingInitData(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), System.currentTimeMillis, "")

  test("apply should download and return as Input Stream") {
    val sharepointClient: Option[MicrosoftGraphSharepoint] = Some(mock[MicrosoftGraphSharepoint])
    when(sharepointClient.get.checkFileExist(any[String], any[String])).thenReturn(Right(true))
    when(sharepointClient.get.getFile(any[String], any[String])).thenReturn(Right(file))

    val sharepointConfig = spy(
      new SharepointDownloadConfigModule(Arguments(todayMinusTenDay, todayMinusTenDay, "", "", ProcessMode.date))(
        rootConfig.getConfig("config-downloader.downloader_server_tester_Sharepoint"),
        "yyyyMMdd",
        "File_1",
        VaultMicrosoftGraphProvider,
        VaultDecryptionHelper,
        sharepointClient
      ) {
        override val hdfsDestination: String = suitePath.toString
      }
    )

    when(sharepointConfig.usingIOStream).thenReturn(true)

    val sharepointDownloader = new SharepointDownloader(sharepointConfig)(1, 2)
    val result               = sharepointDownloader.apply()

    result.map { result =>
      result.dataStream.get.foreach { stream =>
        IOUtils.contentEquals(stream, file)
      }
    }
  }

  test("apply should download files from SharePoint and save to HDFS") {
    val sharepointClient: Option[MicrosoftGraphSharepoint] = Some(mock[MicrosoftGraphSharepoint])
    when(sharepointClient.get.checkFileExist(any[String], any[String])).thenReturn(Right(true))
    when(sharepointClient.get.getFile(any[String], any[String])).thenReturn(Right(file))

    val sharepointConfig = new SharepointDownloadConfigModule(Arguments(todayMinusTenDay, todayMinusTenDay, "", "", ProcessMode.date))(
      rootConfig.getConfig("config-downloader.downloader_server_tester_Sharepoint"),
      "yyyyMMdd",
      "File_1",
      VaultMicrosoftGraphProvider,
      VaultDecryptionHelper,
      sharepointClient
    ) {
      override val hdfsDestination: String = suitePath.toString
    }

    val sharepointDownloader = new SharepointDownloader(sharepointConfig)(1, 2)
    val result               = sharepointDownloader.apply()
    assert(result.nonEmpty)
  }

  test("apply should handle no file to download") {
    val sharepointClient: Option[MicrosoftGraphSharepoint] = Some(mock[MicrosoftGraphSharepoint])
    when(sharepointClient.get.checkFileExist(any[String], any[String])).thenReturn(Right(false))
    val sharepointConfig = new SharepointDownloadConfigModule(Arguments(todayMinusTenDay, todayMinusTenDay, "", "", ProcessMode.date))(
      rootConfig.getConfig("config-downloader.downloader_server_tester_Sharepoint"),
      "yyyyMMdd",
      "File_1",
      VaultMicrosoftGraphProvider,
      VaultDecryptionHelper,
      sharepointClient
    ) {
      override val hdfsDestination: String = suitePath.toString
    }

    val sharepointDownloader = new SharepointDownloader(sharepointConfig)(1, 2)
    val result               = sharepointDownloader.apply()
    assert(result.isEmpty)
  }

  test("checkFileExist should return true if file exists") {
    val sharepointClient = mock[MicrosoftGraphSharepoint]
    when(sharepointClient.checkFileExist(any[String], any[String])).thenReturn(Right(true))

    val sharepointDownloader = new SharepointDownloader(mock[SharepointDownloadConfigModule])(1, 2)
    val result               = sharepointDownloader.checkFileExist("filePath", "siteName", sharepointClient)
    assert(result)
  }

  test("saveFromSharepointToHDFS should save file to HDFS") {

    val sharepointClient: Option[MicrosoftGraphSharepoint] = Some(mock[MicrosoftGraphSharepoint])

    val desPath       = new Path(suitePath.toString)
    val fullPathFile1 = "File_1"
    when(sharepointClient.get.getFile(any[String], any[String])).thenReturn(Right(file))

    val sharepointConfig = new SharepointDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_Sharepoint"),
      "yyyy-MM-dd",
      "File_1",
      VaultMicrosoftGraphProvider,
      VaultDecryptionHelper,
      sharepointClient
    )

    // act
    val actual = new SharepointDownloader(sharepointConfig)(3, 2).saveFromSharepointToHDFS(
      fullPathFile1,
      "siteName",
      sharepointClient.get,
      desPath,
      isReprocess = false
    )

    actual shouldBe Some(DownloadResult(new Path(suitePath, fullPathFile1), fullPathFile1))
  }

  test("saveFromSharepointToHDFS should handle failure and return None when saving to HDFS fails") {
    val sharepointClient: Option[MicrosoftGraphSharepoint] = Some(mock[MicrosoftGraphSharepoint])

    val desPath       = new Path(suitePath.toString)
    val fullPathFile1 = "File_1"
    when(sharepointClient.get.getFile(any[String], any[String])).thenReturn(Right(file))

    val sharepointConfig = new SharepointDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_Sharepoint"),
      "yyyy-MM-dd",
      "File_1",
      VaultMicrosoftGraphProvider,
      VaultDecryptionHelper,
      sharepointClient
    )

    val sharepointDownloader = new SharepointDownloader(sharepointConfig)(3, 2)

    val spyDownloader = spy(sharepointDownloader)

    doThrow(new RuntimeException("Simulated upload failure"))
      .when(spyDownloader)
      .uploadFileToHDFS(any[Path], any[InputStream], any[FileSystemOperation])

    doAnswer(_ => () => Future.successful(())).when(spyDownloader).downloadFailedCase(any[String], any[String])

    // Act
    val result = spyDownloader.saveFromSharepointToHDFS(
      fullPathFile1,
      "siteName",
      sharepointClient.get,
      desPath,
      isReprocess = false
    )

    // Assert
    result shouldBe None
    val expectedErrorMessage = s"java.lang.RuntimeException: Simulated upload failure"
    verify(spyDownloader).downloadFailedCase(fullPathFile1, expectedErrorMessage)
  }

  test("handleFailedStatusWithSpecificReason should handle different failure reasons") {
    val sharepointDownloader = new SharepointDownloader(mock[SharepointDownloadConfigModule])(1, 2)
    val result               = sharepointDownloader.handleFailedStatusWithSpecificReason("filePath", FailedReason.noFile)
    assert(result.nonEmpty)
  }

  test("getUnprocessList with File_1 (is-skip-checking = true) should return all files in File_1 even the files already processed") {
    val sharepointClient: Option[MicrosoftGraphSharepoint] = Some(mock[MicrosoftGraphSharepoint])
    when(sharepointClient.get.checkFileExist(any[String], any[String])).thenReturn(Right(true))
    when(sharepointClient.get.getFile(any[String], any[String])).thenReturn(Right(file))

    val sharepointConfig = new SharepointDownloadConfigModule(Arguments(todayMinusTenDay, todayMinusTenDay, "", "", ProcessMode.date))(
      rootConfig.getConfig("config-downloader.downloader_server_tester_Sharepoint"),
      "yyyyMMdd",
      "File_1",
      VaultMicrosoftGraphProvider,
      VaultDecryptionHelper,
      sharepointClient
    ) {
      override val hdfsDestination: String = suitePath.toString
    }

    val expectedFileServer = rootConfig.getConfig("config-downloader.downloader_server_tester_Sharepoint").getConfig("File_1")
    val expectedFile       = expectedFileServer.getConfig("file")
    val fileNameList       = expectedFile.getStringList("name").asScala.toSeq
    val fileType           = expectedFile.getString("type")

    val filePath         = expectedFileServer.getString("download-path")
    val fullFileNameList = fileNameList.map(fileName => filePath + fileName + "." + fileType)

    val processedFileNameList = fullFileNameList
    val sharepointDownloader  = new SharepointDownloader(sharepointConfig)(1, 2)
    val result                = sharepointDownloader.getUnprocessList(fullFileNameList, processedFileNameList)

    assert(result.equals(fullFileNameList))
  }

  test("getUnprocessList with File_2 (is-skip-checking = false) should not return file(s) that already processed") {
    val sharepointClient: Option[MicrosoftGraphSharepoint] = Some(mock[MicrosoftGraphSharepoint])
    when(sharepointClient.get.checkFileExist(any[String], any[String])).thenReturn(Right(true))
    when(sharepointClient.get.getFile(any[String], any[String])).thenReturn(Right(file))

    val sharepointConfig = new SharepointDownloadConfigModule(Arguments(todayMinusTenDay, todayMinusTenDay, "", "", ProcessMode.date))(
      rootConfig.getConfig("config-downloader.downloader_server_tester_Sharepoint"),
      "yyyyMMdd",
      "File_2",
      VaultMicrosoftGraphProvider,
      VaultDecryptionHelper,
      sharepointClient
    ) {
      override val hdfsDestination: String = suitePath.toString
    }

    val expectedFileServer = rootConfig.getConfig("config-downloader.downloader_server_tester_Sharepoint").getConfig("File_2")
    val expectedFile       = expectedFileServer.getConfig("file")
    val fileNameList       = expectedFile.getStringList("name").asScala.toSeq
    val fileType           = expectedFile.getString("type")

    val filePath         = expectedFileServer.getString("download-path")
    val fullFileNameList = fileNameList.map(fileName => filePath + fileName + "." + fileType)

    val processedFileNameList = fullFileNameList.filter(_ contains "processed")
    val expectedFileNameList  = fullFileNameList.filterNot(_ contains "processed")

    val sharepointDownloader = new SharepointDownloader(sharepointConfig)(1, 2)
    val result               = sharepointDownloader.getUnprocessList(fullFileNameList, processedFileNameList)

    assert(result.equals(expectedFileNameList))
  }

  test("uploadFileToHDFS should successfully upload a file to HDFS") {
    val mockFileSystemOperation = mock[FileSystemOperation]
    val mockInputStream         = mock[InputStream]
    val uploadPath              = new Path("/path/to/hdfs/file")

    val downloader = new SharepointDownloader(mock[SharepointDownloadConfigModule])(3, 2)
    downloader.uploadFileToHDFS(uploadPath, mockInputStream, mockFileSystemOperation)

    verify(mockFileSystemOperation).uploadStream(uploadPath, mockInputStream)
  }

  test("uploadFileToHDFS should throw Runtime Exception when upload fails") {
    // Arrange
    val mockFileSystemOperation = mock[FileSystemOperation]
    val mockInputStream         = mock[InputStream]
    val uploadPath              = new Path("/path/to/hdfs/file")

    when(mockFileSystemOperation.uploadStream(any[Path], any[InputStream]))
      .thenThrow(new RuntimeException("Simulated upload failure"))

    val downloader = new SharepointDownloader(mock[SharepointDownloadConfigModule])(3, 2)

    val exception = intercept[RuntimeException] {
      downloader.uploadFileToHDFS(uploadPath, mockInputStream, mockFileSystemOperation)
    }
    assert(exception.getMessage.contains(s"Failed to upload file to HDFS at $uploadPath"))
    verify(mockFileSystemOperation).uploadStream(uploadPath, mockInputStream)
  }
}
