package com.agoda.finance.processors.downloads.smb

import com.agoda.common.fileclient.service.FileClientException
import com.agoda.common.fileclient.service.smb.{SmbClientException, SmbFileClient}
import com.agoda.finance.config.protocol.SMBDownloadConfigModule
import com.agoda.finance.constant.{ProcessMode, TrackingStatus}
import com.agoda.finance.model.{Arguments, Status}
import com.agoda.finance.services.Tracker
import com.agoda.finance.utils.{TrackingInitData, VaultDecryptProvider, VaultSecretProvider}
import com.agoda.hadoop.fs.FileSystemOperation
import com.agoda.ml.spark.HiveSupport
import com.agoda.ml.spark.services.etl.{DataFrameETLExtensionLocalTest, SparkETLLocalTest}
import com.typesafe.config.{Config, ConfigFactory}
import org.apache.commons.io.IOUtils
import org.apache.hadoop.fs.Path
import org.apache.spark.sql.Row
import org.apache.spark.sql.functions.col
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers.anyString
import org.mockito.Mockito.{spy, when}
import org.scalatest.mockito.MockitoSugar.mock

import java.io.{File, FileInputStream}

class SMBDownloaderTest extends SparkETLLocalTest with DataFrameETLExtensionLocalTest with HiveSupport {

  implicit val tracker: Tracker = new Tracker()

  setupAll {
    sqlContext.sql("CREATE DATABASE IF NOT EXISTS finance_downloader")
    sqlContext.sql("CREATE DATABASE IF NOT EXISTS testdb")
    System.setProperty("config.resource", "configs/SMB_settings.conf")
    ConfigFactory.invalidateCaches()
  }

  teardownAll {
    sqlContext.sql("DROP DATABASE IF EXISTS finance_downloader CASCADE")
    sqlContext.sql("DROP DATABASE IF EXISTS testdb CASCADE")
    System.setProperty("config.resource", "application.conf")
    ConfigFactory.invalidateCaches()
  }

  teardown {
    tracker.flushTempTrackingDataFrame()
  }

  val VaultCredentialConnector: VaultSecretProvider = mock[VaultSecretProvider]
  val VaultDecryptionHelper: VaultDecryptProvider   = mock[VaultDecryptProvider]
  when(VaultCredentialConnector.getUsername()).thenReturn("usernameTest")
  when(VaultCredentialConnector.getPassword()).thenReturn("passwordTest")
  when(VaultCredentialConnector.getPrivateKey()).thenReturn("")

  when(VaultDecryptionHelper.getPassPhrase()).thenReturn("")
  when(VaultDecryptionHelper.getPrivateKey()).thenReturn("")
  when(VaultDecryptionHelper.getPublicKey()).thenReturn("")

  val partitiondate: String = "20221028"
  val rootConfig: Config    = ConfigFactory.load("configs/SMB_settings.conf") // ConfigFactory.load()
  implicit val trackingData: TrackingInitData =
    TrackingInitData(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), System.currentTimeMillis, "")

  val validationCols                = Seq("status_id", "status_detail", "details").map(c => col(c))
  val tempFile: File                = java.io.File.createTempFile("file", ".tmp")
  val mockFileClient: SmbFileClient = mock[SmbFileClient]
  when(mockFileClient.fileExists(anyString()))
    .thenReturn(true)
  when(mockFileClient.download(anyString()))
    .thenReturn(tempFile)

  test("Download and return Input Stream") {
    val filedate: String = "20221028"
    val smbConfig = spy(
      new SMBDownloadConfigModule(
        Arguments(partitiondate, filedate, "", "", ProcessMode.date)
      )(
        rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile_SMB"),
        "yyyyMMdd",
        "File_1",
        VaultCredentialConnector,
        VaultDecryptionHelper
      ) {
        override val hdfsDestination: String           = suitePath.toString
        override val fileClient: Option[SmbFileClient] = Some(mockFileClient)
      }
    )

    when(smbConfig.usingIOStream).thenReturn(true)

    val fileInputStream     = new FileInputStream(tempFile)
    val expectedInputStream = IOUtils.toBufferedInputStream(fileInputStream)

    val SMBDownloader = new SMBDownloader(smbConfig)(1, 2)
    val fileList      = SMBDownloader.apply()

    fileList.map { result =>
      result.dataStream.get.foreach { stream =>
        IOUtils.contentEquals(stream, expectedInputStream)
      }
    }
  }

  test("ProcessMode.date: Download single file and it exists") {
    val filedate: String = "20221028"
    val smbConfig = new SMBDownloadConfigModule(
      Arguments(partitiondate, filedate, "", "", ProcessMode.date)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile_SMB"),
      "yyyyMMdd",
      "File_1",
      VaultCredentialConnector,
      VaultDecryptionHelper
    ) {
      override val hdfsDestination: String           = suitePath.toString
      override val fileClient: Option[SmbFileClient] = Some(mockFileClient)
    }
    smbConfig.actualDownloadPath shouldBe "upload/testdir"

    val hdfs = FileSystemOperation.getOrCreate()

    val SMBDownloader = new SMBDownloader(smbConfig)(1, 2)
    val fileList      = SMBDownloader.apply()
    tracker.writeToTrackingTable()

    fileList.size shouldBe 1
    smbConfig.fileNameList.map { file =>
      hdfs.checkIfFileExists(new Path(smbConfig.hdfsDestination, new Path(file).getName)) shouldBe true
    }
  }

  test("ProcessMode.date: Download single file at dynamic path and it exists") {
    val filedate: String = "20221028"
    val smbConfig = new SMBDownloadConfigModule(
      Arguments(partitiondate, filedate, "", "", ProcessMode.date)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile_SMB"),
      "yyyyMMdd",
      "File_2",
      VaultCredentialConnector,
      VaultDecryptionHelper
    ) {
      override val hdfsDestination: String           = suitePath.toString
      override val fileClient: Option[SmbFileClient] = Some(mockFileClient)
    }
    smbConfig.actualDownloadPath shouldBe "upload/testdir/2022/10/20221028"

    val hdfs = FileSystemOperation.getOrCreate()

    val SMBDownloader = new SMBDownloader(smbConfig)(1, 2)
    val fileList      = SMBDownloader.apply()
    tracker.writeToTrackingTable()

    fileList.size shouldBe 1
    smbConfig.fileNameList.map { file =>
      hdfs.checkIfFileExists(new Path(smbConfig.hdfsDestination, new Path(file).getName)) shouldBe true
    }
  }

  test("ProcessMode.file: Download single file and it exists") {
    val filename: String = "PCLN_FIN_BOOKING_20221230"
    val smbConfig = new SMBDownloadConfigModule(
      Arguments(partitiondate, filename, "", "", ProcessMode.file)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile_SMB"),
      "yyyyMMdd",
      "File_1",
      VaultCredentialConnector,
      VaultDecryptionHelper
    ) {
      override val hdfsDestination: String           = suitePath.toString
      override val fileClient: Option[SmbFileClient] = Some(mockFileClient)
    }
    smbConfig.actualDownloadPath shouldBe "upload/testdir"

    val hdfs = FileSystemOperation.getOrCreate()

    val SMBDownloader = new SMBDownloader(smbConfig)(1, 2)
    val fileList      = SMBDownloader.apply()
    tracker.writeToTrackingTable()

    fileList.size shouldBe 1
    smbConfig.fileNameList.map { file =>
      hdfs.checkIfFileExists(new Path(smbConfig.hdfsDestination, new Path(file).getName)) shouldBe true
    }
  }

  test("ProcessMode.file: Download single file at dynamic path and it exists") {
    val filename: String = "PCLN_FIN_BOOKING_20221230"
    val smbConfig = new SMBDownloadConfigModule(
      Arguments(partitiondate, filename, "", "", ProcessMode.file)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile_SMB"),
      "yyyyMMdd",
      "File_2",
      VaultCredentialConnector,
      VaultDecryptionHelper
    ) {
      override val hdfsDestination: String           = suitePath.toString
      override val fileClient: Option[SmbFileClient] = Some(mockFileClient)
    }
    smbConfig.actualDownloadPath shouldBe "upload/testdir/2022/12/20221230"

    val hdfs = FileSystemOperation.getOrCreate()

    val SMBDownloader = new SMBDownloader(smbConfig)(1, 2)
    val fileList      = SMBDownloader.apply()
    tracker.writeToTrackingTable()

    fileList.size shouldBe 1
    smbConfig.fileNameList.map { file =>
      hdfs.checkIfFileExists(new Path(smbConfig.hdfsDestination, new Path(file).getName)) shouldBe true
    }
  }

  test("ProcessMode.date: No fileClient") {
    val filedate: String = "20221028"
    val smbConfig = new SMBDownloadConfigModule(
      Arguments(partitiondate, filedate, "", "", ProcessMode.date)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile_SMB"),
      "yyyyMMdd",
      "File_1",
      VaultCredentialConnector,
      VaultDecryptionHelper
    ) {
      override val hdfsDestination: String           = suitePath.toString
      override val fileClient: Option[SmbFileClient] = None
    }
    smbConfig.actualDownloadPath shouldBe "upload/testdir"

    val SMBDownloader = new SMBDownloader(smbConfig)(1, 2)
    val fileList      = SMBDownloader.apply()
    tracker.writeToTrackingTable()

    fileList.size shouldBe 0
    val fetched: Seq[Row] =
      sqlContext.table("testdb.downloader_status").select(validationCols: _*).collect().toSeq
    val expected: Seq[Row] =
      Seq(Row(1, "NotReady", "Cannot connect to url for file (invalid smbclient): PCLN_FIN_BOOKING_20221028.csv"))
    fetched shouldBe expected
  }

  test("ProcessMode.date: try to download file and file was already download ignorechecking = false") {
    val filedate: String = "20221028"
    val smbConfig = new SMBDownloadConfigModule(
      Arguments(partitiondate, filedate, "", "", ProcessMode.date)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile_SMB"),
      "yyyyMMdd",
      "File_1",
      VaultCredentialConnector,
      VaultDecryptionHelper
    ) {
      override val hdfsDestination: String           = suitePath.toString
      override val fileClient: Option[SmbFileClient] = Some(mockFileClient)
    }
    smbConfig.actualDownloadPath shouldBe "upload/testdir"

    // Manually write to tracking table
    tracker.track(
      Seq(
        Status(
          "PCLN_FIN_BOOKING_20221028.csv",
          "PCLN_FIN_BOOKING_20221028.csv",
          "pcln_daily_file",
          TrackingStatus.Transformed,
          "Transformed",
          smbConfig.isReprocess
        )
      )
    )
    tracker.writeToTrackingTable()

    val SMBDownloader = new SMBDownloader(smbConfig)(1, 2)
    val fileList      = SMBDownloader.apply()
    tracker.writeToTrackingTable()

    fileList.size shouldBe 0
    val fetched: Seq[Row] =
      sqlContext.table("testdb.downloader_status").select(validationCols: _*).collect().toSeq
    val expected: Seq[Row] =
      Seq(
        Row(0, "Ready", "Connect to url for file PCLN_FIN_BOOKING_20221028.csv successfully"),
        Row(2, "Downloaded", "already processed : PCLN_FIN_BOOKING_20221028.csv"),
        Row(6, "Transformed", "Transformed")
      )
    fetched shouldBe expected
  }

  test("ProcessMode.date: try to download file and file was already download ignorechecking = true") {
    val filedate: String = "20221028"
    val smbConfig = new SMBDownloadConfigModule(
      Arguments(partitiondate, filedate, "", "", ProcessMode.date, isIgnoreChecking = true)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile_SMB"),
      "yyyyMMdd",
      "File_1",
      VaultCredentialConnector,
      VaultDecryptionHelper
    ) {
      override val hdfsDestination: String           = suitePath.toString
      override val fileClient: Option[SmbFileClient] = Some(mockFileClient)
    }
    smbConfig.actualDownloadPath shouldBe "upload/testdir"

    // Manually write to tracking table
    tracker.track(
      Seq(
        Status(
          "PCLN_FIN_BOOKING_20221028.csv",
          "PCLN_FIN_BOOKING_20221028.csv",
          "pcln_daily_file",
          TrackingStatus.Transformed,
          "Transformed",
          smbConfig.isReprocess
        )
      )
    )
    tracker.writeToTrackingTable()

    val SMBDownloader = new SMBDownloader(smbConfig)(1, 2)
    val fileList      = SMBDownloader.apply()
    tracker.writeToTrackingTable()

    fileList.size shouldBe 1
    val fetched: Seq[Row] =
      sqlContext.table("testdb.downloader_status").select(validationCols: _*).collect().toSeq
    val expected: Seq[Row] =
      Seq(
        Row(0, "Ready", "Connect to url for file PCLN_FIN_BOOKING_20221028.csv successfully"),
        Row(2, "Downloaded", "Download success : PCLN_FIN_BOOKING_20221028.csv"),
        Row(6, "Transformed", "Transformed")
      )
    fetched shouldBe expected
  }

  test("ProcessMode.date: file doesn't exist") {
    when(mockFileClient.fileExists(anyString()))
      .thenReturn(false)
    val filedate: String = "20221028"
    val smbConfig = new SMBDownloadConfigModule(
      Arguments(partitiondate, filedate, "", "", ProcessMode.date)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile_SMB"),
      "yyyyMMdd",
      "File_1",
      VaultCredentialConnector,
      VaultDecryptionHelper
    ) {
      override val hdfsDestination: String           = suitePath.toString
      override val fileClient: Option[SmbFileClient] = Some(mockFileClient)
    }
    smbConfig.actualDownloadPath shouldBe "upload/testdir"

    val SMBDownloader = new SMBDownloader(smbConfig)(1, 2)
    val fileList      = SMBDownloader.apply()
    tracker.writeToTrackingTable()

    fileList.size shouldBe 0
    val fetched: Seq[Row] =
      sqlContext.table("testdb.downloader_status").select(validationCols: _*).collect().toSeq
    val expected: Seq[Row] =
      Seq(
        Row(0, "Ready", "Connect to url for file PCLN_FIN_BOOKING_20221028.csv successfully"),
        Row(3, "DownloadFailed", "Cannot find in SMB : PCLN_FIN_BOOKING_20221028.csv")
      )
    fetched shouldBe expected
  }

  test("ProcessMode.file: download fail") {
    val filename: String              = "PCLN_FIN_BOOKING_20221230"
    val mockFileClient: SmbFileClient = mock[SmbFileClient]
    when(mockFileClient.fileExists(anyString()))
      .thenReturn(true)
    val smbConfig = new SMBDownloadConfigModule(
      Arguments(partitiondate, filename, "", "", ProcessMode.file)
    )(
      rootConfig.getConfig("config-downloader.downloader_server_tester_singleFile_SMB"),
      "yyyyMMdd",
      "File_2",
      VaultCredentialConnector,
      VaultDecryptionHelper
    ) {
      override val hdfsDestination: String           = suitePath.toString
      override val fileClient: Option[SmbFileClient] = Some(mockFileClient)
    }
    smbConfig.actualDownloadPath shouldBe "upload/testdir/2022/12/20221230"

    val SMBDownloader = new SMBDownloader(smbConfig)(1, 2)
    SMBDownloader.apply()
    tracker.writeToTrackingTable()
    val fetched: Set[Row] =
      sqlContext.table("testdb.downloader_status").select(validationCols: _*).collect().toSet
    val expected: Set[Row] =
      Set(
        Row(1, "NotReady", "Unable to connect to url for file PCLN_FIN_BOOKING_20221230"),
        Row(3, "DownloadFailed", "java.lang.NullPointerException"),
        Row(3, "DownloadFailed", "Failure java.lang.NullPointerException")
      )
    fetched shouldBe expected
  }

}
