package com.agoda.finance.processors.zip

import com.agoda.commons.vault.client.VaultClient
import com.agoda.finance.common.utils.DataLakeFileUtils
import com.agoda.finance.config.protocol.SFTPDownloadConfigModule
import com.agoda.finance.constant.{ProcessMode, TrackingStatus}
import com.agoda.finance.model.{Arguments, DownloadResult, Status}
import com.agoda.finance.services.Tracker
import com.agoda.finance.utils.{TrackingInitData, VaultDecryptProvider, VaultSecretProvider}
import com.agoda.hadoop.fs.FileSystemOperation
import com.agoda.ml.spark.{HiveSupport, SparkSharedLocalTest}
import com.typesafe.config.{Config, ConfigFactory}
import org.apache.hadoop.conf.Configuration
import org.apache.hadoop.fs.{FSDataOutputStream, FileSystem, Path}
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{doAnswer, spy, verify, when}
import org.scalatest.FunSuite
import org.scalatest.Matchers.convertToAnyShouldWrapper
import org.scalatest.Matchers.{be, endWith, include}
import org.scalatest.mockito.MockitoSugar.mock
import org.apache.commons.io.IOUtils
import resource.managed
import java.io.{ByteArrayInputStream, FileInputStream}
import java.util.zip.ZipInputStream
import org.mockito.ArgumentCaptor

class UnZipFileProcessorTest extends FunSuite with SparkSharedLocalTest with HiveSupport {

  implicit val tracker: Tracker = new Tracker()

  val rootConfig: Config = ConfigFactory.load()
  implicit val trackingData: TrackingInitData = TrackingInitData(
    DateTime.now.minusDays(1).toString("yyyy-MM-dd"),
    System.currentTimeMillis,
    ""
  )
  val VaultCredentialConnector: VaultSecretProvider = mock[VaultSecretProvider]
  val VaultDecryptionHelper: VaultDecryptProvider   = mock[VaultDecryptProvider]
  when(VaultCredentialConnector.getUsername()).thenReturn("")
  when(VaultCredentialConnector.getPassword()).thenReturn("")
  when(VaultCredentialConnector.getPrivateKey()).thenReturn("")

  when(VaultDecryptionHelper.getPassPhrase()).thenReturn("")
  when(VaultDecryptionHelper.getPrivateKey()).thenReturn("")
  when(VaultDecryptionHelper.getPublicKey()).thenReturn("")

  implicit val vaultClient: Option[VaultClient] = None
  test("Unzip file with multiple files") {
    val excelFile = getClass.getResource("/test-data/DailyVANsSettlement_304100_410032_20230126.zip").getFile

    val downloadResult = DownloadResult(new Path(excelFile), "DailyVANsSettlement_304100_410032_20230126.zip")

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(
        DateTime.now.minusDays(1).toString("yyyy-MM-dd"),
        "",
        "",
        "",
        ProcessMode.prod
      )
    )(
      rootConfig
        .getConfig("config-downloader.downloader_server_standard"),
      "yyyy-MM-dd",
      "enett_upc",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )
    FileSystemOperation.getOrCreate().deleteDirectoryIfExists(new Path("test-data/DailyVANsSettlement_304100_410032_20230126"), true)
    val result         = new UnZipFileProcessor(sftpConfig).apply(downloadResult)
    val expectedFolder = getClass.getResource("/test-data/DailyVANsSettlement_304100_410032_20230126").getFile
    val expectedFile1 =
      getClass.getResource("/test-data/DailyVANsSettlement_304100_410032_20230126/DailyVANsSettlement_304100_410032_20230126.csv").getFile
    val expectedFile2 =
      getClass.getResource("/test-data/DailyVANsSettlement_304100_410032_20230126/DailyVANsSettlement_304100_410032_20230418.csv").getFile
    result shouldBe Some(
      DownloadResult(
        new Path(expectedFolder),
        "DailyVANsSettlement_304100_410032_20230126.zip",
        None
      )
    )

    DataLakeFileUtils.isFileExist(new Path(expectedFolder)) shouldBe true
    DataLakeFileUtils.isFileExist(new Path(expectedFile1)) shouldBe true
    DataLakeFileUtils.isFileExist(new Path(expectedFile2)) shouldBe true

  }
  test("throw UnsupportedOperationException") {
    val downloadResult = DownloadResult(new Path("fail.csv"), "DailyVANsSettlement_304100_410032_20230126.zip")

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(
        DateTime.now.minusDays(1).toString("yyyy-MM-dd"),
        "",
        "",
        "",
        ProcessMode.prod
      )
    )(
      rootConfig
        .getConfig("config-downloader.downloader_server_standard"),
      "yyyy-MM-dd",
      "enett_upc",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )
    assertThrows[UnsupportedOperationException](new UnZipFileProcessor(sftpConfig).apply(downloadResult))
  }
  test("should return default path if compressed file type is empty") {
    val excelFile = getClass.getResource("/test-data/DailyVANsSettlement_304100_410032_20230126.zip").getFile

    val downloadResult = DownloadResult(new Path(excelFile), "DailyVANsSettlement_304100_410032_20230126.zip")

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(
        DateTime.now.minusDays(1).toString("yyyy-MM-dd"),
        "",
        "",
        "",
        ProcessMode.prod
      )
    )(
      rootConfig
        .getConfig("config-downloader.downloader_server_standard"),
      "yyyy-MM-dd",
      "enett_nat_file_1",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )
    FileSystemOperation.getOrCreate().deleteDirectoryIfExists(new Path("test-data/DailyVANsSettlement_304100_410032_20230126"), true)
    val result = new UnZipFileProcessor(sftpConfig).apply(downloadResult)
    result shouldBe Some(
      downloadResult
    )

  }
  test("decompress GZFile Successfully") {
    implicit val mockTracker: Tracker = mock[Tracker]
    val gzFile                        = getClass.getResource("/test-data/settlement_detail_report_batch_11_20250307.csv.gz").getFile

    val downloadResult = DownloadResult(new Path(gzFile), "settlement_detail_report_batch_11_20250307.csv.gz")

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(
        DateTime.now.minusDays(1).toString("yyyy-MM-dd"),
        "",
        "",
        "",
        ProcessMode.prod
      )
    )(
      rootConfig
        .getConfig("config-downloader.downloader_server_standard"),
      "yyyy-MM-dd",
      "enett_upc",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )
    val result = new UnZipFileProcessor(sftpConfig)(sqlContext, trackingData, mockTracker).apply(downloadResult)
    val expectedFile =
      getClass.getResource("/test-data/settlement_detail_report_batch_11_20250307.csv").getFile
    result shouldBe Some(
      DownloadResult(
        new Path(expectedFile),
        "settlement_detail_report_batch_11_20250307.csv.gz",
        None
      )
    )
    val df  = sqlContext.sparkSession.read.csv(expectedFile)
    val msg = "Unzip File Success " + downloadResult.originalFileName
    val successStatus = Seq(
      Status(
        downloadResult.path.getName,
        downloadResult.path.toString,
        "enett_upc",
        TrackingStatus.Unzipped,
        msg,
        false,
        originalFileName = downloadResult.originalFileName
      )
    )
    df.count() shouldBe 3601
    verify(mockTracker).track(successStatus)(sqlContext, trackingData)
  }
  test("decompress GZFile Failed") {
    implicit val mockTracker: Tracker = mock[Tracker]
    val gzFile                        = getClass.getResource("/test-data/settlement_detail_report_batch_11_20250307.csv.gz").getFile

    val downloadResult = DownloadResult(new Path(gzFile), "settlement_detail_report_batch_11_20250307.csv.gz")

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(
        DateTime.now.minusDays(1).toString("yyyy-MM-dd"),
        "",
        "",
        "",
        ProcessMode.prod
      )
    )(
      rootConfig
        .getConfig("config-downloader.downloader_server_standard"),
      "yyyy-MM-dd",
      "enett_upc",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )
    val spyUnZipFileProcessor = spy(new UnZipFileProcessor(sftpConfig)(sqlContext, trackingData, mockTracker))
    val conf                  = new Configuration()
    val spyFileSystem         = spy(FileSystem.get(conf))
    val mockFsDataInputStream = mock[FSDataOutputStream]
    when(spyUnZipFileProcessor.getHdfsFileSystem()).thenReturn(spyFileSystem)
    doAnswer(_ => mockFsDataInputStream).when(spyFileSystem).create(any[Path])
    when(spyFileSystem.create(new Path(gzFile.replace(".gz", "")))).thenReturn(mockFsDataInputStream)
    when(mockFsDataInputStream.write(any(), any(), any())).thenThrow(new RuntimeException())
    val result = spyUnZipFileProcessor.apply(downloadResult)
    result shouldBe None
    val msg = s"Unzip File failed ${downloadResult.originalFileName} exception: null"
    val successStatus = Seq(
      Status(
        downloadResult.path.getName,
        downloadResult.path.toString,
        "enett_upc",
        TrackingStatus.UnzippedFailed,
        msg,
        false,
        originalFileName = downloadResult.originalFileName
      )
    )
    verify(mockTracker).track(successStatus)(sqlContext, trackingData)
  }

  test("decompress GZFile Successfully with IOStream mode") {
    implicit val mockTracker: Tracker = mock[Tracker]
    val gzFile                        = getClass.getResource("/test-data/settlement_detail_report_batch_11_20250307.csv.gz").getFile

    // Create a FileInputStream and wrap it in a managed resource for IOStream mode
    val fileInputStream    = new FileInputStream(gzFile)
    val managedInputStream = managed(fileInputStream)

    val downloadResult = DownloadResult(
      new Path(gzFile),
      "settlement_detail_report_batch_11_20250307.csv.gz",
      None,                    // encryptedPath
      Some(managedInputStream) // dataStream
    )

    // Create config with IOStream enabled
    val configWithIOStream = rootConfig
      .getConfig("config-downloader.downloader_server_standard")
      .withValue("using-io-stream", com.typesafe.config.ConfigValueFactory.fromAnyRef(true))

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(
        DateTime.now.minusDays(1).toString("yyyy-MM-dd"),
        "",
        "",
        "",
        ProcessMode.prod
      )
    )(
      configWithIOStream,
      "yyyy-MM-dd",
      "enett_upc",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    val result = new UnZipFileProcessor(sftpConfig)(sqlContext, trackingData, mockTracker).apply(downloadResult)

    result.isDefined shouldBe true
    result.get.originalFileName shouldBe "settlement_detail_report_batch_11_20250307.csv.gz"
    result.get.dataStream.isDefined shouldBe true
    result.get.path.toString should endWith("settlement_detail_report_batch_11_20250307.csv")

    // Verify the decompressed data is accessible through the dataStream
    result.get.dataStream.get.acquireAndGet { decompressedStream =>
      val decompressedData = IOUtils.toByteArray(decompressedStream)
      decompressedData.length should be > 0
      // Convert to string to verify content structure
      val content = new String(decompressedData)
      content.length should be > 0
    }

    val msg = "Unzip File Success " + downloadResult.originalFileName
    val successStatus = Seq(
      Status(
        downloadResult.path.getName,
        downloadResult.path.toString,
        "enett_upc",
        TrackingStatus.Unzipped,
        msg,
        false,
        originalFileName = downloadResult.originalFileName
      )
    )
    verify(mockTracker).track(successStatus)(sqlContext, trackingData)
  }

  test("decompress GZFile with HDFS mode when IOStream disabled") {
    implicit val mockTracker: Tracker = mock[Tracker]
    val gzFile                        = getClass.getResource("/test-data/settlement_detail_report_batch_11_20250307.csv.gz").getFile

    val downloadResult = DownloadResult(
      new Path(gzFile),
      "settlement_detail_report_batch_11_20250307.csv.gz",
      None,
      None // No dataStream - should use HDFS mode
    )

    // Config without IOStream enabled (default)
    val configWithoutIOStream = rootConfig
      .getConfig("config-downloader.downloader_server_standard")

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(DateTime.now.minusDays(1).toString("yyyy-MM-dd"), "", "", "", ProcessMode.prod)
    )(configWithoutIOStream, "yyyy-MM-dd", "enett_upc", VaultCredentialConnector, VaultDecryptionHelper)

    val result = new UnZipFileProcessor(sftpConfig)(sqlContext, trackingData, mockTracker).apply(downloadResult)

    result.isDefined shouldBe true
    result.get.dataStream shouldBe None // HDFS mode doesn't return dataStream
    result.get.path.toString should include("settlement_detail_report_batch_11_20250307.csv")
  }

  test("decompress GZFile Failed with IOStream mode - exception handling") {
    implicit val mockTracker: Tracker = mock[Tracker]
    val gzFile                        = getClass.getResource("/test-data/settlement_detail_report_batch_11_20250307.csv.gz").getFile

    // Create a corrupted/invalid input stream that will cause GZIPInputStream to throw exception
    val corruptedData        = "This is not a valid GZIP file".getBytes
    val corruptedInputStream = new ByteArrayInputStream(corruptedData)
    val managedInputStream   = managed(corruptedInputStream)

    val downloadResult = DownloadResult(
      new Path(gzFile),
      "settlement_detail_report_batch_11_20250307.csv.gz",
      None,
      Some(managedInputStream)
    )

    val configWithIOStream = rootConfig
      .getConfig("config-downloader.downloader_server_standard")
      .withValue("using-io-stream", com.typesafe.config.ConfigValueFactory.fromAnyRef(true))

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(
        DateTime.now.minusDays(1).toString("yyyy-MM-dd"),
        "",
        "",
        "",
        ProcessMode.prod
      )
    )(
      configWithIOStream,
      "yyyy-MM-dd",
      "enett_upc",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    val result = new UnZipFileProcessor(sftpConfig)(sqlContext, trackingData, mockTracker).apply(downloadResult)

    result shouldBe None

    val expectedMessage = s"Unzip File failed ${downloadResult.originalFileName} exception: Not in GZIP format"
    val failureStatus = Seq(
      Status(
        downloadResult.path.getName,
        downloadResult.path.toString,
        "enett_upc",
        TrackingStatus.UnzippedFailed,
        expectedMessage,
        false,
        originalFileName = downloadResult.originalFileName
      )
    )

    verify(mockTracker).track(failureStatus)(sqlContext, trackingData)
  }

  test("decompress ZIP file Successfully with IOStream mode") {
    implicit val mockTracker: Tracker = mock[Tracker]
    val zipFile                       = getClass.getResource("/test-data/DailyVANsSettlement_304100_410032_20230126.zip").getFile

    // Create a FileInputStream and wrap it in a managed resource for IOStream mode
    val fileInputStream    = new FileInputStream(zipFile)
    val managedInputStream = managed(fileInputStream)

    val downloadResult = DownloadResult(
      new Path(zipFile),
      "DailyVANsSettlement_304100_410032_20230126.zip",
      None,                    // encryptedPath
      Some(managedInputStream) // dataStream
    )

    // Create config with IOStream enabled
    val configWithIOStream = rootConfig
      .getConfig("config-downloader.downloader_server_standard")
      .withValue("using-io-stream", com.typesafe.config.ConfigValueFactory.fromAnyRef(true))

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(
        DateTime.now.minusDays(1).toString("yyyy-MM-dd"),
        "",
        "",
        "",
        ProcessMode.prod
      )
    )(
      configWithIOStream,
      "yyyy-MM-dd",
      "enett_upc",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    val expectedOutputDir = new Path(zipFile.replace(".zip", ""))
    val result            = new UnZipFileProcessor(sftpConfig)(sqlContext, trackingData, mockTracker).apply(downloadResult)

    result.isDefined shouldBe true
    result.get.originalFileName shouldBe "DailyVANsSettlement_304100_410032_20230126.zip"
    result.get.dataStream.isDefined shouldBe true
    result.get.path shouldBe expectedOutputDir

    // Verify the combined data is accessible through the dataStream
    result.get.dataStream.get.acquireAndGet { combinedStream =>
      val combinedData = IOUtils.toByteArray(combinedStream)
      combinedData.length should be > 0
      // Convert to string to verify content structure (should contain data from multiple CSV files)
      val content = new String(combinedData)
      content.length should be > 0
    }

    val msg = "Unzip File Success " + downloadResult.originalFileName
    val successStatus = Seq(
      Status(
        downloadResult.path.getName,
        downloadResult.path.toString,
        "enett_upc",
        TrackingStatus.Unzipped,
        msg,
        false,
        originalFileName = downloadResult.originalFileName
      )
    )
    verify(mockTracker).track(successStatus)(sqlContext, trackingData)
  }

  test("decompress ZIP file Failed with IOStream mode - exception handling") {
    implicit val mockTracker: Tracker = mock[Tracker]
    val zipFile                       = getClass.getResource("/test-data/DailyVANsSettlement_304100_410032_20230126.zip").getFile

    // Create a failing input stream that will throw exception when read
    val failingInputStream = new ByteArrayInputStream(Array.empty[Byte]) {
      override def read(): Int                                   = throw new RuntimeException("Simulated IO failure")
      override def read(b: Array[Byte]): Int                     = throw new RuntimeException("Simulated IO failure")
      override def read(b: Array[Byte], off: Int, len: Int): Int = throw new RuntimeException("Simulated IO failure")
    }
    val managedInputStream = managed(failingInputStream)

    val downloadResult = DownloadResult(
      new Path(zipFile),
      "DailyVANsSettlement_304100_410032_20230126.zip",
      None,
      Some(managedInputStream)
    )

    val configWithIOStream = rootConfig
      .getConfig("config-downloader.downloader_server_standard")
      .withValue("using-io-stream", com.typesafe.config.ConfigValueFactory.fromAnyRef(true))

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(
        DateTime.now.minusDays(1).toString("yyyy-MM-dd"),
        "",
        "",
        "",
        ProcessMode.prod
      )
    )(
      configWithIOStream,
      "yyyy-MM-dd",
      "enett_upc",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    val result = new UnZipFileProcessor(sftpConfig)(sqlContext, trackingData, mockTracker).apply(downloadResult)

    result shouldBe None

    // Verify that failure tracking was called using ArgumentCaptor to capture the actual message
    val failureStatusCaptor = ArgumentCaptor.forClass(classOf[Seq[Status]])
    verify(mockTracker)
      .track(failureStatusCaptor.capture())(org.mockito.ArgumentMatchers.eq(sqlContext), org.mockito.ArgumentMatchers.eq(trackingData))

    val capturedStatus = failureStatusCaptor.getValue
    capturedStatus.head.fileName shouldBe downloadResult.path.getName
    capturedStatus.head.filePath shouldBe downloadResult.path.toString
    capturedStatus.head.fileListType shouldBe "enett_upc"
    capturedStatus.head.status shouldBe TrackingStatus.UnzippedFailed
    capturedStatus.head.details should include("Unzip File failed")
    capturedStatus.head.details should include(downloadResult.originalFileName)
    capturedStatus.head.isReprocess shouldBe false
    capturedStatus.head.originalFileName shouldBe downloadResult.originalFileName
  }

  test("verify decompressed data matches original file content - IOStream mode") {
    implicit val mockTracker: Tracker = mock[Tracker]
    val gzFile                        = getClass.getResource("/test-data/settlement_detail_report_batch_11_20250307.csv.gz").getFile
    val originalCsvFile               = getClass.getResource("/test-data/settlement_detail_report_batch_11_20250307.csv").getFile

    // Read the original uncompressed file content for comparison
    val originalContent = {
      val fileInputStream = new FileInputStream(originalCsvFile)
      try IOUtils.toString(fileInputStream, "UTF-8")
      finally fileInputStream.close()
    }

    // Create input stream for the compressed file
    val fileInputStream    = new FileInputStream(gzFile)
    val managedInputStream = managed(fileInputStream)

    val downloadResult = DownloadResult(
      new Path(gzFile),
      "settlement_detail_report_batch_11_20250307.csv.gz",
      None,
      Some(managedInputStream)
    )

    val configWithIOStream = rootConfig
      .getConfig("config-downloader.downloader_server_standard")
      .withValue("using-io-stream", com.typesafe.config.ConfigValueFactory.fromAnyRef(true))

    val sftpConfig = new SFTPDownloadConfigModule(
      Arguments(
        DateTime.now.minusDays(1).toString("yyyy-MM-dd"),
        "",
        "",
        "",
        ProcessMode.prod
      )
    )(
      configWithIOStream,
      "yyyy-MM-dd",
      "enett_upc",
      VaultCredentialConnector,
      VaultDecryptionHelper
    )

    val result = new UnZipFileProcessor(sftpConfig)(sqlContext, trackingData, mockTracker).apply(downloadResult)

    result.isDefined shouldBe true
    result.get.dataStream.isDefined shouldBe true

    // Verify the decompressed content matches the original file exactly
    result.get.dataStream.get.acquireAndGet { decompressedStream =>
      val decompressedContent = IOUtils.toString(decompressedStream, "UTF-8")

      // Content should match exactly
      decompressedContent shouldBe originalContent

      // Additional validation - check line count
      val decompressedLines = decompressedContent.split("\n")
      val originalLines     = originalContent.split("\n")
      decompressedLines.length shouldBe originalLines.length

      // Verify first few lines match (CSV headers and data)
      if (decompressedLines.nonEmpty && originalLines.nonEmpty) {
        decompressedLines.head shouldBe originalLines.head // Header row
        if (decompressedLines.length > 1 && originalLines.length > 1) {
          decompressedLines(1) shouldBe originalLines(1) // First data row
        }
      }
    }

    verify(mockTracker).track(any[Seq[Status]])(org.mockito.ArgumentMatchers.eq(sqlContext), org.mockito.ArgumentMatchers.eq(trackingData))
  }

}
