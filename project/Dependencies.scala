import sbt.{ExclusionRule, _}

object Dependencies {

  val version = new {
    val sparkassembly      = "3.3.1.0" //latest is here: https://gitlab.agodadev.io/it-data-frameworks/spark-assembly/-/tags
    val sparkutils         = "3.2.85"  //latest is here: https://gitlab.agodadev.io/it-data-frameworks/spark_frameworks/-/tags
    val hadooputils        = "2.4.15"  //latest is here: https://gitlab.agodadev.io/it-data-frameworks/common_frameworks/-/tags
    val environment        = "2.3.72"  //latest is here: https://gitlab.agodadev.io/it-data-frameworks/common_frameworks/-/tags
    val commonutils        = "1.0.46"  //latest is here: https://gitlab.agodadev.io/it-data-frameworks/common_frameworks/-/tags
    val scalatest          = "3.0.9"
    val scalamock          = "3.6.0"
    val adpmessagingclient = "2.9.0.0"
    val jsch               = "0.2.8"
    val scalaarm           = "2.0"
    val monix              = "2.3.3"
    val commonio           = "2.8.0"
    val mockito            = "3.7.7"
    val scalatags          = "0.9.2"
    val commonlibfinance   = "6.0.32"
    val commonutility      = "6.0.8"
    val hadoop             = "3.2.0"
    val h2                 = "1.4.200"
    val bouncycastle       = "1.69"
    val dwolla             = "0.3.0"
    val datanucleus        = "4.1.1"
    val agvaultScala       = "1.0.0"
    val oozieUtils         = "0.1.8"
    val bapiq              = "2.0.9"
    val sparkexcel         = "3.2.1_0.17.0"
    val mockJavamail       = "1.9"
    val commonfileclient   = "0.0.3"
    val poi                = "5.2.3"
    val pureConfig         = "0.17.2"
    val sparkXML           = "0.16.0"
    val macwire            = "2.5.8"
    val cronUtil           = "9.2.1"
  }

  val baseDependencies = Seq(
    "org.apache.hadoop" % "hadoop-common"                % version.hadoop,
    "org.apache.hadoop" % "hadoop-common"                % version.hadoop % "test" classifier "tests",
    "org.apache.hadoop" % "hadoop-mapreduce-client-core" % version.hadoop,
    "org.apache.hadoop" % "hadoop-mapreduce-client-core" % version.hadoop % "test" classifier "tests",
    "com.agoda.ml"     %% "commonutils"                  % version.commonutils
      exclude ("com.agoda.adp.messaging", "adp-messaging-client-scala_2.11"),
    "com.agoda.ml" %% "oozie-utils" % version.oozieUtils
    // https://mavenlibs.com/maven/dependency/org.mockito/mockito-inline

      excludeAll ExclusionRule(organization = "org.apache.hadoop", name = "hadoop-auth"),
    "com.agoda.ml"             %% "commonutils"                          % version.commonutils        % "test" classifier "tests",
    "com.agoda.ml"             %% "commonutils"                          % version.commonutils        % "test" classifier "tests",
    "com.agoda.ml"             %% "environment"                          % version.environment,
    "org.scalatest"            %% "scalatest"                            % version.scalatest,
    "org.scalamock"            %% "scalamock-scalatest-support"          % version.scalamock          % "test",
    "org.mockito"               % "mockito-inline"                       % "3.4.3"                    % "test",
    "com.agoda.adp.messaging"   % "adp-messaging-client"                 % version.adpmessagingclient exclude ("org.scala-lang", "scala-compiler"),
    "com.agoda.adp.messaging"   % "adp-messaging-log4j12"                % version.adpmessagingclient exclude ("org.scala-lang", "scala-compiler"),
    "com.agoda.adp.messaging"   % "adp-messaging-client-testutils"       % version.adpmessagingclient % "test",
    "com.jsuereth"             %% "scala-arm"                            % version.scalaarm,
    "io.monix"                 %% "monix-eval"                           % version.monix,
    "org.mockito"               % "mockito-core"                         % version.mockito,
    "commons-io"                % "commons-io"                           % version.commonio,
    "com.lihaoyi"              %% "scalatags"                            % version.scalatags,
    "org.bouncycastle"          % "bcprov-jdk15on"                       % version.bouncycastle,
    "com.dwolla"               %% "fs2-pgp"                              % version.dwolla,
    "org.datanucleus"           % "datanucleus-accessplatform-jdo-rdbms" % version.datanucleus        % "test",
    "com.agoda.commons"        %% "ag-vault-scala"                       % version.agvaultScala,
    "com.agoda.finance.commons" % "file-client"                          % version.commonfileclient,
    "com.cronutils"             % "cron-utils"                           % version.cronUtil
  ).map(d =>
    d excludeAll (
      ExclusionRule(organization = "com.fasterxml.jackson.core"),
      ExclusionRule(organization = "com.fasterxml.jackson.module")
    )
  )

  val commonFinanceDependencies = Seq(
    "com.agoda.finance" %% "commonfinance" % version.commonlibfinance
  ).map(d =>
    d excludeAll (
      ExclusionRule(organization = "com.fasterxml.jackson.core"),
      ExclusionRule(organization = "com.github.luben"),
      ExclusionRule(organization = "com.typesafe"),
      ExclusionRule(organization = "com.fasterxml.jackson.module"),
      ExclusionRule("shaded-ag-vault_2.12")
    )
  )

  val commonUtilityDependencies = Seq(
    "com.agoda.finance" %% "commonutility" % version.commonutility
  )

  val sparkDependencies = baseDependencies ++ commonFinanceDependencies ++ Seq(
    "com.agoda.ml"   %% "hadooputils"   % version.hadooputils,
    "com.agoda.ml"   %% "hadooputils"   % version.hadooputils   % "test" classifier "tests",
    "com.agoda.ml"   %% "sparkutils"    % version.sparkutils,
    "com.agoda.ml"   %% "sparkutils"    % version.sparkutils    % "test" classifier "tests",
    "com.agoda.ml"   %% "sparkassembly" % version.sparkassembly % Provided,
    "com.h2database"  % "h2"            % version.h2            % Test,
    "com.crealytics" %% "spark-excel"   % "3.3.1_0.18.5",
    "com.databricks" %% "spark-xml"     % version.sparkXML
  )

  val uploaderDependencies = Seq(
    "com.github.pureconfig"  %% "pureconfig"           % version.pureConfig,
    "com.agoda.bapiq"        %% "booking-query-client" % version.bapiq,
    "com.crealytics"         %% "spark-excel"          % version.sparkexcel,
    "org.jvnet.mock-javamail" % "mock-javamail"        % version.mockJavamail % Test
  )

  val downloaderDependencies = Seq(
    "org.apache.poi"    % "poi"  % version.poi,
    "com.github.mwiede" % "jsch" % version.jsch
  )

  val macwireDependencies = Seq(
    "com.softwaremill.macwire" %% "macros"     % version.macwire % "provided",
    "com.softwaremill.macwire" %% "macrosakka" % version.macwire % "provided",
    "com.softwaremill.macwire" %% "util"       % version.macwire,
    "com.softwaremill.macwire" %% "proxy"      % version.macwire
  )
}
