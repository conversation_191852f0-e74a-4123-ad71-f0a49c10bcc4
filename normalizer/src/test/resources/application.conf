# Configuration for TEST environment
include "test-env"
include "_generic_settings"

project-name = "Normalizer"
app-name = "Normalizer"


schema = "finance_normalizer"
current_env = "staging"

deploy {
  is-release = false
  global-cache-dependencies = false
  target-folder = /user/${hadoop.hdfs.user}/apps/${project-name}/${app-name}/qa/
}

hadoop.use-smart-table-loader = "false"
hadoop.clusterUrl = "https://hadoop-knox-sgprod.agoda.local:8442/gateway/agoda/yarn/cluster/app/"

normalizer: {
  include "normalizer_mapper.conf"

  lookbackdate = 1
  sources: ["test_data_table", "test_data_table2", "test_data_table3", "test_data_table4",
            "test_data_table5", "test_data_table6", "test_data_table7",
            "test_data_table_partition_format", "test_data_table8", "test_data_table9",
            "test_generic_offset_datamonth", "test_generic_offset_datamonth_gte",
            "test_generic_is_date_in_month_range", "test_generic_is_positive", "test_data_table10"
            "test_generic_is_date_in_month_range", "test_generic_is_positive",
            "test_generic_is_in_range_relative_to_process_date", "test_generic_datadate_lt_today",
            "test_generic_timestamp_lt_datadate", "test_select_expr_table"
            ]

  test_data_table {
    sourceName: "test_data_table"
    tableList: [{
      tablename: "finance_sftp_downloader.test_table"
      partitiondatekey: "datadate"
    }]
    enrichTableList: [{
      tablename: "finance_sftp_downloader.test_enrich_table"
      partitiondatekey: "datadate"
    }]
    datasetlogicfunction: [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "TT"}
    ]
  }

  test_data_table2 {
    sourceName: "test_data_table2"
    tableList: [{
      tablename: "finance_sftp_downloader.test_table2"
      partitiondatekey: "datadate"
    }]
    outputpartitionkey: [
      {partitionname: "source", value: "TX"}
    ]
  }

  test_data_table3 {
    sourceName: "test_data_table3"
    tableList: [{
      tablename: "finance_sftp_downloader.test_table3"
      partitiondatekey: "datadate"
    }]
    outputpartitionkey: [
      {partitionname: "source", value: "GG"}
    ]
  }

  test_data_table4 {
    sourceName: "test_data_table4"
    tableList: [{
      tablename: "finance_sftp_downloader.test_table4",
      extractfunctions: [
        {name: "generic_is_equal_to", constants: ["B1"], columns: ["b"]},
        {name: "generic_is_equal_to", constants: ["C1"], columns: ["c"]},
        {name: "marriott_transaction_within_date"}
      ]
    }]
    outputpartitionkey: [
      {partitionname: "source", value: "MAR"}
    ]
  }

  test_data_table5 {
    sourceName: "test_data_table5"
    tableList: [{
      tablename: "finance_sftp_downloader.test_table5",
      extractfunctions: [
        {name: "generic_is_equal_to", constants: ["B1"], columns: ["b"]},
        {name: "generic_is_equal_to", constants: ["C1"], columns: ["c"]},
        {name: "violet_messaginglog_previous_date"}
      ]
    }]
    outputpartitionkey: [
      {partitionname: "source", value: "VIO"}
    ]
  }
  test_data_table6 {
    sourceName: "test_data_table6"
    tableList: [{
      tablename: "finance_sftp_downloader.test_table6"
      extractfunctions: [
        {name: "generic_is_equal_to", constants: ["B1"], columns: ["b"]},
        {name: "generic_is_equal_to", constants: ["C1"], columns: ["c"]},
        {name: "fact_booking_within_month"}
      ]
    }]
    outputpartitionkey: [
      {partitionname: "source", value: "DBS"}
    ]
  }
  test_data_table7 {
    sourceName: "test_data_table7"
    tableList: [{
      tablename: "finance_sftp_downloader.test_table7"
      partitiondatekey: "datadate"
      extractfunctions: [
        {name: "generic_is_equal_to", constants: ["B1"], columns: ["b"]},
      ]
    }]
    enrichTableList: [{
      tablename: "finance_sftp_downloader.test_enrich_table7"
      partitiondatekey: "datadate"
      extractfunctions: [
        {name: "generic_is_equal_to", constants: ["B2"], columns: ["b"]},
      ]
    }]
    datasetlogicfunction: [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", colName: "ax"}
    ]
  }
  test_data_table_partition_format {
    sourceName: "test_data_table_partition_format"
    tableList: [{
      tablename: "finance_sftp_downloader.test_table_partition_format"
      partitiondatekey: "datadate"
      partitiondateformat: "yyyyMM"
    }]
    enrichTableList: [{
      tablename: "finance_sftp_downloader.test_enrich_table_partition_format"
      partitiondatekey: "datadate"
      partitiondateformat: "yyyyMM"
    }]
    datasetlogicfunction: [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "TT"}
    ]
  }
  test_data_table8 {
    sourceName: "test_data_table8"
    tableList: [{
      tablename: "finance_sftp_downloader.test_table8"
      extractfunctions: [
        {name: "generic_datadate_lte_today", columns: ["datadate"]}
      ]
    }]
    outputpartitionkey: [
      {partitionname: "source", value: "JTB"}
    ]
  }
  test_data_table9 {
    sourceName: "test_data_table9"
    tableList: [{
      tablename: "finance_sftp_downloader.test_table9"
      extractfunctions: [
        {name: "generic_offset_datadate", constants: ["1"], columns: ["datadate"]}
      ]
    }]
    outputpartitionkey: [
      {partitionname: "source", value: "JTB"}
    ]
  }

  test_data_table10 {
    sourceName: "test_data_table10"
    tableList: [{
      tablename: "finance_sftp_downloader.test_table10"
      partitiondatekey: "datadate"
    }]
  }

  test_generic_offset_datamonth{
    sourceName: "test_generic_offset_datamonth"
    tableList: [{
      tablename: "test_generic_offset_datamonth"
      extractfunctions: [
        { name: "generic_offset_datamonth", constants: ["1"], columns: ["datamonth"] }
      ]
    }]
  }

  test_generic_is_date_in_month_range{
    sourceName: "test_generic_is_date_in_month_range"
    tableList: [{
      tablename: "test_generic_is_date_in_month_range"
      extractfunctions: [
        { name: "generic_is_date_in_month_range", constants: ["-3", "-1", "yyyyMMdd"], columns: ["datadate"] }
      ]
    }]
  }

  test_generic_is_positive{
    sourceName: "test_generic_is_positive"
    tableList: [{
      tablename: "test_generic_is_positive"
      extractfunctions: [
        { name: "generic_is_positive", columns: ["a"] }
      ]
    }]
  }

  test_generic_offset_datamonth_gte {
    sourceName: "test_generic_offset_datamonth_gte"
    tableList: [{
      tablename: "test_generic_offset_datamonth_gte"
      extractfunctions: [
        { name: "generic_offset_datamonth_gte", constants: ["6"], columns: ["datamonth"] }
      ]
    }]
  }

   test_generic_is_in_range_relative_to_process_date{
    sourceName: "test_generic_is_in_range_relative_to_process_date"
        tableList: [{
          tablename: "test_generic_is_in_range_relative_to_process_date"
          extractfunctions: [
            { name: "generic_offset_date_greater_than_process_month", constants: ["3", "yyyy-MM-dd"], columns: ["datadate"] },
            { name: "generic_offset_date_less_than_process_month", constants: ["1", "yyyy-MM-dd"], columns: ["datadate"] }
          ]
        }]
    }

  test_generic_datadate_lt_today {
    sourceName: "test_generic_datadate_lt_today"
    tableList: [{
      tablename: "test_generic_datadate_lt_today"
      extractfunctions: [
        {name: "generic_datadate_lt_today", columns: ["datadate"]}
      ]
    }]
  }

  test_generic_timestamp_lt_datadate {
    sourceName: "test_generic_timestamp_lt_datadate"
    tableList: [{
      tablename: "test_generic_timestamp_lt_datadate"
      extractfunctions: [
        {name: "generic_timestamp_lt_datadate", columns: ["event_time"]}
      ]
    }]
  }

  test_select_expr_table {
    sourceName: "test_select_expr_table"
    tableList: [{
      tablename: "finance_sftp_downloader.test_select_expr_table"
      partitiondatekey: "datadate"
      selectcolumn: ["a", "b", "c", "a * 2 as double_a", "concat(b, '_', c) as b_c"]
    }]
  }

  table: {
    name: ${schema}".test_normalized_data"
    mode: "overwrite" //"default is append"
    trackColumns: ["datadate"]
    partition: [
      {
        key: "datadate"
        value: "0"
      },
      {
        value: "outputpartitionkey"
      }
    ]
  }
}

app {
  adpMessaging {
    apiKey: "finance"
    appName: "Normalizer.qa.processor"
    sendAllMsg = false
  }
}

tracking-status {
  tablename: ${schema}".insurance_normalizer_status"
}

result {
  mail {
    alert-sender = "<EMAIL> (<EMAIL>)"
    alert-recipients = "<EMAIL>"
  }
}

status: {
  path: ""
  filename: "normailizer_status_{date}"
}

schema = "generic_mirror"
mirror-status: ${schema}".mirror_normalizer_status"
