
mappers: [
  {
    aliasColName: "ax", dataType: "DecimalType", precision: 18, scale: 4
    colMapper: [
      { sourceName: ${test_data_table.sourceName}, colName: "a" },
      { sourceName: ${test_data_table2.sourceName},
      function: { name:  "generic_sum", columns : ["e","e2"] }  },
      { sourceName: ${test_data_table3.sourceName}, colName: "g1" },
      { sourceName: ${test_data_table4.sourceName}, colName: "a" },
      { sourceName: ${test_data_table5.sourceName}, colName: "a" },
      { sourceName: ${test_data_table6.sourceName}, colName: "a" },
      { sourceName: ${test_data_table7.sourceName}, colName: "a" },
      { sourceName: ${test_data_table_partition_format.sourceName}, colName: "a" }
      { sourceName: ${test_data_table10.sourceName}, colName: "a" },
      { sourceName: ${test_select_expr_table.sourceName}, colName: "a" },
    ]
  },
  {
     aliasColName: "bx", dataType: "StringType" ,
     colMapper: [
       { sourceName: ${test_data_table.sourceName}, colName: "b" },
       { sourceName: ${test_data_table2.sourceName}, colName: "f" },
       { sourceName: ${test_data_table3.sourceName}, colName: "g2" },
       { sourceName: ${test_data_table4.sourceName}, colName: "b" },
       { sourceName: ${test_data_table5.sourceName}, colName: "b" },
       { sourceName: ${test_data_table6.sourceName}, colName: "b" },
       { sourceName: ${test_data_table7.sourceName}, colName: "b" },
       { sourceName: ${test_data_table_partition_format.sourceName}, colName: "b" }
       { sourceName: ${test_data_table10.sourceName}, colName: "b" },
       { sourceName: ${test_select_expr_table.sourceName}, colName: "b" },
     ]
  },
  {
     aliasColName: "cx", dataType: "StringType" ,
     colMapper: [
       { sourceName: ${test_data_table.sourceName}, colName: "c", mapper: [
         {key : "SOLD", value : "SALE"},
         {key : "CANCELLED ACCEPTED", value : "REFUND"},
         {key: "(?i)^accepted(?i)-.*", value: "ADJUSTMENT"},
         {key: "DEFAULT_MAP_VALUE", value: "UNKNOWN"}
       ] },
       { sourceName: ${test_data_table2.sourceName}, colName: "g" },
       { sourceName: ${test_data_table3.sourceName}, colName: "g3" },
       { sourceName: ${test_data_table4.sourceName}, colName: "c" },
       { sourceName: ${test_data_table5.sourceName}, colName: "c" },
       { sourceName: ${test_data_table6.sourceName}, colName: "c" },
       { sourceName: ${test_data_table7.sourceName}, colName: "c" },
       { sourceName: ${test_data_table_partition_format.sourceName}, colName: "c" }
       { sourceName: ${test_data_table10.sourceName}, colName: "c" },
       { sourceName: ${test_select_expr_table.sourceName}, colName: "c" },
     ]
  },
  {
     aliasColName: "dx", dataType: "StringType" ,
     colMapper: [
       { sourceName: ${test_data_table.sourceName}},
       { sourceName: ${test_data_table2.sourceName},
       function: { name:  "generic_exclude_suffix", columns : ["h"], constants : ["C", "D"] } },
       { sourceName: ${test_data_table3.sourceName}},
       { sourceName: ${test_data_table4.sourceName}},
       { sourceName: ${test_data_table5.sourceName}},
       { sourceName: ${test_data_table6.sourceName}},
       { sourceName: ${test_data_table7.sourceName}},
       { sourceName: ${test_data_table_partition_format.sourceName}}
       { sourceName: ${test_data_table10.sourceName}},
       { sourceName: ${test_select_expr_table.sourceName}},
     ]
  },
  {
    aliasColName: "ex", dataType: "BooleanType" ,
    colMapper: [
      { sourceName: ${test_data_table.sourceName}},
      { sourceName: ${test_data_table2.sourceName},
        function: { name:  "generic_or_logical", columns : ["h"], constants = ["TT"], operators : ["=="] } },
      { sourceName: ${test_data_table2.sourceName},
              function: { name:  "generic_multiconstants_or_logical", columns : ["h","g"], multiconstants = [["KK","OO"],["OK","KO"]]} },
      { sourceName: ${test_data_table3.sourceName}},
      { sourceName: ${test_data_table4.sourceName}},
      { sourceName: ${test_data_table5.sourceName}},
      { sourceName: ${test_data_table6.sourceName}},
      { sourceName: ${test_data_table7.sourceName}},
      { sourceName: ${test_data_table_partition_format.sourceName}}
      { sourceName: ${test_data_table10.sourceName}},
      { sourceName: ${test_select_expr_table.sourceName}},
    ]
  },
  {
    aliasColName: "datadate", dataType: "IntegerType" ,
    colMapper: [
      { sourceName: ${test_data_table.sourceName}, colName: "datadate" },
      { sourceName: ${test_data_table2.sourceName}, value: 20200801 },
      { sourceName: ${test_data_table3.sourceName}, colName: "datadate" },
      { sourceName: ${test_data_table4.sourceName}, colName: "datadate"},
      { sourceName: ${test_data_table5.sourceName}, colName: "datadate"},
      { sourceName: ${test_data_table6.sourceName}, colName: "datadate"},
      { sourceName: ${test_data_table7.sourceName}, colName: "datadate"},
      { sourceName: ${test_data_table_partition_format.sourceName}, colName: "datadate" }
      { sourceName: ${test_data_table10.sourceName}, dateValue: "get_process_date" },
      { sourceName: ${test_select_expr_table.sourceName}, colName: "datadate" },
    ]
  },
  {
    aliasColName: "ggid", dataType: "LongType" ,
    colMapper: [
      { sourceName: ${test_data_table.sourceName}},
      { sourceName: ${test_data_table2.sourceName}},
      { sourceName: ${test_data_table3.sourceName},
        function: {
          name: "generic_convert_string_to_long", columns: ["ggid"]
        }},
      { sourceName: ${test_data_table4.sourceName}},
      { sourceName: ${test_data_table5.sourceName}},
      { sourceName: ${test_data_table6.sourceName}},
      { sourceName: ${test_data_table7.sourceName}},
      { sourceName: ${test_data_table_partition_format.sourceName}}
      { sourceName: ${test_data_table10.sourceName}},
      { sourceName: ${test_select_expr_table.sourceName}},
    ]
  },
  {
    aliasColName: "meta_data", dataType: "StringType" ,
    colMapper: [
      { sourceName: ${test_data_table.sourceName}},
      { sourceName: ${test_data_table2.sourceName},
        json: [
          {key : "mb", value : "dx"},
          {key : "ma", value : "g"},
        ]},
      { sourceName: ${test_data_table3.sourceName}},
      { sourceName: ${test_data_table4.sourceName}},
      { sourceName: ${test_data_table5.sourceName}},
      { sourceName: ${test_data_table6.sourceName}},
      { sourceName: ${test_data_table7.sourceName}},
      { sourceName: ${test_data_table_partition_format.sourceName}}
      { sourceName: ${test_data_table10.sourceName}},
    ]
  }
]