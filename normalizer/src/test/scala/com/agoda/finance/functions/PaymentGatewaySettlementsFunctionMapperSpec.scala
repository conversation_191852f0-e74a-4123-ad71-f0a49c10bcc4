package com.agoda.finance.functions

import com.agoda.finance.functions.specific.PaymentGatewaySettlements.{
  AdyenSettlementLogic,
  AlipayOspSettlementLogic,
  NttSettlementLogic,
  PrimeroSettlementLogic
}
import com.agoda.finance.functions.utils.PaymentGatewaySettlementUtils
import com.agoda.ml.spark.DataFrameMatchers.beEqualTo
import com.agoda.ml.spark.{HiveSupport, SparkSharedLocalTest}
import org.apache.spark.sql.functions.{array, col}
import org.scalatest.{FunSuite, Matchers}

class PaymentGatewaySettlementsFunctionMapperSpec extends FunSuite with SparkSharedLocalTest with HiveSupport with Matchers {
  import sqlContext.implicits._

  test("genericPaymentSettlementFunction - get_transaction_type") {
    val baseDf = Seq(
      "12345-1-67890", // Valid reference with type 1
      "12345-3-67890", // Valid reference with type 3
      "invalid",       // Invalid reference (returns 1)
      "P12345",        // P-prefixed number (returns 1)
      "B12345"         // B-prefixed number (returns 1)
    ).toDF("reference")

    val res = PaymentGatewaySettlementsFunctionMapper.genericPaymentSettlementFunction(
      Some("get_transaction_type"),
      Vector.empty,
      Vector(col("reference"))
    )

    val expect   = Some(PaymentGatewaySettlementUtils.getTransactionType(col("reference")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("genericPaymentSettlementFunction - get_prebooking_id") {
    val baseDf = Seq(
      "12345-1-67890", // Valid sale reference
      "12345-3-67890", // Valid refund reference (no prebooking)
      "P12345",        // Valid P-prefixed
      "invalid",       // Invalid reference
      "B12345"         // B-prefixed (no prebooking)
    ).toDF("reference")

    val res = PaymentGatewaySettlementsFunctionMapper.genericPaymentSettlementFunction(
      Some("get_prebooking_id"),
      Vector.empty,
      Vector(col("reference"))
    )

    val expect   = Some(PaymentGatewaySettlementUtils.getPrebookingId(col("reference")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("genericPaymentSettlementFunction - get_itinerary_id") {
    val baseDf = Seq(
      "12345-3-67890", // Valid refund reference
      "12345-1-67890", // Valid sale reference (no itinerary)
      "invalid",       // Invalid reference
      "P12345"         // P-prefixed (no itinerary)
    ).toDF("reference")

    val res = PaymentGatewaySettlementsFunctionMapper.genericPaymentSettlementFunction(
      Some("get_itinerary_id"),
      Vector.empty,
      Vector(col("reference"))
    )

    val expect   = Some(PaymentGatewaySettlementUtils.getItineraryId(col("reference")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("genericPaymentSettlementFunction - get_hotel_itinerary_id") {
    val baseDf = Seq(
      "12345-10-67890", // Valid hotel reference
      "12345-1-67890",  // Valid sale reference (no hotel)
      "invalid",        // Invalid reference
      "P12345"          // P-prefixed (no hotel)
    ).toDF("reference")

    val res = PaymentGatewaySettlementsFunctionMapper.genericPaymentSettlementFunction(
      Some("get_hotel_itinerary_id"),
      Vector.empty,
      Vector(col("reference"))
    )

    val expect   = Some(PaymentGatewaySettlementUtils.getHotelItineraryId(col("reference")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("genericPaymentSettlementFunction - convert_datestring_to_bigint") {
    val baseDf = Seq(
      "2025-06-04 15:30:00"
    ).toDF("date")

    val res = PaymentGatewaySettlementsFunctionMapper.genericPaymentSettlementFunction(
      Some("convert_datestring_to_bigint"),
      Vector.empty,
      Vector(col("date"))
    )

    val expect   = Some(PaymentGatewaySettlementUtils.convertDateStringtoBigInt(col("date")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("genericPaymentSettlementFunction - get_event_type_id") {
    val baseDf = Seq(
      ("33", "PAYMENT"), // FileType 33, event "PAYMENT" -> true in reconciliation rules -> "2"
      ("33", "REFUND"),  // FileType 33, event "REFUND" -> true in reconciliation rules -> "4"
      ("33", "UNKNOWN"), // FileType 33, event "UNKNOWN" -> false in reconciliation rules -> null
      ("99", "PAYMENT")  // Unknown file type -> true in reconciliation rules -> null (no event mapping)
    ).toDF("fileType", "event")

    val res = PaymentGatewaySettlementsFunctionMapper.genericPaymentSettlementFunction(
      Some("get_event_type_id"),
      Vector("33"),
      Vector(col("event"))
    )

    val expect   = Some(PaymentGatewaySettlementUtils.getEventTypeId("33")(col("event")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("genericPaymentSettlementFunction - is_used_for_reconciliation") {
    val baseDf = Seq(
      ("33", "PAYMENT"), // FileType 33, PAYMENT -> true
      ("33", "REFUND"),  // FileType 33, REFUND -> true
      ("33", "CANCEL"),  // FileType 33, CANCEL -> true
      ("33", "UNKNOWN"), // FileType 33, unknown event -> false
      ("99", "PAYMENT")  // Unknown file type -> true
    ).toDF("fileType", "event")

    val res = PaymentGatewaySettlementsFunctionMapper.genericPaymentSettlementFunction(
      Some("is_used_for_reconciliation"),
      Vector("33"),
      Vector(col("event"))
    )

    val expect   = Some(PaymentGatewaySettlementUtils.isUsedForReconciliation("33")(col("event")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("genericPaymentSettlementFunction - is_gateway_transaction") {
    val baseDf = Seq(
      ("20", "PAYMENTCOST"),     // FileType 33, PAYMENTCOST -> true
      ("20", "SETTLED"),         // FileType 33, SETTLED -> true
      ("20", "MANUALCORRECTED"), // FileType 33, MANUALCORRECTED -> true
      ("20", "UNKNOWN"),         // FileType 33, UNKNOWN -> false
      ("99", "PAYMENT")          // Unknown file type -> true
    ).toDF("fileType", "event")

    val res = PaymentGatewaySettlementsFunctionMapper.genericPaymentSettlementFunction(
      Some("is_gateway_transaction"),
      Vector("20"),
      Vector(col("event"))
    )

    val expect   = Some(PaymentGatewaySettlementUtils.isGatewayTransaction("20")(col("event")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("genericPaymentSettlementFunction - convert_double_to_string") {
    val baseDf = Seq(123.456, 789.012).toDF("amount")
    val res = PaymentGatewaySettlementsFunctionMapper.genericPaymentSettlementFunction(
      Some("convert_double_to_string"),
      Vector("2"),
      Vector(col("amount"))
    )

    val expect   = Some(PaymentGatewaySettlementUtils.formatDoubleToStringWithDecimalPlace(2)(col("amount")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("genericPaymentSettlementFunction - format_string_to_uppercase") {
    val baseDf = Seq("abc", "", null, " ", "ABC").toDF("stringData")
    val res = PaymentGatewaySettlementsFunctionMapper.genericPaymentSettlementFunction(
      Some("format_string_to_uppercase"),
      Vector(),
      Vector(col("stringData"))
    )

    val expect   = Some(PaymentGatewaySettlementUtils.formatStringToUpperCase(col("stringData")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("alipayOspSettlementFunction - alipay_osp_get_source_amount") {
    val baseDf = Seq(("10000", "GCash"), ("5050", "GCash"), ("0", "GCash")).toDF("amount", "payment_method_type")
    val res = PaymentGatewaySettlementsFunctionMapper.alipayOspSettlementFunction(
      Some("alipay_osp_get_source_amount"),
      Vector(col("amount"), col("payment_method_type"))
    )

    val expect   = Some(AlipayOspSettlementLogic.getSourceAmount(col("amount"), col("payment_method_type")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("alipayOspSettlementFunction - alipay_osp_get_source_amount for TOSSPAY") {
    val baseDf = Seq(("10000", "TOSSPAY"), ("5050", "TOSSPAY"), ("0", "TOSSPAY")).toDF("amount", "payment_method_type")
    val res = PaymentGatewaySettlementsFunctionMapper.alipayOspSettlementFunction(
      Some("alipay_osp_get_source_amount"),
      Vector(col("amount"), col("payment_method_type"))
    )

    val expect   = Some(AlipayOspSettlementLogic.getSourceAmount(col("amount"), col("payment_method_type")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("alipayOspSettlementFunction - alipay_osp_get_fee_amount") {
    val baseDf = Seq("1000", "505", "0").toDF("fee")
    val res = PaymentGatewaySettlementsFunctionMapper.alipayOspSettlementFunction(
      Some("alipay_osp_get_fee_amount"),
      Vector(col("fee"))
    )

    val expect   = Some(AlipayOspSettlementLogic.getFeeAmount(col("fee")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("alipayOspSettlementFunction - alipay_osp_get_received_amount") {
    val baseDf = Seq(("10000", "1000"), ("5050", "505"), ("0", "0")).toDF("settlement", "fee")
    val res = PaymentGatewaySettlementsFunctionMapper.alipayOspSettlementFunction(
      Some("alipay_osp_get_received_amount"),
      Vector(col("settlement"), col("fee"))
    )

    val expect   = Some(AlipayOspSettlementLogic.getReceivedAmount(col("settlement"), col("fee")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("adyenSettlementFunction - adyen_get_source_amount") {
    val baseDf = Seq(
      ("100", "USD", "success"),
      ("200", "EUR", "success")
    ).toDF("amount", "currency", "status")

    val res = PaymentGatewaySettlementsFunctionMapper.adyenSettlementFunction(
      Some("adyen_get_source_amount"),
      Vector(col("amount"), col("currency"), col("status"))
    )

    val expect   = Some(AdyenSettlementLogic.getSourceAmount(array(col("amount"), col("currency"), col("status"))))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("paymentSettlementFunction - should handle index constants correctly") {
    val baseDf = Seq(
      ("val0", "val1", "val2", "val3", "val4", "val5", "val6")
    ).toDF("col0", "col1", "col2", "col3", "col4", "col5", "col6")

    // Test FIRST through SEVENTH constants with adyen_get_received_amount which uses all indices
    val res = PaymentGatewaySettlementsFunctionMapper.paymentSettlementFunction(
      Some("adyen_get_received_amount"),
      Vector.empty,
      Vector(
        col("col" + PaymentGatewaySettlementsFunctionMapper.FIRST),
        col("col" + PaymentGatewaySettlementsFunctionMapper.SECOND),
        col("col" + PaymentGatewaySettlementsFunctionMapper.THIRD),
        col("col" + PaymentGatewaySettlementsFunctionMapper.FOURTH),
        col("col" + PaymentGatewaySettlementsFunctionMapper.FIFTH),
        col("col" + PaymentGatewaySettlementsFunctionMapper.SIXTH),
        col("col" + PaymentGatewaySettlementsFunctionMapper.SEVENTH)
      )
    )

    val expect = Some(
      AdyenSettlementLogic.getReceivedAmount(
        col("col0"),
        col("col1"),
        col("col2"),
        col("col3"),
        col("col4"),
        col("col5"),
        col("col6")
      )
    )
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("paymentSettlementFunction - should handle Adyen function") {
    val baseDf = Seq(
      ("100", "USD", "success")
    ).toDF("amount", "currency", "status")

    val res = PaymentGatewaySettlementsFunctionMapper.paymentSettlementFunction(
      Some("adyen_get_source_amount"),
      Vector.empty,
      Vector(col("amount"), col("currency"), col("status"))
    )

    val expect   = Some(AdyenSettlementLogic.getSourceAmount(array(col("amount"), col("currency"), col("status"))))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("paymentSettlementFunction - should handle generic function") {
    val baseDf = Seq(123.456).toDF("amount")

    val res = PaymentGatewaySettlementsFunctionMapper.paymentSettlementFunction(
      Some("convert_double_to_string"),
      Vector("2"),
      Vector(col("amount"))
    )

    val expect   = Some(PaymentGatewaySettlementUtils.formatDoubleToStringWithDecimalPlace(2)(col("amount")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("paymentSettlementFunction - should handle generic function format_string_to_double_with_rounding") {
    val baseDf = Seq("123.456", "123.45679", "123.45623").toDF("amount")

    val res = PaymentGatewaySettlementsFunctionMapper.paymentSettlementFunction(
      Some("format_string_to_double_with_rounding"),
      Vector("4"),
      Vector(col("amount"))
    )

    val expect   = Some(PaymentGatewaySettlementUtils.formatStringToDoubleWithRounding(4)(col("amount")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("paymentSettlementFunction - should handle Primero function for getSourceCurrency") {
    val baseDf = Seq(
      ("settled", "INR", "USD")
    ).toDF("type", "grossCurrency", "netCurrency")

    val res = PaymentGatewaySettlementsFunctionMapper.paymentSettlementFunction(
      Some("primero_get_source_currency"),
      Vector.empty,
      Vector(col("type"), col("grossCurrency"), col("netCurrency"))
    )

    val expect   = Some(PrimeroSettlementLogic.getSourceCurrency(col("type"), col("grossCurrency"), col("netCurrency")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("paymentSettlementFunction - should handle Primero function for primeroGetExchangeRate") {
    val baseDf = Seq(
      ("settled", "100.0000", "0", "0", "0", "0", "0", "0", "0")
    ).toDF("type", "grossCredit", "grossDebit", "netCredit", "netDebit", "commission", "brazilianTaxesNc", "interchange", "markUp")

    val res = PaymentGatewaySettlementsFunctionMapper.paymentSettlementFunction(
      Some("primero_get_exchange_rate"),
      Vector.empty,
      Vector(
        col("type"),
        col("grossCredit"),
        col("grossDebit"),
        col("netCredit"),
        col("netDebit"),
        col("commission"),
        col("brazilianTaxesNc"),
        col("interchange"),
        col("markUp")
      )
    )

    val expect = Some(
      PrimeroSettlementLogic.getExchangeRate(
        col("type"),
        col("grossCredit"),
        col("grossDebit"),
        col("netCredit"),
        col("netDebit"),
        col("commission"),
        col("brazilianTaxesNc"),
        col("interchange"),
        col("markUp")
      )
    )
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("paymentSettlementFunction - should handle Primero function for getReceivedAmount") {
    val baseDf = Seq(
      ("100.0000", "0", "0", "0", "0", "0")
    ).toDF("netCredit", "netDebit", "commission", "brazilianTaxesNc", "interchange", "markUp")

    val res = PaymentGatewaySettlementsFunctionMapper.paymentSettlementFunction(
      Some("primero_get_received_amount"),
      Vector.empty,
      Vector(col("netCredit"), col("netDebit"), col("commission"), col("brazilianTaxesNc"), col("interchange"), col("markUp"))
    )

    val expect = Some(
      PrimeroSettlementLogic.getReceivedAmount(
        col("netCredit"),
        col("netDebit"),
        col("commission"),
        col("brazilianTaxesNc"),
        col("interchange"),
        col("markUp")
      )
    )
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("paymentSettlementFunction - should handle Primero function for getEventTypeId") {
    val baseDf = Seq(
      ("Settled", "10", "0", "10", "5"),
      ("Refund", "10", "0", "10", "0")
    ).toDF("eventType", "grossCredit", "grossDebit", "netCredit", "netDebit")
    val res = PaymentGatewaySettlementsFunctionMapper.paymentSettlementFunction(
      Some("primero_get_event_type_id"),
      Vector("20"),
      Vector(col("eventType"), col("grossCredit"), col("grossDebit"), col("netCredit"), col("netDebit"))
    )

    val expect =
      Some(PrimeroSettlementLogic.getEventTypeId("20")(col("eventType"), col("grossCredit"), col("grossDebit"), col("netCredit"), col("netDebit")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("paymentSettlementFunction - should handle Primero function for getFeeAmount") {
    val baseDf = Seq(
      ("0", "1.0"),
      ("10.2", "5.5")
    ).toDF("commission", "brazilian_taxes_nc")
    val res = PaymentGatewaySettlementsFunctionMapper.paymentSettlementFunction(
      Some("primero_get_fee_amount"),
      Vector.empty,
      Vector(col("commission"), col("brazilian_taxes_nc"))
    )

    val expect   = Some(PrimeroSettlementLogic.getFeeAmount(col("commission"), col("brazilian_taxes_nc")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("paymentSettlementFunction - should handle Alipay OSP function") {
    val baseDf = Seq(("10000", "GCash")).toDF("amount", "payment_method_type")

    val res = PaymentGatewaySettlementsFunctionMapper.paymentSettlementFunction(
      Some("alipay_osp_get_source_amount"),
      Vector.empty,
      Vector(col("amount"), col("payment_method_type"))
    )

    val expect   = Some(AlipayOspSettlementLogic.getSourceAmount(col("amount"), col("payment_method_type")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("paymentSettlementFunction - should handle ntt function for nttGetExchangeRate") {
    val baseDf = Seq(
      ("100.0000", "0", "0", "0", "0", "0")
    ).toDF("capturedPc", "payableSC", "commissionSC", "markupSC", "schemeFeesSC", "interchangeSC")

    val res = PaymentGatewaySettlementsFunctionMapper.paymentSettlementFunction(
      Some("ntt_get_exchange_rate"),
      Vector.empty,
      Vector(
        col("capturedPc"),
        col("payableSC"),
        col("commissionSC"),
        col("markupSC"),
        col("schemeFeesSC"),
        col("interchangeSC")
      )
    )

    val expect = Some(
      NttSettlementLogic.getExchangeRate(
        col("capturedPc"),
        col("payableSC"),
        col("commissionSC"),
        col("markupSC"),
        col("schemeFeesSC"),
        col("interchangeSC")
      )
    )
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("paymentSettlementFunction - should handle ntt function for getReceivedAmount") {
    val baseDf = Seq(
      ("100.0000", "0", "0", "0", "0")
    ).toDF("payableSC", "commissionSC", "markupSC", "schemeFeesSC", "interchangeSC")

    val res = PaymentGatewaySettlementsFunctionMapper.paymentSettlementFunction(
      Some("ntt_get_received_amount"),
      Vector.empty,
      Vector(
        col("payableSC"),
        col("commissionSC"),
        col("markupSC"),
        col("schemeFeesSC"),
        col("interchangeSC")
      )
    )

    val expect = Some(
      NttSettlementLogic.getReceivedAmount(
        col("payableSC"),
        col("commissionSC"),
        col("markupSC"),
        col("schemeFeesSC"),
        col("interchangeSC")
      )
    )
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("paymentSettlementFunction - should handle ntt function for getEventTypeId") {
    val baseDf = Seq(
      ("Settled", "10"),
      ("Refund", "10")
    ).toDF("eventType", "capturedPc")
    val res = PaymentGatewaySettlementsFunctionMapper.paymentSettlementFunction(
      Some("ntt_get_event_type_id"),
      Vector("10"),
      Vector(col("eventType"), col("capturedPc"))
    )

    val expect =
      Some(NttSettlementLogic.getEventTypeId("10")(col("eventType"), col("capturedPc")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)

    expectDf should beEqualTo(resultDf)
  }

  test("paymentSettlementFunction - should handle unknown function") {
    val res = PaymentGatewaySettlementsFunctionMapper.paymentSettlementFunction(
      Some("unknown_function"),
      Vector.empty,
      Vector(col("dummy"))
    )

    res shouldBe None
  }

  test("juspaySettlementFunction - juspay_get_gateway_id") {
    val baseDf = Seq("BOKU", "RAZORPAY").toDF("gatewayName")
    val res = PaymentGatewaySettlementsFunctionMapper.juspaySettlementFunction(
      Some("juspay_get_gateway_id"),
      Vector.empty,
      Vector(col("gatewayName"))
    )
    val expect   = Some(com.agoda.finance.functions.specific.PaymentGatewaySettlements.JuspaySettlementLogic.getGatewayId(col("gatewayName")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)
    expectDf should beEqualTo(resultDf)
  }

  test("juspaySettlementFunction - juspay_get_source_amount") {
    val baseDf = Seq(("100.1234", "50.1234"), ("0", "0")).toDF("grossCredit", "grossDebit")
    val res = PaymentGatewaySettlementsFunctionMapper.juspaySettlementFunction(
      Some("juspay_get_source_amount"),
      Vector.empty,
      Vector(col("grossCredit"), col("grossDebit"))
    )
    val expect = Some(
      com.agoda.finance.functions.specific.PaymentGatewaySettlements.JuspaySettlementLogic.getSourceAmount(col("grossCredit"), col("grossDebit"))
    )
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)
    expectDf should beEqualTo(resultDf)
  }

  test("juspaySettlementFunction - juspay_get_fee_amount") {
    val baseDf = Seq(("1.1", "2.2", "3.3", "4.4"), ("0", "0", "0", "0")).toDF("commission", "schemeFees", "interchange", "markup")
    val res = PaymentGatewaySettlementsFunctionMapper.juspaySettlementFunction(
      Some("juspay_get_fee_amount"),
      Vector.empty,
      Vector(col("commission"), col("schemeFees"), col("interchange"), col("markup"))
    )
    val expect = Some(
      com.agoda.finance.functions.specific.PaymentGatewaySettlements.JuspaySettlementLogic
        .getFeeAmount(col("commission"), col("schemeFees"), col("interchange"), col("markup"))
    )
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)
    expectDf should beEqualTo(resultDf)
  }

  test("juspaySettlementFunction - juspay_get_exchange_rate") {
    val baseDf = Seq(("1.0", "100", "50", "60", "10", "5", "2", "1", "0")).toDF(
      "exchangeRate",
      "grossCredit",
      "grossDebit",
      "netCredit",
      "netDebit",
      "commission",
      "schemeFees",
      "interchange",
      "markup"
    )
    val res = PaymentGatewaySettlementsFunctionMapper.juspaySettlementFunction(
      Some("juspay_get_exchange_rate"),
      Vector.empty,
      Vector(
        col("exchangeRate"),
        col("grossCredit"),
        col("grossDebit"),
        col("netCredit"),
        col("netDebit"),
        col("commission"),
        col("schemeFees"),
        col("interchange"),
        col("markup")
      )
    )
    val expect = Some(
      com.agoda.finance.functions.specific.PaymentGatewaySettlements.JuspaySettlementLogic.getExchangeRate(
        col("exchangeRate"),
        col("grossCredit"),
        col("grossDebit"),
        col("netCredit"),
        col("netDebit"),
        col("commission"),
        col("schemeFees"),
        col("interchange"),
        col("markup")
      )
    )
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)
    expectDf should beEqualTo(resultDf)
  }

  test("juspaySettlementFunction - juspay_get_received_amount") {
    val baseDf = Seq(("1.0", "100", "50", "60", "10", "5", "2", "1", "0")).toDF(
      "exchangeRate",
      "grossCredit",
      "grossDebit",
      "netCredit",
      "netDebit",
      "commission",
      "schemeFees",
      "interchange",
      "markup"
    )
    val res = PaymentGatewaySettlementsFunctionMapper.juspaySettlementFunction(
      Some("juspay_get_received_amount"),
      Vector.empty,
      Vector(
        col("exchangeRate"),
        col("grossCredit"),
        col("grossDebit"),
        col("netCredit"),
        col("netDebit"),
        col("commission"),
        col("schemeFees"),
        col("interchange"),
        col("markup")
      )
    )
    val expect = Some(
      com.agoda.finance.functions.specific.PaymentGatewaySettlements.JuspaySettlementLogic.getReceivedAmount(
        col("exchangeRate"),
        col("grossCredit"),
        col("grossDebit"),
        col("netCredit"),
        col("netDebit"),
        col("commission"),
        col("schemeFees"),
        col("interchange"),
        col("markup")
      )
    )
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)
    expectDf should beEqualTo(resultDf)
  }

  test("juspaySettlementFunction - juspay_get_mid") {
    val baseDf =
      Seq(("AgodaCOM123", "SOME_US_BANK_US_"), ("OtherMerchant", "SOME_US_BANK_US_"), ("AgodaCOM456", "SOME_SG_BANK_SG_"), ("AgodaCOM789", null))
        .toDF("merchantAccount", "aquirer")
    val res = PaymentGatewaySettlementsFunctionMapper.juspaySettlementFunction(
      Some("juspay_get_mid"),
      Vector.empty,
      Vector(col("merchantAccount"), col("aquirer"))
    )
    val expect =
      Some(com.agoda.finance.functions.specific.PaymentGatewaySettlements.JuspaySettlementLogic.getMid(col("merchantAccount"), col("aquirer")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)
    expectDf should beEqualTo(resultDf)
  }

  test("juspaySettlementFunction - juspay_get_ar_event_type_id") {
    val baseDf = Seq(("10", "5", "SETTLED"), ("5", "10", "REFUND"), ("5", "10", "UNKNOWN")).toDF("grossCredit", "grossDebit", "recordType")
    val res = PaymentGatewaySettlementsFunctionMapper.juspaySettlementFunction(
      Some("juspay_get_ar_event_type_id"),
      Vector("10"),
      Vector(col("grossCredit"), col("grossDebit"), col("recordType"))
    )
    val expect = Some(
      com.agoda.finance.functions.specific.PaymentGatewaySettlements.JuspaySettlementLogic
        .getEventTypeId("10")(col("grossCredit"), col("grossDebit"), col("recordType"))
    )
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)
    expectDf should beEqualTo(resultDf)
  }

  test("paymentSettlementFunction - should handle paypal_get_source_amount") {
    val baseDf = Seq(("DR", "100.00"), ("CR", "200.00")).toDF("transactionDebitOrCredit", "grossTransactionAmount")
    val res = PaymentGatewaySettlementsFunctionMapper.paymentSettlementFunction(
      Some("paypal_get_source_amount"),
      Vector.empty,
      Vector(col("transactionDebitOrCredit"), col("grossTransactionAmount"))
    )
    val expect = Some(
      com.agoda.finance.functions.specific.PaymentGatewaySettlements.PaypalSettlementLogic
        .getSourceAmount(col("transactionDebitOrCredit"), col("grossTransactionAmount"))
    )
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)
    expectDf should beEqualTo(resultDf)
  }

  test("paypalSettlementFunction - paypal_get_prebooking_id") {
    val baseDf = Seq(("INV123", "PAYMENT"), ("INV456", "REFUND")).toDF("invoice_id", "transactionType")
    val res = PaymentGatewaySettlementsFunctionMapper.paypalSettlementFunction(
      Some("paypal_get_prebooking_id"),
      Vector("33"),
      Vector(col("invoice_id"), col("transactionType"))
    )
    val expect = Some(
      com.agoda.finance.functions.specific.PaymentGatewaySettlements.PaypalSettlementLogic
        .getPreBookingId("33")(col("invoice_id"), col("transactionType"))
    )
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)
    expectDf should beEqualTo(resultDf)
  }

  test("paypalSettlementFunction - paypal_get_itinerary_id") {
    val baseDf = Seq(("INV123", "PAYMENT"), ("INV456", "REFUND")).toDF("invoice_id", "transactionType")
    val res = PaymentGatewaySettlementsFunctionMapper.paypalSettlementFunction(
      Some("paypal_get_itinerary_id"),
      Vector("33"),
      Vector(col("invoice_id"), col("transactionType"))
    )
    val expect = Some(
      com.agoda.finance.functions.specific.PaymentGatewaySettlements.PaypalSettlementLogic
        .getItineraryId("33")(col("invoice_id"), col("transactionType"))
    )
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)
    expectDf should beEqualTo(resultDf)
  }

  test("paypalSettlementFunction - paypal_get_event_type_id") {
    val baseDf = Seq(("CR", "PAYMENT"), ("DR", "REFUND"), ("CR", "UNKNOWN")).toDF("transactionDebitOrCredit", "transactionEventCode")
    val res = PaymentGatewaySettlementsFunctionMapper.paypalSettlementFunction(
      Some("paypal_get_event_type_id"),
      Vector("33"),
      Vector(col("transactionDebitOrCredit"), col("transactionEventCode"))
    )
    val expect = Some(
      com.agoda.finance.functions.specific.PaymentGatewaySettlements.PaypalSettlementLogic
        .getEventTypeId("33")(col("transactionDebitOrCredit"), col("transactionEventCode"))
    )
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)
    expectDf should beEqualTo(resultDf)
  }

  test("paypalSettlementFunction - paypal_get_fee_currency and paypal_get_received_currency") {
    val baseDf = Seq(("USD", "EUR"), (null, "JPY"), ("", "GBP"), (null, null)).toDF("feeCurrency", "grossTransactionCurrency")
    val feeRes = PaymentGatewaySettlementsFunctionMapper.paypalSettlementFunction(
      Some("paypal_get_fee_currency"),
      Vector.empty,
      Vector(col("feeCurrency"), col("grossTransactionCurrency"))
    )
    val receivedRes = PaymentGatewaySettlementsFunctionMapper.paypalSettlementFunction(
      Some("paypal_get_received_currency"),
      Vector.empty,
      Vector(col("feeCurrency"), col("grossTransactionCurrency"))
    )
    val expect = Some(
      com.agoda.finance.functions.specific.PaymentGatewaySettlements.PaypalSettlementLogic
        .getFeeCurrency(col("feeCurrency"), col("grossTransactionCurrency"))
    )
    val expectDf         = baseDf.withColumn("result", expect.get)
    val feeResultDf      = baseDf.withColumn("result", feeRes.get)
    val receivedResultDf = baseDf.withColumn("result", receivedRes.get)
    expectDf should beEqualTo(feeResultDf)
    expectDf should beEqualTo(receivedResultDf)
  }

  test("paypalSettlementFunction - paypal_get_source_amount, paypal_get_fee_amount, paypal_get_received_amount") {
    val baseDf = Seq(("DR", "100.00"), ("CR", "200.00")).toDF("transactionDebitOrCredit", "amount")
    val sourceRes = PaymentGatewaySettlementsFunctionMapper.paypalSettlementFunction(
      Some("paypal_get_source_amount"),
      Vector.empty,
      Vector(col("transactionDebitOrCredit"), col("amount"))
    )
    val feeRes = PaymentGatewaySettlementsFunctionMapper.paypalSettlementFunction(
      Some("paypal_get_fee_amount"),
      Vector.empty,
      Vector(col("transactionDebitOrCredit"), col("amount"))
    )
    val receivedRes = PaymentGatewaySettlementsFunctionMapper.paypalSettlementFunction(
      Some("paypal_get_received_amount"),
      Vector.empty,
      Vector(col("transactionDebitOrCredit"), col("amount"))
    )
    val expectSource = Some(
      com.agoda.finance.functions.specific.PaymentGatewaySettlements.PaypalSettlementLogic
        .getSourceAmount(col("transactionDebitOrCredit"), col("amount"))
    )
    val expectFee = Some(
      com.agoda.finance.functions.specific.PaymentGatewaySettlements.PaypalSettlementLogic
        .getFeeAmount(col("transactionDebitOrCredit"), col("amount"))
    )
    val expectReceived = Some(
      com.agoda.finance.functions.specific.PaymentGatewaySettlements.PaypalSettlementLogic
        .getReceivedAmount(col("transactionDebitOrCredit"), col("amount"))
    )
    val expectSourceDf   = baseDf.withColumn("result", expectSource.get)
    val expectFeeDf      = baseDf.withColumn("result", expectFee.get)
    val expectReceivedDf = baseDf.withColumn("result", expectReceived.get)
    val sourceResultDf   = baseDf.withColumn("result", sourceRes.get)
    val feeResultDf      = baseDf.withColumn("result", feeRes.get)
    val receivedResultDf = baseDf.withColumn("result", receivedRes.get)
    expectSourceDf should beEqualTo(sourceResultDf)
    expectFeeDf should beEqualTo(feeResultDf)
    expectReceivedDf should beEqualTo(receivedResultDf)
  }

  test("paypalSettlementFunction - paypal_get_exchange_rate") {
    val baseDf = Seq("0", "100.00", null).toDF("grossTransactionAmount")
    val res = PaymentGatewaySettlementsFunctionMapper.paypalSettlementFunction(
      Some("paypal_get_exchange_rate"),
      Vector.empty,
      Vector(col("grossTransactionAmount"))
    )
    val expect =
      Some(com.agoda.finance.functions.specific.PaymentGatewaySettlements.PaypalSettlementLogic.getExchangeRate(col("grossTransactionAmount")))
    val expectDf = baseDf.withColumn("result", expect.get)
    val resultDf = baseDf.withColumn("result", res.get)
    expectDf should beEqualTo(resultDf)
  }

}
