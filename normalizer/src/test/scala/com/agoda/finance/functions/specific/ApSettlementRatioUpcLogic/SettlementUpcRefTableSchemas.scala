package com.agoda.finance.functions.specific.ApSettlementRatioUpcLogic

import com.agoda.ml.spark.services.etl.{DataFrameETLExtensionLocalTest, SparkETLLocalTest}
import org.apache.spark.sql.types._
import org.apache.spark.sql.{DataFrame, SparkSession}

trait SettlementUpcRefTableSchemas extends SparkETLLocalTest with DataFrameETLExtensionLocalTest {

  val factBookingAllSchema = StructType(
    Seq(
      StructField("booking_id", IntegerType),
      StructField("prebooking_id", LongType),
      StructField("booking_date", StringType),
      StructField("booking_datetime", TimestampType),
      StructField("booking_datetimez", TimestampType),
      StructField("affiliate_model_id", LongType),
      StructField("arrival_time_window_id", LongType),
      StructField("booking_filter_id", LongType),
      StructField("booking_status_state_id", LongType),
      StructField("charge_option_id", LongType),
      StructField("partner_loyalty_point_programs_currency_id", StringType),
      StructField("customer_id", LongType),
      StructField("default_attr_site_id", LongType),
      StructField("discount_promotion", StringType),
      StructField("guest_nationality", StringType),
      StructField("language_id", LongType),
      StructField("origin", StringType),
      StructField("original_payment_method_id", LongType),
      StructField("original_payment_model_id", LongType),
      StructField("original_payment_type_id", LongType),
      StructField("original_promotion_code_type_id", LongType),
      StructField("page_type_id", LongType),
      StructField("platform_id", LongType),
      StructField("page_type_platform", StringType),
      StructField("pricing_template_sk", LongType),
      StructField("partner_loyalty_point_programs_id", LongType),
      StructField("rate_channel_id", LongType),
      StructField("rateplan_distribution_id", LongType),
      StructField("hotel_id", LongType),
      StructField("server", StringType),
      StructField("server_data_center", StringType),
      StructField("financelift_id", LongType),
      StructField("site_id", LongType),
      StructField("fe_site_id", LongType),
      StructField("storefront_id", LongType),
      StructField("supplier_id", LongType),
      StructField("time_id", StringType),
      StructField("workflow_reason_id", LongType),
      StructField("promotion_id", LongType),
      StructField("web_store_id", LongType),
      StructField("checkin_date", StringType),
      StructField("checkout_date", StringType),
      StructField("cancellation_date", StringType),
      StructField("schedule_charge_date", StringType),
      StructField("supplier_due_date", TimestampType),
      StructField("first_acknowledge_date", TimestampType),
      StructField("last_acknowledge_date", TimestampType),
      StructField("session_id", StringType),
      StructField("tracking_cookie_id", StringType),
      StructField("lead_time_day", IntegerType),
      StructField("room_nights", IntegerType),
      StructField("no_of_room", IntegerType),
      StructField("los", IntegerType),
      StructField("no_of_extra_bed", IntegerType),
      StructField("no_of_adults", IntegerType),
      StructField("no_of_children", IntegerType),
      StructField("is_user_logged_in", BooleanType),
      StructField("is_first_booking", BooleanType),
      StructField("is_agent_assist", BooleanType),
      StructField("fraud_action", IntegerType),
      StructField("agoda_cancellation_fee_id", LongType),
      StructField("booking_route_id", LongType),
      StructField("pricing_refundable_flag", IntegerType),
      StructField("deals_filter_id", LongType),
      StructField("tracking_tag", StringType),
      StructField("ratemodel_type", IntegerType),
      StructField("adv_guarantee_flag", BooleanType),
      StructField("is_email_whitelist", BooleanType),
      StructField("exchange_rate_option", IntegerType),
      StructField("other_special_needs", StringType),
      StructField("original_lead_time_day", IntegerType),
      StructField("original_room_nights", IntegerType),
      StructField("original_no_of_room", IntegerType),
      StructField("original_los", IntegerType),
      StructField("original_selling_amount", DoubleType),
      StructField("original_supplier_amount", DoubleType),
      StructField("original_local_currency", StringType),
      StructField("original_local_supplier_amount", DoubleType),
      StructField("original_payment_currency", StringType),
      StructField("original_local_payment_amount", DoubleType),
      StructField("original_local_cc_payment_amount", DoubleType),
      StructField("original_redeem_amount", DoubleType),
      StructField("financelift_amount_usd", DoubleType),
      StructField("selling_amount", DoubleType),
      StructField("supplier_amount", DoubleType),
      StructField("customer_payment_amount", DoubleType),
      StructField("credit_card_payment_amount", DoubleType),
      StructField("discount_amount", DoubleType),
      StructField("original_additional_cost_amount", DoubleType),
      StructField("additional_cost_amount", DoubleType),
      StructField("original_loyalty_point", DoubleType),
      StructField("original_point_cost_amount_usd", DoubleType),
      StructField("original_gst_cost_amount_usd", DoubleType),
      StructField("loyalty_point", DoubleType),
      StructField("point_cost_amount_usd", DoubleType),
      StructField("gst_cost_amount_usd", DoubleType),
      StructField("original_point_cost_amount_local", DoubleType),
      StructField("original_gst_cost_amount_local", DoubleType),
      StructField("point_cost_amount_local", DoubleType),
      StructField("gst_cost_amount_local", DoubleType),
      StructField("booking_flag", IntegerType),
      StructField("is_agency_prepay", IntegerType),
      StructField("is_nha", IntegerType),
      StructField("is_agoda_reception", IntegerType),
      StructField("original_fxi_amount", DoubleType),
      StructField("fxi_amount", DoubleType),
      StructField("margin", DoubleType),
      StructField("original_margin", DoubleType),
      StructField("availability_type", IntegerType),
      StructField("process_datetime", TimestampType),
      StructField("cancellation_datetime", TimestampType),
      StructField("cancellation_datetimez", TimestampType),
      StructField("first_payment_date", TimestampType),
      StructField("original_bnpl_fully_charge_date", StringType),
      StructField("supplier_booking_value", DoubleType),
      StructField("customer_booking_value", DoubleType),
      StructField("is_pointsmax_booking", IntegerType),
      StructField("is_not_cc_required", IntegerType),
      StructField("is_bor", IntegerType),
      StructField("champagne_flag", BooleanType),
      StructField("pointsmax_type_name", StringType),
      StructField("room_type_id", LongType),
      StructField("search_id", StringType),
      StructField("downlift_campaign_id", LongType),
      StructField("downlift_percent", DoubleType),
      StructField("is_use_giftcard", IntegerType),
      StructField("is_use_reward", IntegerType),
      StructField("redeemed_giftcard_usd", DoubleType),
      StructField("redeemed_reward_usd", DoubleType),
      StructField("original_redeemed_giftcard_usd", DoubleType),
      StructField("earned_giftcard_usd", DoubleType),
      StructField("earned_reward_usd", DoubleType),
      StructField("dispatch_campaign_id", LongType),
      StructField("app_version", StringType),
      StructField("api_version", StringType),
      StructField("lead_time_booking_checkin_id", IntegerType),
      StructField("lead_time_booking_checkout_id", IntegerType),
      StructField("lead_time_booking_cancel_id", IntegerType),
      StructField("lead_time_cancel_checkin_id", IntegerType),
      StructField("lead_time_cancel_checkout_id", IntegerType),
      StructField("breakfast_included", IntegerType),
      StructField("is_nha_display", IntegerType),
      StructField("booking_external_reference", StringType),
      StructField("merchant_of_record", StringType),
      StructField("revenue", StringType),
      StructField("rate_contract", StringType),
      StructField("tax_amount", DoubleType),
      StructField("service_charge_amount", DoubleType),
      StructField("is_no_show", BooleanType),
      StructField("client_ip_address", StringType),
      StructField("bor_status_id", LongType),
      StructField("is_allotment_alert", BooleanType),
      StructField("aa_reported_date", TimestampType),
      StructField("created_log_time", TimestampType),
      StructField("updated_log_time", TimestampType),
      StructField("browser_sk", LongType),
      StructField("cancellation_policy_sk", LongType),
      StructField("discount_promotion_sk", LongType),
      StructField("operating_system_sk", LongType),
      StructField("page_type_sk", LongType),
      StructField("server_sk", LongType),
      StructField("partner_loyalty_point_programs_currency_sk", LongType),
      StructField("origin_country_sk", LongType),
      StructField("guest_nationality_country_sk", LongType),
      StructField("device_sk", LongType),
      StructField("pointsmax_type_sk", LongType),
      StructField("app_version_sk", LongType),
      StructField("api_version_sk", LongType),
      StructField("promocode_campaign_sk", LongType),
      StructField("hotel_sk", LongType),
      StructField("datadate", IntegerType),
      StructField("ebe_device_type_id", IntegerType),
      StructField("guest_first_name_md5", StringType),
      StructField("guest_last_name_md5", StringType),
      StructField("origin_city_name", StringType),
      StructField("discount_promotion_text", StringType),
      StructField("last_minute_flag", BooleanType),
      StructField("is_first_mobile_app_booking", BooleanType),
      StructField("is_first_pointsmax_booking", BooleanType),
      StructField("is_ar_booking", BooleanType),
      StructField("ar_reported_date", TimestampType),
      StructField("cancellation_probability", DoubleType),
      StructField("runs_0", StringType),
      StructField("runs_1", StringType),
      StructField("runs_2", StringType),
      StructField("runs_3", StringType),
      StructField("runs_4", StringType),
      StructField("runs_5", StringType),
      StructField("runs_6", StringType),
      StructField("runs_7", StringType),
      StructField("runs_8", StringType),
      StructField("runs_9", StringType),
      StructField("runs_10", StringType),
      StructField("runs_11", StringType),
      StructField("runs_12", StringType),
      StructField("runs_13", StringType),
      StructField("runs_14", StringType),
      StructField("runs_15", StringType),
      StructField("runs_16", StringType),
      StructField("runs_17", StringType),
      StructField("runs_18", StringType),
      StructField("runs_19", StringType),
      StructField("runs_20", StringType),
      StructField("runs_21", StringType),
      StructField("runs_22", StringType),
      StructField("runs_23", StringType),
      StructField("runs_24", StringType),
      StructField("runs_25", StringType),
      StructField("runs_26", StringType),
      StructField("runs_27", StringType),
      StructField("runs_28", StringType),
      StructField("runs_29", StringType),
      StructField("runs_30", StringType),
      StructField("runs_31", StringType),
      StructField("runs_32", StringType),
      StructField("runs_33", StringType),
      StructField("runs_34", StringType),
      StructField("runs_35", StringType),
      StructField("runs_36", StringType),
      StructField("runs_37", StringType),
      StructField("runs_38", StringType),
      StructField("runs_39", StringType),
      StructField("runs_40", StringType),
      StructField("runs_41", StringType),
      StructField("runs_42", StringType),
      StructField("runs_43", StringType),
      StructField("runs_44", StringType),
      StructField("runs_45", StringType),
      StructField("runs_46", StringType),
      StructField("runs_47", StringType),
      StructField("runs_48", StringType),
      StructField("runs_49", StringType),
      StructField("runs_50", StringType),
      StructField("runs_51", StringType),
      StructField("runs_52", StringType),
      StructField("runs_53", StringType),
      StructField("runs_54", StringType),
      StructField("runs_55", StringType),
      StructField("runs_56", StringType),
      StructField("runs_57", StringType),
      StructField("runs_58", StringType),
      StructField("runs_59", StringType),
      StructField("runs_60", StringType),
      StructField("runs_61", StringType),
      StructField("runs_62", StringType),
      StructField("runs_63", StringType),
      StructField("runs_64", StringType),
      StructField("runs_65", StringType),
      StructField("runs_66", StringType),
      StructField("runs_67", StringType),
      StructField("runs_68", StringType),
      StructField("runs_69", StringType),
      StructField("runs_70", StringType),
      StructField("runs_71", StringType),
      StructField("runs_72", StringType),
      StructField("runs_73", StringType),
      StructField("runs_74", StringType),
      StructField("runs_75", StringType),
      StructField("runs_76", StringType),
      StructField("runs_77", StringType),
      StructField("runs_78", StringType),
      StructField("runs_79", StringType),
      StructField("runs_80", StringType),
      StructField("runs_81", StringType),
      StructField("runs_82", StringType),
      StructField("runs_83", StringType),
      StructField("runs_84", StringType),
      StructField("runs_85", StringType),
      StructField("runs_86", StringType),
      StructField("runs_87", StringType),
      StructField("runs_88", StringType),
      StructField("runs_89", StringType),
      StructField("runs_90", StringType),
      StructField("runs_91", StringType),
      StructField("runs_92", StringType),
      StructField("runs_93", StringType),
      StructField("runs_94", StringType),
      StructField("runs_95", StringType),
      StructField("runs_96", StringType),
      StructField("runs_97", StringType),
      StructField("runs_98", StringType),
      StructField("runs_99", StringType),
      StructField("selling_tax", DoubleType),
      StructField("supplier_tax", DoubleType),
      StructField("booking_status", StringType),
      StructField("booking_state_group", StringType),
      StructField("booking_status_state", StringType),
      StructField("promotion_type", StringType),
      StructField("promotion_discount_type_name", StringType),
      StructField("supplier_code", StringType),
      StructField("charge_option", StringType),
      StructField("rate_channel_name", StringType),
      StructField("guest_nationality_country_code", StringType),
      StructField("guest_nationality_country_name", StringType),
      StructField("original_payment_model_name", StringType),
      StructField("origin_country_code", StringType),
      StructField("origin_country_name", StringType),
      StructField("language_name", StringType),
      StructField("page_type_name", StringType),
      StructField("browser_version", StringType),
      StructField("browser", StringType),
      StructField("browser_family", StringType),
      StructField("os", StringType),
      StructField("os_family", StringType),
      StructField("platform_name", StringType),
      StructField("platform_group_name", StringType),
      StructField("finance_lift_type", StringType),
      StructField("original_payment_method_name", StringType),
      StructField("original_payment_type_name", StringType),
      StructField("affiliate_model_name", StringType),
      StructField("storefront_name", StringType),
      StructField("workflow_reason_name", StringType),
      StructField("booking_route_type", StringType),
      StructField("exchange_rate_option_name", StringType),
      StructField("bor_status", StringType),
      StructField("supplier_name", StringType),
      StructField("lead_time_next_booking", IntegerType),
      StructField("lead_time_prev_booking", IntegerType),
      StructField("traffic_sub_group_id", LongType),
      StructField("traffic_sub_group_name", StringType),
      StructField("traffic_group_id", LongType),
      StructField("traffic_group_name", StringType),
      StructField("affiliate_id", LongType),
      StructField("affiliate_name", StringType),
      StructField("cancellation_policy", IntegerType),
      StructField("cancellation_policy_code", StringType),
      StructField("cancellation_policy_description", StringType),
      StructField("campaign_name", StringType),
      StructField("hotel_room_type_name", StringType),
      StructField("master_hotel_id", LongType),
      StructField("hotel_name", StringType),
      StructField("city_id", LongType),
      StructField("city_name", StringType),
      StructField("country_id", LongType),
      StructField("country_name", StringType),
      StructField("region_id", LongType),
      StructField("region_name", StringType),
      StructField("device_model", StringType),
      StructField("device_type_name", StringType),
      StructField("device_family", StringType),
      StructField("customer_email_md5", StringType),
      StructField("customer_nationality", StringType),
      StructField("partner_loyalty_point_programs_name", StringType),
      StructField("is_third_party_supply", IntegerType),
      StructField("external_source_updated", IntegerType),
      StructField("dmc_specific_data", StringType),
      StructField("original_fully_charge_date", StringType),
      StructField("rec_created_when", TimestampType),
      StructField("device_brand", StringType),
      StructField("area_id", LongType),
      StructField("is_newsletter", IntegerType),
      StructField("giftcard_usd_amount", DoubleType),
      StructField("uplift_amount_usd", DoubleType),
      StructField("user_login_sk", LongType),
      StructField("booking_datetimez_origin_city", TimestampType),
      StructField("booking_datetimez_destination_city", TimestampType),
      StructField("current_market_manager", StringType),
      StructField("hotel_score", StringType),
      StructField("area_name", StringType),
      StructField("country_code", StringType),
      StructField("pricing_template_id", LongType),
      StructField("origin_country_id", LongType),
      StructField("merchant_of_record_type", StringType),
      StructField("revenue_type", StringType),
      StructField("rate_contract_type", StringType),
      StructField("is_family_booking", BooleanType),
      StructField("chain_name", StringType),
      StructField("chain_id", LongType),
      StructField("is_nha_accommodation_type", IntegerType),
      StructField("accommodation_type_id", LongType),
      StructField("accommodation_type_name", StringType),
      StructField("brand_name", StringType),
      StructField("brand_id", LongType),
      StructField("nha_flag", IntegerType),
      StructField("connection_type_id", LongType),
      StructField("channel_manager", StringType),
      StructField("last_full_refund_date", StringType),
      StructField("fraud_score", IntegerType),
      StructField("origin_region_id", LongType),
      StructField("origin_region_name", StringType),
      StructField("web_store_description", StringType),
      StructField("device_name", StringType),
      StructField("lpc_30_site_id", LongType),
      StructField("lc_1_site_id", LongType),
      StructField("giftcard_earn_date", TimestampType),
      StructField("giftcard_percent", DoubleType),
      StructField("giftcard_day_to_earn", IntegerType),
      StructField("giftcard_earn_id", IntegerType),
      StructField("giftcard_expiry_days", IntegerType),
      StructField("giftcard_rule_id", IntegerType),
      StructField("site_name", StringType),
      StructField("city_bd_target_flag", IntegerType),
      StructField("country_bd_target_flag", IntegerType),
      StructField("state_abbr", StringType),
      StructField("state_name", StringType),
      StructField("current_star_rating", DoubleType),
      StructField("original_is_nha_accommodation_type", IntegerType),
      StructField("owner_id", LongType),
      StructField("owner_name", StringType),
      StructField("origin_language_id", LongType),
      StructField("origin_language_name", StringType),
      StructField("original_payment_category_id", LongType),
      StructField("original_payment_category_name", StringType),
      StructField("is_completed_booking", IntegerType),
      StructField("booking_status_id", LongType),
      StructField("aa_solution", StringType),
      StructField("ar_solution", StringType),
      StructField("ar_reason", StringType),
      StructField("installment_flag", IntegerType),
      StructField("installment_month_period", IntegerType),
      StructField("installment_interest", DoubleType),
      StructField("mse_clicked_flag", BooleanType),
      StructField("whitelabel_id", LongType),
      StructField("cancel_by", StringType),
      StructField("cancel_reason", StringType),
      StructField("origin_region_name_market_group", StringType),
      StructField("region_name_market_group", StringType),
      StructField("hotel_ratecategory_id", LongType),
      StructField("hotel_ratecategory_description", StringType),
      StructField("past_booking_number", IntegerType),
      StructField("coordinator_runtime", TimestampType),
      StructField("commission_equivalent_percent", DoubleType),
      StructField("margin_prediction", DoubleType),
      StructField("customer_vip_effective", IntegerType),
      StructField("customer_vip_incl_holdout", IntegerType),
      StructField("ar_solution_id", LongType),
      StructField("ar_reason_id", LongType),
      StructField("aa_solution_id", LongType),
      StructField("itinerary_id", LongType),
      StructField("is_first_mobile_device_booking", BooleanType),
      StructField("whitelabel_name", StringType),
      StructField("dltv_conservative", DoubleType),
      StructField("original_dltv_conservative", DoubleType),
      StructField("agp_contract_id", IntegerType),
      StructField("is_morp", IntegerType),
      StructField("multi_product_id", LongType),
      StructField("multi_product_type_id", IntegerType),
      StructField("fraud_check_date", TimestampType),
      StructField("promotion_campaign_id", IntegerType),
      StructField("origin_state_name", StringType),
      StructField("mixandsave_id", StringType),
      StructField("multi_product_type_name", StringType),
      StructField("dltv_aggressive", DoubleType),
      StructField("original_dltv_aggressive", DoubleType),
      StructField("checkin_datetime", TimestampType),
      StructField("checkout_datetime", TimestampType),
      StructField("origin_country_ip_address", StringType),
      StructField("margin_net_interco", DoubleType),
      StructField("original_margin_net_interco", DoubleType),
      StructField("customer_vip_benefit", IntegerType),
      StructField("is_ccof", IntegerType),
      StructField("is_first_ccof", IntegerType),
      StructField("staytype", StringType),
      StructField("payout_condition", IntegerType),
      StructField("payout_method", IntegerType),
      StructField("is_easy_cancel", IntegerType),
      StructField("selling_amount_exclusive", DoubleType),
      StructField("original_selling_amount_exclusive", DoubleType),
      StructField("resell_ref_booking_id", IntegerType),
      StructField("resell_ref_selling_amount", DoubleType),
      StructField("resell_ref_selling_tax", DoubleType),
      StructField("resell_ref_supplier_amount", DoubleType),
      StructField("resell_ref_supplier_tax", DoubleType),
      StructField("resell_ref_booking_status_id", IntegerType),
      StructField("resell_ref_booking_status", StringType),
      StructField("resell_ref_cancellation_date", StringType),
      StructField("resell_status_id", IntegerType),
      StructField("resell_status_description", StringType),
      StructField("is_resell_booking", IntegerType),
      StructField("original_supplier_id", IntegerType),
      StructField("origin_longitude", StringType),
      StructField("origin_latitude", StringType),
      StructField("special_campaign_id", LongType),
      StructField("special_campaign_name", StringType),
      StructField("co_funding_amount", DoubleType),
      StructField("dmc_hotel_id", StringType),
      StructField("base_rate_channel_id", LongType),
      StructField("base_rate_channel_name", StringType),
      StructField("stack_rate_channel_id", LongType),
      StructField("stack_rate_channel_name", StringType),
      StructField("is_wizard_refund", IntegerType),
      StructField("additional_info", StringType),
      StructField("pricing_request_id", StringType),
      StructField("lpc_30_hotel_id", LongType),
      StructField("stay_package_type", IntegerType),
      StructField("lpc_1_hotel_id", LongType),
      StructField("original_hotel_room_type_name", StringType),
      StructField("is_agx", BooleanType),
      StructField("streamlining_id", StringType),
      StructField("streamlining_room_group_id", LongType),
      StructField("cost_loyalty", DoubleType),
      StructField("original_cost_loyalty", DoubleType),
      StructField("cost_ceg_opex", DoubleType),
      StructField("cost_ceg_absorption", DoubleType),
      StructField("original_cost_ceg_absorption", DoubleType),
      StructField("cost_merchant_fee", DoubleType),
      StructField("original_cost_merchant_fee", DoubleType),
      StructField("cost_tax", DoubleType),
      StructField("original_cost_tax", DoubleType),
      StructField("cost_chargebacks", DoubleType),
      StructField("original_cost_chargebacks", DoubleType),
      StructField("income_fxi", DoubleType),
      StructField("original_income_fxi", DoubleType),
      StructField("income_rebate", DoubleType),
      StructField("original_income_rebate", DoubleType),
      StructField("income_ap_clearing", DoubleType),
      StructField("original_income_ap_clearing", DoubleType),
      StructField("fcpa", DoubleType),
      StructField("cpc_cost", DoubleType),
      StructField("cpa_cost", DoubleType),
      StructField("original_bnpl_fully_auth_date", StringType),
      StructField("bnpl_fully_auth_date", StringType),
      StructField("display_currency", StringType),
      StructField("no_of_males", IntegerType),
      StructField("no_of_females", IntegerType),
      StructField("governed_program_name", StringType),
      StructField("hotel_payment_method_actual", StringType),
      StructField("is_centrally_manage_chain", BooleanType),
      StructField("reference_commission", DoubleType),
      StructField("ceg_handout_non_ebe", DoubleType),
      StructField("cost_ceg_handout_non_ebe", DoubleType),
      StructField("cpa_cost_hc_cancelled", DoubleType),
      StructField("cost_merchant_fee_prediction", DoubleType),
      StructField("cost_chargeback_prediction", DoubleType),
      StructField("cost_tax_prediction", DoubleType),
      StructField("income_ap_clearing_prediction", DoubleType),
      StructField("income_fxi_prediction", DoubleType),
      StructField("income_rebate_prediction", DoubleType),
      StructField("income_cofunding", DoubleType),
      StructField("total_variable_cost", DoubleType),
      StructField("total_cost", DoubleType),
      StructField("total_cost_experiment", DoubleType),
      StructField("package_id", LongType),
      StructField("cart_id", LongType),
      StructField("channel_discount_amount", DoubleType),
      StructField("channel_discount_percent", DoubleType),
      StructField("reject_reason_code", IntegerType),
      StructField("reject_reason_msg", StringType),
      StructField("is_cart_flow", BooleanType),
      StructField("booking_session_id", StringType),
      StructField("reference_sell_inclusive", DoubleType),
      StructField("hotel_ratecategory_inventory_type_id", LongType),
      StructField("hotel_ratecategory_inventory_type_name", StringType),
      StructField("partner_reward_id", LongType),
      StructField("partner_reward_name", StringType),
      StructField("original_partner_reward_points_redeemed", LongType),
      StructField("original_partner_reward_points_amount_usd", DoubleType),
      StructField("original_partner_reward_points_earned", LongType),
      StructField("partner_reward_points_redeemed", LongType),
      StructField("partner_reward_points_amount_usd", DoubleType),
      StructField("cashback_days_to_earn", IntegerType),
      StructField("cashback_earn_date", TimestampType),
      StructField("cashback_earn_id", LongType),
      StructField("cashback_expiry_days", IntegerType),
      StructField("cashback_expiry_date", TimestampType),
      StructField("cashback_percentage", DoubleType),
      StructField("cashback_usd_amount", DoubleType),
      StructField("cashback_remaining_value", DoubleType),
      StructField("cashback_claimed_usd_amount", DoubleType),
      StructField("claim_rate", DoubleType),
      StructField("transaction_fee", DoubleType),
      StructField("is_offline_booking", BooleanType),
      StructField("external_member_id", StringType),
      StructField("external_sub_member_id", StringType),
      StructField("external_program_id", StringType),
      StructField("external_program_name", StringType),
      StructField("cost_cashback", DoubleType),
      StructField("original_income_cofunding", DoubleType),
      StructField("original_co_funding_amount", DoubleType),
      StructField("income_agp", DoubleType),
      StructField("is_partial_success", BooleanType),
      StructField("external_loyalty_display_tier_id", IntegerType),
      StructField("master_room_type_id", IntegerType),
      StructField("dynamic_mapping_type_id", IntegerType),
      StructField("analytics_session_id", StringType),
      StructField("is_rate_channel_swap", BooleanType),
      StructField("hotel_trip_search_type", StringType),
      StructField("min_days_gap_to_trip_bkg", IntegerType),
      StructField("is_attached_in_trip", BooleanType),
      StructField("original_cancellation_policy_code", StringType),
      StructField("is_all_guest_same_nationality", BooleanType),
      StructField("is_smart_flex", BooleanType),
      StructField("resell_type_id", IntegerType),
      StructField("resell_type_description", StringType),
      StructField("original_pass_through_tax", DoubleType),
      StructField("pass_through_tax", DoubleType),
      StructField("income_expected_override", DoubleType),
      StructField("income_nha_fudge", DoubleType),
      StructField("true_margin", DoubleType),
      StructField("original_true_margin", DoubleType),
      StructField("original_rate_channel_id", LongType),
      StructField("original_rate_channel_name", StringType),
      StructField("original_ess_amount", DoubleType),
      StructField("ess_amount", DoubleType),
      StructField("total_strategic_fudges", DoubleType),
      StructField("current_discount_amount", DoubleType),
      StructField("paid_cannibalization_vc_fudge", DoubleType),
      StructField("hedging_cost", DoubleType),
      StructField("original_discount_amount", DoubleType),
      StructField("current_financelift_amount_usd", DoubleType),
      StructField("cost_pass_through_tax_prediction", DoubleType),
      StructField("nha_bor_status_id", LongType),
      StructField("nha_bor_status", StringType),
      StructField("cost_it_dc", DoubleType),
      StructField("is_ess_related", BooleanType),
      StructField("ess_related_product_name", StringType),
      StructField("is_cancel_and_rebook", BooleanType),
      StructField("cancel_and_rebook_version", StringType),
      StructField("supplier_funded_discount", DoubleType),
      StructField("is_original_refundable", IntegerType),
      StructField("original_reference_sell_inclusive", DoubleType),
      StructField("redeemed_cashback_usd", DoubleType),
      StructField("original_redeemed_cashback_usd", DoubleType),
      StructField("is_cashback_redemption", BooleanType),
      StructField("original_multi_product_transaction_type", StringType),
      StructField("multi_product_transaction_type", StringType),
      StructField("is_smart_saver", BooleanType),
      StructField("wai_discount_usd", DoubleType),
      StructField("upcoming_hotel_booking", LongType),
      StructField("is_sl", BooleanType),
      StructField("upcoming_activity_booking", LongType),
      StructField("upcoming_flight_booking", LongType),
      StructField("original_chain_id", LongType),
      StructField("original_brand_id", LongType),
      StructField("original_chain_name", StringType),
      StructField("original_brand_name", StringType),
      StructField("is_package", BooleanType),
      StructField("original_wai_discount_usd", DoubleType),
      StructField("datamonth", IntegerType)
    )
  )
  private val settlementSchema = StructType(
    Seq(
      StructField("type", StringType),
      StructField("sub_type", StringType),
      StructField("source", StringType),
      StructField("booking_id", LongType),
      StructField("sub_supplier_id", IntegerType),
      StructField("supplier_id", IntegerType),
      StructField("approval_id", IntegerType),
      StructField("payment_method", StringType),
      StructField("accounting_date", TimestampType),
      StructField("transaction_date", TimestampType),
      StructField("settlement_currency", StringType),
      StructField("posting_currency", StringType),
      StructField("settlement_amount", DecimalType(24, 2)),
      StructField("posting_amount", DecimalType(24, 2)),
      StructField("source_transaction_code", StringType),
      StructField("vat_rate", DecimalType(24, 2)),
      StructField("uuid", StringType),
      StructField("destination_bank_country_name", StringType),
      StructField("destination_bank_country_id", IntegerType),
      StructField("settlement_amount_usd", DecimalType(24, 2)),
      StructField("original_settlement_amount", DecimalType(24, 2)),
      StructField("original_settlement_currency", StringType),
      StructField("rate_currency", StringType),
      StructField("rate_ex", DecimalType(24, 2)),
      StructField("settlement_ex", DecimalType(24, 2)),
      StructField("posting_ex", DecimalType(24, 2)),
      StructField("reporting_date", TimestampType),
      StructField("value_date", TimestampType),
      StructField("agoda_bank_account_number", StringType),
      StructField("cid", IntegerType),
      StructField("is_violet", BooleanType),
      StructField("is_allotment_reject", BooleanType),
      StructField("is_advance_guarantee", BooleanType),
      StructField("is_agency", BooleanType),
      StructField("is_adjustment", BooleanType),
      StructField("advance_payment_contract_id", IntegerType),
      StructField("merchant_of_record_entity", IntegerType),
      StructField("merchant_of_record_entity_type", IntegerType),
      StructField("revenue_entity", IntegerType),
      StructField("revenue_entity_type", IntegerType),
      StructField("rate_contract_entity", IntegerType),
      StructField("rate_contract_entity_type", IntegerType),
      StructField("adjustment_reference", StringType),
      StructField("adjustment_reason_id", IntegerType),
      StructField("adjustment_reason", StringType),
      StructField("adjustment_remark", StringType),
      StructField("approval_reference", StringType),
      StructField("payment_model", IntegerType),
      StructField("batch_paypal_pay_amount", DecimalType(24, 2)),
      StructField("batch_paypal_pay_amount_usd", DecimalType(24, 2)),
      StructField("batch_paypal_fee_amount", DecimalType(24, 2)),
      StructField("batch_paypal_fee_amount_usd", DecimalType(24, 2)),
      StructField("booking_transaction_date", TimestampType),
      StructField("is_cancelled", BooleanType),
      StructField("transaction_currency", StringType),
      StructField("transaction_amount", DecimalType(24, 2)),
      StructField("interchange_amount", DecimalType(24, 2)),
      StructField("transaction_id", StringType),
      StructField("upc_product_name", StringType),
      StructField("provider_card_classification_id", StringType),
      StructField("acquirer_reference_number", StringType),
      StructField("transaction_authorisation_number", StringType),
      StructField("merchant_country", StringType),
      StructField("merchant_name", StringType),
      StructField("merchant_address", StringType),
      StructField("merchant_city", StringType),
      StructField("merchant_post_code", StringType),
      StructField("datadate", IntegerType),
      StructField("payment_method_id", IntegerType),
      StructField("product_type", StringType),
      StructField("source_name", StringType),
      StructField("report_date", IntegerType)
    )
  )
  private val financialTransactionsSchema = StructType(
    Seq(
      StructField("booking_id", LongType),
      StructField("action_id", LongType),
      StructField("itinerary_id", LongType),
      StructField("itinerary_payment_id", LongType),
      StructField("transaction_id", StringType),
      StructField("transaction_type", IntegerType),
      StructField("action_type", IntegerType),
      StructField("gateway_id", IntegerType),
      StructField("payment_method_id", IntegerType),
      StructField("payment_type_id", IntegerType),
      StructField("reference_type", IntegerType),
      StructField("charge_date", TimestampType),
      StructField("transaction_date", TimestampType),
      StructField("cid", LongType),
      StructField("payment_model", IntegerType),
      StructField("booking_date", TimestampType),
      StructField("start_date", TimestampType),
      StructField("end_date", TimestampType),
      StructField("cancellation_date", TimestampType),
      StructField("commission_model_cutoff_date", TimestampType),
      StructField("sub_supplier_id", IntegerType),
      StructField("supplier_booking_id", StringType),
      StructField("supplier_id", IntegerType),
      StructField("service_origin_iso3", StringType),
      StructField("merchant_of_record_entity", IntegerType),
      StructField("revenue_entity", IntegerType),
      StructField("rate_contract_entity", IntegerType),
      StructField("merchant_of_record_entity_type", IntegerType),
      StructField("revenue_entity_type", IntegerType),
      StructField("rate_contract_entity_type", IntegerType),
      StructField("whitelabel_id", IntegerType),
      StructField("customer_origin_iso2", StringType),
      StructField("service_origin_id", IntegerType),
      StructField("service_provider_reference", StringType),
      StructField("service_provider", StringType),
      StructField("is_test", BooleanType),
      StructField("customer_payment_currency", StringType),
      StructField("supplier_currency", StringType),
      StructField("fx_customer_payment_rate", DecimalType(24, 8)),
      StructField("fx_supplier_rate", DecimalType(18, 8)),
      StructField("customer_payment", DecimalType(24, 8)),
      StructField("customer_payment_usd", DecimalType(24, 8)),
      StructField("customer_fx_uplift", DecimalType(24, 8)),
      StructField("customer_fx_uplift_usd", DecimalType(24, 8)),
      StructField("customer_pointmax_cost", DecimalType(24, 8)),
      StructField("customer_pointmax_cost_usd", DecimalType(24, 8)),
      StructField("customer_pointmax_gst_cost", DecimalType(24, 8)),
      StructField("customer_pointmax_gst_cost_usd", DecimalType(24, 8)),
      StructField("supplier_net_exclusive", DecimalType(18, 8)),
      StructField("supplier_net_exclusive_usd", DecimalType(18, 8)),
      StructField("supplier_net_exclusive_customer", DecimalType(18, 8)),
      StructField("supplier_net_inclusive", DecimalType(18, 8)),
      StructField("supplier_net_inclusive_usd", DecimalType(18, 8)),
      StructField("supplier_net_inclusive_customer", DecimalType(18, 8)),
      StructField("supplier_net_inclusive_cancellation_fee", DecimalType(18, 8)),
      StructField("supplier_net_inclusive_cancellation_fee_usd", DecimalType(18, 8)),
      StructField("supplier_net_inclusive_cancellation_fee_customer", DecimalType(18, 8)),
      StructField("supplier_sales_exclusive", DecimalType(18, 8)),
      StructField("supplier_sales_exclusive_usd", DecimalType(18, 8)),
      StructField("supplier_sales_exclusive_customer", DecimalType(18, 8)),
      StructField("supplier_sales_inclusive", DecimalType(18, 8)),
      StructField("supplier_sales_inclusive_usd", DecimalType(18, 8)),
      StructField("supplier_sales_inclusive_customer", DecimalType(18, 8)),
      StructField("supplier_sales_inclusive_cancellation_fee", DecimalType(18, 8)),
      StructField("supplier_sales_inclusive_cancellation_fee_usd", DecimalType(18, 8)),
      StructField("supplier_sales_inclusive_cancellation_fee_customer", DecimalType(18, 8)),
      StructField("supplier_absorption", DecimalType(18, 8)),
      StructField("supplier_absorption_usd", DecimalType(18, 8)),
      StructField("supplier_absorption_customer", DecimalType(18, 8)),
      StructField("supplier_commission", DecimalType(18, 8)),
      StructField("supplier_commission_usd", DecimalType(18, 8)),
      StructField("supplier_commission_customer", DecimalType(18, 8)),
      StructField("supplier_uplift", DecimalType(18, 8)),
      StructField("supplier_uplift_usd", DecimalType(18, 8)),
      StructField("supplier_uplift_customer", DecimalType(18, 8)),
      StructField("supplier_downlift", DecimalType(18, 8)),
      StructField("supplier_downlift_usd", DecimalType(18, 8)),
      StructField("supplier_downlift_customer", DecimalType(18, 8)),
      StructField("supplier_margin", DecimalType(18, 8)),
      StructField("supplier_margin_usd", DecimalType(18, 8)),
      StructField("supplier_margin_customer", DecimalType(18, 8)),
      StructField("supplier_fee", DecimalType(18, 8)),
      StructField("supplier_fee_usd", DecimalType(18, 8)),
      StructField("supplier_fee_customer", DecimalType(18, 8)),
      StructField("supplier_supplier_fee", DecimalType(18, 8)),
      StructField("supplier_supplier_fee_usd", DecimalType(18, 8)),
      StructField("supplier_supplier_fee_customer", DecimalType(18, 8)),
      StructField("supplier_gst", DecimalType(18, 8)),
      StructField("supplier_gst_usd", DecimalType(18, 8)),
      StructField("supplier_gst_customer", DecimalType(18, 8)),
      StructField("supplier_tax", DecimalType(18, 8)),
      StructField("supplier_tax_usd", DecimalType(18, 8)),
      StructField("supplier_tax_customer", DecimalType(18, 8)),
      StructField("supplier_processing_fee", DecimalType(18, 8)),
      StructField("supplier_processing_fee_usd", DecimalType(18, 8)),
      StructField("supplier_processing_fee_customer", DecimalType(18, 8)),
      StructField("supplier_reference_sales_inclusive", DecimalType(18, 8)),
      StructField("supplier_reference_sales_inclusive_usd", DecimalType(18, 8)),
      StructField("supplier_reference_sales_inclusive_customer", DecimalType(18, 8)),
      StructField("supplier_reference_sales_inclusive_cancellation_fee", DecimalType(18, 8)),
      StructField("supplier_reference_sales_inclusive_cancellation_fee_usd", DecimalType(18, 8)),
      StructField("supplier_reference_sales_inclusive_cancellation_fee_customer", DecimalType(18, 8)),
      StructField("supplier_agoda_agency_commission", DecimalType(18, 8)),
      StructField("supplier_agoda_agency_commission_usd", DecimalType(18, 8)),
      StructField("supplier_agoda_agency_commission_customer", DecimalType(18, 8)),
      StructField("supplier_agoda_agency_commission_cancellation_fee", DecimalType(18, 8)),
      StructField("supplier_agoda_agency_commission_cancellation_fee_usd", DecimalType(18, 8)),
      StructField("supplier_agoda_agency_commission_cancellation_fee_customer", DecimalType(18, 8)),
      StructField("supplier_tax_pay_to_property", DecimalType(18, 8)),
      StructField("supplier_tax_pay_to_property_usd", DecimalType(18, 8)),
      StructField("supplier_tax_pay_to_property_customer", DecimalType(18, 8)),
      StructField("supplier_tax_pay_to_government", DecimalType(18, 8)),
      StructField("supplier_tax_pay_to_government_usd", DecimalType(18, 8)),
      StructField("supplier_tax_pay_to_government_customer", DecimalType(18, 8)),
      StructField("supplier_withholding_tax", DecimalType(18, 8)),
      StructField("supplier_withholding_tax_usd", DecimalType(18, 8)),
      StructField("supplier_withholding_tax_customer", DecimalType(18, 8)),
      StructField("supplier_tax_on_commission_pass_to_property", DecimalType(18, 8)),
      StructField("supplier_tax_on_commission_pass_to_property_usd", DecimalType(18, 8)),
      StructField("supplier_tax_on_commission_pass_to_property_customer", DecimalType(18, 8)),
      StructField("supplier_normal_and_withholding_tax", DecimalType(18, 8)),
      StructField("supplier_normal_and_withholding_tax_usd", DecimalType(18, 8)),
      StructField("supplier_normal_and_withholding_tax_customer", DecimalType(18, 8)),
      StructField("promotion_amount", DecimalType(18, 8)),
      StructField("promotion_amount_usd", DecimalType(18, 8)),
      StructField("promotion_amount_customer", DecimalType(18, 8)),
      StructField("customer_cashback", DecimalType(18, 8)),
      StructField("customer_cashback_usd", DecimalType(18, 8)),
      StructField("customer_cashback_customer", DecimalType(18, 8)),
      StructField("tracking_tag", StringType),
      StructField("cid_name", StringType),
      StructField("payment_model_name", StringType),
      StructField("supplier_name", StringType),
      StructField("sub_supplier_name", StringType),
      StructField("mor_name", StringType),
      StructField("rev_name", StringType),
      StructField("rce_name", StringType),
      StructField("mor_entity_type_name", StringType),
      StructField("rev_entity_type_name", StringType),
      StructField("rce_entity_type_name", StringType),
      StructField("whitelabel_name", StringType),
      StructField("transaction_type_name", StringType),
      StructField("action_type_name", StringType),
      StructField("gateway_name", StringType),
      StructField("payment_method_name", StringType),
      StructField("payment_type_name", StringType),
      StructField("payment_state_name", StringType),
      StructField("reference_type_name", StringType),
      StructField("client_id", IntegerType),
      StructField("booking_month", IntegerType),
      StructField("created_log_time", TimestampType),
      StructField("source_type", StringType),
      StructField("points_redeemed", DecimalType(24, 8)),
      StructField("partner_id", LongType),
      StructField("partner_currency", StringType),
      StructField("partner_amount", DecimalType(24, 8)),
      StructField("partner_amount_usd", DecimalType(24, 8)),
      StructField("booking_datadate_line_number", IntegerType),
      StructField("source_datadate", IntegerType),
      StructField("whitelabel_group_id", IntegerType),
      StructField("uuid", StringType),
      StructField("member_id", IntegerType),
      StructField("customer_iso2", StringType),
      StructField("customer_iso2_currency", StringType),
      StructField("fx_customer_iso2_rate", DecimalType(18, 8)),
      StructField("supplier_ess_fee_and_tax_detail", StringType), // Converted the struct to string as it will not fit in csv to load
      StructField("adjustment_identifier", StringType),
      StructField("resell_booking_id", LongType),
      StructField("resell_type_id", IntegerType),
      StructField("resell_status_id", IntegerType),
      StructField("partner_transaction_id", StringType),
      StructField("partner_name", StringType),
      StructField("supplier_net_inclusive_liability", DecimalType(18, 8)),
      StructField("supplier_net_inclusive_liability_usd", DecimalType(18, 8)),
      StructField("supplier_net_inclusive_liability_customer", DecimalType(18, 8)),
      StructField("partner_code", StringType),
      StructField("partner_program_id", StringType),
      StructField("partner_payment_model", StringType),
      StructField("supplier_df_rate_difference", DecimalType(18, 8)),
      StructField("supplier_df_rate_difference_usd", DecimalType(18, 8)),
      StructField("supplier_df_rate_difference_customer", DecimalType(18, 8)),
      StructField("program_name", StringType),
      StructField("supplier_sales_inclusive_ceg_fast_track", DecimalType(18, 8)),
      StructField("supplier_sales_inclusive_ceg_fast_track_usd", DecimalType(18, 8)),
      StructField("supplier_sales_inclusive_ceg_fast_track_customer", DecimalType(18, 8)),
      StructField("supplier_sales_exclusive_ceg_fast_track", DecimalType(18, 8)),
      StructField("supplier_sales_exclusive_ceg_fast_track_usd", DecimalType(18, 8)),
      StructField("supplier_sales_exclusive_ceg_fast_track_customer", DecimalType(18, 8)),
      StructField("supplier_margin_ceg_fast_track", DecimalType(18, 8)),
      StructField("supplier_margin_ceg_fast_track_usd", DecimalType(18, 8)),
      StructField("supplier_margin_ceg_fast_track_customer", DecimalType(18, 8)),
      StructField("supplier_processing_fee_ceg_fast_track", DecimalType(18, 8)),
      StructField("supplier_processing_fee_ceg_fast_track_usd", DecimalType(18, 8)),
      StructField("supplier_processing_fee_ceg_fast_track_customer", DecimalType(18, 8)),
      StructField("supplier_downlift_ceg_fast_track", DecimalType(18, 8)),
      StructField("supplier_downlift_ceg_fast_track_usd", DecimalType(18, 8)),
      StructField("supplier_downlift_ceg_fast_track_customer", DecimalType(18, 8)),
      StructField("supplier_reference_commission", DecimalType(18, 8)),
      StructField("supplier_reference_commission_usd", DecimalType(18, 8)),
      StructField("supplier_reference_commission_customer", DecimalType(18, 8)),
      StructField("supplier_partner_discount", DecimalType(18, 8)),
      StructField("supplier_partner_discount_usd", DecimalType(18, 8)),
      StructField("supplier_partner_discount_customer", DecimalType(18, 8)),
      StructField("room_index", IntegerType),
      StructField("date_of_stay", StringType),
      StructField("customer_iso2_id", IntegerType),
      StructField("hotel_funding", DecimalType(18, 8)),
      StructField("hotel_funding_usd", DecimalType(18, 8)),
      StructField("hotel_funding_customer", DecimalType(18, 8)),
      StructField("room_nights", IntegerType),
      StructField("related_booking_id", LongType),
      StructField("supplier_sales_inclusive_claim", DecimalType(18, 8)),
      StructField("supplier_sales_inclusive_claim_usd", DecimalType(18, 8)),
      StructField("supplier_sales_inclusive_claim_customer", DecimalType(18, 8)),
      StructField("related_product_type", StringType),
      StructField("daily_datetime", TimestampType),
      StructField("adjustment_file_id", StringType),
      StructField("adjustment_type", StringType),
      StructField("whitelabel_group_name", StringType),
      StructField("is_finance_violet", BooleanType),
      StructField("payout_payment_method_id", IntegerType),
      StructField("sub_supplier_country_id", IntegerType),
      StructField("sub_supplier_country_name", StringType),
      StructField("supplier_net_inclusive_claim", DecimalType(18, 8)),
      StructField("supplier_net_inclusive_claim_usd", DecimalType(18, 8)),
      StructField("supplier_net_inclusive_claim_customer", DecimalType(18, 8)),
      StructField("is_bypass_payment", BooleanType),
      StructField("supplier_net_inclusive_minus_tax", DecimalType(18, 8)),
      StructField("supplier_net_inclusive_minus_tax_usd", DecimalType(18, 8)),
      StructField("supplier_net_inclusive_minus_tax_customer", DecimalType(18, 8)),
      StructField("supplier_reference_sales_inclusive_minus_tax", DecimalType(18, 8)),
      StructField("supplier_reference_sales_inclusive_minus_tax_usd", DecimalType(18, 8)),
      StructField("supplier_reference_sales_inclusive_minus_tax_customer", DecimalType(18, 8)),
      StructField("hotel_ratecategory_id", LongType),
      StructField("bnpl_fully_auth_date", TimestampType),
      StructField("sub_product_type", StringType),
      StructField("co_funding_amount", DecimalType(18, 8)),
      StructField("co_funding_amount_usd", DecimalType(18, 8)),
      StructField("co_funding_amount_customer", DecimalType(18, 8)),
      StructField("promotion_campaign_id", IntegerType),
      StructField("payout_designated_item_id_amount", DecimalType(18, 8)),
      StructField("payout_designated_item_id_amount_usd", DecimalType(18, 8)),
      StructField("chain_name", StringType),
      StructField("service_origin_name", StringType),
      StructField("is_advance_guarantee", BooleanType),
      StructField("agp_contract_id", IntegerType),
      StructField("is_finance_agency", BooleanType),
      StructField("payout_designated_item_id", IntegerType),
      StructField("promotion_code", StringType),
      StructField("vat_rate", DecimalType(18, 4)),
      StructField("local_amount_inc_gst", DecimalType(18, 8)),
      StructField("local_commission_inc_gst", DecimalType(18, 8)),
      StructField("usd_amount_inc_gst", DecimalType(18, 8)),
      StructField("usd_commission_inc_gst", DecimalType(18, 8)),
      StructField("local_amount_exc_gst", DecimalType(18, 8)),
      StructField("local_commission_exc_gst", DecimalType(18, 8)),
      StructField("usd_amount_exc_gst", DecimalType(18, 8)),
      StructField("usd_commission_exc_gst", DecimalType(18, 8)),
      StructField("usd_total_amount_exc_gst", DecimalType(18, 8)),
      StructField("local_total_amount_exc_gst", DecimalType(18, 8)),
      StructField("local_gst", DecimalType(18, 8)),
      StructField("usd_gst", DecimalType(18, 8)),
      StructField("exchange_rate_option", IntegerType),
      StructField("datadate", IntegerType),
      StructField("product_type", StringType),
      StructField("affiliate_model", StringType)
    )
  )
  private val ledgerBookingBalanceSchema = StructType(
    Seq(
      StructField("booking_id", LongType),
      StructField("supplier_currency", StringType),
      StructField("product_type", StringType),
      StructField("whitelabel", StringType),
      StructField("whitelabel_group", StringType),
      StructField("is_cancelled", BooleanType),
      StructField("booking_date", StringType),
      StructField("payment_usd_total_amount_exc_gst", DecimalType(24, 2)),
      StructField("payment_local_total_amount_exc_gst", DecimalType(24, 2)),
      StructField("payment_local_total_amount_inc_gst", DecimalType(24, 2)),
      StructField("payment_usd_total_amount_inc_gst", DecimalType(24, 2)),
      StructField("approval_usd_total_amount_exc_gst", DecimalType(24, 2)),
      StructField("approval_local_total_amount_exc_gst", DecimalType(24, 2)),
      StructField("approval_local_total_amount_inc_gst", DecimalType(24, 2)),
      StructField("approval_usd_total_amount_inc_gst", DecimalType(24, 2)),
      StructField("booking_usd_total_amount_exc_gst", DecimalType(24, 2)),
      StructField("booking_local_total_amount_exc_gst", DecimalType(24, 2)),
      StructField("booking_usd_total_amount_inc_gst", DecimalType(24, 2)),
      StructField("booking_local_total_amount_inc_gst", DecimalType(24, 2)),
      StructField("accounting_datadate", IntegerType),
      StructField("datadate", IntegerType)
    )
  )
  private val factBookingVehicleAllSchema = StructType(
    Seq(
      StructField("booking_id", LongType),
      StructField("itinerary_id", LongType),
      StructField("multi_product_id", LongType),
      StructField("multi_product_type_id", IntegerType),
      StructField("multi_product_type_name", StringType),
      StructField("booking_date", StringType),
      StructField("booking_datetime", TimestampType),
      StructField("booking_datetimez", TimestampType),
      StructField("time_id", StringType),
      StructField("is_cancelled", BooleanType),
      StructField("cancellation_policy", StringType),
      StructField("cancellation_date", StringType),
      StructField("cancellation_datetime", TimestampType),
      StructField("cancellation_datetimez", TimestampType),
      StructField("arrival_time_window_id", IntegerType),
      StructField("lead_time_day", IntegerType),
      StructField("vehicle_booking_state_id", IntegerType),
      StructField("vehicle_booking_state", StringType),
      StructField("charge_option_id", IntegerType),
      StructField("customer_id", LongType),
      StructField("origin_city_name", StringType),
      StructField("origin_country_id", LongType),
      StructField("origin_country_name", StringType),
      StructField("origin_country_code", StringType),
      StructField("origin_region_id", LongType),
      StructField("origin_region_name", StringType),
      StructField("origin_timezone", StringType),
      StructField("origin_language_id", LongType),
      StructField("origin_language_name", StringType),
      StructField("original_payment_method_id", LongType),
      StructField("original_payment_method_name", StringType),
      StructField("original_payment_model_id", LongType),
      StructField("original_payment_model_name", StringType),
      StructField("original_payment_type_id", LongType),
      StructField("original_payment_type_name", StringType),
      StructField("platform_id", LongType),
      StructField("platform_name", StringType),
      StructField("platform_group_name", StringType),
      StructField("server", StringType),
      StructField("language_id", LongType),
      StructField("language_name", StringType),
      StructField("storefront_id", LongType),
      StructField("storefront_name", StringType),
      StructField("supplier_id", LongType),
      StructField("supplier_name", StringType),
      StructField("supplier_code", StringType),
      StructField("provider_code", StringType),
      StructField("booking_external_reference", StringType),
      StructField("supplier_specific_data", StringType),
      StructField("supplier_status_code", StringType),
      StructField("reject_reason_code", IntegerType),
      StructField("reject_reason_msg", StringType),
      StructField("supplier_commission_amount", StringType),
      StructField("supplier_commission_percentage", StringType),
      StructField("whitelabel_id", IntegerType),
      StructField("whitelabel_name", StringType),
      StructField("fraud_score", IntegerType),
      StructField("fraud_check_ip", StringType),
      StructField("fraud_action", IntegerType),
      StructField("session_id", StringType),
      StructField("client_ip_address", StringType),
      StructField("tracking_cookie_id", StringType),
      StructField("tracking_cookie_date", TimestampType),
      StructField("tracking_tag", StringType),
      StructField("display_currency", StringType),
      StructField("is_first_vehicle_booking", BooleanType),
      StructField("is_confirmed", BooleanType),
      StructField("is_departed", BooleanType),
      StructField("booking_flag", IntegerType),
      StructField("is_completed_booking", IntegerType),
      StructField("rec_status", IntegerType),
      StructField("booking_route_id", IntegerType),
      StructField("booking_route_type", StringType),
      StructField("vehicle_code", StringType),
      StructField("vehicle_name", StringType),
      StructField("vehicle_classification", StringType),
      StructField("trip_pickup_start_date", StringType),
      StructField("trip_pickup_start_datetime", TimestampType),
      StructField("trip_dropoff_end_date", StringType),
      StructField("trip_dropoff_end_datetime", TimestampType),
      StructField("trip_pickup_location_id", LongType),
      StructField("trip_dropoff_location_id", LongType),
      StructField("driver_age", IntegerType),
      StructField("flight_no", StringType),
      StructField("trip_pickup_city_id", LongType),
      StructField("trip_pickup_city_name", StringType),
      StructField("trip_dropoff_city_id", LongType),
      StructField("trip_dropoff_city_name", StringType),
      StructField("trip_pickup_country_id", LongType),
      StructField("trip_pickup_country_name", StringType),
      StructField("trip_dropoff_country_id", LongType),
      StructField("trip_dropoff_country_name", StringType),
      StructField("trip_pickup_country_code", StringType),
      StructField("trip_dropoff_country_code", StringType),
      StructField("trip_pickup_region_id", LongType),
      StructField("trip_pickup_region_name", StringType),
      StructField("trip_dropoff_region_id", LongType),
      StructField("trip_dropoff_region_name", StringType),
      StructField("trip_pickup_address_line", StringType),
      StructField("trip_dropoff_address_line", StringType),
      StructField("trip_pickup_postal_code", StringType),
      StructField("trip_dropoff_postal_code", StringType),
      StructField("trip_pickup_airport_code", StringType),
      StructField("trip_dropoff_airport_code", StringType),
      StructField("trip_pickup_airport_name", StringType),
      StructField("trip_dropoff_airport_name", StringType),
      StructField("trip_pickup_is_airport", BooleanType),
      StructField("trip_dropoff_is_airport", BooleanType),
      StructField("trip_pickup_airport_provider_location", StringType),
      StructField("trip_dropoff_airport_provider_location", StringType),
      StructField("trip_pickup_extra_info", StringType),
      StructField("trip_dropoff_extra_info", StringType),
      StructField("trip_pickup_supplier_location_code", StringType),
      StructField("trip_dropoff_supplier_location_code", StringType),
      StructField("customer_email_domain", StringType),
      StructField("customer_email_md5", StringType),
      StructField("is_email_blacklist", LongType),
      StructField("customer_nationality_id", LongType),
      StructField("customer_nationality", StringType),
      StructField("is_newsletter", LongType),
      StructField("los", IntegerType),
      StructField("total_surcharge", DoubleType),
      StructField("surcharge_details", StringType),
      StructField("base_discount", DoubleType),
      StructField("campaign_discount", DoubleType),
      StructField("total_fare", DoubleType),
      StructField("agoda_fee", DoubleType),
      StructField("first_payment_date", TimestampType),
      StructField("payment_gateway_id", LongType),
      StructField("payment_gateway_name", StringType),
      StructField("original_payment_currency", StringType),
      StructField("original_supplier_currency", StringType),
      StructField("original_creditcard_currency", StringType),
      StructField("original_selling_amount", DoubleType),
      StructField("original_local_payment_amount", DoubleType),
      StructField("original_supplier_amount", DoubleType),
      StructField("original_tax_amount", DoubleType),
      StructField("original_service_charge_amount", DoubleType),
      StructField("original_pricing_downlift_amount", DoubleType),
      StructField("original_uplift_amount", DoubleType),
      StructField("original_supplier_amount_exclusive", DoubleType),
      StructField("original_selling_amount_exclusive", DoubleType),
      StructField("original_exchange_rate_supplier_to_payment", DoubleType),
      StructField("original_site_exchange_rate", DoubleType),
      StructField("original_uplift_exchange_rate", DoubleType),
      StructField("selling_amount", DoubleType),
      StructField("local_payment_amount", DoubleType),
      StructField("supplier_amount", DoubleType),
      StructField("tax_amount", DoubleType),
      StructField("service_charge_amount", DoubleType),
      StructField("pricing_downlift_amount", DoubleType),
      StructField("uplift_amount", DoubleType),
      StructField("financelift_id", IntegerType),
      StructField("supplier_amount_exclusive", DoubleType),
      StructField("selling_amount_exclusive", DoubleType),
      StructField("original_margin", DoubleType),
      StructField("margin", DoubleType),
      StructField("rec_created_when", TimestampType),
      StructField("rec_modified_when", TimestampType),
      StructField("process_datetime", TimestampType),
      StructField("datadate", IntegerType),
      StructField("merchant_of_record", StringType),
      StructField("revenue", StringType),
      StructField("rate_contract", StringType),
      StructField("merchant_of_record_type", StringType),
      StructField("revenue_type", StringType),
      StructField("rate_contract_type", StringType),
      StructField("created_log_time", TimestampType),
      StructField("updated_log_time", TimestampType),
      StructField("original_payment_amount", DoubleType),
      StructField("original_redeem_amount", DoubleType),
      StructField("payment_amount", DoubleType),
      StructField("redeem_amount", DoubleType),
      StructField("refund_amount", DoubleType),
      StructField("postbooking_state_id", IntegerType),
      StructField("postbooking_state", StringType),
      StructField("is_absorption", BooleanType),
      StructField("partner_reward_id", IntegerType),
      StructField("partner_reward_name", StringType),
      StructField("partner_reward_last_modified", TimestampType),
      StructField("original_partner_reward_points_redeemed", LongType),
      StructField("original_partner_reward_points_amount_usd", DoubleType),
      StructField("original_partner_reward_points_earned", LongType),
      StructField("partner_reward_points_redeemed", LongType),
      StructField("partner_reward_points_amount_usd", DoubleType),
      StructField("external_member_id", StringType),
      StructField("external_sub_member_id", StringType),
      StructField("external_program_id", StringType),
      StructField("external_program_name", StringType),
      StructField("is_offline_booking", BooleanType),
      StructField("is_cart_flow", BooleanType),
      StructField("confirmation_datetime", TimestampType),
      StructField("is_partial_success", BooleanType),
      StructField("analytics_session_id", StringType),
      StructField("default_attr_affiliate_id", LongType),
      StructField("default_attr_affiliate_name", StringType),
      StructField("default_attr_affiliate_type", StringType),
      StructField("default_attr_site_id", LongType),
      StructField("default_attr_site_name", StringType),
      StructField("default_attr_site_origin", StringType),
      StructField("default_attr_tag", StringType),
      StructField("default_attr_traffic_group_id", LongType),
      StructField("default_attr_traffic_group_name", StringType),
      StructField("default_attr_traffic_sub_group_id", LongType),
      StructField("default_attr_traffic_sub_group_name", StringType),
      StructField("lpc_30_affiliate_id", LongType),
      StructField("lpc_30_affiliate_name", StringType),
      StructField("lpc_30_affiliate_type", StringType),
      StructField("lpc_30_site_id", LongType),
      StructField("lpc_30_site_name", StringType),
      StructField("lpc_30_site_origin", StringType),
      StructField("lpc_30_tag", StringType),
      StructField("lpc_30_traffic_group_id", LongType),
      StructField("lpc_30_traffic_group_name", StringType),
      StructField("lpc_30_traffic_sub_group_id", LongType),
      StructField("lpc_30_traffic_sub_group_name", StringType),
      StructField("lc_1_affiliate_id", LongType),
      StructField("lc_1_affiliate_name", StringType),
      StructField("lc_1_affiliate_type", StringType),
      StructField("lc_1_site_id", LongType),
      StructField("lc_1_site_name", StringType),
      StructField("lc_1_site_origin", StringType),
      StructField("lc_1_tag", StringType),
      StructField("lc_1_traffic_group_id", LongType),
      StructField("lc_1_traffic_group_name", StringType),
      StructField("lc_1_traffic_sub_group_id", LongType),
      StructField("lc_1_traffic_sub_group_name", StringType),
      StructField("trip_pickup_location_name", StringType),
      StructField("trip_dropoff_location_name", StringType),
      StructField("trip_pickup_location_type", StringType),
      StructField("trip_dropoff_location_type", StringType),
      StructField("trip_pickup_phone_number", StringType),
      StructField("trip_dropoff_phone_number", StringType),
      StructField("charge_option_name", StringType),
      StructField("installment_flag", IntegerType),
      StructField("installment_interest", DoubleType),
      StructField("installment_month_period", IntegerType),
      StructField("original_multi_product_transaction_type", StringType),
      StructField("multi_product_transaction_type", StringType),
      StructField("extra_charge_details", StringType),
      StructField("booking_session_id", StringType),
      StructField("upcoming_hotel_booking", LongType),
      StructField("upcoming_flight_booking", LongType),
      StructField("upcoming_activity_booking", LongType),
      StructField("vehicle_doors", IntegerType),
      StructField("vehicle_seats", IntegerType),
      StructField("vehicle_suitcases", IntegerType),
      StructField("vehicle_transmission", StringType),
      StructField("vehicle_is_aircon", BooleanType),
      StructField("vehicle_is_airbag", BooleanType),
      StructField("vehicle_fuel_type", StringType),
      StructField("vehicle_image_url", StringType),
      StructField("pick_up_supplier_operation_hours", StringType),
      StructField("drop_off_supplier_operation_hours", StringType),
      StructField("provider_icon_url", StringType),
      StructField("acriss_code", StringType),
      StructField("vehicle_mileage_policy", StringType),
      StructField("mileage_policy_code", StringType),
      StructField("mileage_policy_description", StringType),
      StructField("mileage_policy_free_distance", DoubleType),
      StructField("mileage_policy_is_free_coverage", StringType),
      StructField("mileage_policy_charge_amount", DoubleType),
      StructField("mileage_policy_charge_currency", StringType),
      StructField("mileage_policy_charge_unit", StringType),
      StructField("mileage_policy_charge_per_unit", StringType),
      StructField("vehicle_fuel_policy", StringType),
      StructField("fuel_policy_code", StringType),
      StructField("fuel_policy_coverage_type", StringType),
      StructField("fuel_policy_description", StringType),
      StructField("fuel_policy_is_free_coverage", StringType),
      StructField("datamonth", IntegerType)
    )
  )
  private val apReconciliationManualDetailAdjustmentSchema = StructType(
    Seq(
      StructField("uuid", StringType),
      StructField("booking_id", LongType),
      StructField("approval_id", IntegerType),
      StructField("supplier_id", IntegerType),
      StructField("sub_supplier_id", IntegerType),
      StructField("supplier_currency", StringType),
      StructField("local_amount_inc_gst", DecimalType(24, 2)),
      StructField("local_commission_inc_gst", DecimalType(24, 2)),
      StructField("local_total_amount_inc_gst", DecimalType(24, 2)),
      StructField("local_amount_exc_gst", DecimalType(24, 2)),
      StructField("local_commission_exc_gst", DecimalType(24, 2)),
      StructField("local_total_amount_exc_gst", DecimalType(24, 2)),
      StructField("usd_amount_inc_gst", DecimalType(24, 2)),
      StructField("usd_commission_inc_gst", DecimalType(24, 2)),
      StructField("usd_total_amount_inc_gst", DecimalType(24, 2)),
      StructField("usd_amount_exc_gst", DecimalType(24, 2)),
      StructField("usd_commission_exc_gst", DecimalType(24, 2)),
      StructField("usd_total_amount_exc_gst", DecimalType(24, 2)),
      StructField("accounting_date", StringType),
      StructField("transaction_date", StringType),
      StructField("is_agency", BooleanType),
      StructField("is_advance_guarantee", BooleanType),
      StructField("merchant_of_record_entity", IntegerType),
      StructField("revenue_entity", IntegerType),
      StructField("rate_contract_entity", IntegerType),
      StructField("approval_reference", StringType),
      StructField("payment_method_id", IntegerType),
      StructField("payment_method", StringType),
      StructField("remark", StringType),
      StructField("reason", StringType),
      StructField("type", StringType),
      StructField("sub_type", StringType),
      StructField("source", StringType),
      StructField("status", StringType),
      StructField("reconciliation_instance", StringType),
      StructField("side", StringType),
      StructField("datadate", IntegerType),
      StructField("product_type", StringType),
      StructField("submission_id", StringType),
      StructField("submitted_by", StringType),
      StructField("submitted_email", StringType),
      StructField("adjustment_data_status", StringType),
      StructField("adjustment_datadate", IntegerType)
    )
  )
  private val exchangeRatesSchema = StructType(
    Seq(
      StructField("exchange_rate_date", TimestampType),
      StructField("source_currency_code", StringType),
      StructField("real_exchange_rate", DoubleType),
      StructField("destination_currency_code", StringType),
      StructField("rec_status", IntegerType),
      StructField("rec_created_when", TimestampType),
      StructField("rec_created_by", StringType),
      StructField("rec_modify_when", TimestampType),
      StructField("rec_modify_by", StringType),
      StructField("ttl", IntegerType),
      StructField("created_log_time", TimestampType),
      StructField("updated_log_time", TimestampType),
      StructField("currency_code", StringType),
      StructField("datadate", StringType)
    )
  )
  var settlementsDataframe: DataFrame                    = _
  var financialTransactionsDataframe: DataFrame          = _
  var ledgerBookingBalanceDataframe: DataFrame           = _
  var factBookingAllDataframe: DataFrame                 = _
  var factBookingVehicleDataframe: DataFrame             = _
  var ebeBookingSettlementAdjustmentDataframe: DataFrame = _
  var exchangeRatesDataframe: DataFrame                  = _
  var settlementsUnmatchMatchedDataframe: DataFrame      = _

  def setUpAll()(implicit spark: SparkSession): Unit = {
    spark.conf.set("spark.sql.session.timeZone", "UTC+7")

    settlementsDataframe = spark.read
      .option("header", true)
      .schema(settlementSchema)
      .csv(getClass.getResource("/settlement_upc_data/all_tables_csv/settlements_upc.csv").getPath)

    financialTransactionsDataframe = spark.read
      .option("header", true)
      .schema(financialTransactionsSchema)
      .csv(getClass.getResource("/settlement_upc_data/all_tables_csv/financial_transactions_upc.csv").getPath)

    ledgerBookingBalanceDataframe = spark.read
      .option("header", true)
      .schema(ledgerBookingBalanceSchema)
      .csv(getClass.getResource("/settlement_upc_data/all_tables_csv/ledger_booking_balance.csv").getPath)

    factBookingAllDataframe = spark.read
      .option("header", true)
      .schema(factBookingAllSchema)
      .csv(getClass.getResource("/settlement_upc_data/all_tables_csv/fact_booking_all.csv").getPath)

    factBookingVehicleDataframe = spark.read
      .option("header", true)
      .schema(factBookingVehicleAllSchema)
      .csv(getClass.getResource("/settlement_upc_data/all_tables_csv/fact_booking_vehicle.csv").getPath)

    ebeBookingSettlementAdjustmentDataframe = spark.read
      .option("header", true)
      .schema(apReconciliationManualDetailAdjustmentSchema)
      .csv(getClass.getResource("/settlement_upc_data/all_tables_csv/manual_adjustment.csv").getPath)

    exchangeRatesDataframe = spark.read
      .option("header", true)
      .schema(exchangeRatesSchema)
      .csv(getClass.getResource("/settlement_upc_data/all_tables_csv/exchange_rates.csv").getPath)

    settlementsUnmatchMatchedDataframe = spark.read
      .option("header", true)
      .schema(settlementSchema)
      .csv(getClass.getResource("/settlement_upc_data/all_tables_csv/settlement_unmatched_upc.csv").getPath)

  }

}
