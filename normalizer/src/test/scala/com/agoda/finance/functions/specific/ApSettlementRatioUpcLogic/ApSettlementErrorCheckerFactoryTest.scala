package com.agoda.finance.functions.specific.ApSettlementRatioUpcLogic

import com.agoda.ml.spark.services.etl.{DataFrameETLExtensionLocalTest, SparkETLLocalTest}
import org.apache.spark.sql.types._
import org.apache.spark.sql.{Row, SparkSession}

class ApSettlementErrorCheckerFactoryTest extends SparkETLLocalTest with DataFrameETLExtensionLocalTest {
  override def beforeAll(): Unit = {
    super.beforeAll()
    setUpAll()(sparkSession)
  }

  def setUpAll()(implicit spark: SparkSession): Unit =
    spark.conf.set("spark.sql.session.timeZone", "UTC+7")

  // Helper to create an empty DataFrame with a given schema
  def emptyDf(schema: StructType): org.apache.spark.sql.DataFrame =
    spark.createDataFrame(spark.sparkContext.emptyRDD[Row], schema)

  def settlementDf(rows: Seq[Row]): org.apache.spark.sql.DataFrame = {
    val schema = StructType(
      Seq(
        StructField("booking_id", StringType),
        StructField("original_settlement_currency", StringType),
        StructField("settlement_currency", StringType),
        StructField("original_settlement_amount", StringType),
        StructField("settlement_amount", StringType),
        StructField("transaction_date", StringType)
      )
    )
    spark.createDataFrame(spark.sparkContext.parallelize(rows), schema)
  }

  def bookingDf(rows: Seq[Row]): org.apache.spark.sql.DataFrame = {
    val schema = StructType(
      Seq(
        StructField("booking_id", StringType),
        StructField("supplier_currency", StringType),
        StructField("local_total_amount_inc_gst", StringType),
        StructField("usd_total_amount_inc_gst", StringType)
      )
    )
    spark.createDataFrame(spark.sparkContext.parallelize(rows), schema)
  }

  test("settlementChecker: detects null and empty fields with correct error reasons") {
    val emptySettlementTodayDf = {
      val schema = StructType(Seq(StructField("booking_id", StringType)))
      emptyDf(schema)
    }

    val rows = Seq(
      Row("b1", null, "USD", "100", "100", "2024-01-01"), // original_settlement_currency is null
      Row("b2", "USD", null, "100", "100", "2024-01-01"), // settlement_currency is null
      Row("b3", "USD", "USD", null, "100", "2024-01-01"), // original_settlement_amount is null
      Row("b4", "USD", "USD", "", "100", "2024-01-01"),   // original_settlement_amount is empty
      Row("b5", "USD", "USD", "100", null, "2024-01-01"), // settlement_amount is null
      Row("b6", "USD", "USD", "100", "", "2024-01-01"),   // settlement_amount is empty
      Row("b7", "USD", "USD", "100", "100", null),        // transaction_date is null
      Row("b8", "USD", "USD", "100", "100", ""),          // transaction_date is empty
      Row("b9", "USD", "USD", "100", "100", "2024-01-01") // valid row
    )
    val df      = settlementDf(rows)
    val checker = ApSettlementErrorCheckerFactory.settlementChecker

    val errors = checker
      .collectErrors(df, emptySettlementTodayDf)
      .select("booking_id", "error_reason")
      .collect()
      .map(r => (r.getAs[String]("booking_id"), r.getAs[String]("error_reason")))
      .toSet

    assert(errors.contains(("b1", "original_settlement_currency is null")))
    assert(errors.contains(("b2", "settlement_currency is null")))
    assert(errors.contains(("b3", "original_settlement_amount is null or empty")))
    assert(errors.contains(("b4", "original_settlement_amount is null or empty")))
    assert(errors.contains(("b5", "settlement_amount is null or empty")))
    assert(errors.contains(("b6", "settlement_amount is null or empty")))
    assert(errors.contains(("b7", "transaction_date is null or empty")))
    assert(errors.contains(("b8", "transaction_date is null or empty")))
    assert(!errors.exists(_._1 == "b9")) // valid row should not have error
  }

  test("bookingUniverseChecker: detects null/empty fields and multi-currency error") {
    val emptySettlementTodayDf = {
      val schema = StructType(Seq(StructField("booking_id", StringType)))
      emptyDf(schema)
    }

    val rows = Seq(
      Row(null, "USD", "100", "100"), // booking_id is null
      Row("b1", null, "100", "100"),  // supplier_currency is null
      Row("b2", "", "100", "100"),    // supplier_currency is empty
      Row("b3", "USD", null, "100"),  // local_total_amount_inc_gst is null
      Row("b4", "USD", "", "100"),    // local_total_amount_inc_gst is empty
      Row("b5", "USD", "100", null),  // usd_total_amount_inc_gst is null
      Row("b6", "USD", "100", ""),    // usd_total_amount_inc_gst is empty
      Row("b7", "USD", "100", "100"), // valid row
      Row("b8", "USD", "100", "100"), // for multi-currency
      Row("b8", "THB", "100", "100")  // for multi-currency
    )
    val df      = bookingDf(rows)
    val checker = ApSettlementErrorCheckerFactory.bookingUniverseChecker
    val errors = checker
      .collectErrors(df, emptySettlementTodayDf)
      .select("booking_id", "error_reason")
      .collect()
      .map(r => (r.getAs[String]("booking_id"), r.getAs[String]("error_reason")))
      .toSet

    assert(errors.contains((null, "booking_id is null")))
    assert(errors.contains(("b1", "supplier_currency is null or empty")))
    assert(errors.contains(("b2", "supplier_currency is null or empty")))
    assert(errors.contains(("b3", "local_total_amount_inc_gst is null or empty")))
    assert(errors.contains(("b4", "local_total_amount_inc_gst is null or empty")))
    assert(errors.contains(("b5", "usd_total_amount_inc_gst is null or empty")))
    assert(errors.contains(("b6", "usd_total_amount_inc_gst is null or empty")))
    assert(!errors.exists(_._1 == "b7")) // valid row should not have error
    // Multi-currency error for b8
    assert(errors.exists { case (id, reason) => id == "b8" && reason == "booking_id has multiple currencies" })
  }

  test("crossDfError: detects row count mismatch between factBookingData and todaySettlementsUpc") {
    val factRows = Seq(
      Row("b1", "USD", "100", "100"),
      Row("b2", "USD", "100", "100")
    )
    val todayRows = Seq(
      Row("b1"),
      Row("b2"),
      Row("b3") // extra booking_id
    )
    val factDf = bookingDf(factRows)
    val todayDf = {
      val schema = StructType(Seq(StructField("booking_id", StringType)))
      spark.createDataFrame(
        spark.sparkContext.parallelize(todayRows),
        schema
      )
    }
    val checker = ApSettlementErrorCheckerFactory.bookingUniverseChecker
    val errors = checker
      .collectErrors(factDf, todayDf)
      .select("error_reason")
      .collect()
      .map(_.getAs[String]("error_reason"))

    assert(errors.exists(_.contains("Row count mismatch")))
  }

  override def afterAll(): Unit = {
    spark.stop()
    super.afterAll()
  }
}
