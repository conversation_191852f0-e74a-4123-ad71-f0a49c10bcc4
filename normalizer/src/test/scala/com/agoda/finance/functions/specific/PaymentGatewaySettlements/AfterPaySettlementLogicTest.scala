package com.agoda.finance.functions.specific.PaymentGatewaySettlements

import com.agoda.ml.spark.DataFrameMatchers.beEqualTo
import com.agoda.ml.spark.{HiveSupport, SparkSharedLocalTest}
import org.apache.spark.sql.Row
import org.apache.spark.sql.functions.col
import org.apache.spark.sql.types.{LongType, StringType, StructField, StructType}
import org.scalatest.{FunSuite, Matchers}

class AfterPaySettlementLogicTest extends FunSuite with SparkSharedLocalTest with HiveSupport with Matchers {
  import sqlContext.implicits._

  test("getAmount should convert currency amounts correctly") {
    val resultSchema = StructType(
      List(
        StructField("amount", StringType, nullable = true)
      )
    )
    val inputValue = Seq(
      Row("$1000"),
      Row("£2000"),
      Row("$0"),
      Row("invalid"),
      Row(null)
    )

    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)

    val actual = df
      .withColumn("result", AfterPaySettlementLogic.getAmount(col("amount")))
      .select("result")

    val expected = Seq(
      Row("10.0000"), // $1000/100
      Row("20.0000"), // £2000/100
      Row("0.0000"),  // $0/100
      Row("0.0000"),  // invalid -> 0
      Row("0.0000")   // null -> 0
    )

    val expectedDf = spark.createDataFrame(
      spark.sparkContext.parallelize(expected),
      StructType(List(StructField("result", StringType, nullable = true)))
    )

    actual should beEqualTo(expectedDf)
  }

  test("getGatewayUniqueId should generate correct IDs") {
    val resultSchema = StructType(
      List(
        StructField("eventType", StringType),
        StructField("orderId", StringType),
        StructField("refundId", StringType)
      )
    )
    val inputValue = Seq(
      Row("Refund", "ORDER123", "REF456"),
      Row("Payment", "ORDER789", "REF000"),
      Row("Refund", null, null),
      Row(null, "ORDER999", "REF111")
    )

    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)

    val actual = df
      .withColumn(
        "result",
        AfterPaySettlementLogic.getGatewayUniqueId(
          col("eventType"),
          col("orderId"),
          col("refundId")
        )
      )
      .select("result")

    val expected = Seq(
      Row("ORDER123-REF456"), // Refund case: orderId-refundId
      Row("ORDER789"),        // Non-refund case: just orderId
      Row("null-null"),       // Null handling for refund
      Row("ORDER999")         // Null event type defaults to orderId
    )

    val expectedDf = spark.createDataFrame(
      spark.sparkContext.parallelize(expected),
      StructType(List(StructField("result", StringType, nullable = true)))
    )

    actual should beEqualTo(expectedDf)
  }

  test("convertDateStringtoBigInt should handle various date formats") {
    // Test valid date formats
    val validDates = Seq(
      ("26 Jun 25", 1750896000000L),          // 26 Jun 2025 00:00:00 UTC
      ("2025-06-26T10:30:00", 1750933800000L) // 2025-06-26T10:30:00 UTC
    )

    val dateConverter = AfterPaySettlementLogic.convertDateStringtoBigInt("yyyy-MM-dd'T'HH:mm:ss")

    // Create DataFrame with valid dates
    val validInputSchema = StructType(List(StructField("dateString", StringType, nullable = true)))
    val validInputValue  = validDates.map { case (date, _) => Row(date) }
    val df               = spark.createDataFrame(spark.sparkContext.parallelize(validInputValue), validInputSchema)

    val actual = df
      .withColumn("result", dateConverter(col("dateString")))
      .select("result")

    val expectedSchema = StructType(List(StructField("result", LongType, nullable = true)))
    val expected       = validDates.map { case (_, timestamp) => Row(timestamp) }
    val expectedDf     = spark.createDataFrame(spark.sparkContext.parallelize(expected), expectedSchema)

    actual should beEqualTo(expectedDf)
  }

  test("convertDateStringtoBigInt should handle date-only formats") {
    // Test date-only formats like dd/MM/yyyy
    val dateOnlyDates = Seq(
      ("26/06/2025", 1750896000000L), // 26 Jun 2025 00:00:00 UTC
      ("01/01/2025", 1735689600000L)  // 01 Jan 2025 00:00:00 UTC
    )

    val dateConverter = AfterPaySettlementLogic.convertDateStringtoBigInt("dd/MM/yyyy")

    // Create DataFrame with date-only dates
    val validInputSchema = StructType(List(StructField("dateString", StringType, nullable = true)))
    val validInputValue  = dateOnlyDates.map { case (date, _) => Row(date) }
    val df               = spark.createDataFrame(spark.sparkContext.parallelize(validInputValue), validInputSchema)

    val actual = df
      .withColumn("result", dateConverter(col("dateString")))
      .select("result")

    val expectedSchema = StructType(List(StructField("result", LongType, nullable = true)))
    val expected       = dateOnlyDates.map { case (_, timestamp) => Row(timestamp) }
    val expectedDf     = spark.createDataFrame(spark.sparkContext.parallelize(expected), expectedSchema)

    actual should beEqualTo(expectedDf)

    // Test invalid dates
    val invalidDates = Seq(
      Row(null),
      Row("")
    )

    val invalidDf = spark
      .createDataFrame(spark.sparkContext.parallelize(invalidDates), validInputSchema)
      .withColumn("result", dateConverter(col("dateString")))

    // The collect() operation should throw an exception
    val thrown = intercept[org.apache.spark.SparkException] {
      invalidDf.collect()
    }

    // Check that the root cause is our IllegalArgumentException
    val rootCause = thrown.getCause.getCause
    assert(rootCause.isInstanceOf[IllegalArgumentException])
    assert(rootCause.getMessage == "Date string cannot be null or empty")
  }
}
