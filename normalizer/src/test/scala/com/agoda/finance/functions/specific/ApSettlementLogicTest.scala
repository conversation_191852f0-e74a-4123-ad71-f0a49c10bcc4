package com.agoda.finance.functions.specific

import com.agoda.finance.functions.specific.ApSettlementLogic._
import com.agoda.ml.spark.DataFrameMatchers._
import com.agoda.ml.spark._
import org.apache.spark.sql._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import org.scalatest.FunSuite
import org.scalatest.Matchers.convertToAnyShouldWrapper

import java.sql.Timestamp
import java.text.SimpleDateFormat

class ApSettlementLogicTest extends FunSuite with SparkSharedLocalTest with HiveSupport {

  val nullS: String = null.asInstanceOf[String]
  val D: String     = "DEDUCT"

  val resultSchema: StructType = StructType(
    List(
      StructField("sub_supplier_id", LongType),
      StructField("transaction_datetime", TimestampType),
      StructField("posting_datetime", TimestampType),
      StructField("original_settlement_currency", StringType),
      <PERSON>ruct<PERSON>ield("settlement_currency", StringType),
      <PERSON>ruct<PERSON>ield("original_settlement_amount", DoubleType),
      StructField("settlement_amount", DoubleType),
      StructField("settlement_amount_usd", DoubleType),
      StructField("rate_currency", StringType),
      StructField("rate_ex", DoubleType),
      StructField("settlement_ex", DoubleType),
      StructField("posting_ex", DoubleType),
      StructField("report_date", IntegerType)
    )
  )

  val resultTutukaSchema: StructType = StructType(
    List(
      StructField("sub_supplier_id", LongType),
      StructField("transaction_datetime", TimestampType),
      StructField("posting_datetime", TimestampType),
      StructField("original_settlement_currency", StringType),
      StructField("settlement_currency", StringType),
      StructField("original_settlement_amount", DoubleType),
      StructField("settlement_amount", DoubleType),
      StructField("settlement_amount_usd", DoubleType),
      StructField("rate_currency", StringType),
      StructField("rate_ex", DoubleType),
      StructField("settlement_ex", DoubleType),
      StructField("posting_ex", DoubleType),
      StructField("report_date", IntegerType),
      StructField("product_name", StringType),
      StructField("provider_card_classification_id", StringType),
      StructField("acquirer_reference_number", StringType),
      StructField("transaction_authorisation_number", StringType),
      StructField("merchant_country", StringType),
      StructField("merchant_name", StringType),
      StructField("merchant_address", StringType),
      StructField("merchant_city", StringType),
      StructField("merchant_post_code", StringType)
    )
  )

  val resultCheckoutSchema: StructType = StructType(
    List(
      StructField("sub_supplier_id", LongType),
      StructField("transaction_datetime", TimestampType),
      StructField("posting_datetime", TimestampType),
      StructField("original_settlement_currency", StringType),
      StructField("settlement_currency", StringType),
      StructField("original_settlement_amount", DoubleType),
      StructField("settlement_amount", DoubleType),
      StructField("settlement_amount_usd", DoubleType),
      StructField("rate_currency", StringType),
      StructField("rate_ex", DoubleType),
      StructField("settlement_ex", DoubleType),
      StructField("posting_ex", DoubleType),
      StructField("report_date", IntegerType),
      StructField("product_name", StringType),
      StructField("provider_card_classification_id", StringType)
    )
  )

  val bookingInfoSchema: StructType = StructType(
    List(
      StructField("sub_supplier_id", LongType),
      StructField("booking_id", StringType),
      StructField("batch_id", StringType),
      StructField("dmc_id", StringType)
    )
  )

  test("appendWexAdditionalInfo") {
    val wexData = wex(
      Row("09/24/2023", "09/23/2023", 1001L, "THB", 4200.00, "THB", 4200.00, "AgodaUPCTrans_20231103.csv"), // same currency
      Row("09/24/2023", "09/23/2023", 1002L, "THB", 4200.00, "THB", 4200.00, "AgodaUPCTrans_20231103.csv"), // different currency
      Row("09/24/2023", "09/23/2023", 1003L, "THB", 4200.00, "THB", 4200.00, "AgodaUPCTrans_20231103.csv"), // no hotel
      Row("09/01/2023", "09/23/2023", 1004L, "THB", 4200.00, "THB", 4200.00, "AgodaUPCTrans_20231103.csv"), // no ex transaction date
      Row("09/24/2023", "09/01/2023", 1004L, "THB", 4200.00, "THB", 4200.00, "AgodaUPCTrans_20231103.csv"), // no ex posting date
      Row("09/24/2023", "09/23/2023", 1005L, "THB", 4200.00, "THB", 4200.00, "AgodaUPCTrans_20231103.csv"), // no current hotel
      Row("09/24/2023", "09/23/2023", 1006L, "THB", 4200.00, "THB", 4200.00, "AgodaUPCTrans_20231103.csv")  // no audit hotel
    )
      .withColumn("booking_id", lit("1"))
      .withColumn("batch_id", lit("2"))

    val history = hotelHistory(
      Row(1001, "THB", 20230924),
      Row(1002, "SGD", 20230924),
      Row(1004, "THB", 20230901),
      Row(1004, "THB", 20230924),
      Row(1005, "THB", 20230924),
      Row(1006, "THB", 20230924)
    )

    val ex = exchangeRate(
      Row("HKD", 0.12700, "20230923"),
      Row("SGD", 0.7300, "20230923"),
      Row("THB", 0.0270, "20230923"),
      Row("HKD", 0.12786, "20230924"),
      Row("SGD", 0.7325, "20230924"),
      Row("THB", 0.0278, "20230924")
    )

    val expect = result(
      Row(1001L, ts("2023-09-24"), ts("2023-09-23"), "THB", "THB", 4200.00, 4200.00, 116.75999999999999, "THB", 0.0278, 0.0278, 0.027, 20231103),
      Row(
        1002L,
        ts("2023-09-24"),
        ts("2023-09-23"),
        "THB",
        "SGD",
        4200.00,
        159.39931740614333,
        116.75999999999999,
        "SGD",
        0.7325,
        0.0278,
        0.027,
        20231103
      ),
      Row(1003L, ts("2023-09-24"), ts("2023-09-23"), "THB", nullS, 4200.00, nullS, 116.75999999999999, nullS, nullS, 0.0278, 0.027, 20231103),
      Row(1004L, ts("2023-09-01"), ts("2023-09-23"), "THB", "THB", 4200.00, nullS, 113.4, "THB", nullS, nullS, 0.027, 20231103),
      Row(1004L, ts("2023-09-24"), ts("2023-09-01"), "THB", "THB", 4200.00, 4200.00, 116.75999999999999, "THB", 0.0278, 0.0278, nullS, 20231103),
      Row(1005L, ts("2023-09-24"), ts("2023-09-23"), "THB", "THB", 4200.00, 4200.00, 116.75999999999999, "THB", 0.0278, 0.0278, 0.027, 20231103),
      Row(1006L, ts("2023-09-24"), ts("2023-09-23"), "THB", "THB", 4200.00, 4200.00, 116.75999999999999, "THB", 0.0278, 0.0278, 0.027, 20231103)
    )

    val actual =
      ApSettlementLogic.appendWexAdditionalInfo(wexData, history, ex).select(resultSchema.fields.map(f => col(f.name)): _*)

    actual should beEqualTo(expect)
  }
  val uuid1 = "1c0d820a-1521-4955-ab95-6d0d5d9fad18"
  val uuid2 = "2b879da0-1b0c-4aa8-8300-7dd66754d353"
  val uuid3 = "3c8882cf-42ce-4aa6-aa45-4b3f4a1d8e3e"
  val uuid4 = "47d7e368-67eb-4138-a034-f00e0193c496"
  val uuid5 = "5df02e71-9775-48d2-9760-231bcd941e33"
  val uuid6 = "6f1af334-63f8-4ea8-94f5-c4958d473740"
  val uuid7 = "7f711254-9d0a-4ce0-8d95-9b86161c580d"
  val uuid8 = "80181354-21da-4447-9476-a10e92f70f95"
  val uuid9 = "d9b281f7-e3f1-4e1e-bce0-bda459691a85"

  test("appendEnettAdditionalInfo") {

    // same currency
    // different currency
    // no hotel
    // no ex transaction date
    // no ex posting date
    // no current hotel
    // no audit hotel
    // not found uuid
    val enettData = enett(
      Row(
        uuid1,
        11L,
        12L,
        "13",
        14L,
        "2023-09-24 00:00:00:000",
        "2023-09-23 00:00:00:000",
        "THB",
        4200.00,
        "THB",
        4200.00,
        "asdasf",
        "DailyVANsSettlement_304100_410032_20240307"
      ),
      Row(
        uuid2,
        21L,
        22L,
        "23",
        24L,
        "2023-09-24 00:00:00:000",
        "2023-09-23 00:00:00:000",
        "THB",
        4200.00,
        "THB",
        4200.00,
        "dsgftd",
        "DailyVANsSettlement_304100_410032_20240307"
      ),
      Row(
        uuid3,
        31L,
        32L,
        "33",
        34L,
        "2023-09-24 00:00:00:000",
        "2023-09-23 00:00:00:000",
        "THB",
        4200.00,
        "THB",
        4200.00,
        "eyrtur",
        "DailyVANsSettlement_304100_410032_20240307"
      ),
      Row(
        uuid4,
        41L,
        42L,
        "43",
        44L,
        "2023-09-01 00:00:00:000",
        "2023-09-23 00:00:00:000",
        "THB",
        4200.00,
        "THB",
        4200.00,
        "owrufj",
        "DailyVANsSettlement_304100_410032_20240307"
      ),
      Row(
        uuid5,
        51L,
        52L,
        "53",
        54L,
        "2023-09-24 00:00:00:000",
        "2023-09-01 00:00:00:000",
        "THB",
        4200.00,
        "THB",
        4200.00,
        "pwowma",
        "DailyVANsSettlement_304100_410032_20240307"
      ),
      Row(
        uuid6,
        61L,
        62L,
        "63",
        64L,
        "2023-09-24 00:00:00:000",
        "2023-09-23 00:00:00:000",
        "THB",
        4200.00,
        "THB",
        4200.00,
        "virhbs",
        "DailyVANsSettlement_304100_410032_20240307"
      ),
      Row(
        uuid7,
        71L,
        72L,
        "73",
        74L,
        "2023-09-24 00:00:00:000",
        "2023-09-23 00:00:00:000",
        "THB",
        4200.00,
        "THB",
        4200.00,
        "ibunyg",
        "DailyVANsSettlement_304100_410032_20240307"
      ),
      Row(
        uuid8,
        81L,
        82L,
        "83",
        14L,
        "2023-09-24 00:00:00:000",
        "2023-09-23 00:00:00:000",
        "THB",
        4200.00,
        "THB",
        4200.00,
        "addece",
        "DailyVANsSettlement_304100_410032_20240307"
      )
    )

    val upcData = upcTransaction(
      Row(uuid1, "Map(hotelId -> 1001)", 1686736930124L, "20230923", "1c0"),
      Row(uuid2, "Map(hotelId -> 1002)", 1686736914649L, "20230923", "2b8"),
      Row(uuid3, "Map(hotelId -> 1003)", 1686736899185L, "20230923", "3c8"),
      Row(uuid4, "Map(hotelId -> 1004)", 1686736894016L, "20230923", "47d"),
      Row(uuid5, "Map(hotelId -> 1004)", 1686736894016L, "20230923", "5df"),
      Row(uuid6, "Map(hotelId -> 1005)", 1686736894020L, "20230923", "6f1"),
      Row(uuid7, "Map(hotelId -> 1006)", 1686736894020L, "20230923", "7f7")
    )

    val history = hotelHistory(
      Row(1001, "THB", 20230924),
      Row(1002, "SGD", 20230924),
      Row(1004, "THB", 20230901),
      Row(1004, "THB", 20230924),
      Row(1005, "THB", 20230924),
      Row(1006, "THB", 20230924),
      Row(81, "THB", 20230924)
    )

    val ex = exchangeRate(
      Row("HKD", 0.12700, "20230923"),
      Row("SGD", 0.7300, "20230923"),
      Row("THB", 0.0270, "20230923"),
      Row("HKD", 0.12786, "20230924"),
      Row("SGD", 0.7325, "20230924"),
      Row("THB", 0.0278, "20230924")
    )

    val expect = result(
      Row(1001L, ts("2023-09-24"), ts("2023-09-23"), "THB", "THB", 4200.00, 4200.00, 116.75999999999999, "THB", 0.0278, 0.0278, 0.027, 20240307),
      Row(
        1002L,
        ts("2023-09-24"),
        ts("2023-09-23"),
        "THB",
        "SGD",
        4200.00,
        159.39931740614333,
        116.75999999999999,
        "SGD",
        0.7325,
        0.0278,
        0.027,
        20240307
      ),
      Row(1003L, ts("2023-09-24"), ts("2023-09-23"), "THB", nullS, 4200.00, nullS, 116.75999999999999, nullS, nullS, 0.0278, 0.027, 20240307),
      Row(1004L, ts("2023-09-01"), ts("2023-09-23"), "THB", "THB", 4200.00, nullS, 113.4, "THB", nullS, nullS, 0.027, 20240307),
      Row(1004L, ts("2023-09-24"), ts("2023-09-01"), "THB", "THB", 4200.00, 4200.00, 116.75999999999999, "THB", 0.0278, 0.0278, nullS, 20240307),
      Row(1005L, ts("2023-09-24"), ts("2023-09-23"), "THB", "THB", 4200.00, 4200.00, 116.75999999999999, "THB", 0.0278, 0.0278, 0.027, 20240307),
      Row(1006L, ts("2023-09-24"), ts("2023-09-23"), "THB", "THB", 4200.00, 4200.00, 116.75999999999999, "THB", 0.0278, 0.0278, 0.027, 20240307),
      Row(81L, ts("2023-09-24"), ts("2023-09-23"), "THB", "THB", 4200.00, 4200.00, 116.75999999999999, "THB", 0.0278, 0.0278, 0.027, 20240307)
    )

    val actual =
      ApSettlementLogic.appendEnettAdditionalInfo(enettData, upcData, history, ex).select(resultSchema.fields.map(f => col(f.name)): _*)

    actual should beEqualTo(expect)
  }

  test("appendTutukaAdditionalInfo") {

    val tutukaData = tutuka(
      Row(
        uuid1,
        "764",
        4200.00,
        "764",
        4200.00,
        D,
        "asdasf",
        "AgodaVCNEURHighRiskDailySettlements20230924.csv",
        "",
        "",
        ""
      ),
      Row(
        uuid2,
        "764",
        4200.00,
        "764",
        4200.00,
        D,
        "dsgftd",
        "AgodaVCNEURHighRiskDailySettlements20230924.csv",
        "",
        "",
        ""
      ), // different currency
      Row(
        uuid3,
        "764",
        4200.00,
        "764",
        4200.00,
        D,
        "eyrtur",
        "AgodaVCNEURHighRiskDailySettlements20230924.csv",
        "",
        "",
        ""
      ), // no hotel
      Row(
        uuid4,
        "764",
        4200.00,
        "764",
        4200.00,
        D,
        "owrufj",
        "AgodaVCNEURHighRiskDailySettlements20230901.csv",
        "",
        "",
        ""
      ), // no ex transaction date
      Row(
        uuid5,
        "764",
        4200.00,
        "764",
        4200.00,
        D,
        "pwowma",
        "AgodaVCNEURHighRiskDailySettlements20230924.csv",
        "",
        "",
        ""
      ), // no ex posting date
      Row(
        uuid6,
        "764",
        4200.00,
        "764",
        4200.00,
        D,
        "virhbs",
        "AgodaVCNEURHighRiskDailySettlements20230924.csv",
        "",
        "",
        ""
      ), // no current hotel
      Row(
        uuid7,
        "764",
        4200.00,
        "764",
        4200.00,
        D,
        "ibunyg",
        "AgodaVCNEURHighRiskDailySettlements20230924.csv",
        "",
        "",
        ""
      ) // no audit hotel
    )

    val upcData = upcTransaction(
      Row(
        uuid1,
        "Map(hotelId -> 1001, bookingId -> 2001, dmcId -> 3001, productType -> 1, providerCampaignReference -> EB95D939-E0FC-60B6-CB26F5C9FCAF4F18)",
        1686736930124L,
        "20230923",
        "1c0"
      ),
      Row(uuid2, "Map(hotelId -> 1002, bookingId -> 2002, dmcId -> 3002)", 1686736914649L, "20230923", "2b8"),
      Row(uuid3, "Map(hotelId -> 1003, bookingId -> 2003, dmcId -> 3003)", 1686736899185L, "20230923", "3c8"),
      Row(uuid4, "Map(hotelId -> 1004, bookingId -> 2004, dmcId -> 3004)", 1686736894016L, "20230923", "47d"),
      Row(uuid5, "Map(hotelId -> 1004, bookingId -> 2004, dmcId -> 3004)", 1686736894016L, "20230923", "5df"),
      Row(uuid6, "Map(hotelId -> 1005, batchId -> 2005, dmcId -> 3005)", 1686736894020L, "20230923", "6f1"),
      Row(uuid7, "Map(hotelId -> 1006, batchId -> 2006, dmcId -> 3006)", 1686736894020L, "20230923", "7f7")
    )

    val currencyData = currency(
      Row("764", "THB")
    )

    val history = hotelHistory(
      Row(1001, "THB", 20230923),
      Row(1002, "SGD", 20230923),
      Row(1004, "THB", 20230831),
      Row(1004, "THB", 20230923),
      Row(1005, "THB", 20230923),
      Row(1006, "THB", 20230923)
    )

    val ex = exchangeRate(
      Row("HKD", 0.12700, "20230923"),
      Row("SGD", 0.7300, "20230924"),
      Row("THB", 0.0270, "20230924"),
      Row("HKD", 0.12786, "20230924"),
      Row("SGD", 0.7325, "20230923"),
      Row("THB", 0.0278, "20230923")
    )

    val cd = campaignDetails(
      Row("Agoda VCN EUR HighRisk", "MBS", "EB95D939-E0FC-60B6-CB26F5C9FCAF4F18")
    )

    val expect = resultTutuka(
      Row(
        1001L,
        ts("2023-09-23"),
        ts("2023-09-24"),
        "THB",
        "THB",
        4200.00,
        4200.00,
        116.75999999999999,
        "THB",
        0.0278,
        0.0278,
        0.0270,
        20230924,
        "MBS",
        "EB95D939-E0FC-60B6-CB26F5C9FCAF4F18",
        "",
        "",
        null,
        null,
        null,
        null,
        null
      ),
      Row(
        1002L,
        ts("2023-09-23"),
        ts("2023-09-24"),
        "THB",
        "SGD",
        4200.00,
        159.39931740614333,
        116.75999999999999,
        "SGD",
        0.7325,
        0.0278,
        0.0270,
        20230924,
        "MBS",
        "EB95D939-E0FC-60B6-CB26F5C9FCAF4F18",
        "",
        "",
        null,
        null,
        null,
        null,
        null
      ),
      Row(
        1003L,
        ts("2023-09-23"),
        ts("2023-09-24"),
        "THB",
        nullS,
        4200.00,
        nullS,
        116.75999999999999,
        nullS,
        nullS,
        0.0278,
        0.0270,
        20230924,
        "MBS",
        "EB95D939-E0FC-60B6-CB26F5C9FCAF4F18",
        "",
        "",
        null,
        null,
        null,
        null,
        null
      ),
      Row(
        1004L,
        ts("2023-08-31"),
        ts("2023-09-01"),
        "THB",
        "THB",
        4200.00,
        nullS,
        nullS,
        "THB",
        nullS,
        nullS,
        nullS,
        20230901,
        "MBS",
        "EB95D939-E0FC-60B6-CB26F5C9FCAF4F18",
        "",
        "",
        null,
        null,
        null,
        null,
        null
      ),
      Row(
        1004L,
        ts("2023-09-23"),
        ts("2023-09-24"),
        "THB",
        "THB",
        4200.00,
        4200.00,
        116.75999999999999,
        "THB",
        0.0278,
        0.0278,
        0.0270,
        20230924,
        "MBS",
        "EB95D939-E0FC-60B6-CB26F5C9FCAF4F18",
        "",
        "",
        null,
        null,
        null,
        null,
        null
      ),
      Row(
        1005L,
        ts("2023-09-23"),
        ts("2023-09-24"),
        "THB",
        "THB",
        4200.00,
        4200.00,
        116.75999999999999,
        "THB",
        0.0278,
        0.0278,
        0.0270,
        20230924,
        "MBS",
        "EB95D939-E0FC-60B6-CB26F5C9FCAF4F18",
        "",
        "",
        null,
        null,
        null,
        null,
        null
      ),
      Row(
        1006L,
        ts("2023-09-23"),
        ts("2023-09-24"),
        "THB",
        "THB",
        4200.00,
        4200.00,
        116.75999999999999,
        "THB",
        0.0278,
        0.0278,
        0.0270,
        20230924,
        "MBS",
        "EB95D939-E0FC-60B6-CB26F5C9FCAF4F18",
        "",
        "",
        null,
        null,
        null,
        null,
        null
      )
    )

    val expectBookingInfo = bookingInfo(
      Row(1001L, "2001", "0", "3001"),
      Row(1002L, "2002", "0", "3002"),
      Row(1003L, "2003", "0", "3003"),
      Row(1004L, "2004", "0", "3004"),
      Row(1004L, "2004", "0", "3004"),
      Row(1005L, "0", "2005", "3005"),
      Row(1006L, "0", "2006", "3006")
    )

    val response =
      ApSettlementLogic
        .appendTutukaAdditionalInfo(tutukaData, upcData, currencyData, history, ex, cd)

    val actual            = response.select(resultTutukaSchema.fields.map(f => col(f.name)): _*)
    val actualBookingInfo = response.select(bookingInfoSchema.fields.map(f => col(f.name)): _*)

    actual should beEqualTo(expect)
    actualBookingInfo should beEqualTo(expectBookingInfo)
  }

  test("appendTutukaAdditionalInfo - TransactionAuthorizationNumber, AcquirerReference, TransactionNarrative") {

    val tutukaData = tutuka(
      Row(
        uuid1,
        "764",
        4200.00,
        "764",
        4200.00,
        D,
        "asdasf",
        "AgodaVCNEURHighRiskDailySettlements20230924.csv",
        "acqRef1",
        "authNum1",
        "IBIS HOTEL SALMIYA\\SALMIYA\\KUWAIT\\12345 KWTKWT"
      ),
      Row(
        uuid2,
        "764",
        4200.00,
        "764",
        4200.00,
        D,
        "dsgftd",
        "AgodaVCNEURHighRiskDailySettlements20230924.csv",
        "",
        "",
        "CITY TOWER HOTEL\\SHARQ\\KUWAIT CITY\\68599 KWTKWT"
      ), // different currency
      Row(
        uuid3,
        "764",
        4200.00,
        "764",
        4200.00,
        D,
        "eyrtur",
        "AgodaVCNEURHighRiskDailySettlements20230924.csv",
        "",
        "",
        "Hotel on Booking.com\\Weteringschans 28\\Amsterdam\\1017 SG NLDNLD"
      ), // no hotel
      Row(
        uuid4,
        "764",
        4200.00,
        "764",
        4200.00,
        D,
        "owrufj",
        "AgodaVCNEURHighRiskDailySettlements20230901.csv",
        "",
        "",
        "BOOKING.COM \\Herengracht 597 \\AMSTERDAM \\1017 CE NH NLD"
      ), // no ex transaction date
      Row(
        uuid5,
        "764",
        4200.00,
        "764",
        4200.00,
        D,
        "pwowma",
        "AgodaVCNEURHighRiskDailySettlements20230924.csv",
        "",
        "",
        "SK NETWORKS _ WALKERHI\\JEONNAMYEOSU-SISUJEONG-DONG \\YEOSU-SI \\59723 KOR"
      ), // no ex posting date
      Row(
        uuid6,
        "764",
        4200.00,
        "764",
        4200.00,
        D,
        "virhbs",
        "AgodaVCNEURHighRiskDailySettlements20230924.csv",
        "",
        "",
        "IBIS HOTEL SALMIYA\\SALMIYA\\KUWAIT"
      ), // no current hotel
      Row(
        uuid7,
        "764",
        4200.00,
        "764",
        4200.00,
        D,
        "ibunyg",
        "AgodaVCNEURLowRiskDailySettlements20230924.csv",
        "",
        "",
        ""
      ) // no audit hotel
    )

    val upcData = upcTransaction(
      Row(
        uuid1,
        "Map(hotelId -> 1001, bookingId -> 2001, dmcId -> 3001, productType -> 1, providerCampaignReference -> EB95D939-E0FC-60B6-CB26F5C9FCAF4F18)",
        1686736930124L,
        "20230923",
        "1c0"
      ),
      Row(uuid2, "Map(hotelId -> 1002, bookingId -> 2002, dmcId -> 3002)", 1686736914649L, "20230923", "2b8"),
      Row(uuid3, "Map(hotelId -> 1003, bookingId -> 2003, dmcId -> 3003)", 1686736899185L, "20230923", "3c8"),
      Row(uuid4, "Map(hotelId -> 1004, bookingId -> 2004, dmcId -> 3004)", 1686736894016L, "20230923", "47d"),
      Row(uuid5, "Map(hotelId -> 1004, bookingId -> 2004, dmcId -> 3004)", 1686736894016L, "20230923", "5df"),
      Row(uuid6, "Map(hotelId -> 1005, batchId -> 2005, dmcId -> 3005)", 1686736894020L, "20230923", "6f1"),
      Row(uuid7, "Map(hotelId -> 1006, batchId -> 2006, dmcId -> 3006)", 1686736894020L, "20230923", "7f7")
    )

    val currencyData = currency(
      Row("764", "THB")
    )

    val history = hotelHistory(
      Row(1001, "THB", 20230923),
      Row(1002, "SGD", 20230923),
      Row(1004, "THB", 20230831),
      Row(1004, "THB", 20230923),
      Row(1005, "THB", 20230923),
      Row(1006, "THB", 20230923)
    )

    val ex = exchangeRate(
      Row("HKD", 0.12700, "20230923"),
      Row("SGD", 0.7300, "20230924"),
      Row("THB", 0.0270, "20230924"),
      Row("HKD", 0.12786, "20230924"),
      Row("SGD", 0.7325, "20230923"),
      Row("THB", 0.0278, "20230923")
    )

    val cd = campaignDetails(
      Row("Agoda VCN EUR HighRisk", "MBS", "EB95D939-E0FC-60B6-CB26F5C9FCAF4F18"),
      Row("Agoda_VCN_EUR_LowRisk", "MBK", "EB95D939-E0FC-60B6-CB26F5C9FCAF4F19")
    )

    val expect = resultTutuka(
      Row(
        1001L,
        ts("2023-09-23"),
        ts("2023-09-24"),
        "THB",
        "THB",
        4200.00,
        4200.00,
        116.75999999999999,
        "THB",
        0.0278,
        0.0278,
        0.0270,
        20230924,
        "MBS",
        "EB95D939-E0FC-60B6-CB26F5C9FCAF4F18",
        "acqRef1",
        "authNum1",
        "KWT",
        "IBIS HOTEL SALMIYA",
        "SALMIYA",
        "KUWAIT",
        "12345"
      ),
      Row(
        1002L,
        ts("2023-09-23"),
        ts("2023-09-24"),
        "THB",
        "SGD",
        4200.00,
        159.39931740614333,
        116.75999999999999,
        "SGD",
        0.7325,
        0.0278,
        0.0270,
        20230924,
        "MBS",
        "EB95D939-E0FC-60B6-CB26F5C9FCAF4F18",
        "",
        "",
        "KWT",
        "CITY TOWER HOTEL",
        "SHARQ",
        "KUWAIT CITY",
        "68599"
      ),
      Row(
        1003L,
        ts("2023-09-23"),
        ts("2023-09-24"),
        "THB",
        nullS,
        4200.00,
        nullS,
        116.75999999999999,
        nullS,
        nullS,
        0.0278,
        0.0270,
        20230924,
        "MBS",
        "EB95D939-E0FC-60B6-CB26F5C9FCAF4F18",
        "",
        "",
        "NLD",
        "Hotel on Booking.com",
        "Weteringschans 28",
        "Amsterdam",
        "1017"
      ),
      Row(
        1004L,
        ts("2023-08-31"),
        ts("2023-09-01"),
        "THB",
        "THB",
        4200.00,
        nullS,
        nullS,
        "THB",
        nullS,
        nullS,
        nullS,
        20230901,
        "MBS",
        "EB95D939-E0FC-60B6-CB26F5C9FCAF4F18",
        "",
        "",
        "NLD",
        "BOOKING.COM ",
        "Herengracht 597 ",
        "AMSTERDAM ",
        "1017"
      ),
      Row(
        1004L,
        ts("2023-09-23"),
        ts("2023-09-24"),
        "THB",
        "THB",
        4200.00,
        4200.00,
        116.75999999999999,
        "THB",
        0.0278,
        0.0278,
        0.0270,
        20230924,
        "MBS",
        "EB95D939-E0FC-60B6-CB26F5C9FCAF4F18",
        "",
        "",
        "KOR",
        "SK NETWORKS _ WALKERHI",
        "JEONNAMYEOSU-SISUJEONG-DONG ",
        "YEOSU-SI ",
        "59723"
      ),
      Row(
        1005L,
        ts("2023-09-23"),
        ts("2023-09-24"),
        "THB",
        "THB",
        4200.00,
        4200.00,
        116.75999999999999,
        "THB",
        0.0278,
        0.0278,
        0.0270,
        20230924,
        "MBS",
        "EB95D939-E0FC-60B6-CB26F5C9FCAF4F18",
        "",
        "",
        "",
        "",
        "",
        "",
        ""
      ),
      Row(
        1006L,
        ts("2023-09-23"),
        ts("2023-09-24"),
        "THB",
        "THB",
        4200.00,
        4200.00,
        116.75999999999999,
        "THB",
        0.0278,
        0.0278,
        0.0270,
        20230924,
        "MBK",
        "EB95D939-E0FC-60B6-CB26F5C9FCAF4F19",
        "",
        "",
        null,
        null,
        null,
        null,
        null
      )
    )

    val expectBookingInfo = bookingInfo(
      Row(1001L, "2001", "0", "3001"),
      Row(1002L, "2002", "0", "3002"),
      Row(1003L, "2003", "0", "3003"),
      Row(1004L, "2004", "0", "3004"),
      Row(1004L, "2004", "0", "3004"),
      Row(1005L, "0", "2005", "3005"),
      Row(1006L, "0", "2006", "3006")
    )

    val response =
      ApSettlementLogic
        .appendTutukaAdditionalInfo(tutukaData, upcData, currencyData, history, ex, cd)

    val actual            = response.select(resultTutukaSchema.fields.map(f => col(f.name)): _*)
    val actualBookingInfo = response.select(bookingInfoSchema.fields.map(f => col(f.name)): _*)

    actual should beEqualTo(expect)
    actualBookingInfo should beEqualTo(expectBookingInfo)
  }

  test("appendTutukaAdditionalInfo-numeric_code_157") {

    val tutukaData = tutuka(
      Row(
        uuid1,
        "157",
        4200.00,
        "764",
        4200.00,
        D,
        "asdasf",
        "Settlements20230924.csv",
        "",
        "",
        null,
        null,
        null,
        null,
        null
      )
    )

    val upcData = upcTransaction(
      Row(uuid1, "Map(hotelId -> 1001, bookingId -> 2001, dmcId -> 3001)", 1686736930124L, "20230923", "1c0")
    )

    val currencyData = currency(
      Row("764", "THB")
    )

    val history = hotelHistory(
      Row(1001, "THB", 20230924)
    )

    val ex = exchangeRate(
      Row("THB", 0.0278, "20230924")
    )

    val cd = campaignDetails(
      Row("Agoda VCN EUR HighRisk", "MBS", "EB95D939-E0FC-60B6-CB26F5C9FCAF4F18")
    )

    val settlement_currency_code =
      ApSettlementLogic
        .appendTutukaAdditionalInfo(tutukaData, upcData, currencyData, history, ex, cd)
        .select("settlement_currency_code")
        .limit(1)
        .collect()
        .head
        .getString(0)

    settlement_currency_code shouldEqual "CNH"

  }

  test("appendUpcAdditionalInfo-UUID") {
    val settlementSchema = StructType(
      List(
        StructField("uuid", StringType),
        StructField("booking_id", StringType),
        StructField("batch_id", StringType),
        StructField("sub_supplier_id", StringType),
        StructField("dmc_id", StringType),
        StructField("hash_key", StringType)
      )
    )
    val settlementData = Seq(
      Row(uuid1, null, null, null, null, "asdasf"),
      Row(uuid2, null, null, null, null, "dsgftd"),
      Row(uuid3, null, null, null, null, "eyrtur"),
      Row(uuid4, null, null, null, null, "owrufj"),
      Row(uuid5, null, null, null, null, "pwowma"),
      Row(uuid6, null, null, null, null, "virhbs"),
      Row(uuid7, null, null, null, null, "ibunyg"),
      Row(uuid8, null, null, null, null, "flgidm"),
      Row(uuid9, null, null, null, null, "dselgb")
    )
    val settlementDf = df(settlementData: _*)(settlementSchema)

    val upcTransactionSchema = StructType(
      List(
        StructField("cardidentifier", StringType),
        StructField("metadata", StringType),
        StructField("logtime", LongType),
        StructField("partition_key", StringType)
      )
    )

    val upcTransactionData = Seq(
      Row(
        uuid1,
        "Map(chainId -> 0, usdAmount -> 72.3801, hotelId -> 133740, dmcId -> 3038, bookingId -> 943372560, checkOutDate -> 2023-07-06," +
          " isBookingRefundable -> True, checkInDate -> 2023-07-05, siteId -> 1893645, paymentModelId -> 4," +
          " bookingDate -> 2023-06-14T13:21:50.503850128+07:00)",
        1686736930124L,
        "1c0"
      ),
      Row(
        uuid2,
        "Map(chainId -> 0, hotelId -> 9648197, dmcId -> 332, checkOutDate -> 2023-06-18T00:00:00Z, cid -> 1799657," +
          " checkInDate -> 2023-06-17T00:00:00Z, paymentModelId -> 0, batchId -> 187566," +
          " bookingDate -> 2023-05-09T02:33:39.968323977+07:00)",
        1686736914649L,
        "2b8"
      ),
      Row(
        uuid3,
        "Map(chainId -> 166, usdAmount -> 1, hotelId -> 87510, dmcId -> 332, bookingId -> 931719508, checkOutDate -> 2023-06-25T00:00:00Z," +
          " isBookingRefundable -> True, checkInDate -> 2023-06-19T00:00:00Z, siteId -> -38, paymentModelId -> 1," +
          " bookingDate -> 2023-05-22T10:49:54.086359652+07:00)",
        1686736899185L,
        "3c8"
      ),
      Row(
        uuid4,
        "Map(chainId -> 339, usdAmount -> 1, hotelId -> 2532277, dmcId -> 27914, bookingId -> 617134598, checkOutDate -> 2023-07-03T00:00:00Z," +
          " isBookingRefundable -> False, checkInDate -> 2023-07-02T00:00:00Z, siteId -> 1758195, paymentModelId -> 1," +
          " bookingDate -> 2023-06-13T17:28:36.021176391+07:00)",
        1686736894016L,
        "47d"
      ),
      Row(
        uuid5,
        "Map(chainId -> 0, usdAmount -> 1, hotelId -> 7443098, dmcId -> 332, bookingId -> 943719896, checkOutDate -> 2023-06-24," +
          " isBookingRefundable -> False, checkInDate -> 2023-06-19, siteId -> 1895365, paymentModelId -> 1," +
          " bookingDate -> 2023-06-14T13:23:58.032395828+07:00)",
        1686736894016L,
        "5df"
      ),
      Row(
        uuid5,
        "Map(chainId -> 0, usdAmount -> 1, hotelId -> 7443098, dmcId -> 332, bookingId -> 777, checkOutDate -> 2023-06-24," +
          " isBookingRefundable -> False, checkInDate -> 2023-06-19, siteId -> 1895365, paymentModelId -> 1," +
          " bookingDate -> 2023-06-14T13:23:58.032395828+07:00)",
        1686736894020L,
        "5df"
      ),
      Row(
        uuid6,
        "asdaslkhixxx",
        1686736894020L,
        "6f1"
      ),
      Row(
        uuid7,
        null,
        1686736894020L,
        "7f7"
      ),
      Row(
        uuid8,
        "Map(bookingId -> 888, productType -> 5)",
        1686736894020L,
        "801"
      ),
      Row(
        uuid9,
        "Map(businessId -> 999, hotelId -> 999)",
        1686736894020L,
        "d9b"
      )
    )
    val upcTransactionDf = df(upcTransactionData: _*)(upcTransactionSchema)

    val expectedData = Seq(
      Row(uuid1, "943372560", "0", "133740", "3038", "asdasf", "HOTEL"),
      Row(uuid2, "0", "187566", "9648197", "332", "dsgftd", "HOTEL"),
      Row(uuid3, "931719508", "0", "87510", "332", "eyrtur", "HOTEL"),
      Row(uuid4, "617134598", "0", "2532277", "27914", "owrufj", "HOTEL"),
      Row(uuid5, "777", "0", "7443098", "332", "pwowma", "HOTEL"),
      Row(uuid6, "0", "0", "0", "0", "virhbs", "HOTEL"),
      Row(uuid7, "0", "0", "0", "0", "ibunyg", "HOTEL"),
      Row(uuid8, "888", "0", "0", "0", "flgidm", "FLIGHT"),
      Row(uuid9, "999", "0", "999", "0", "dselgb", "VEHICLE")
    )

    val expectedSchema = StructType(
      List(
        StructField("uuid", StringType),
        StructField("booking_id", StringType),
        StructField("batch_id", StringType),
        StructField("sub_supplier_id", StringType),
        StructField("dmc_id", StringType),
        StructField("hash_key", StringType),
        StructField("product_type", StringType)
      )
    )

    val expected = df(expectedData: _*)(expectedSchema)

    val actual = ApSettlementLogic.appendUpcAdditionalInfo(settlementDf, upcTransactionDf, "uuid")

    actual should beEqualTo(expected)
  }

  test("appendUpcAdditionalInfo-non-UUID") {
    val settlementSchema = StructType(
      List(
        StructField("uuid", StringType),
        StructField("booking_id", StringType),
        StructField("batch_id", StringType),
        StructField("sub_supplier_id", StringType),
        StructField("dmc_id", StringType),
        StructField("hash_key", StringType)
      )
    )
    val settlementData = Seq(
      Row("bookingId=1001&dmcId=2001&businessId=3001", null, null, null, null, "asdasf"),
      Row("bookingId=1002&dmcId=2002&merchantId=3002", null, null, null, null, "dsgftd"),
      Row("bookingId=0&batchId=1003&businessId=3003", null, null, null, null, "eyrtur")
    )
    val settlementDf = spark.createDataFrame(spark.sparkContext.parallelize(settlementData), settlementSchema)

    val upcTransactionSchema = StructType(
      List(
        StructField("cardidentifier", StringType),
        StructField("metadata", StringType),
        StructField("logtime", LongType),
        StructField("partition_key", StringType)
      )
    )
    val upcTransactionData = Seq(
      Row(
        uuid1,
        "Map(chainId -> 0, usdAmount -> 72.3801, hotelId -> 133740, dmcId -> 3038, bookingId -> 943372560, checkOutDate -> 2023-07-06," +
          " isBookingRefundable -> True, checkInDate -> 2023-07-05, siteId -> 1893645, paymentModelId -> 4," +
          " bookingDate -> 2023-06-14T13:21:50.503850128+07:00)",
        1686736930124L,
        "1"
      ),
      Row(
        uuid2,
        "Map(chainId -> 0, hotelId -> 9648197, dmcId -> 332, checkOutDate -> 2023-06-18T00:00:00Z, cid -> 1799657," +
          " checkInDate -> 2023-06-17T00:00:00Z," +
          " paymentModelId -> 0, batchId -> 187566, bookingDate -> 2023-05-09T02:33:39.968323977+07:00)",
        1686736914649L,
        "2"
      )
    )
    val upcTransactionDf = spark.createDataFrame(spark.sparkContext.parallelize(upcTransactionData), upcTransactionSchema)

    val expectSchema = StructType(
      List(
        StructField("booking_id", StringType),
        StructField("batch_id", StringType),
        StructField("sub_supplier_id", StringType),
        StructField("dmc_id", StringType)
      )
    )

    val expectedData = Seq(
      Row("1001", "0", "3001", "2001"),
      Row("1002", "0", "3002", "2002"),
      Row("0", "1003", "3003", "0")
    )
    val expected = spark.createDataFrame(spark.sparkContext.parallelize(expectedData), expectSchema)

    val actual = ApSettlementLogic
      .appendUpcAdditionalInfo(settlementDf, upcTransactionDf, "uuid")
      .select(expectSchema.fields.map(f => col(f.name)): _*)

    actual should beEqualTo(expected)
  }

  test("getTutukaSourceTransactionCode") {
    val resultSchema = StructType(
      List(
        StructField("booking_id", StringType),
        StructField("transaction_id", StringType),
        StructField("tracking_number", StringType),
        StructField("transaction_description", StringType),
        StructField("expected_result", StringType)
      )
    )
    val inputData = Seq(
      Row("1", "716172", "757840300006918", "DEDUCT", "716172_757840300006918_DD"),
      Row("2", "158525", "645433100073254", "LOAD", "158525_645433100073254_LD"),
      Row("3", "158525", "645433100073254", "LOAD REVERSAL", "158525_645433100073254_LR"),
      Row("4", "158525", "645433100073254", "REVERSAL", "158525_645433100073254_RV"),
      Row("5", "158525", "645433100073254", "CHARGEBACK", "158525_645433100073254_CB"),
      Row("6", "158525", "645433100073254", "CHARGEBACK REVERSED", "158525_645433100073254_CR"),
      Row("7", "158525", "645433100073254", "CHARGEBACK REJECTED", "158525_645433100073254_CRJ"),
      Row("8", "158525", "645433100073254", "2ND PRESENTMENT", "158525_645433100073254_SP"),
      Row("9", "158525", "645433100073254", "2ND PRESENTMENT REVERSAL", "158525_645433100073254_SR"),
      Row("10", "158525", "645433100073254", "ARBITRATION", "158525_645433100073254_AR"),
      Row("11", "158525", "645433100073254", "PRE ARBITRATION", "158525_645433100073254_PA")
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputData), resultSchema)

    val actual = df
      .withColumn(
        "result",
        ApSettlementLogic
          .getTutukaSourceTransactionCode(
            col("transaction_id"),
            col("tracking_number"),
            col("transaction_description")
          )
      )
      .select("booking_id", "result")

    val expected = df.select(
      col("booking_id"),
      col("expected_result").as("result")
    )

    actual should beEqualTo(expected)
  }

  test("getPaymentMethodId") {
    val resultSchema = StructType(
      List(
        StructField("booking_id", StringType),
        StructField("approval_id", StringType),
        StructField("payment_method_id", IntegerType),
        StructField("has_upc_info", BooleanType)
      )
    )
    val inputData = Seq(
      Row(nullS, nullS, -1, false),
      Row(nullS, nullS, 2, true),
      Row("0", nullS, 2, true),
      Row(nullS, "0", 2, true),
      Row(nullS, "1234", 5, true),
      Row("0", "1234", 5, true),
      Row("56789", nullS, 3, true),
      Row("56789", "0", 3, true),
      Row("56789", "1234", 3, true)
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputData), resultSchema)

    val actual = df
      .drop("payment_method_id")
      .withColumn(
        "payment_method_id",
        ApSettlementLogic
          .getPaymentMethodId(
            col("booking_id"),
            col("approval_id"),
            col("has_upc_info")
          )
      )

    val expected = df

    actual should beEqualTo(expected)
  }

  test("getPaymentMethodName") {
    val resultSchema = StructType(
      List(
        StructField("booking_id", StringType),
        StructField("approval_id", StringType),
        StructField("payment_method_name", StringType),
        StructField("has_upc_info", BooleanType)
      )
    )
    val inputData = Seq(
      Row(nullS, nullS, "Unknown", false),
      Row(nullS, nullS, "PlasticCard", true),
      Row("0", nullS, "PlasticCard", true),
      Row(nullS, "0", "PlasticCard", true),
      Row(nullS, "1234", "UpcOnEPass", true),
      Row("0", "1234", "UpcOnEPass", true),
      Row("56789", nullS, "UniversalPlasticCard", true),
      Row("56789", "0", "UniversalPlasticCard", true),
      Row("56789", "1234", "UniversalPlasticCard", true)
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputData), resultSchema)

    val actual = df
      .drop("payment_method_name")
      .withColumn(
        "payment_method_name",
        ApSettlementLogic
          .getPaymentMethodName(
            col("booking_id"),
            col("approval_id"),
            col("has_upc_info")
          )
      )

    val expected = df

    actual should beEqualTo(expected)
  }

  test("appendTutukaCurrencyCode") {
    val resultSchema = StructType(
      List(
        StructField("booking_id", StringType),
        StructField("settlement_currency", StringType),
        StructField("cardholder_currency", StringType),
        StructField("expected_settlement_currency_code", StringType),
        StructField("expected_posting_currency_code", StringType)
      )
    )
    val inputData = Seq(
      Row("1", "392", "978", "JPY", "EUR"),
      Row("2", "978", "840", "EUR", "USD"),
      Row("3", "840", "392", "USD", "JPY")
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputData), resultSchema)

    val currencySchema = StructType(
      List(
        StructField("numeric_code", StringType),
        StructField("currency_code", StringType)
      )
    )
    val currencyData = Seq(
      Row("840", "USD"),
      Row("978", "EUR"),
      Row("392", "JPY")
    )
    val currencyDf = spark.createDataFrame(spark.sparkContext.parallelize(currencyData), currencySchema)
    val expected = df.select(
      col("booking_id"),
      col("expected_settlement_currency_code").as("settlement_currency_code"),
      col("expected_posting_currency_code").as("cardholder_currency_code")
    )

    val actual =
      ApSettlementLogic.appendTutukaCurrencyCode(df, currencyDf).select("booking_id", "settlement_currency_code", "cardholder_currency_code")

    actual should beEqualTo(expected)
  }

  test("manageTutukaAmounts") {
    val tutukaAmountSchema = StructType(
      List(
        StructField("transaction_description", StringType),
        StructField("transaction_settlement_amount", DoubleType),
        StructField("transaction_cardholder_amount", DoubleType)
      )
    )

    val amtData =
      df(
        Row("DEDUCT", 100.00, 200.00),
        Row("LOAD", 100.00, 200.00),
        Row("LOAD REVERSAL", 100.00, 200.00),
        Row("REVERSAL", 100.00, 200.00),
        Row("CHARGEBACK", 100.00, 200.00),
        Row("CHARGEBACK REVERSED", 100.00, 200.00),
        Row("CHARGEBACK REJECTED", 100.00, 200.00),
        Row("2ND PRESENTMENT", 100.00, 200.00),
        Row("2ND PRESENTMENT REVERSAL", 100.00, 200.00),
        Row("ARBITRATION", 100.00, 200.00),
        Row("PRE ARBITRATION", 100.00, 200.00)
      )(tutukaAmountSchema)

    val expect =
      df(
        Row("DEDUCT", 100.00, 200.00),
        Row("LOAD", -100.00, -200.00), //negate
        Row("LOAD REVERSAL", 100.00, 200.00),
        Row("REVERSAL", -100.00, -200.00), //negate
        Row("CHARGEBACK", -100.00, -200.00),
        Row("CHARGEBACK REVERSED", 100.00, 200.00),
        Row("CHARGEBACK REJECTED", 100.00, 200.00),
        Row("2ND PRESENTMENT", 100.00, 200.00),
        Row("2ND PRESENTMENT REVERSAL", -100.00, -200.00), //negate
        Row("ARBITRATION", -100.00, -200.00),              //negate
        Row("PRE ARBITRATION", -100.00, -200.00)           //negate
      )(tutukaAmountSchema)

    val actual = ApSettlementLogic.manageTutukaAmounts(amtData)

    actual should beEqualTo(expect)

  }

  test("appendApprovalExchangeRates") {
    val approvalSchema = StructType(
      List(
        StructField("booking_id", IntegerType),
        StructField("local_currency", StringType),
        StructField("transaction_date", StringType),
        StructField("datadate", IntegerType)
      )
    )
    val approvalDf = df(
      Row(1, "HKD", "2023-09-23 12:30:57", 20231017),
      Row(2, "HKD", "2023-09-24 12:30:57", 20231017),
      Row(3, "HKD", "2023-09-25 12:30:57", 20231017),
      Row(4, "XXX", "2023-09-24 12:30:57", 20231017)
    )(approvalSchema)

    val ex = exchangeRate(
      Row("HKD", 0.12700, "20230923"),
      Row("SGD", 0.7300, "20230923"),
      Row("THB", 0.0270, "20230923"),
      Row("HKD", 0.12786, "20230924"),
      Row("SGD", 0.7325, "20230924"),
      Row("THB", 0.0278, "20230924")
    )

    val resultSchema = StructType(
      List(
        StructField("booking_id", IntegerType),
        StructField("local_currency", StringType),
        StructField("transaction_date", StringType),
        StructField("rate_ex", DoubleType),
        StructField("settlement_ex", DoubleType),
        StructField("posting_ex", DoubleType),
        StructField("report_date", IntegerType)
      )
    )
    val expect = df(
      Row(1, "HKD", "2023-09-23 12:30:57", 0.12700, 0.12700, 0.12700, 20231017),
      Row(2, "HKD", "2023-09-24 12:30:57", 0.12786, 0.12786, 0.12786, 20231017),
      Row(3, "HKD", "2023-09-25 12:30:57", null, null, null, 20231017),
      Row(4, "XXX", "2023-09-24 12:30:57", null, null, null, 20231017)
    )(resultSchema)

    val actual = ApSettlementLogic.appendApprovalExchangeRates(approvalDf, ex).drop("datadate")

    actual should beEqualTo(expect)
  }

  test("correctCNY") {
    val input = dfCols(
      Row("RMB"),
      Row("rmb"),
      Row("CNH"),
      Row("cnh"),
      Row("THB"),
      Row("USD")
    )("currency" -> StringType)

    val expect = dfCols(
      Row("CNY"),
      Row("CNY"),
      Row("CNY"),
      Row("CNY"),
      Row("THB"),
      Row("USD")
    )("currency" -> StringType)

    val actual = input.select(ApSettlementLogic.correctCNY(input("currency")).as("currency"))

    actual should beEqualTo(expect)
  }

  test("checkValidTransaction") {
    val transactionSchema = StructType(
      List(
        StructField("booking_id", StringType),
        StructField("batch_id", StringType),
        StructField("sub_supplier_id", StringType),
        StructField("is_valid_transaction", BooleanType)
      )
    )

    val transactionData = Seq(
      Row("1", "2", "3", true),
      Row(nullS, nullS, nullS, false),
      Row(nullS, nullS, "0", false),
      Row(nullS, "0", nullS, false),
      Row(nullS, "0", "0", false),
      Row("0", nullS, nullS, false),
      Row("0", nullS, "0", false),
      Row("0", "0", nullS, false),
      Row("0", "0", "0", false)
    )

    val input    = df(transactionData: _*)(transactionSchema).drop("is_valid_transaction")
    val expected = df(transactionData: _*)(transactionSchema)

    import com.agoda.finance.functions.specific.ApSettlementLogic._
    val actual = input.checkValidTransaction()

    actual should beEqualTo(expected)
  }

  test("ttkReportDateTimestamp,ttkReportDateMinusOneTimestamp") {
    val expected = dfCols(
      Row("AgodaVCNTHBHighRiskDailySettlements20231021.csv"),
      Row("AgodaVCNTHBHighRiskDailySettlements20231321.csv"),
      Row("AgodaVCNTHBHighRiskDailySettlements222.csv"),
      Row("AgodaVCNTHBHighRiskDailySettlements.csv"),
      Row("XXXXXXX"),
      Row(nullS)
    )("file_name" -> StringType)
      .withColumn(
        "ttkReportDate",
        when(col("file_name") === "AgodaVCNTHBHighRiskDailySettlements20231021.csv", lit("2023-10-21 00:00:00").cast(TimestampType))
          .otherwise(lit(null).cast(TimestampType))
      )
      .withColumn(
        "ttkReportDateMinusOne",
        when(col("file_name") === "AgodaVCNTHBHighRiskDailySettlements20231021.csv", lit("2023-10-20 00:00:00").cast(TimestampType))
          .otherwise(lit(null).cast(TimestampType))
      )

    val actual = expected
      .drop("ttkReportDate", "ttkReportDateMinusOne")
      .withColumn("ttkReportDate", ttkReportDateTimestamp())
      .withColumn("ttkReportDateMinusOne", ttkReportDateMinusOneTimestamp())

    actual should beEqualTo(expected)
  }

  test("wexReportDate") {
    val expected = dfCols(
      Row("AgodaUPCTrans_20231103.csv", "20231103"),
      Row("AgodaUPCTrans_20230231.csv", "20230231"),
      Row("AgodaUPCTrans_20230231", "20230231"),
      Row("AgodaUPCTrans", ""),
      Row("AgodaUPCTrans_.csv", ""),
      Row("XXXXXXX", ""),
      Row(nullS, nullS)
    )("file_name" -> StringType, "report_date" -> StringType)

    val actual = expected
      .drop("report_date")
      .withColumn("report_date", wexReportDate())

    actual should beEqualTo(expected)
  }

  test("enettReportDate") {
    val expected = dfCols(
      Row("DailyVANsSettlement_304100_410032_20240307", "20240307"),
      Row("DailyVANsSettlement_304100_410032_20230231", "20230231"),
      Row("DailyVANsSettlement_xxxx_yyyy_20240307", "20240307"),
      Row("DailyVANsSettlement_304100_410032", ""),
      Row("DailyVANsSettlement", ""),
      Row("XXXXXXX", ""),
      Row(nullS, nullS)
    )("file_name" -> StringType, "report_date" -> StringType)

    val actual = expected
      .drop("report_date")
      .withColumn("report_date", enettReportDate())

    actual should beEqualTo(expected)
  }

  test("appendCheckoutAdditionalInfo") {

    val checkoutData = checkout(
      Row(
        "757840300006918",                                                                   // transaction_id
        uuid1,                                                                               // card_reference
        "2025-05-21 03:55:56",                                                               // settled_on
        "EUR",                                                                               // billing_currency
        4200.00,                                                                             // billing_amount
        "PLN",                                                                               // reconciliation_currency
        4400.00,                                                                             // reconciliation_amount
        "presentment",                                                                       // message_type
        "purchase",                                                                          // transaction_type
        "asdasf",                                                                            // hash_key
        "issuing_settlement_activity_ent_ggahrnuxsmbeppjqlkwvs7lirm_20250522_20250523_1.csv" // file_name
      ),                                                                                     // same currency
      Row(
        "757840300006919",
        uuid2,
        "2025-05-21 03:55:56",
        "EUR",
        4200.00,
        "SGD",
        4300.00,
        "presentment",
        "purchase",
        "dsgftd",
        "issuing_settlement_activity_ent_ggahrnuxsmbeppjqlkwvs7lirm_20250522_20250523_1.csv"
      ), // different currency
      Row(
        "757840300006920",
        uuid3,
        "2025-05-21T03:55:56",
        "EUR",
        4200.00,
        "SGD",
        4300.00,
        "presentment",
        "purchase",
        "eyrtur",
        "issuing_settlement_activity_ent_ggahrnuxsmbeppjqlkwvs7lirm_20250522_20250523_1.csv"
      ), // no hotel
      Row(
        "757840300006921",
        uuid4,
        "2025-05-01T03:55:56",
        "EUR",
        4200.00,
        "SGD",
        4300.00,
        "presentment",
        "purchase",
        "owrufj",
        "issuing_settlement_activity_ent_ggahrnuxsmbeppjqlkwvs7lirm_20250522_20250523_1.csv"
      ), // no ex transaction date
      Row(
        "757840300006922",
        uuid5,
        "2025-05-21T03:55:56",
        "EUR",
        4200.00,
        "SGD",
        4300.00,
        "presentment",
        "purchase",
        "pwowma",
        "issuing_settlement_activity_ent_ggahrnuxsmbeppjqlkwvs7lirm_20250522_20250523_1.csv"
      ), // no ex posting date
      Row(
        "757840300006923",
        uuid6,
        "2025-05-21T03:55:56",
        "EUR",
        4200.00,
        "SGD",
        4300.00,
        "presentment",
        "purchase",
        "virhbs",
        "issuing_settlement_activity_ent_ggahrnuxsmbeppjqlkwvs7lirm_20250522_20250523_1.csv"
      ), // no current hotel
      Row(
        "757840300006924",
        uuid7,
        "2025-05-21T03:55:56",
        "EUR",
        4200.00,
        "SGD",
        4300.00,
        "presentment",
        "purchase",
        "ibunyg",
        "issuing_settlement_activity_ent_ggahrnuxsmbeppjqlkwvs7lirm_20250522_20250523_1.csv"
      ) // no audit hotel
    )

    val upcData = upcTransaction(
      Row(
        uuid1,
        "Map(hotelId -> 1001, bookingId -> 2001, dmcId -> 3001, productType -> 1, providerCampaignReference -> abcd)",
        1686736930124L,
        "20250218",
        "1c0"
      ),
      Row(
        uuid2,
        "Map(hotelId -> 1002, bookingId -> 2002, dmcId -> 3002, providerCampaignReference -> abcd)",
        1686736914649L,
        "20250219",
        "2b8"
      ),
      Row(
        uuid3,
        "Map(hotelId -> 1003, bookingId -> 2003, dmcId -> 3003, providerCampaignReference -> abcd)",
        1686736899185L,
        "20250220",
        "3c8"
      ),
      Row(uuid4, "Map(hotelId -> 1004, bookingId -> 2004, dmcId -> 3004)", 1686736894016L, "20250218", "47d"),
      Row(uuid5, "Map(hotelId -> 1004, bookingId -> 2004, dmcId -> 3004)", 1686736894016L, "20250219", "5df"),
      Row(uuid6, "Map(hotelId -> 1005, batchId -> 2005, dmcId -> 3005)", 1686736894020L, "20250220", "6f1"),
      Row(uuid7, "Map(hotelId -> 1006, batchId -> 2006, dmcId -> 3006)", 1686736894020L, "20240220", "7f7")
    )

    val campaign = checkoutCampaignDetails(
      Row("abcd", "MBS")
    )

    val history = hotelHistory(
      Row(1001, "PLN", 20250521),
      Row(1002, "SGD", 20250521),
      Row(1004, "SGD", 20250521),
      Row(1004, "SGD", 20250401),
      Row(1005, "SGD", 20250521),
      Row(1006, "SGD", 20250521)
    )

    val ex = exchangeRate(
      Row("PLN", 0.0260, "20250521"),
      Row("EUR", 0.0278, "20250522"),
      Row("EUR", 0.0280, "20250521"),
      Row("SGD", 0.7300, "20250522"),
      Row("SGD", 0.7302, "20250521"),
      Row("HKD", 0.12700, "20240505")
    )

    val expect = resultCheckout(
      Row(
        1001L,
        ts("2025-05-21 03:55:56"),
        ts("2025-05-22 00:00:00"),
        "EUR",
        "PLN",
        4200.0,
        4523.076923076924,
        117.60000000000001,
        "PLN",
        0.0260,
        0.028,
        0.0278,
        20250522,
        "MBS",
        "abcd"
      ),
      Row(
        1002L,
        ts("2025-05-21 03:55:56"),
        ts("2025-05-22 00:00:00"),
        "EUR",
        "SGD",
        4200.00,
        161.05176663927693,
        117.60000000000001,
        "SGD",
        0.7302,
        0.028,
        0.0278,
        20250522,
        "MBS",
        "abcd"
      ),
      Row(
        1003L,
        ts("2025-05-21 03:55:56"),
        ts("2025-05-22 00:00:00"),
        "EUR",
        null,
        4200.00,
        null,
        117.60000000000001,
        null,
        null,
        0.028,
        0.0278,
        20250522,
        "MBS",
        "abcd"
      ),
      Row(
        1004L,
        ts("2025-05-01 03:55:56"),
        ts("2025-05-02 00:00:00"),
        "EUR",
        null,
        4200.00,
        null,
        null,
        null,
        null,
        null,
        null,
        20250502,
        "MTA",
        null
      ),
      Row(
        1004L,
        ts("2025-05-21 03:55:56"),
        ts("2025-05-22 00:00:00"),
        "EUR",
        "SGD",
        4200.0,
        161.05176663927693,
        117.60000000000001,
        "SGD",
        0.7302,
        0.028,
        0.0278,
        20250522,
        "MTA",
        null
      ),
      Row(
        1005L,
        ts("2025-05-21 03:55:56"),
        ts("2025-05-22 00:00:00"),
        "EUR",
        "SGD",
        4200.0,
        161.05176663927693,
        117.60000000000001,
        "SGD",
        0.7302,
        0.028,
        0.0278,
        20250522,
        "MTA",
        null
      ),
      Row(
        1006L,
        ts("2025-05-21 03:55:56"),
        ts("2025-05-22 00:00:00"),
        "EUR",
        "SGD",
        4200.0,
        161.05176663927693,
        117.60000000000001,
        "SGD",
        0.7302,
        0.028,
        0.0278,
        20250522,
        "MTA",
        null
      )
    )

    val expectBookingInfo = bookingInfo(
      Row(1001L, "2001", "0", "3001"),
      Row(1002L, "2002", "0", "3002"),
      Row(1003L, "2003", "0", "3003"),
      Row(1004L, "2004", "0", "3004"),
      Row(1004L, "2004", "0", "3004"),
      Row(1005L, "0", "2005", "3005"),
      Row(1006L, "0", "2006", "3006")
    )

    val response =
      ApSettlementLogic
        .appendCheckoutAdditionalInfo(checkoutData, upcData, history, ex, campaign)

    val actual            = response.select(resultCheckoutSchema.fields.map(f => col(f.name)): _*)
    val actualBookingInfo = response.select(bookingInfoSchema.fields.map(f => col(f.name)): _*)

    actual should beEqualTo(expect)
    actualBookingInfo should beEqualTo(expectBookingInfo)
  }

  test("getCheckoutTransactionDescription") {
    val expected = dfCols(
      Row("presentment", "purchase", "DEDUCT"),
      Row("presentment_reversal", "purchase", "REVERSAL"),
      Row("chargeback", "xyz", "CHARGEBACK"),
      Row("chargeback_reversal", "xyz", "CHARGEBACK REVERSED"),
      Row("representment", "purchase", "2ND PRESENTMENT"),
      Row("representment_reversal", "xyz", "2ND PRESENTMENT REVERSAL"),
      Row("pre_arbitration_or_arbitration", "", "PRE ARBITRATION"),
      Row("presentment", "refund", "LOAD"),
      Row("presentment_reversal", "refund", "LOAD REVERSAL")
    )("message_type" -> StringType, "transaction_type" -> StringType, "result" -> StringType)
    val actual = expected
      .drop("result")
      .withColumn("result", getCheckoutTransactionDescription(col("message_type"), col("transaction_type")))
    actual.show()
    actual should beEqualTo(expected)
  }

  test("checkoutReportDate") {
    val expected = dfCols(
      Row("2024-05-21", 20240522),
      Row(nullS, nullS)
    )("settled_on" -> StringType, "report_date" -> IntegerType)
    val actual = expected
      .drop("report_date")
      .withColumn("report_date", checkoutReportDate())

    actual should beEqualTo(expected)
  }

  test("checkoutSettledOnDate") {
    val expected = dfCols(
      Row("2024-05-21", 20240521),
      Row(nullS, nullS)
    )("settled_on" -> StringType, "settled_on_int" -> IntegerType)
    val actual = expected
      .drop("settled_on_int")
      .withColumn("settled_on_int", checkoutSettledOnDate())

    actual should beEqualTo(expected)
  }

  test("checkOutTransactionDateTimestamp") {
    val dateString = "2024-05-21"

    val dateFormat = new SimpleDateFormat("yyyy-MM-dd")
    val date       = dateFormat.parse(dateString)
    val timestamp  = new Timestamp(date.getTime)

    val expected = dfCols(
      Row("2024-05-21", timestamp),
      Row(nullS, nullS)
    )("settled_on" -> StringType, "transaction_datetime" -> TimestampType)
    val actual = expected
      .drop("transaction_datetime")
      .withColumn("transaction_datetime", checkOutTransactionDateTimestamp())

    actual should beEqualTo(expected)
  }

  test("checkoutDatePlusOneTimestamp") {
    val dateString = "2024-05-22"

    val dateFormat = new SimpleDateFormat("yyyy-MM-dd")
    val date       = dateFormat.parse(dateString)
    val timestamp  = new Timestamp(date.getTime)

    val expected = dfCols(
      Row("2024-05-21", timestamp),
      Row(nullS, nullS)
    )("settled_on" -> StringType, "posting_datetime" -> TimestampType)
    val actual = expected
      .drop("posting_datetime")
      .withColumn("posting_datetime", checkoutDatePlusOneTimestamp())

    actual should beEqualTo(expected)
  }

  val upcTransactionSchema: StructType = StructType(
    List(
      StructField("cardidentifier", StringType),
      StructField("metadata", StringType),
      StructField("logtime", LongType),
      StructField("datadate", StringType),
      StructField("partition_key", StringType)
    )
  )

  val auditHotelSchema: StructType = StructType(
    List(
      StructField("hotel_id", IntegerType),
      StructField("rate_currency_code", StringType),
      StructField("rec_modify_when", TimestampType),
      StructField("audit_date_time", TimestampType),
      StructField("datadate", IntegerType)
    )
  )

  val hotelHistorySchema: StructType = StructType(
    List(
      StructField("hotel_id", IntegerType),
      StructField("rate_currency_code", StringType),
      StructField("datadate", IntegerType)
    )
  )

  val hotelSchema: StructType = StructType(
    List(
      StructField("hotel_id", IntegerType),
      StructField("rate_currency_code", StringType)
    )
  )

  val exchangeRateSchema: StructType = StructType(
    List(
      StructField("currency_code", StringType),
      StructField("real_exchange_rate", DoubleType),
      StructField("datadate", StringType)
    )
  )

  val campaignDetailsSchema: StructType = StructType(
    List(
      StructField("campaign_name", StringType),
      StructField("product_name", StringType),
      StructField("provider_campaign_reference", StringType)
    )
  )

  val checkoutCampaignDetailsSchema: StructType = StructType(
    List(
      StructField("provider_campaign_reference", StringType),
      StructField("product_name", StringType)
    )
  )

  val wexSchema: StructType = StructType(
    List(
      StructField("transaction_date", StringType),
      StructField("posting_date", StringType),
      StructField("hotel_id", LongType),
      StructField("source_currency", StringType),
      StructField("source_amount", DoubleType),
      StructField("billing_currency", StringType),
      StructField("transaction_amount", DoubleType),
      StructField("file_name", StringType)
    )
  )

  val enettSchema: StructType = StructType(
    List(
      StructField("integrator_reference", StringType),
      StructField("hotel_id", LongType),
      StructField("dmc_id", LongType),
      StructField("batch_id", StringType),
      StructField("booking_id", LongType),
      StructField("settlement_date", StringType),
      StructField("postdate", StringType),
      StructField("trans_currency_code", StringType),
      StructField("trans_amount", DoubleType),
      StructField("post_currency_code", StringType),
      StructField("post_amount", DoubleType),
      StructField("hash_key", StringType),
      StructField("file_name", StringType)
    )
  )

  val tutukaSchema: StructType = StructType(
    List(
      StructField("wallet_reference", StringType),
      StructField("settlement_currency", StringType),
      StructField("transaction_settlement_amount", DoubleType),
      StructField("cardholder_currency", StringType),
      StructField("transaction_cardholder_amount", DoubleType),
      StructField("transaction_description", StringType),
      StructField("hash_key", StringType),
      StructField("file_name", StringType),
      StructField("acquirer_reference_number", StringType),
      StructField("transaction_authorisation_number", StringType),
      StructField("transaction_narrative", StringType)
    )
  )

  val checkoutSchema: StructType = StructType(
    List(
      StructField("transaction_id", StringType),
      StructField("card_reference", StringType),
      StructField("settled_on", StringType),
      StructField("billing_currency", StringType),
      StructField("billing_amount", DoubleType),
      StructField("reconciliation_currency", StringType),
      StructField("reconciliation_amount", DoubleType),
      StructField("message_type", StringType),
      StructField("transaction_type", StringType),
      StructField("hash_key", StringType),
      StructField("file_name", StringType)
    )
  )

  val currencySchema: StructType = StructType(
    List(
      StructField("numeric_code", StringType),
      StructField("currency_code", StringType)
    )
  )

  def ts(time: String): Timestamp =
    if (time.length > 10) {
      new Timestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(time).getTime)
    } else { new Timestamp(new SimpleDateFormat("yyyy-MM-dd").parse(time).getTime) }

  def upcTransaction(rows: Row*): DataFrame = spark.createDataFrame(spark.sparkContext.parallelize(rows), upcTransactionSchema)

  def auditHotel(rows: Row*): DataFrame = spark.createDataFrame(spark.sparkContext.parallelize(rows), auditHotelSchema)

  def hotelHistory(rows: Row*): DataFrame = spark.createDataFrame(spark.sparkContext.parallelize(rows), hotelHistorySchema)

  def hotel(rows: Row*): DataFrame = spark.createDataFrame(spark.sparkContext.parallelize(rows), hotelSchema)

  def exchangeRate(rows: Row*): DataFrame = spark.createDataFrame(spark.sparkContext.parallelize(rows), exchangeRateSchema)

  def campaignDetails(rows: Row*): DataFrame = spark.createDataFrame(spark.sparkContext.parallelize(rows), campaignDetailsSchema)

  def checkoutCampaignDetails(rows: Row*): DataFrame = spark.createDataFrame(spark.sparkContext.parallelize(rows), checkoutCampaignDetailsSchema)

  def wex(rows: Row*): DataFrame = spark.createDataFrame(spark.sparkContext.parallelize(rows), wexSchema)

  def enett(rows: Row*): DataFrame = spark.createDataFrame(spark.sparkContext.parallelize(rows), enettSchema)

  def tutuka(rows: Row*): DataFrame = spark.createDataFrame(spark.sparkContext.parallelize(rows), tutukaSchema)

  def checkout(rows: Row*): DataFrame = spark.createDataFrame(spark.sparkContext.parallelize(rows), checkoutSchema)

  def currency(rows: Row*): DataFrame = spark.createDataFrame(spark.sparkContext.parallelize(rows), currencySchema)

  def result(rows: Row*): DataFrame = spark.createDataFrame(spark.sparkContext.parallelize(rows), resultSchema)

  def resultTutuka(rows: Row*): DataFrame = spark.createDataFrame(spark.sparkContext.parallelize(rows), resultTutukaSchema)

  def resultCheckout(rows: Row*): DataFrame = spark.createDataFrame(spark.sparkContext.parallelize(rows), resultCheckoutSchema)

  def bookingInfo(rows: Row*): DataFrame = spark.createDataFrame(spark.sparkContext.parallelize(rows), bookingInfoSchema)

  def df(rows: Row*)(schema: StructType): DataFrame = spark.createDataFrame(spark.sparkContext.parallelize(rows), schema)

  def dfCols(rows: Row*)(columns: (String, DataType)*): DataFrame =
    spark.createDataFrame(spark.sparkContext.parallelize(rows), StructType(fields = columns.map(c => StructField(c._1, c._2))))
}
