package com.agoda.finance.functions.specific.ApSettlementRatioUpcLogic

import com.agoda.ml.spark.services.etl.{DataFrameETLExtensionLocalTest, SparkETLLocalTest}
import org.apache.spark.sql.functions.expr
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.scalatest.Matchers

case class DataRow(
    source: String,
    local_total_amount_inc_gst: Double = 0.0,
    usd_total_amount_inc_gst: Double = 0.0,
    running_local_balance: Double = 0.0,
    matched_status: String = "",
    previous_matched_statuses: Seq[String] = Seq.empty,
    booking_settlement_ex_rate: Double = 0.0
)

class ApSettlementUdfTest extends SparkETLLocalTest with DataFrameETLExtensionLocalTest with Matchers {

  override def beforeAll(): Unit = {
    super.beforeAll()
    setUpAll()(sparkSession)
    TestUdf.registerSettlementUsdCalculationUdf()(sparkSession)
  }

  def setUpAll()(implicit spark: SparkSession): Unit =
    spark.conf.set("spark.sql.session.timeZone", "UTC+7")

  private def calculateUsdValues(rows: Seq[DataRow]): Seq[Double] = {
    if (rows.isEmpty) return Seq.empty[Double]
    val inputDF  = toDF(rows)
    val resultDF = inputDF.groupBy().agg(expr("toGetSettlementUsdValue(collect_list(struct(*)))").as("usd_values"))
    val result   = resultDF.collect().head.getAs[Seq[Double]]("usd_values")
    if (result == null) Seq.empty[Double] else result
  }

  private def toDF(rows: Seq[DataRow]): DataFrame =
    sparkSession.createDataFrame(rows)

  object TestUdf extends ApSettlementUdf

  test("should calculate USD values for BOOKING and SETTLEMENT rows") {
    val rows = Seq(
      DataRow(source = "BOOKING", local_total_amount_inc_gst = 100.0, usd_total_amount_inc_gst = 120.0),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = 50.0,
        running_local_balance = 20.0,
        matched_status = "MATCHED",
        booking_settlement_ex_rate = 1.2
      )
    )
    val result = calculateUsdValues(rows)
    result shouldBe Seq(120.0, 60.0)
  }

  test("should handle empty input") {
    val result = calculateUsdValues(Seq.empty)
    result shouldBe empty
  }

  test("should handle MATCHED_UNDER") {
    val rows = Seq(
      DataRow(source = "BOOKING", local_total_amount_inc_gst = 25.0),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -50.0,
        running_local_balance = -20.0,
        matched_status = "MATCHED_UNDER",
        booking_settlement_ex_rate = 1.2
      )
    )
    val result = calculateUsdValues(rows)
    result shouldBe Seq(0.0, -0.0)
  }

  test("should handle MATCHED") {
    val rows = Seq(
      DataRow(
        source = "BOOKING",
        local_total_amount_inc_gst = 2870.91,
        usd_total_amount_inc_gst = 83.47,
        running_local_balance = 2870.91,
        matched_status = "NOT_APPLICABLE",
        booking_settlement_ex_rate = 0.029073
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -2870.91,
        matched_status = "MATCHED",
        previous_matched_statuses = Seq("NOT_APPLICABLE"),
        booking_settlement_ex_rate = 0.029165
      )
    )
    val result = calculateUsdValues(rows)
    result shouldBe Seq(83.47, -83.47)
  }

  test("should handle MATCHED_OVER") {
    val rows = Seq(
      DataRow(
        source = "BOOKING",
        local_total_amount_inc_gst = 78.26,
        usd_total_amount_inc_gst = 86.39,
        running_local_balance = 78.26,
        matched_status = "NOT_APPLICABLE",
        booking_settlement_ex_rate = 1.1101
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -79.0,
        running_local_balance = -0.74,
        matched_status = "MATCHED_OVER",
        previous_matched_statuses = Seq("NOT_APPLICABLE"),
        booking_settlement_ex_rate = 1.1084
      )
    )
    val result = calculateUsdValues(rows)
    result shouldBe Seq(86.39, -87.210216)
  }

  test("should handle MATCHED, MATCHED_UNDER, MATCHED") {
    val rows = Seq(
      DataRow(
        source = "BOOKING",
        local_total_amount_inc_gst = 1031.14,
        usd_total_amount_inc_gst = 768.58,
        running_local_balance = 1031.14,
        matched_status = "NOT_APPLICABLE",
        booking_settlement_ex_rate = 0.7398
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -1031.14,
        matched_status = "MATCHED",
        previous_matched_statuses = Seq("NOT_APPLICABLE"),
        booking_settlement_ex_rate = 0.7456
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = 58.51,
        running_local_balance = 58.51,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED"),
        booking_settlement_ex_rate = 0.7444
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -58.510000000000005,
        matched_status = "MATCHED",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.7475
      )
    )
    val result = calculateUsdValues(rows)
    result shouldBe Seq(768.58, -768.58, 43.554843999999996, -43.554844)
  }

  test("should handle MATCHED_UNDER, MATCHED") {
    val rows = Seq(
      DataRow(
        source = "BOOKING",
        local_total_amount_inc_gst = 533324.0,
        usd_total_amount_inc_gst = 394.82,
        running_local_balance = 533324.0,
        matched_status = "NOT_APPLICABLE",
        previous_matched_statuses = Seq.empty[String],
        booking_settlement_ex_rate = 0.0007403
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -266662.0,
        running_local_balance = 266662.0,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE"),
        booking_settlement_ex_rate = 0.0007138
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -266662.0,
        matched_status = "MATCHED",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.0007138
      )
    )
    val result = calculateUsdValues(rows)
    result shouldBe Seq(394.82, -197.41, -197.41)
  }

  test("should handle MATCHED_UNDER, MATCHED_OVER") {
    val rows = Seq(
      DataRow(
        source = "BOOKING",
        local_total_amount_inc_gst = 761.79,
        usd_total_amount_inc_gst = 553.13,
        running_local_balance = 761.79,
        matched_status = "NOT_APPLICABLE",
        previous_matched_statuses = Seq.empty[String],
        booking_settlement_ex_rate = 0.7441
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -754.76,
        running_local_balance = 7.03,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE"),
        booking_settlement_ex_rate = 0.7468
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -7.05,
        running_local_balance = -0.02,
        matched_status = "MATCHED_OVER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.7423
      )
    )
    val result = calculateUsdValues(rows)
    result shouldBe Seq(553.13, -548.0255697764476, -5.119276223552413)
  }

  test("should handle MATCHED_OVER, MATCHED") {
    val rows = Seq(
      DataRow(
        source = "BOOKING",
        local_total_amount_inc_gst = 24699.0,
        usd_total_amount_inc_gst = 192.31,
        running_local_balance = 24699.0,
        matched_status = "NOT_APPLICABLE",
        previous_matched_statuses = Seq.empty[String],
        booking_settlement_ex_rate = 0.007475
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -49397.0,
        running_local_balance = -24698.0,
        matched_status = "MATCHED_OVER",
        previous_matched_statuses = Seq("NOT_APPLICABLE"),
        booking_settlement_ex_rate = 0.007786
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = 24698.0,
        matched_status = "MATCHED",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_OVER"),
        booking_settlement_ex_rate = 0.007475
      )
    )
    val result = calculateUsdValues(rows)
    result shouldBe Seq(192.31, -384.608628, 192.298628)
  }

  test("should handle MATCHED_OVER, MATCHED_UNDER") {
    val rows = Seq(
      DataRow(
        source = "BOOKING",
        local_total_amount_inc_gst = 189.62,
        usd_total_amount_inc_gst = 247.77,
        running_local_balance = 189.62,
        matched_status = "NOT_APPLICABLE",
        previous_matched_statuses = Seq.empty[String],
        booking_settlement_ex_rate = 1.3067
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -243.1,
        running_local_balance = -53.48,
        matched_status = "MATCHED_OVER",
        previous_matched_statuses = Seq("NOT_APPLICABLE"),
        booking_settlement_ex_rate = 1.3067
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = 55.0,
        running_local_balance = 1.52,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_OVER"),
        booking_settlement_ex_rate = 1.3053
      )
    )
    val result = calculateUsdValues(rows)
    result shouldBe Seq(247.77, -317.652316, 71.86849999999998)
  }

  test("should handle MATCHED, MATCHED_UNDER, MATCHED_UNDER, MATCHED_UNDER, MATCHED_OVER") {
    val rows = Seq(
      DataRow(
        source = "BOOKING",
        local_total_amount_inc_gst = 312290.0,
        usd_total_amount_inc_gst = 233.19,
        running_local_balance = 312290.0,
        matched_status = "NOT_APPLICABLE",
        previous_matched_statuses = Seq.empty[String],
        booking_settlement_ex_rate = 0.0007079
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -312290.0,
        matched_status = "MATCHED",
        previous_matched_statuses = Seq("NOT_APPLICABLE"),
        booking_settlement_ex_rate = 0.0007466
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = 312290.0,
        running_local_balance = 312290.0,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED"),
        booking_settlement_ex_rate = 0.0007243
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = 312290.0,
        running_local_balance = 624580.0,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.0007119
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -312290.0,
        running_local_balance = 312290.0,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED", "MATCHED_UNDER", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.0007028
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -326184.0,
        running_local_balance = -13894.0,
        matched_status = "MATCHED_OVER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.0006954
      )
    )
    val result = calculateUsdValues(rows)
    result shouldBe Seq(233.19, -233.19, 226.191647, 226.191647, -226.191647, -235.8535346)
  }

  test("should handle MATCHED_OVER, MATCHED_OVER, MATCHED") {
    val rows = Seq(
      DataRow(
        source = "BOOKING",
        local_total_amount_inc_gst = 34304.0,
        usd_total_amount_inc_gst = 263.93,
        running_local_balance = 34304.0,
        matched_status = "NOT_APPLICABLE",
        previous_matched_statuses = Seq.empty[String],
        booking_settlement_ex_rate = 0.007457
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -200682.0,
        running_local_balance = -166378.0,
        matched_status = "MATCHED_OVER",
        previous_matched_statuses = Seq("NOT_APPLICABLE"),
        booking_settlement_ex_rate = 0.007694
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = 83189.0,
        running_local_balance = -83189.0,
        matched_status = "MATCHED_OVER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_OVER"),
        booking_settlement_ex_rate = 0.007457
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = 83189.0,
        matched_status = "MATCHED",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_OVER", "MATCHED_OVER"),
        booking_settlement_ex_rate = 0.007457
      )
    )
    val result = calculateUsdValues(rows)
    result shouldBe Seq(263.93, -1544.0423320000002, 620.340373, 659.7719590000002)
  }

  test("should handle MATCHED_UNDER, MATCHED, MATCHED_OVER, MATCHED, MATCHED_OVER, MATCHED, MATCHED_OVER, MATCHED_OVER, MATCHED") {
    val rows = Seq(
      DataRow(
        source = "BOOKING",
        local_total_amount_inc_gst = 11980.8,
        usd_total_amount_inc_gst = 140.92,
        running_local_balance = 11980.8,
        matched_status = "NOT_APPLICABLE",
        previous_matched_statuses = Seq.empty[String],
        booking_settlement_ex_rate = 0.011467
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -11664.9,
        running_local_balance = 315.9,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE"),
        booking_settlement_ex_rate = 0.011661
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -315.9,
        matched_status = "MATCHED",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.01151
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -11980.8,
        running_local_balance = -11980.8,
        matched_status = "MATCHED_OVER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED"),
        booking_settlement_ex_rate = 0.01151
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = 11980.8,
        matched_status = "MATCHED",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED", "MATCHED_OVER"),
        booking_settlement_ex_rate = 0.01151
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -1198.8,
        running_local_balance = -1198.8,
        matched_status = "MATCHED_OVER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED", "MATCHED_OVER", "MATCHED"),
        booking_settlement_ex_rate = 0.01151
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = 1198.8,
        matched_status = "MATCHED",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED", "MATCHED_OVER", "MATCHED", "MATCHED_OVER"),
        booking_settlement_ex_rate = 0.01151
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -11980.8,
        running_local_balance = -11980.8,
        matched_status = "MATCHED_OVER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED", "MATCHED_OVER", "MATCHED", "MATCHED_OVER", "MATCHED"),
        booking_settlement_ex_rate = 0.01151
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = 11664.9,
        running_local_balance = -315.9,
        matched_status = "MATCHED_OVER",
        previous_matched_statuses = Seq(
          "NOT_APPLICABLE",
          "MATCHED_UNDER",
          "MATCHED",
          "MATCHED_OVER",
          "MATCHED",
          "MATCHED_OVER",
          "MATCHED",
          "MATCHED_OVER"
        ),
        booking_settlement_ex_rate = 0.01151
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = 315.9,
        matched_status = "MATCHED",
        previous_matched_statuses = Seq(
          "NOT_APPLICABLE",
          "MATCHED_UNDER",
          "MATCHED",
          "MATCHED_OVER",
          "MATCHED",
          "MATCHED_OVER",
          "MATCHED",
          "MATCHED_OVER",
          "MATCHED_OVER"
        ),
        booking_settlement_ex_rate = 0.01151
      )
    )
    val result = calculateUsdValues(rows)
    result shouldBe Seq(
      140.92, -137.2043359375, -3.7156640625000006, -137.89900799999998, 137.89900799999998, -13.798187999999998, 13.798187999999998,
      -137.89900799999998, 134.26299899999998, 3.6360090000000054
    )
  }

  test("should handle MATCHED_UNDER, MATCHED_OVER, MATCHED, MATCHED_UNDER, MATCHED, MATCHED_UNDER, MATCHED_OVER") {
    val rows = Seq(
      DataRow(
        source = "BOOKING",
        local_total_amount_inc_gst = 779812.0,
        usd_total_amount_inc_gst = 582.74,
        running_local_balance = 779812.0,
        matched_status = "NOT_APPLICABLE",
        previous_matched_statuses = Seq.empty[String],
        booking_settlement_ex_rate = 0.0007575
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -438306.0,
        running_local_balance = 341506.0,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE"),
        booking_settlement_ex_rate = 0.0007506
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -438306.0,
        running_local_balance = -96800.0,
        matched_status = "MATCHED_OVER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.0007506
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = 96800.0,
        matched_status = "MATCHED",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED_OVER"),
        booking_settlement_ex_rate = 0.000725
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = 438306.0,
        running_local_balance = 438306.0,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED_OVER", "MATCHED"),
        booking_settlement_ex_rate = 0.000722
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -438306.0,
        matched_status = "MATCHED",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED_OVER", "MATCHED", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.0007154
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = 438306.0,
        running_local_balance = 438306.0,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED_OVER", "MATCHED", "MATCHED_UNDER", "MATCHED"),
        booking_settlement_ex_rate = 0.0006952
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -442889.99999999994,
        running_local_balance = -4584.0,
        matched_status = "MATCHED_OVER",
        previous_matched_statuses = Seq(
          "NOT_APPLICABLE",
          "MATCHED_UNDER",
          "MATCHED_OVER",
          "MATCHED",
          "MATCHED_UNDER",
          "MATCHED",
          "MATCHED_UNDER"
        ),
        booking_settlement_ex_rate = 0.0006849
      )
    )
    val result = calculateUsdValues(rows)
    result shouldBe Seq(
      582.74, -327.5384816340349, -327.8595983659651, 72.65807999999998, 316.456932, -316.456932, 304.7103312, -307.84991279999997
    )
  }

  test("should handle MATCHED_UNDER, MATCHED_UNDER, MATCHED_UNDER, MATCHED_UNDER, MATCHED_UNDER, MATCHED") {
    val rows = Seq(
      DataRow(
        source = "BOOKING",
        local_total_amount_inc_gst = 1530012.0,
        usd_total_amount_inc_gst = 1100.4,
        running_local_balance = 1530012.0,
        matched_status = "NOT_APPLICABLE",
        previous_matched_statuses = Seq.empty[String],
        booking_settlement_ex_rate = 0.0007192
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -255002.0,
        running_local_balance = 1275010.0,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE"),
        booking_settlement_ex_rate = 0.0007636
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -255002.0,
        running_local_balance = 1020008.0,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.0007636
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -255002.0,
        running_local_balance = 765006.0,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.0007636
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -255002.0,
        running_local_balance = 510004.0,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.0007636
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -255002.0,
        running_local_balance = 255002.0,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.0007636
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -255002.0,
        matched_status = "MATCHED",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.0007636
      )
    )
    val result = calculateUsdValues(rows)
    result shouldBe Seq(1100.4, -183.40000000000003, -183.4, -183.4, -183.40000000000003, -183.4, -183.4)
  }

  test("should handle MATCHED_UNDER, MATCHED_UNDER, MATCHED_UNDER, MATCHED_UNDER, MATCHED_UNDER, MATCHED_UNDER, MATCHED") {
    val rows = Seq(
      DataRow(
        source = "BOOKING",
        local_total_amount_inc_gst = 2893380.0,
        usd_total_amount_inc_gst = 2084.74,
        running_local_balance = 2893380.0,
        matched_status = "NOT_APPLICABLE",
        previous_matched_statuses = Seq.empty[String],
        booking_settlement_ex_rate = 0.0007216
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -413339.99999999994,
        running_local_balance = 2480040.0,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE"),
        booking_settlement_ex_rate = 0.0007636
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -413339.99999999994,
        running_local_balance = 2066700.0,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.0007636
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -413339.99999999994,
        running_local_balance = 1653360.0,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.0007636
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -413339.99999999994,
        running_local_balance = 1240020.0,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.0007636
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -413339.99999999994,
        running_local_balance = 826680.0,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.0007636
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -413339.99999999994,
        running_local_balance = 413340.0,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.0007636
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -413339.99999999994,
        matched_status = "MATCHED",
        previous_matched_statuses = Seq(
          "NOT_APPLICABLE",
          "MATCHED_UNDER",
          "MATCHED_UNDER",
          "MATCHED_UNDER",
          "MATCHED_UNDER",
          "MATCHED_UNDER",
          "MATCHED_UNDER"
        ),
        booking_settlement_ex_rate = 0.0007636
      )
    )
    val result = calculateUsdValues(rows)
    result shouldBe Seq(
      2084.74, -297.81999999999994, -297.81999999999994, -297.81999999999994, -297.81999999999994, -297.81999999999994, -297.82, -297.82
    )
  }

  test("should handle MATCHED_UNDER, MATCHED_UNDER, MATCHED_UNDER, MATCHED_UNDER, MATCHED_UNDER, MATCHED_UNDER, MATCHED_OVER, MATCHED") {
    val rows = Seq(
      DataRow(
        source = "BOOKING",
        local_total_amount_inc_gst = 40673.7,
        usd_total_amount_inc_gst = 481.38,
        running_local_balance = 40673.7,
        matched_status = "NOT_APPLICABLE",
        previous_matched_statuses = Seq.empty[String],
        booking_settlement_ex_rate = 0.011467
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -19291.95,
        running_local_balance = 21381.75,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE"),
        booking_settlement_ex_rate = 0.011807
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -19291.95,
        running_local_balance = 2089.8,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.011807
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = 20336.85,
        running_local_balance = 22426.65,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.01151
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = 20336.85,
        running_local_balance = 42763.5,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.01151
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -20336.85,
        running_local_balance = 22426.65,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.01151
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -20336.85,
        running_local_balance = 2089.8,
        matched_status = "MATCHED_UNDER",
        previous_matched_statuses = Seq("NOT_APPLICABLE", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER", "MATCHED_UNDER"),
        booking_settlement_ex_rate = 0.01151
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = -40673.7,
        running_local_balance = -38583.9,
        matched_status = "MATCHED_OVER",
        previous_matched_statuses = Seq(
          "NOT_APPLICABLE",
          "MATCHED_UNDER",
          "MATCHED_UNDER",
          "MATCHED_UNDER",
          "MATCHED_UNDER",
          "MATCHED_UNDER",
          "MATCHED_UNDER"
        ),
        booking_settlement_ex_rate = 0.01151
      ),
      DataRow(
        source = "SETTLEMENT",
        local_total_amount_inc_gst = 38583.9,
        matched_status = "MATCHED",
        previous_matched_statuses = Seq(
          "NOT_APPLICABLE",
          "MATCHED_UNDER",
          "MATCHED_UNDER",
          "MATCHED_UNDER",
          "MATCHED_UNDER",
          "MATCHED_UNDER",
          "MATCHED_UNDER",
          "MATCHED_OVER"
        ),
        booking_settlement_ex_rate = 0.01151
      )
    )
    val result = calculateUsdValues(rows)
    result shouldBe Seq(
      481.38, -228.32343482397718, -228.3234348239772, 240.68999999999986, 240.68999999999986, -240.68999999999988, -240.68999999999986,
      -468.83381935204557, 444.100689
    )
  }

}
