package com.agoda.finance.functions.specific

import com.agoda.ml.spark.DataFrameMatchers.beEqualTo
import com.agoda.ml.spark.{HiveSupport, SparkSharedLocalTest}

import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import org.scalatest.FunSuite
import org.scalatest.Matchers.convertToAnyShouldWrapper

import java.sql.Timestamp
import java.text.SimpleDateFormat

class TripjackLogicTest extends FunSuite with SparkSharedLocalTest with HiveSupport {
  import sqlContext.implicits._

  test("getTripjackTransactionType should return SALE when amendmentType is null") {
    val df = Seq[String](
      null,
      null
    ).toDF("amendmentType")

    val actual = df
      .withColumn("result", TripjackLogic.getTripjackTransactionType(col("amendmentType")))
      .select("result")

    val expected = Seq("SALE", "SALE").toDF("result")

    actual should beEqualTo(expected)
  }

  test("getTripjackTransactionType should map amendmentType to correct transaction type") {
    val testData = Seq(
      ("CANCELLATION", "REFUND"),
      ("CANCELLATION QUOTATION", "REFUND"),
      ("CORRECTION", "REISSUE"),
      ("CUSTOM", "REFUND"),
      ("FARE CHANGE", "REISSUE"),
      ("FULL REFUND", "REFUND"),
      ("MISCELLANEOUS", "REFUND"),
      ("NO SHOW", "REFUND"),
      ("REISSUE", "REISSUE"),
      ("REISSUE QUOTATION", "REISSUE"),
      ("SSR", "REFUND"),
      ("VOIDED", "REFUND")
    ).toDF("amendmentType", "expected")

    val actual = testData
      .withColumn("result", TripjackLogic.getTripjackTransactionType(col("amendmentType")))
      .select("amendmentType", "result")

    val expected = testData.select(
      col("amendmentType"),
      col("expected").as("result")
    )

    actual should beEqualTo(expected)
  }

  test("getTripjackTransactionType should return SALE for unknown amendmentType") {
    val df = Seq("UNKNOWN_TYPE", "INVALID", "").toDF("amendmentType")

    val actual = df
      .withColumn("result", TripjackLogic.getTripjackTransactionType(col("amendmentType")))
      .select("result")

    val expected = Seq("SALE", "SALE", "SALE").toDF("result")

    actual should beEqualTo(expected)
  }

  test("getTripjackTransactionDate should use amendmentDate for REFUND transaction type") {
    val bookingDateFormat = new SimpleDateFormat("yyyy-MM-dd")
    val bookingDate       = new Timestamp(bookingDateFormat.parse("2025-01-15").getTime)
    val bookingTime       = "14:30:25.123"
    val amendmentDate     = "2025-01-20T16:45:30.456"

    val testData = Seq(
      ("REFUND", bookingDate, bookingTime, amendmentDate)
    ).toDF("transactionType", "bookingDate", "bookingTime", "amendmentDate")

    val actual = testData
      .withColumn(
        "result",
        TripjackLogic.getTripjackTransactionDate(
          col("transactionType"),
          col("bookingDate"),
          col("bookingTime"),
          col("amendmentDate")
        )
      )
      .select("result")

    // Expected: 2025-01-20T16:45:30.456 minus 5 hours 30 minutes = 2025-01-20T11:15:30.456
    val expectedTimestamp = new Timestamp(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").parse("2025-01-20T11:15:30.456").getTime)
    val expected          = Seq(expectedTimestamp).toDF("result")

    actual should beEqualTo(expected)
  }

  test("getTripjackTransactionDate should use amendmentDate for REISSUE transaction type") {
    val bookingDateFormat = new SimpleDateFormat("yyyy-MM-dd")
    val bookingDate       = new Timestamp(bookingDateFormat.parse("2025-01-15").getTime)
    val bookingTime       = "14:30:25.123"
    val amendmentDate     = "2025-01-20T16:45:30.456"

    val testData = Seq(
      ("REISSUE", bookingDate, bookingTime, amendmentDate)
    ).toDF("transactionType", "bookingDate", "bookingTime", "amendmentDate")

    val actual = testData
      .withColumn(
        "result",
        TripjackLogic.getTripjackTransactionDate(
          col("transactionType"),
          col("bookingDate"),
          col("bookingTime"),
          col("amendmentDate")
        )
      )
      .select("result")

    // Expected: 2025-01-20T16:45:30.456 minus 5 hours 30 minutes = 2025-01-20T11:15:30.456
    val expectedTimestamp = new Timestamp(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").parse("2025-01-20T11:15:30.456").getTime)
    val expected          = Seq(expectedTimestamp).toDF("result")

    actual should beEqualTo(expected)
  }

  test("getTripjackTransactionDate should use bookingDate + bookingTime for SALE transaction type") {
    val bookingDateFormat = new SimpleDateFormat("yyyy-MM-dd")
    val bookingDate       = new Timestamp(bookingDateFormat.parse("2025-01-15").getTime)
    val bookingTime       = "14:30:25.123"
    val amendmentDate     = "2025-01-20T16:45:30.456"

    val testData = Seq(
      ("SALE", bookingDate, bookingTime, amendmentDate)
    ).toDF("transactionType", "bookingDate", "bookingTime", "amendmentDate")

    val actual = testData
      .withColumn(
        "result",
        TripjackLogic.getTripjackTransactionDate(
          col("transactionType"),
          col("bookingDate"),
          col("bookingTime"),
          col("amendmentDate")
        )
      )
      .select("result")

    // Expected: 2025-01-15 14:30:25.123 minus 5 hours 30 minutes = 2025-01-15 09:00:25.123
    val expectedTimestamp = new Timestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").parse("2025-01-15 09:00:25.123").getTime)
    val expected          = Seq(expectedTimestamp).toDF("result")

    actual should beEqualTo(expected)
  }

  test("getTripjackTransactionDate should use bookingDate + bookingTime for other transaction types") {
    val bookingDateFormat = new SimpleDateFormat("yyyy-MM-dd")
    val bookingDate       = new Timestamp(bookingDateFormat.parse("2025-01-15").getTime)
    val bookingTime       = "14:30:25.123"
    val amendmentDate     = "2025-01-20T16:45:30.456"

    val testData = Seq(
      ("EXCHANGE", bookingDate, bookingTime, amendmentDate),
      ("VOID", bookingDate, bookingTime, amendmentDate),
      ("UNKNOWN", bookingDate, bookingTime, amendmentDate)
    ).toDF("transactionType", "bookingDate", "bookingTime", "amendmentDate")

    val actual = testData
      .withColumn(
        "result",
        TripjackLogic.getTripjackTransactionDate(
          col("transactionType"),
          col("bookingDate"),
          col("bookingTime"),
          col("amendmentDate")
        )
      )
      .select("result")

    // Expected: 2025-01-15 14:30:25.123 minus 5 hours 30 minutes = 2025-01-15 09:00:25.123
    val expectedTimestamp = new Timestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").parse("2025-01-15 09:00:25.123").getTime)
    val expected          = Seq(expectedTimestamp, expectedTimestamp, expectedTimestamp).toDF("result")

    actual should beEqualTo(expected)
  }

  test("getTripjackTransactionDate should return null when required fields are null") {
    val bookingDateFormat = new SimpleDateFormat("yyyy-MM-dd")
    val bookingDate       = new Timestamp(bookingDateFormat.parse("2025-01-15").getTime)
    val bookingTime       = "14:30:25.123"
    val amendmentDate     = "2025-01-20T16:45:30.456"

    val testData = Seq(
      ("SALE", null.asInstanceOf[Timestamp], bookingTime, amendmentDate),               // null bookingDate for SALE
      ("SALE", bookingDate, null.asInstanceOf[String], amendmentDate),                  // null bookingTime for SALE
      ("SALE", null.asInstanceOf[Timestamp], null.asInstanceOf[String], amendmentDate), // both null for SALE
      ("REFUND", bookingDate, bookingTime, null.asInstanceOf[String]),                  // null amendmentDate for REFUND
      ("REISSUE", bookingDate, bookingTime, null.asInstanceOf[String])                  // null amendmentDate for REISSUE
    ).toDF("transactionType", "bookingDate", "bookingTime", "amendmentDate")

    val actual = testData
      .withColumn(
        "result",
        TripjackLogic.getTripjackTransactionDate(
          col("transactionType"),
          col("bookingDate"),
          col("bookingTime"),
          col("amendmentDate")
        )
      )
      .select("result")

    val expected = Seq(
      Option.empty[Timestamp],
      Option.empty[Timestamp],
      Option.empty[Timestamp],
      Option.empty[Timestamp],
      Option.empty[Timestamp]
    ).toDF("result")
      .withColumn("result", col("result").cast(TimestampType))

    actual should beEqualTo(expected)
  }

  test("getTripjackTransactionDate should handle edge cases with time zones") {
    val bookingDateFormat = new SimpleDateFormat("yyyy-MM-dd")
    val bookingDate       = new Timestamp(bookingDateFormat.parse("2025-01-01").getTime)
    val bookingTime       = "05:29:59.999" // Edge case - close to offset boundary
    val amendmentDate     = "2025-01-01T05:29:59.999"

    val testData = Seq(
      ("SALE", bookingDate, bookingTime, amendmentDate),
      ("REFUND", bookingDate, bookingTime, amendmentDate)
    ).toDF("transactionType", "bookingDate", "bookingTime", "amendmentDate")

    val actual = testData
      .withColumn(
        "result",
        TripjackLogic.getTripjackTransactionDate(
          col("transactionType"),
          col("bookingDate"),
          col("bookingTime"),
          col("amendmentDate")
        )
      )
      .select("result")

    // Expected: Both should result in 2024-12-31 23:59:59.999 (previous day due to negative offset)
    val expectedTimestamp = new Timestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").parse("2024-12-31 23:59:59.999").getTime)
    val expected          = Seq(expectedTimestamp, expectedTimestamp).toDF("result")

    actual should beEqualTo(expected)
  }
}
