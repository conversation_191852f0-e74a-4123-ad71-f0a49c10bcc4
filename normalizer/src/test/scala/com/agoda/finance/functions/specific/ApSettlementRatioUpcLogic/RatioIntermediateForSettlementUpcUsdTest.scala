package com.agoda.finance.functions.specific.ApSettlementRatioUpcLogic

import com.agoda.ml.spark.services.etl.{DataFrameETLExtensionLocalTest, SparkETLLocalTest}
import org.apache.spark.sql.functions.{col, countDistinct}
import org.apache.spark.sql.types._

class RatioIntermediateForSettlementUpcUsdTest extends SparkETLLocalTest with DataFrameETLExtensionLocalTest with SettlementUpcRefTableSchemas {

  val dataDate                                               = 20250624
  var bookingSettlementUniverse: SettlementUpcTransformLogic = _

  override def beforeAll(): Unit = {
    super.beforeAll()
    setUpAll()(sparkSession)
    bookingSettlementUniverse = new SettlementUpcTransformLogic(
      processedUpcBookingTableNameDaily = "dummy_booking_table",
      processedUpcSettlementsTableNameDaily = "dummy_settlement_table",
      processedUpcSettlementsTableNameDailyWithUsd = "dummy_settlement_usd_table",
      dataDate = dataDate
    )(spark.sqlContext)

  }

  def getUsdGeneratedDataframe: org.apache.spark.sql.DataFrame = {
    val (bookingUniverse, settlementsCombined, settlementLedgerAdjustment) =
      bookingSettlementUniverse.prepareUpcBookingSettlementUniverseForRatioCalculation(
        financialTransactionsDf = financialTransactionsDataframe,
        ledgerBookingBalanceDf = ledgerBookingBalanceDataframe,
        settlementDf = settlementsDataframe,
        unmatchedSettlementBookingsDf = settlementsUnmatchMatchedDataframe,
        reconManualBookingAdjustment = ebeBookingSettlementAdjustmentDataframe,
        factBookingVehicle = factBookingVehicleDataframe,
        factBookingAll = factBookingAllDataframe,
        exchangeRate = exchangeRatesDataframe
      )

    val ratioIntermediateForSettlementUpcUsd = new RatioIntermediateForSettlementUpcUsd()
    ratioIntermediateForSettlementUpcUsd.prepareRatioIntermediateTableForUsdCalculation(
      bookingUniverse,
      settlementsCombined,
      settlementLedgerAdjustment,
      dataDate
    )
  }

  test("usdGeneratedDataframe should have 3 unique booking_id values") {
    val usdGeneratedDataframe = getUsdGeneratedDataframe
    val bookingIdCount        = usdGeneratedDataframe.select("booking_id").distinct().count()
    bookingIdCount shouldBe 4
  }

  test("Each booking_id should have exactly one unique booking_settlement_currency") {
    val usdGeneratedDataframe = getUsdGeneratedDataframe
    val currencyCounts = usdGeneratedDataframe
      .groupBy("booking_id")
      .agg(countDistinct("booking_settlement_currency").as("currency_count"))
      .collect()

    assert(
      currencyCounts.forall(row => row.getAs[Long]("currency_count") == 1),
      s"Some booking_id(s) have more than one booking_settlement_currency: ${currencyCounts.filter(row => row.getAs[Long]("currency_count") != 1).mkString(", ")}"
    )
  }

  test("calculated_usd_amount should never be null for source = 'SETTLEMENT'") {
    val usdGeneratedDataframe = getUsdGeneratedDataframe

    val nullCount = usdGeneratedDataframe
      .filter(col("source") === "SETTLEMENT" && col("calculated_usd_amount").isNull)
      .count()

    nullCount shouldBe 0
  }

  test("calculated_usd_amount and local_total_amount_inc_gst should be Decimal(24,2)") {
    val usdGeneratedDataframe = getUsdGeneratedDataframe

    val schema = usdGeneratedDataframe.schema

    val calculatedUsdAmountField = schema("calculated_usd_amount")
    val localTotalAmountField    = schema("local_total_amount_inc_gst")

    calculatedUsdAmountField.dataType shouldBe DecimalType(24, 2)
    localTotalAmountField.dataType shouldBe DecimalType(24, 2)
  }

  test("usdGeneratedDataframe output schema should match the expected schema") {
    val usdGeneratedDataframe = getUsdGeneratedDataframe

    val expectedSchema = StructType(
      Seq(
        StructField("booking_id", LongType, nullable = true),
        StructField("booking_settlement_currency", StringType, nullable = true),
        StructField("uuid", StringType, nullable = true),
        StructField("local_total_amount_inc_gst", DecimalType(24, 2), nullable = true),
        StructField("usd_total_amount_inc_gst", DecimalType(24, 2), nullable = true),
        StructField("transaction_date", TimestampType, nullable = true),
        StructField("source", StringType, nullable = true),
        StructField("source_table", StringType, nullable = false),
        StructField("reference", StringType, nullable = false),
        StructField("booking_settlement_ex_rate", DoubleType, nullable = true),
        StructField("row_num", IntegerType, nullable = false),
        StructField("running_local_balance", DecimalType(35, 2), nullable = true),
        StructField("matched_status", StringType, nullable = false),
        StructField("previous_matched_statuses", ArrayType(StringType, containsNull = false), nullable = false),
        StructField(
          "grouped_rows_list",
          ArrayType(
            StructType(
              Seq(
                StructField("local_total_amount_inc_gst", DoubleType, nullable = true),
                StructField("usd_total_amount_inc_gst", DoubleType, nullable = true),
                StructField("source", StringType, nullable = true),
                StructField("running_local_balance", DoubleType, nullable = true),
                StructField("matched_status", StringType, nullable = false),
                StructField("previous_matched_statuses", ArrayType(StringType, containsNull = false), nullable = false),
                StructField("booking_settlement_ex_rate", DoubleType, nullable = true)
              )
            ),
            containsNull = false
          ),
          nullable = false
        ),
        StructField("calculated_usd_amount_list", ArrayType(DoubleType, containsNull = false), nullable = true),
        StructField("calculated_usd_amount", DecimalType(24, 2), nullable = true),
        StructField("datadate", IntegerType, nullable = false)
      )
    )

    val actualSchema = usdGeneratedDataframe.schema

    assert(
      actualSchema.equals(expectedSchema),
      s"""
         |Schema does not match!
         |Expected: ${expectedSchema.treeString}
         |Actual:   ${actualSchema.treeString}
     """.stripMargin
    )
  }

}
