package com.agoda.finance.functions.specific.ApSettlementRatioUpcLogic

import com.agoda.ml.spark.services.etl.{DataFrameETLExtensionLocalTest, SparkETLLocalTest}
import org.apache.spark.sql.functions.col
import org.apache.spark.sql.types._

class SettlementUpcTransformLogicTest extends SparkETLLocalTest with DataFrameETLExtensionLocalTest with SettlementUpcRefTableSchemas {

  val dataDate                                               = 20250624
  var bookingSettlementUniverse: SettlementUpcTransformLogic = _

  var bookingUniverse: org.apache.spark.sql.DataFrame            = _
  var settlementsCombined: org.apache.spark.sql.DataFrame        = _
  var settlementLedgerAdjustment: org.apache.spark.sql.DataFrame = _
  var settlementEnriched: org.apache.spark.sql.DataFrame         = _

  override def beforeAll(): Unit = {
    super.beforeAll()
    setUpAll()(sparkSession)
    bookingSettlementUniverse = new SettlementUpcTransformLogic(
      processedUpcBookingTableNameDaily = "dummy_booking_table",
      processedUpcSettlementsTableNameDaily = "dummy_settlement_table",
      processedUpcSettlementsTableNameDailyWithUsd = "dummy_settlement_enriched_table",
      dataDate = dataDate
    )(spark.sqlContext)

    val bookingAndSettlementUniverseDf = bookingSettlementUniverse.prepareUpcBookingSettlementUniverseForRatioCalculation(
      financialTransactionsDf = financialTransactionsDataframe,
      ledgerBookingBalanceDf = ledgerBookingBalanceDataframe,
      settlementDf = settlementsDataframe,
      unmatchedSettlementBookingsDf = settlementsUnmatchMatchedDataframe,
      reconManualBookingAdjustment = ebeBookingSettlementAdjustmentDataframe,
      factBookingVehicle = factBookingVehicleDataframe,
      factBookingAll = factBookingAllDataframe,
      exchangeRate = exchangeRatesDataframe
    )
    bookingUniverse = bookingAndSettlementUniverseDf._1
    settlementsCombined = bookingAndSettlementUniverseDf._2
    settlementLedgerAdjustment = bookingAndSettlementUniverseDf._3

    val settlementEnrichedDf = bookingSettlementUniverse.prepareUpcSettlementData(
      financialTransactionsDf = financialTransactionsDataframe,
      ledgerBookingBalanceDf = ledgerBookingBalanceDataframe,
      settlementDf = settlementsDataframe,
      unmatchedSettlementBookingsDf = settlementsUnmatchMatchedDataframe,
      reconManualBookingAdjustment = ebeBookingSettlementAdjustmentDataframe,
      factBookingVehicle = factBookingVehicleDataframe,
      factBookingAll = factBookingAllDataframe,
      exchangeRate = exchangeRatesDataframe
    )

    settlementEnriched = settlementEnrichedDf
  }

  test("prepareUpcBookingSettlementUniverseForRatioCalculation returns expected DataFrames") {
    assert(bookingUniverse.count() > 0, "Booking universe should not be empty")
    assert(settlementsCombined.count() > 0, "Combined settlements should not be empty")
    assert(settlementLedgerAdjustment.count() >= 0, "Settlement ledger adjustment should be present")
  }

  test("Booking with zero amounts: booking_settlement_amount should be null") {
    assert(
      settlementsCombined.filter(col("booking_settlement_amount").isNotNull).count() == 18,
      "Should have non-null booking_settlement_amount"
    )
  }

  test("settlementsCombined should only have three records for datadate today") {
    val todayCount = settlementsCombined
      .filter(col("datadate") === dataDate && col("reference") === "TODAY")
      .count()
    assert(todayCount == 4, s"Expected 4 records for datadate $dataDate, but found $todayCount")
  }

  test("bookingUniverse should not contain the UUID a5ce82b1-a698-4c86-aa96-a34ed0c12b55 as it belongs item PAYMENT") {
    val forbiddenUuid = "a5ce82b1-a698-4c86-aa96-a34ed0c12b55"
    val found         = bookingUniverse.filter(col("uuid") === forbiddenUuid).count()
    assert(found == 0, s"bookingUniverse should not contain the UUID $forbiddenUuid, but it was found.")
  }

  test("bookingUniverse where from == 'LEDGER_BOOKING' should have 3 rows with specific booking_ids") {
    import sparkSession.implicits._
    val expectedBookingIds = Set(1355172752, 1361842984, 1509230980)
    val ledgerBookingRows = bookingUniverse
      .filter(col("source_table") === "LEDGER_BOOKING")
      .select("booking_id")
      .distinct()
      .as[Long]
      .collect()
      .toSet
    assert(ledgerBookingRows.size == 3, s"Expected 3 rows with from == 'LEDGER_BOOKING', but found ${ledgerBookingRows.size}")
    assert(ledgerBookingRows == expectedBookingIds, s"Expected booking_ids $expectedBookingIds, but found $ledgerBookingRows")
  }

  test("when booking_id is missing, source_transaction_code is prefixed with M_") {
    import sparkSession.implicits._

    val settlementUnmatchedBooking = settlementsCombined.filter(col("booking_id") === 999999999)
    val sourceTransactionCodes = settlementUnmatchedBooking
      .select("source_transaction_code")
      .as[String]
      .collect()
    assert(
      sourceTransactionCodes.forall(_.startsWith("M_")),
      s"Expected all source_transaction_code values to start with 'M_', but found: ${sourceTransactionCodes.mkString(", ")}"
    )
  }
  test("should find unmatched case with R_ source_transaction_code for given booking_id and uuid") {
    val bookingId = 1513715561L
    val uuid      = "17bf5516-ad19-4b2e-989f-f233445b236b"

    val filtered = settlementsCombined
      .filter(
        col("booking_id") === bookingId &&
          col("uuid") === uuid
      )
      .select("case", "source_transaction_code")
      .collect()

    assert(filtered.nonEmpty, s"No row found for booking_id=$bookingId and uuid=$uuid")

    val row                   = filtered.head
    val caseValue             = row.getAs[String]("case")
    val sourceTransactionCode = row.getAs[String]("source_transaction_code")

    assert(caseValue == "MATCHED", s"Expected case to be MATCHED, but got $caseValue")
    assert(sourceTransactionCode.startsWith("R_"), s"Expected source_transaction_code to start with R_, but got $sourceTransactionCode")
  }

  test("should find unmatched case with M_ source_transaction_code for given booking_id and uuid") {
    val bookingId = 999999999L
    val uuid      = "3e34d9b7-58e3-4c04-9f53-29b33ba5366f"

    val filtered = settlementsCombined
      .filter(
        col("booking_id") === bookingId &&
          col("uuid") === uuid
      )
      .select("case", "source_transaction_code")
      .collect()

    assert(filtered.nonEmpty, s"No row found for booking_id=$bookingId and uuid=$uuid")

    val row                   = filtered.head
    val caseValue             = row.getAs[String]("case")
    val sourceTransactionCode = row.getAs[String]("source_transaction_code")

    assert(caseValue == "UNMATCHED", s"Expected case to be UNMATCHED, but got $caseValue")
    assert(sourceTransactionCode.startsWith("M_"), s"Expected source_transaction_code to start with M_, but got $sourceTransactionCode")
  }

  test("settlement_enriched dataframe should have the below schema") {

    val expectedSchema = StructType(
      Seq(
        StructField("payment_method", StringType, true),
        StructField("source_name", StringType, true),
        StructField("detail_id", LongType, true),
        StructField("split_settlement_amount", DoubleType, true),
        StructField("split_settlement_amount_usd", DoubleType, true),
        StructField("parent_uuid", StringType, true),
        StructField("is_last_fifo_overcharge", BooleanType, true),
        StructField("reprocess", IntegerType, true),
        StructField("booking_settlement_amount", DecimalType(24, 8), true),
        StructField("booking_settlement_amount_usd", DecimalType(24, 8), true),
        StructField("booking_settlement_currency", StringType, true),
        StructField("report_date", IntegerType, true),
        StructField("type", StringType, true),
        StructField("sub_type", StringType, true),
        StructField("source", StringType, false),
        StructField("source_transaction_code", StringType, true),
        StructField("product_type", StringType, true),
        StructField("booking_id", LongType, true),
        StructField("payment_method_id", IntegerType, true),
        StructField("sub_supplier_id", IntegerType, true),
        StructField("supplier_id", IntegerType, true),
        StructField("approval_id", IntegerType, true),
        StructField("accounting_date", TimestampType, true),
        StructField("transaction_date", TimestampType, true),
        StructField("is_violet", BooleanType, true),
        StructField("is_allotment_reject", BooleanType, true),
        StructField("cid", IntegerType, true),
        StructField("payment_model", IntegerType, true),
        StructField("approval_reference", StringType, true),
        StructField("is_agency", BooleanType, true),
        StructField("is_adjustment", BooleanType, true),
        StructField("is_advance_guarantee", BooleanType, true),
        StructField("merchant_of_record_entity", IntegerType, true),
        StructField("revenue_entity", IntegerType, true),
        StructField("rate_contract_entity", IntegerType, true),
        StructField("merchant_of_record_entity_type", IntegerType, true),
        StructField("revenue_entity_type", IntegerType, true),
        StructField("rate_contract_entity_type", IntegerType, true),
        StructField("advance_payment_contract_id", IntegerType, true),
        StructField("adjustment_reference", StringType, true),
        StructField("adjustment_remark", StringType, true),
        StructField("adjustment_reason_id", IntegerType, true),
        StructField("adjustment_reason", StringType, true),
        StructField("settlement_currency", StringType, true),
        StructField("settlement_amount", DecimalType(24, 2), true),
        StructField("posting_currency", StringType, true),
        StructField("posting_amount", DecimalType(24, 2), true),
        StructField("transaction_currency", StringType, true),
        StructField("transaction_amount", DecimalType(24, 2), true),
        StructField("settlement_amount_usd", DecimalType(24, 2), true),
        StructField("posting_ex", DecimalType(24, 8), true),
        StructField("vat_rate", DecimalType(24, 2), true),
        StructField("destination_bank_country_name", StringType, true),
        StructField("destination_bank_country_id", IntegerType, true),
        StructField("agoda_bank_account_number", StringType, true),
        StructField("booking_transaction_date", TimestampType, true),
        StructField("batch_paypal_pay_amount", DecimalType(24, 2), true),
        StructField("batch_paypal_fee_amount", DecimalType(24, 2), true),
        StructField("batch_paypal_pay_amount_usd", DecimalType(24, 2), true),
        StructField("batch_paypal_fee_amount_usd", DecimalType(24, 2), true),
        StructField("value_date", TimestampType, true),
        StructField("uuid", StringType, true),
        StructField("datadate", IntegerType, false)
      )
    )

    assert(settlementEnriched.schema === expectedSchema)

  }
}
