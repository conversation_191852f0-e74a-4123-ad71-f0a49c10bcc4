package com.agoda.finance.functions.specific.PaymentGatewaySettlements

import com.agoda.ml.spark.{HiveSupport, SparkSharedLocalTest}
import org.apache.spark.sql.Row
import org.apache.spark.sql.functions.col
import org.apache.spark.sql.types.{StringType, StructField, StructType}
import org.scalatest.{FunSuite, Matchers}

class JuspaySettlementLogicTest extends FunSuite with SparkSharedLocalTest with HiveSupport with Matchers {

  // getGatewayId tests
  test("getGatewayId should return correct gateway id for valid gateway name") {
    val resultSchema = StructType(List(StructField("gatewayName", StringType)))
    val inputValue   = Seq(Row("BOKU"), Row("RAZORPAY"))
    val df           = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)

    val actual = df.withColumn("result", JuspaySettlementLogic.getGatewayId(col("gatewayName"))).select("result").collect().map(_.get(0))
    assert(actual(0) == 30)
    assert(actual(1) == 32)
  }

  test("getGatewayIdValue should throw IllegalArgumentException for invalid gateway name") {
    val ex = intercept[IllegalArgumentException] {
      JuspaySettlementLogic.getGatewayIdValue("INVALID")
    }
    assert(ex.getMessage.contains("Invalid gateway name"))
  }

  // getSourceAmount/calculateSourceAmount tests
  test("getSourceAmount should calculate and format correctly") {
    val resultSchema = StructType(List(StructField("grossCredit", StringType), StructField("grossDebit", StringType)))
    val inputValue   = Seq(Row("100.1234", "50.1234"), Row("0", "0"), Row("-10", "-20"))
    val df           = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)
    val actual = df
      .withColumn("result", JuspaySettlementLogic.getSourceAmount(col("grossCredit"), col("grossDebit")))
      .select("result")
      .collect()
      .map(_.getString(0))
    assert(actual(0) == formatDoubleToString(50.0, 4))
    assert(actual(1) == formatDoubleToString(0.0, 4))
    assert(actual(2) == formatDoubleToString(10.0, 4))
  }
  test("getSourceAmount should handle null, empty, and malformed input") {
    val resultSchema = StructType(List(StructField("grossCredit", StringType), StructField("grossDebit", StringType)))
    val inputValue   = Seq(Row(null, null), Row("", ""), Row("abc", "def"))
    val df           = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)
    val actual = df
      .withColumn("result", JuspaySettlementLogic.getSourceAmount(col("grossCredit"), col("grossDebit")))
      .select("result")
      .collect()
      .map(_.getString(0))
    assert(actual.forall(_ == formatDoubleToString(0.0, 4)))
  }
  test("calculateSourceAmount should handle null, empty, and malformed input") {
    assert(JuspaySettlementLogic.calculateSourceAmount(null, null) == 0.0)
    assert(JuspaySettlementLogic.calculateSourceAmount("", "") == 0.0)
    assert(JuspaySettlementLogic.calculateSourceAmount("abc", "def") == 0.0)
  }

  // getFeeAmount tests
  test("getFeeAmount should sum and format all components correctly") {
    val resultSchema = StructType(
      List(
        StructField("commission", StringType),
        StructField("schemeFees", StringType),
        StructField("interchange", StringType),
        StructField("markup", StringType)
      )
    )
    val inputValue = Seq(Row("1.1111", "2.2222", "3.3333", "4.4444"), Row("0", "0", "0", "0"))
    val df         = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)
    val actual = df
      .withColumn("result", JuspaySettlementLogic.getFeeAmount(col("commission"), col("schemeFees"), col("interchange"), col("markup")))
      .select("result")
      .collect()
      .map(_.getString(0))
    assert(actual(0) == formatDoubleToString(1.1111 + 2.2222 + 3.3333 + 4.4444, 4))
    assert(actual(1) == formatDoubleToString(0.0, 4))
  }
  test("getFeeAmount should handle null, empty, and malformed input") {
    val resultSchema = StructType(
      List(
        StructField("commission", StringType),
        StructField("schemeFees", StringType),
        StructField("interchange", StringType),
        StructField("markup", StringType)
      )
    )
    val inputValue = Seq(Row(null, null, null, null), Row("", "", "", ""), Row("a", "b", "c", "d"))
    val df         = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)
    val actual = df
      .withColumn("result", JuspaySettlementLogic.getFeeAmount(col("commission"), col("schemeFees"), col("interchange"), col("markup")))
      .select("result")
      .collect()
      .map(_.getString(0))
    assert(actual.forall(_ == formatDoubleToString(0.0, 4)))
  }

  // getExchangeRate tests
  test("getExchangeRate should calculate or fallback to input exchangeRate") {
    val resultSchema = StructType(
      List(
        StructField("exchangeRate", StringType),
        StructField("grossCredit", StringType),
        StructField("grossDebit", StringType),
        StructField("netCredit", StringType),
        StructField("netDebit", StringType),
        StructField("commission", StringType),
        StructField("schemeFees", StringType),
        StructField("interchange", StringType),
        StructField("markup", StringType)
      )
    )
    val inputValue = Seq(
      Row("1.0", "100", "50", "60", "10", "5", "2", "1", "0"), // normal
      Row("1.0", "0", "0", "0", "0", "0", "0", "0", "0"),      // sourceAmount = 0, fallback
      Row("1.0", "10", "10", "0", "0", "0", "0", "0", "0")     // sourceAmount = 0, fallback
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)
    val actual = df
      .withColumn(
        "result",
        JuspaySettlementLogic.getExchangeRate(
          col("exchangeRate"),
          col("grossCredit"),
          col("grossDebit"),
          col("netCredit"),
          col("netDebit"),
          col("commission"),
          col("schemeFees"),
          col("interchange"),
          col("markup")
        )
      )
      .select("result")
      .collect()
      .map(_.getString(0))
    // First row: receivedAmount = 60-10+5+2+1+0=58, sourceAmount=50, exchangeRate=abs(58/50)
    assert(actual(0) == formatDoubleToString(math.abs(58.0 / 50.0), 8))
    // Fallback to input exchangeRate
    assert(actual(1) == "1.0")
    assert(actual(2) == "1.0")
  }
  test("getExchangeRate should handle null, empty, and malformed input") {
    val resultSchema = StructType(
      List(
        StructField("exchangeRate", StringType),
        StructField("grossCredit", StringType),
        StructField("grossDebit", StringType),
        StructField("netCredit", StringType),
        StructField("netDebit", StringType),
        StructField("commission", StringType),
        StructField("schemeFees", StringType),
        StructField("interchange", StringType),
        StructField("markup", StringType)
      )
    )
    val inputValue = Seq(
      Row(null, null, null, null, null, null, null, null, null),
      Row("", "", "", "", "", "", "", "", ""),
      Row("abc", "def", "ghi", "jkl", "mno", "pqr", "stu", "vwx", "yz")
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)
    val actual = df
      .withColumn(
        "result",
        JuspaySettlementLogic.getExchangeRate(
          col("exchangeRate"),
          col("grossCredit"),
          col("grossDebit"),
          col("netCredit"),
          col("netDebit"),
          col("commission"),
          col("schemeFees"),
          col("interchange"),
          col("markup")
        )
      )
      .select("result")
      .collect()
      .map(_.getString(0))
    // Should fallback to exchangeRate input (null or empty or malformed)
    assert(actual(0) == null)
    assert(actual(1) == "")
    assert(actual(2) == "abc")
  }

  // getReceivedAmount/calculateReceivedAmount tests
  test("getReceivedAmount should calculate and format correctly or return null") {
    val resultSchema = StructType(
      List(
        StructField("exchangeRate", StringType),
        StructField("grossCredit", StringType),
        StructField("grossDebit", StringType),
        StructField("netCredit", StringType),
        StructField("netDebit", StringType),
        StructField("commission", StringType),
        StructField("schemeFees", StringType),
        StructField("interchange", StringType),
        StructField("markup", StringType)
      )
    )
    val inputValue = Seq(
      Row("1.0", "100", "50", "60", "10", "5", "2", "1", "0"), // normal
      Row("0.0", "0.0", "0", "0", "0", "0", "0", "0", "0")     // sourceAmount = 0, exchangeRate = 0, should return null
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)
    val actual = df
      .withColumn(
        "result",
        JuspaySettlementLogic.getReceivedAmount(
          col("exchangeRate"),
          col("grossCredit"),
          col("grossDebit"),
          col("netCredit"),
          col("netDebit"),
          col("commission"),
          col("schemeFees"),
          col("interchange"),
          col("markup")
        )
      )
      .select("result")
      .collect()
      .map(_.get(0))
    assert(actual(0) == formatDoubleToString(58.0, 4))
    assert(actual(1) == formatDoubleToString(0.0, 4))
  }
  test("getReceivedAmount should handle null, empty, and malformed input") {
    val resultSchema = StructType(
      List(
        StructField("exchangeRate", StringType),
        StructField("grossCredit", StringType),
        StructField("grossDebit", StringType),
        StructField("netCredit", StringType),
        StructField("netDebit", StringType),
        StructField("commission", StringType),
        StructField("schemeFees", StringType),
        StructField("interchange", StringType),
        StructField("markup", StringType)
      )
    )
    val inputValue = Seq(
      Row(null, null, null, null, null, null, null, null, null),
      Row("", "", "", "", "", "", "", "", ""),
      Row("abc", "def", "ghi", "jkl", "mno", "pqr", "stu", "vwx", "yz")
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)
    val actual = df
      .withColumn(
        "result",
        JuspaySettlementLogic.getReceivedAmount(
          col("exchangeRate"),
          col("grossCredit"),
          col("grossDebit"),
          col("netCredit"),
          col("netDebit"),
          col("commission"),
          col("schemeFees"),
          col("interchange"),
          col("markup")
        )
      )
      .select("result")
      .collect()
      .map(_.get(0))
    assert(actual.forall(v => Option(v).forall(_ == formatDoubleToString(0.0, 4))))
  }
  test("calculateReceivedAmount should handle null, empty, and malformed input") {
    assert(JuspaySettlementLogic.calculateReceivedAmount(1.0, 10.0, null, null, null, null, null, null) == 0.0)
    assert(JuspaySettlementLogic.calculateReceivedAmount(1.0, 10.0, "", "", "", "", "", "") == 0.0)
    assert(JuspaySettlementLogic.calculateReceivedAmount(1.0, 10.0, "a", "b", "c", "d", "e", "f") == 0.0)
  }

  // getMid tests
  test("getMid should append _US/_SG or trim as per logic") {
    val resultSchema = StructType(List(StructField("merchantAccount", StringType), StructField("aquirer", StringType)))
    val inputValue = Seq(
      Row("AgodaCOM123 ", "SOME_US_BANK_US_"),
      Row("AgodaCOM456", "SOME_SG_BANK_SG_"),
      Row("AgodaCOM789", null),
      Row("OtherMerchant", "SOME_US_BANK_US_"),
      Row("AgodaCOM000", "SOME_OTHER_BANK")
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)
    val actual =
      df.withColumn("result", JuspaySettlementLogic.getMid(col("merchantAccount"), col("aquirer"))).select("result").collect().map(_.getString(0))
    assert(actual(0) == "AgodaCOM123_US")
    assert(actual(1) == "AgodaCOM456_SG")
    assert(actual(2) == "AgodaCOM789")
    assert(actual(3) == "OtherMerchant")
    assert(actual(4) == "AgodaCOM000")
  }
  test("getMid should handle null and empty merchantAccount and aquirer") {
    val resultSchema = StructType(List(StructField("merchantAccount", StringType), StructField("aquirer", StringType)))
    val inputValue = Seq(
      Row("", null),
      Row("", "")
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)
    val actual =
      df.withColumn("result", JuspaySettlementLogic.getMid(col("merchantAccount"), col("aquirer"))).select("result").collect().map(_.getString(0))
    // Should not throw, should return null or empty as per logic
    assert(actual(0) == "")
    assert(actual(1) == "")
  }

  // getEventTypeId tests
  test("getEventTypeId should return correct event type id or null") {
    val resultSchema =
      StructType(List(StructField("grossCredit", StringType), StructField("grossDebit", StringType), StructField("recordType", StringType)))
    val inputValue = Seq(
      Row("10", "5", "SETTLED"), // amount >= 0, rule true
      Row("5", "10", "REFUND"),  // amount < 0, rule true
      Row("5", "10", "UNKNOWN")  // rule false
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)
    val actual = df
      .withColumn("result", JuspaySettlementLogic.getEventTypeId("10")(col("grossCredit"), col("grossDebit"), col("recordType")))
      .select("result")
      .collect()
      .map(_.get(0))
    assert(actual(0) == "2")
    assert(actual(1) == "4")
    assert(actual(2) == null)
  }
  test("getEventTypeId should handle null, empty, and malformed input") {
    val resultSchema =
      StructType(List(StructField("grossCredit", StringType), StructField("grossDebit", StringType), StructField("recordType", StringType)))
    val inputValue = Seq(
      Row("", "", ""),
      Row("abc", "def", "ghi")
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)
    val actual = df
      .withColumn("result", JuspaySettlementLogic.getEventTypeId("10")(col("grossCredit"), col("grossDebit"), col("recordType")))
      .select("result")
      .collect()
      .map(_.get(0))
    // Should return null for all
    assert(actual.forall(_ == null))
  }

  private def formatDoubleToString(value: Double, decimals: Int): String =
    f"%%.${decimals}f".format(value)
}
