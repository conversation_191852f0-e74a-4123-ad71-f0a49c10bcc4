package com.agoda.finance.functions.specific.PaymentGatewaySettlements

import com.agoda.ml.spark.DataFrameMatchers.beEqualTo
import com.agoda.ml.spark.{HiveSupport, SparkSharedLocalTest}
import org.apache.spark.sql.Row
import org.apache.spark.sql.functions.{col, monotonically_increasing_id}
import org.apache.spark.sql.types.{StringType, StructField, StructType}
import org.scalatest.{FunSuite, Matchers}

class AlipayOspSettlementLogicTest extends FunSuite with SparkSharedLocalTest with HiveSupport with Matchers {
  import sqlContext.implicits._

  test("getSourceAmount should calculate correctly") {
    val resultSchema = StructType(
      List(
        StructField("transactionAmount", StringType),
        StructField("paymentMethodType", StringType)
      )
    )
    val inputValue = Seq(
      Row("10000", "GCash"),  // 100.0000
      Row("5050", "GCash"),   // 50.5000
      Row("0", "GCash"),      // 0.0000
      Row("invalid", "GCash") // 0.0000 (invalid input)
    )

    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)

    val actual = df
      .withColumn(
        "result",
        AlipayOspSettlementLogic.getSourceAmount(
          col("transactionAmount"),
          col("paymentMethodType")
        )
      )
      .select("result")

    val expected = Seq("100.0000", "50.5000", "0.0000", "0.0000")
      .toDF("result")
      .withColumn("result", col("result"))

    actual should beEqualTo(expected)
  }

  test("getSourceAmount should calculate correctly for TOSSPAY") {
    val resultSchema = StructType(
      List(
        StructField("transactionAmount", StringType),
        StructField("paymentMethodType", StringType)
      )
    )
    val inputValue = Seq(
      Row("10000", "tosspay"),
      Row("5050", "TOSSPAY"),
      Row("0", "TOSSPAY"),
      Row("invalid", "TOSSPAY")
    )

    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)

    val actual = df
      .withColumn(
        "result",
        AlipayOspSettlementLogic.getSourceAmount(
          col("transactionAmount"),
          col("paymentMethodType")
        )
      )
      .select("result")

    val expected = Seq("10000.0000", "5050.0000", "0.0000", "0.0000")
      .toDF("result")
      .withColumn("result", col("result"))

    actual should beEqualTo(expected)
  }

  test("getReceivedAmount should calculate correctly") {
    val resultSchema = StructType(
      List(
        StructField("settlementAmount", StringType),
        StructField("feeAmount", StringType)
      )
    )
    val inputValue = Seq(
      Row("10000", "1000"),     // 100.0000 - 10.0000 = 90.0000
      Row("5050", "505"),       // 50.5000 - 5.0500 = 45.4500
      Row("0", "0"),            // 0.0000
      Row("invalid", "invalid") // 0.0000 (invalid input)
    )

    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)

    val actual = df
      .withColumn(
        "result",
        AlipayOspSettlementLogic.getReceivedAmount(
          col("settlementAmount"),
          col("feeAmount")
        )
      )
      .select("result")

    val expected = Seq("90.0000", "45.4500", "0.0000", "0.0000")
      .toDF("result")
      .withColumn("result", col("result"))

    actual should beEqualTo(expected)
  }

  test("getFeeAmount should calculate correctly") {
    val resultSchema = StructType(
      List(
        StructField("feeAmount", StringType)
      )
    )
    val inputValue = Seq(
      Row("1000"), // -10.0000 (1000/100 * -1)
      Row("505")   // -5.0500 (505/100 * -1)
    )

    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)

    val actual = df
      .withColumn(
        "result",
        AlipayOspSettlementLogic.getFeeAmount(
          col("feeAmount")
        )
      )
      .select("result")

    val expected = Seq("-10.0000", "-5.0500")
      .toDF("result")
      .withColumn("result", col("result"))

    actual should beEqualTo(expected)
  }
}
