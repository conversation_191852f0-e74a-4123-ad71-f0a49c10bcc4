package com.agoda.finance.functions.specific.PaymentGatewaySettlements

import com.agoda.ml.spark.DataFrameMatchers.beEqualTo
import com.agoda.ml.spark.{HiveSupport, SparkSharedLocalTest}
import org.apache.spark.sql.functions.col
import org.scalatest.{FunSuite, Matchers}
import org.apache.spark.sql.types.{LongType, StringType, StructField, StructType}
import org.apache.spark.sql.Row

class PaypalSettlementLogicTest extends FunSuite with SparkSharedLocalTest with HiveSupport with Matchers {
  import sqlContext.implicits._

  test("getSourceAmount should calculate correctly for DR and CR and edge cases") {
    val df = Seq(
      ("DR", "100.0000"), // -1.0000
      ("CR", "200.0000"), // 2.0000
      ("dr", "50.1234"),  // -0.5012
      ("cr", "0"),        // 0.0000
      ("DR", null),       // 0.0000
      ("", "100.00")      // 1.0000 (treated as CR)
    ).toDF("transactionDebitOrCredit", "grossTransactionAmount")

    val resultDf = df.withColumn(
      "result",
      PaypalSettlementLogic.getSourceAmount(col("transactionDebitOrCredit"), col("grossTransactionAmount"))
    )
    val expected = Seq("-1.0000", "2.0000", "-0.5012", "0.0000", "0.0000", "1.0000").toDF("result")
    resultDf.select("result") should beEqualTo(expected)
  }

  test("getFeeAmount should calculate correctly for DR and CR and edge cases") {
    val df = Seq(
      ("DR", "50.0000"), // -0.5000
      ("CR", "75.0000"), // 0.7500
      ("DR", null),      // 0.0000
      ("", "100.00"),    // 1.0000
      ("DR", "abc")      // 0.0000
    ).toDF("transactionDebitOrCredit", "feeAmount")

    val resultDf = df.withColumn(
      "result",
      PaypalSettlementLogic.getFeeAmount(col("transactionDebitOrCredit"), col("feeAmount"))
    )
    val expected = Seq("-0.5000", "0.7500", "0.0000", "1.0000", "0.0000").toDF("result")
    resultDf.select("result") should beEqualTo(expected)
  }

  test("getReceivedAmount should calculate correctly for DR and CR and edge cases") {
    val df = Seq(
      ("DR", "300.0000"), // -3.0000
      ("CR", "400.0000"), // 4.0000
      ("DR", null),       // 0.0000
      ("", "100.00"),     // 1.0000
      ("DR", "abc")       // 0.0000
    ).toDF("transactionDebitOrCredit", "grossTransactionAmount")

    val resultDf = df.withColumn(
      "result",
      PaypalSettlementLogic.getReceivedAmount(col("transactionDebitOrCredit"), col("grossTransactionAmount"))
    )
    val expected = Seq("-3.0000", "4.0000", "0.0000", "1.0000", "0.0000").toDF("result")
    resultDf.select("result") should beEqualTo(expected)
  }

  test("getFeeCurrency and getReceivedCurrency should prioritize feeCurrency if present, else grossTransactionCurrency and handle null/empty") {
    val schema = StructType(
      List(
        StructField("feeCurrency", StringType, nullable = true),
        StructField("grossTransactionCurrency", StringType, nullable = true)
      )
    )
    val data = Seq(
      Row("USD", "EUR"), // USD
      Row(null, "JPY"),  // JPY
      Row("", "GBP"),    // GBP
      Row(null, null),   // null
      Row("usd", "eur"), // USD
      Row("", ""),       // null
      Row("  ", "INR")   // INR
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(data), schema)

    val feeResult = df.withColumn(
      "result",
      PaypalSettlementLogic.getFeeCurrency(col("feeCurrency"), col("grossTransactionCurrency"))
    )
    val receivedResult = df.withColumn(
      "result",
      PaypalSettlementLogic.getReceivedCurrency(col("feeCurrency"), col("grossTransactionCurrency"))
    )
    val expected = Seq("USD", "JPY", "GBP", null, "USD", null, "INR").toDF("result")
    feeResult.select("result") should beEqualTo(expected)
    receivedResult.select("result") should beEqualTo(expected)
  }

  test("getExchangeRate should return 0.00000000 if grossTransactionAmount is 0 or null or invalid, else 1.00000000") {
    val schema = StructType(List(StructField("grossTransactionAmount", StringType, nullable = true)))
    val data   = Seq(Row("0"), Row("100.00"), Row(null), Row("abc"))
    val df     = spark.createDataFrame(spark.sparkContext.parallelize(data), schema)

    val resultDf = df.withColumn(
      "result",
      PaypalSettlementLogic.getExchangeRate(col("grossTransactionAmount"))
    )
    val expected = Seq("0.00000000", "1.00000000", "0.00000000", "0.00000000").toDF("result")
    resultDf.select("result") should beEqualTo(expected)
  }

  test("getEventTypeId should return mapped eventTypeId, fallback, or null for fileTypeId 7") {
    val resultSchema = StructType(List(StructField("transactionDebitOrCredit", StringType), StructField("transactionEventCode", StringType)))
    val inputValue = Seq(
      Row("CR", "T0003"),   // mapped: 2
      Row("CR", "T0006"),   // mapped: 2
      Row("DR", "T1107"),   // mapped: 4
      Row("CR", "UNKNOWN"), // fallback: 2 (since CR)
      Row("DR", "UNKNOWN"), // fallback: 4 (since DR)
      Row("CR", "T0016")    // not in map, fallback: 2
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)

    val resultDf = df.withColumn(
      "result",
      PaypalSettlementLogic.getEventTypeId("7")(col("transactionDebitOrCredit"), col("transactionEventCode"))
    )
    val expected = Seq(
      "2",
      "2",
      "4",
      null,
      null,
      null
    ).toDF("result")
    resultDf.select("result") should beEqualTo(expected)
  }

  test("getPreBookingId UDF in PaypalSettlementLogic should return correct prebooking id for fileTypeId 7") {
    val schema = StructType(
      List(
        StructField("invoice_id", StringType),
        StructField("transactionType", StringType)
      )
    )
    val data = Seq(
      Row("1234-1-5678", "T0006"), // valid prebooking, valid rule
      Row("1234-1-5678", "T0016"), // valid prebooking, invalid rule
      Row("1234-3-5678", "T0006"), // not prebooking pattern, valid rule
      Row("", "T0006"),            // empty invoice_id, valid rule
      Row("1234-1-5678", "")       // valid prebooking, empty rule
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(data), schema)

    val resultDf = df.withColumn(
      "result",
      PaypalSettlementLogic.getPreBookingId("7")(col("invoice_id"), col("transactionType"))
    )
    val expectedSchema = StructType(List(StructField("result", LongType, nullable = true)))
    val expected = spark.createDataFrame(
      spark.sparkContext.parallelize(
        Seq(
          Row(java.lang.Long.valueOf(1234)), // valid
          Row(0L),                           // invalid rule
          Row(0L),                           // not prebooking pattern
          Row(0L),                           // empty invoice_id
          Row(0L)                            // empty rule
        )
      ),
      expectedSchema
    )
    resultDf.select("result").collect() shouldBe expected.collect()
  }

  test("getItineraryId UDF in PaypalSettlementLogic should return correct itinerary id for fileTypeId 7") {
    val schema = StructType(
      List(
        StructField("invoice_id", StringType),
        StructField("transactionType", StringType)
      )
    )
    val data = Seq(
      Row("1234-3-5678", "T0006"), // valid itinerary, valid rule
      Row("1234-3-5678", "T0016"), // valid itinerary, invalid rule
      Row("1234-1-5678", "T0006"), // not itinerary pattern, valid rule
      Row("", "T0006"),            // empty invoice_id, valid rule
      Row("1234-3-5678", "")       // valid itinerary, empty rule
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(data), schema)

    val resultDf = df.withColumn(
      "result",
      PaypalSettlementLogic.getItineraryId("7")(col("invoice_id"), col("transactionType"))
    )
    val expectedSchema = StructType(List(StructField("result", LongType, nullable = true)))
    val expected = spark.createDataFrame(
      spark.sparkContext.parallelize(
        Seq(
          Row(java.lang.Long.valueOf(1234)), // valid
          Row(0L),                           // invalid rule
          Row(0L),                           // not itinerary pattern
          Row(0L),                           // empty invoice_id
          Row(0L)                            // empty rule
        )
      ),
      expectedSchema
    )
    resultDf.select("result").collect() shouldBe expected.collect()
  }

}
