package com.agoda.finance.functions.specific

import com.agoda.finance.functions.specific.ManualAffiliateCommissionLogic
import com.agoda.ml.spark.DataFrameMatchers.beEqualTo
import com.agoda.ml.spark.SparkSharedLocalTest
import org.apache.spark.sql.Row
import org.apache.spark.sql.functions.{col, collect_set, count, lit, regexp_replace, substring, sum}
import org.apache.spark.sql.types._
import org.scalatest.FunSuite
import org.scalatest.Matchers.{contain, convertToAnyShouldWrapper}

class ManualAffiliateCommissionLogicTest extends FunSuite with SparkSharedLocalTest {
  import sqlContext.implicits._

  // Sample schema for testing
  val agodaTransactionSchema: StructType = StructType(
    Seq(
      StructField("booking_id", StringType, nullable = true),
      StructField("datamonth", StringType, nullable = true),
      StructField("transaction_type", StringType, nullable = true),
      StructField("common_affiliate_name", StringType, nullable = true),
      StructField("partner_currency", StringType, nullable = true),
      StructField("commission_amount", DecimalType(10, 2), nullable = true),
      StructField("commission_amount_partner_currency", DecimalType(10, 2), nullable = true),
      StructField("affiliate_id", StringType, nullable = true)
    )
  )

  val affiliateInvalidTransactionSchema: StructType = StructType(
    Seq(
      StructField("booking_id", StringType, nullable = true),
      StructField("datamonth", StringType, nullable = true),
      StructField("transaction_type", StringType, nullable = true),
      StructField("common_affiliate_name", StringType, nullable = true)
    )
  )

  val partnerReportSchema: StructType = StructType(
    Seq(
      StructField("adv_comm_fc", DecimalType(10, 2), nullable = true),
      StructField("commission_amount", DecimalType(10, 2), nullable = true),
      StructField("total_commission", DecimalType(10, 2), nullable = true)
    )
  )

  test("enrichIntegratorPartnerData should append correct columns") {
    // booking_id, affiliate, site, booking_status, booking_date
    val inputData = Seq[(Integer, Integer, Integer, Integer, String)](
      (1, 218861, 555, 1, "2024-01-01"),     // 0.5
      (2, 136384, 555, 5, "2024-12-01"),     // 0
      (3, 218800, 1838037, 2, "2024-12-15"), // 0
      (4, 111, 1816554, 1, "2024-12-15"),    // 0.5
      (5, 219813, 764, 1, "2024-12-01"),     // 1
      (6, 184015, 764, 1, "2024-12-01"),     // 1
      (7, 300174, 4235, 5, "2024-12-01"),    // 0
      (8, 7356, 7356, 5, "2024-12-01"),
      (15, 279766, 15, 5, "2025-07-09")
    ).toDF("booking_id", "attribution_affiliate_id", "attribution_site_id", "booking_status_id", "booking_date")

    val expectedPlatformFee = Seq[(Integer, Double)](
      (1, 0.5),
      (2, 0),
      (3, 0),
      (4, 0.5),
      (5, 1),
      (6, 1),
      (7, 0),
      (8, 0),
      (15, 0)
    ).toDF("booking_id", "platform_fee_amount")

    val expectedCommonAffName = Seq[(Integer, String)](
      (1, "gimmonix"),
      (2, "juniper"),
      (3, "juniper"),
      (4, "gimmonix"),
      (5, "gimmonix"),
      (6, "gimmonix"),
      (7, "dcs+"),
      (8, null),
      (15, "peakwork")
    ).toDF("booking_id", "common_affiliate_name")

    val expectedPartnerName = Seq[(Integer, String)](
      (1, "Tajawal Offline"),
      (2, "Spider Holiday"),
      (3, "AIMIA ME"),
      (4, "Nustay Sell Rate"),
      (5, "HIS (Skyhub)"),
      (6, "Cozmo"),
      (7, "DCS+"),
      (8, "no name"),
      (15, "DER")
    ).toDF("booking_id", "partner_name")

    val expectedResult = inputData
      .join(expectedPlatformFee, Seq("booking_id"), "inner")
      .join(expectedCommonAffName, Seq("booking_id"), "inner")
      .join(expectedPartnerName, Seq("booking_id"), "inner")

    val result = ManualAffiliateCommissionLogic.enrichIntegratorPartnerData(inputData)

    result should beEqualTo(expectedResult)
  }

  test("summaryAccrualCommissionWithPartnerReport should handle different common_affiliate_name values") {
    val agodaTransactionDF = Seq(
      ("1", "202301", "SALE", "Affiliate1", "USD", BigDecimal(100.00), BigDecimal(90.00), "A1")
    ).toDF(
      "booking_id",
      "datamonth",
      "transaction_type",
      "common_affiliate_name",
      "partner_currency",
      "commission_amount",
      "commission_amount_partner_currency",
      "affiliate_id"
    )

    val affiliateInvalidTransactionDF = Seq(
      ("2", "202301", "SALE", "Affiliate1")
    ).toDF("booking_id", "datamonth", "transaction_type", "common_affiliate_name")

    val partnerReportDF = Seq(
      (1, "Affiliate1", 1, BigDecimal(100.50), BigDecimal(200.75), BigDecimal(300.25))
    ).toDF("booking_id", "common_affiliate_name", "order_id", "adv_comm_fc", "commission_amount", "total_commission")

    val resultCJ = ManualAffiliateCommissionLogic.summaryAccrualCommissionWithPartnerReport(
      agodaTransactionDF,
      affiliateInvalidTransactionDF,
      partnerReportDF,
      "cj"
    )

    val resultPAH = ManualAffiliateCommissionLogic.summaryAccrualCommissionWithPartnerReport(
      agodaTransactionDF,
      affiliateInvalidTransactionDF,
      partnerReportDF,
      "pah"
    )

    val resultRakuten = ManualAffiliateCommissionLogic.summaryAccrualCommissionWithPartnerReport(
      agodaTransactionDF,
      affiliateInvalidTransactionDF,
      partnerReportDF,
      "rakuten"
    )

    // Validate results for each case
    resultCJ.columns should contain allOf ("datamonth", "common_affiliate_name", "transaction_type", "partner_commission_partner_currency")
    resultPAH.columns should contain allOf ("datamonth", "common_affiliate_name", "transaction_type", "partner_commission_partner_currency")
    resultRakuten.columns should contain allOf ("datamonth", "common_affiliate_name", "transaction_type", "partner_commission_partner_currency")
  }

  test("summaryAccrualCommissionWithPartnerReport should throw exception for invalid common_affiliate_name") {
    val agodaTransactionDF = Seq(
      ("1", "202301", "SALE", "Affiliate1", "USD", BigDecimal(100.00), BigDecimal(90.00), "A1")
    ).toDF(
      "booking_id",
      "datamonth",
      "transaction_type",
      "common_affiliate_name",
      "partner_currency",
      "commission_amount",
      "commission_amount_partner_currency",
      "affiliate_id"
    )

    val affiliateInvalidTransactionDF = Seq(
      ("2", "202301", "SALE", "Affiliate1")
    ).toDF("booking_id", "datamonth", "transaction_type", "common_affiliate_name")

    val partnerReportDF = Seq(
      (BigDecimal(100.50), BigDecimal(200.75), BigDecimal(300.25))
    ).toDF("adv_comm_fc", "commission_amount", "total_commission")

    val invalidAffiliateName = "invalid_affiliate"

    val exception = intercept[IllegalArgumentException] {
      ManualAffiliateCommissionLogic.summaryAccrualCommissionWithPartnerReport(
        agodaTransactionDF,
        affiliateInvalidTransactionDF,
        partnerReportDF,
        invalidAffiliateName
      )
    }

    exception.getMessage shouldEqual s"Invalid common_affiliate_name value $invalidAffiliateName"
  }

  test("mappingPAHAffiliateName should return correct affiliate name for given affiliate_id") {
    // Create a DataFrame with test data
    val testData = Seq(
      (243996, "tiket_pah"),
      (267603, "rakuten_pah"),
      (282665, "rakuten_pah"),
      (299836, "ixigo_pah"),
      (285434, "dida_pah"),
      (123456, "") // Non-existent affiliate_id
    ).toDF("affiliate_id", "expected_affiliate_name")

    // Apply the UDF to the test data
    val resultDF = testData.withColumn("actual_affiliate_name", ManualAffiliateCommissionLogic.mappingPAHAffiliateName($"affiliate_id"))

    // Collect the results
    val results = resultDF.select("affiliate_id", "expected_affiliate_name", "actual_affiliate_name").collect()

    val expectedData = Map(
      243996 -> "tiket pah",
      267603 -> "rakuten pah",
      282665 -> "rakuten pah",
      299836 -> "ixigo pah",
      285434 -> "dida pah",
      123456 -> ""
    )

    // Assert the results
    results.foreach { row =>
      val affiliateId = row.getInt(0)
      val expected    = expectedData(affiliateId)
      val actual      = row.getString(2)
      assert(expected == actual, s"For affiliate_id $affiliateId, expected $expected but got $actual")
    }
  }

  test("mappingSwitchflyAffiliateName should return correct affiliate name for given affiliate_id") {
    // Create a DataFrame with test data covering all Switchfly partner mappings
    val testData = Seq(
      (218445, "AADV"),
      (210387, "AAV"),
      (196000, "Flight Centre"),
      (224829, "Snap Travel"),
      (265736, "Snap Travel CCC"),
      (252975, "JAL PAK"),
      (253029, "JAL PAK"),
      (235477, "Mastercard AU"),
      (234903, "Mastercard HK"),
      (260581, "Jetblue"),
      (999999, "no name") // Non-existent affiliate_id should return "no name"
    ).toDF("affiliate_id", "expected_affiliate_name")

    // Apply the UDF to the test data
    val resultDF = testData.withColumn("actual_affiliate_name", ManualAffiliateCommissionLogic.mappingSwitchflyAffiliateName($"affiliate_id"))

    // Collect the results
    val results = resultDF.select("affiliate_id", "expected_affiliate_name", "actual_affiliate_name").collect()

    val expectedData = Map(
      218445 -> "AADV",
      210387 -> "AAV",
      196000 -> "Flight Centre",
      224829 -> "Snap Travel",
      265736 -> "Snap Travel CCC",
      252975 -> "JAL PAK",
      253029 -> "JAL PAK",
      235477 -> "Mastercard AU",
      234903 -> "Mastercard HK",
      260581 -> "Jetblue",
      999999 -> "no name"
    )

    // Assert the results
    results.foreach { row =>
      val affiliateId = row.getInt(0)
      val expected    = expectedData(affiliateId)
      val actual      = row.getString(2)
      assert(expected == actual, s"For affiliate_id $affiliateId, expected $expected but got $actual")
    }
  }

  test("anaMarginCalculation should calculate margin correctly") {
    val constant = BigDecimal(0.026)

    val anaMarginCalculationUDF = ManualAffiliateCommissionLogic.anaMarginCalculation(constant)

    val testData = Seq(
      (BigDecimal(100.00), BigDecimal(50.00), BigDecimal(70.00)), // Normal case - original payment amount existed
      (BigDecimal(100.00), BigDecimal(50.00), null),              // Normal case
      (BigDecimal(200.00), BigDecimal(100.00), null),             // Another normal case
      (null, BigDecimal(50.00), null),                            // Null margin
      (BigDecimal(100.00), null, null),                           // Null originalSellingAmount
      (null, null, null)                                          // Both values null
    ).toDF("margin", "originalSellingAmount", "originalPaymentAmount")

    val resultDF = testData.withColumn("result", anaMarginCalculationUDF(col("margin"), col("originalSellingAmount"), col("originalPaymentAmount")))

    val expectedData = Seq(
      (BigDecimal(100.00), BigDecimal(50.00), BigDecimal(70.00), BigDecimal(98.18)), // 100 - (70 * 0.026)
      (BigDecimal(100.00), BigDecimal(50.00), null, BigDecimal(98.70)),              // 100 - (50 * 0.026)
      (BigDecimal(200.00), BigDecimal(100.00), null, BigDecimal(197.40)),            // 200 - (100 * 0.026)
      (null, BigDecimal(50.00), null, BigDecimal(-1.30)),                            // 0 - (50 * 0.026)
      (BigDecimal(100.00), null, null, BigDecimal(100.00)),                          // 100 - (0 * 0.026)
      (null, null, null, BigDecimal(0.00))                                           // 0 - (0 * 0.026)
    ).toDF("margin", "originalSellingAmount", "originalPaymentAmount", "result")

    assert(resultDF.collect().toSet == expectedData.collect().toSet)
  }

  test("anaVehicleBookingStatement should return correct booking state") {
    // Register the UDF
    val anaVehicleBookingStatementUDF = ManualAffiliateCommissionLogic.anaVehicleBooking

    // Create a test DataFrame
    val testData = Seq(
      (true, false, "Active"),   // is_cancelled = true
      (false, true, "Active"),   // is_departed = true
      (false, false, "Pending"), // Neither cancelled nor departed
      (false, false, null)       // Null vehicle_booking_state
    ).toDF("is_cancelled", "is_departed", "vehicle_booking_state")

    // Apply the UDF
    val resultDF = testData.withColumn(
      "result",
      anaVehicleBookingStatementUDF(
        col("is_cancelled"),
        col("is_departed"),
        col("vehicle_booking_state")
      )
    )

    // Define the expected results
    val expectedData = Seq(
      Row(true, false, "Active", "Booking Cancelled"),
      Row(false, true, "Active", "Booking Departed"),
      Row(false, false, "Pending", "Pending"),
      Row(false, false, null, null)
    )

    val expectedSchema = StructType(
      Seq(
        StructField("is_cancelled", BooleanType, nullable = false),
        StructField("is_departed", BooleanType, nullable = false),
        StructField("vehicle_booking_state", StringType, nullable = true),
        StructField("result", StringType, nullable = true)
      )
    )

    val expectedDF = spark.createDataFrame(
      spark.sparkContext.parallelize(expectedData),
      expectedSchema
    )

    // Assert the results
    assert(resultDF.collect().toSet == expectedDF.collect().toSet)
  }

  test("getAgodaValidTransactions should return valid transactions") {
    val agodaTransactionDF = Seq(
      ("1", "202301", "SALE", "Affiliate1"),
      ("2", "202301", "SALE", "Affiliate2"),
      ("3", "202301", "SALE", "Affiliate3")
    ).toDF("booking_id", "datamonth", "transaction_type", "common_affiliate_name")

    val affiliateInvalidTransactionDF = Seq(
      ("2", "202301", "SALE", "Affiliate2")
    ).toDF("booking_id", "datamonth", "transaction_type", "common_affiliate_name")

    val result = ManualAffiliateCommissionLogic.getAgodaValidTransactions(agodaTransactionDF, affiliateInvalidTransactionDF)

    val expectedData = Seq(
      ("1", "202301", "SALE", "Affiliate1"),
      ("3", "202301", "SALE", "Affiliate3")
    )

    val expectedDF = spark.createDataFrame(
      spark.sparkContext.parallelize(expectedData.map(Row.fromTuple)),
      agodaTransactionDF.schema
    )

    assert(result.collect() === expectedDF.collect())
  }

  test("getFullOuterJoinTransactions should return all transactions with reconciled and mismatched data") {
    val agodaTransactionDF = Seq(
      ("1", "202301", "SALE", "Affiliate1"),
      ("2", "202301", "SALE", "Affiliate2"),
      ("3", "202301", "SALE", "Affiliate3")
    ).toDF("booking_id", "datamonth", "transaction_type", "common_affiliate_name")

    val partnerReportDF = Seq(
      ("1", "Affiliate1", BigDecimal(100.00).setScale(2)),
      ("3", "Affiliate1", BigDecimal(300.00).setScale(2)),
      ("4", "Affiliate1", BigDecimal(400.00).setScale(2))
    ).toDF("order_id", "common_affiliate_name", "commission_amount")

    val result = ManualAffiliateCommissionLogic.getFullOuterJoinAgodaAndPartnerTransactions(
      agodaTransactionDF,
      partnerReportDF,
      "order_id"
    )

    // Cast the `commission_amount` column in the result to DecimalType(10, 2) to match the expected precision and scale
    val resultCasted = result.withColumn("commission_amount", col("commission_amount").cast(DecimalType(10, 2)))

    val expectedData = Seq(
      // Reconciled transactions
      ("1", "202301", "SALE", "Affiliate1", "1", BigDecimal(100.00).setScale(2), true, true, "reconciled"),
      ("3", "202301", "SALE", "Affiliate3", "3", BigDecimal(300.00).setScale(2), true, true, "reconciled"),
      // Mismatched Agoda transactions
      ("2", "202301", "SALE", "Affiliate2", null, null, true, false, "mismatched_agoda"),
      // Mismatched Partner transactions
      (null, null, null, null, "4", BigDecimal(400.00).setScale(2), false, true, "mismatched_partner")
    )

    val expectedSchema = StructType(
      Seq(
        StructField("booking_id", StringType, nullable = true),
        StructField("datamonth", StringType, nullable = true),
        StructField("transaction_type", StringType, nullable = true),
        StructField("common_affiliate_name", StringType, nullable = true),
        StructField("order_id", StringType, nullable = true),
        StructField("commission_amount", DecimalType(10, 2), nullable = true),
        StructField("existed_in_agoda", BooleanType, nullable = true),
        StructField("existed_in_partner", BooleanType, nullable = true),
        StructField("classification", StringType, nullable = true)
      )
    )

    val expectedDF = spark.createDataFrame(
      spark.sparkContext.parallelize(expectedData.map(Row.fromTuple)),
      expectedSchema
    )

    assert(resultCasted.collect().toSet() === expectedDF.collect().toSet())
  }

  test("processAccrualCommission should process transactions correctly with multiple common_affiliate_name values") {
    val agodaTransactionDF = Seq(
      ("1", "202301", "SALE", "Affiliate1", "USD", BigDecimal(100.00), BigDecimal(90.00), "A1"),
      ("2", "202301", "SALE", "Affiliate2", "USD", BigDecimal(200.00), BigDecimal(180.00), "A2"),
      ("3", "202301", "SALE", "Affiliate1", "USD", BigDecimal(150.00), BigDecimal(135.00), "A3")
    ).toDF(
      "booking_id",
      "datamonth",
      "transaction_type",
      "common_affiliate_name",
      "partner_currency",
      "commission_amount",
      "commission_amount_partner_currency",
      "affiliate_id"
    )

    val affiliateInvalidTransactionDF = Seq(
      ("4", "202301", "SALE", "Affiliate1")
    ).toDF("booking_id", "datamonth", "transaction_type", "common_affiliate_name")

    val partnerReportDF = Seq(
      ("1", "Affiliate1", BigDecimal(120.00)),
      ("3", "Affiliate1", BigDecimal(150.00)),
      ("5", "Affiliate1", BigDecimal(300.00))
    ).toDF("order_id", "common_affiliate_name", "commission_amount")

    val result = ManualAffiliateCommissionLogic.processAccrualCommission(
      agodaTransactionDF,
      affiliateInvalidTransactionDF,
      partnerReportDF,
      Seq("datamonth", "common_affiliate_name", "transaction_type", "partner_currency"),
      "order_id",
      "commission_amount"
    )

    // Define the expected DataFrame
    val expectedData = Seq(
      Row(
        "202301",
        "Affiliate1",
        "SALE",
        "USD",
        2L,
        Seq("A3", "A1"),
        BigDecimal(250.00),
        BigDecimal(225.00),
        3L,
        BigDecimal(570.00),
        2L,
        BigDecimal(250.00),
        null,
        BigDecimal(270.00),
        null,
        1L
      ),
      Row(
        "202301",
        "Affiliate2",
        "SALE",
        "USD",
        1L,
        Seq("A2"),
        BigDecimal(200.00),
        BigDecimal(180.00),
        null,
        null,
        null,
        null,
        null,
        null,
        1L,
        null
      )
    )

    val expectedSchema = StructType(
      Seq(
        StructField("datamonth", StringType, nullable = true),
        StructField("common_affiliate_name", StringType, nullable = true),
        StructField("transaction_type", StringType, nullable = true),
        StructField("partner_currency", StringType, nullable = true),
        StructField("number_of_agoda_bookings", LongType, nullable = true),
        StructField("affiliate_ids", ArrayType(StringType), nullable = true),
        StructField("total_agoda_calculated_commission_usd", DecimalType(10, 2), nullable = true),
        StructField("total_agoda_calculated_commission_partner_currency", DecimalType(10, 2), nullable = true),
        StructField("number_of_partner_bookings", LongType, nullable = true),
        StructField("partner_commission_partner_currency", DecimalType(10, 2), nullable = true),
        StructField("number_of_reconciled_bookings", LongType, nullable = true),
        StructField("total_reconciled_agoda_commission_usd", DecimalType(10, 2), nullable = true),
        StructField("total_reconciled_agoda_commission_partner_currency", DecimalType(10, 2), nullable = true),
        StructField("total_reconciled_partner_commission", DecimalType(10, 2), nullable = true),
        StructField("number_of_mismatched_agoda_bookings", LongType, nullable = true),
        StructField("number_of_mismatched_partner_bookings", LongType, nullable = true)
      )
    )

    val expectedDF = spark.createDataFrame(
      spark.sparkContext.parallelize(expectedData),
      expectedSchema
    )

    // Debug: Print schemas and data
    println("Actual Schema:")
    result.printSchema()
    println("Actual Data:")
    result.show(false)

    println("Expected Schema:")
    expectedDF.printSchema()
    println("Expected Data:")
    expectedDF.show(false)

    // Select only the columns that match the expected schema
    val expectedColumns = expectedDF.schema.fieldNames
    val filteredResult  = result.select(expectedColumns.map(col): _*)

    // Cast columns in the actual DataFrame to match the expected schema
    val castedResult = expectedDF.schema.fields.foldLeft(filteredResult) { (df, field) =>
      df.withColumn(field.name, col(field.name).cast(field.dataType))
    }

    // Compare the DataFrames
    assert(castedResult.collect().toSet === expectedDF.collect().toSet)
  }

  test("safeSum should return sum of column if it exists, or null if it does not") {
    val sampleDF = Seq(
      ("1", 100.0),
      ("2", 200.0),
      ("3", 300.0)
    ).toDF("id", "value")

    // Case 1: Column exists
    val resultWithColumn = sampleDF
      .select(ManualAffiliateCommissionLogic.safeSum(sampleDF, "value", "sum_value"))
      .collect()(0)
      .getAs[Double]("sum_value")

    // Case 2: Column does not exist
    val resultWithoutColumn = sampleDF
      .select(ManualAffiliateCommissionLogic.safeSum(sampleDF, "non_existent_column", "sum_non_existent"))
      .collect()(0)
      .isNullAt(0) // Check if the result is null

    assert(resultWithColumn == 600.0) // Sum of the "value" column
    assert(resultWithoutColumn)       // Should return null for non-existent column
  }

  test("mergeShopbackActivityData should join and select correct columns") {
    // Prepare input DataFrames
    val factBookingActivity = Seq(
      (1L, 1001L, "Aff1", 2001L, "Site1", "Confirmed", false, 3001, "2024-01-01", "2024-01-01", "2024-01-02", 100.0, "CreditCard", 202401),
      (2L, 1002L, "Aff2", 2002L, "Site2", "Cancelled", true, 3002, "2024-01-03", "2024-01-03", "2024-01-04", 200.0, "Paypal", 202401)
    ).toDF(
      "booking_id",
      "lpc_30_affiliate_id",
      "lpc_30_affiliate_name",
      "lpc_30_site_id",
      "lpc_30_site_name",
      "booking_state_name",
      "is_cancelled",
      "activity_id",
      "booking_date",
      "booking_start_date",
      "booking_end_date",
      "selling_amount",
      "original_payment_model_name",
      "datamonth"
    )

    val factBookingAttributionActivity = Seq(
      (1L, "tag1", 202401),
      (2L, "tag2", 202401)
    ).toDF("booking_id", "tag", "datamonth")

    val activityGrouping = Seq(
      (3001, "GroupA"),
      (3002, null)
    ).toDF("activity_id", "cpa_group")

    val activityUpsize = Seq(
      (10.0f, 2001L, "2024-01-01", "GroupA"),
      (20.0f, 2002L, "2024-01-03", "general")
    ).toDF("upsize_percent", "site_id", "campaign_date", "cpa_group")

    // Expected output DataFrame
    val expected = Seq(
      Row(
        1L,
        1001L,
        "Aff1",
        2001L,
        "Site1",
        false,
        "tag1",
        "Confirmed",
        "2024-01-01",
        "2024-01-01",
        "2024-01-02",
        100.0,
        "CreditCard",
        10.0f,
        "GroupA"
      ),
      Row(2L, 1002L, "Aff2", 2002L, "Site2", true, "tag2", "Cancelled", "2024-01-03", "2024-01-03", "2024-01-04", 200.0, "Paypal", 20.0f, "General")
    )

    val expectedSchema = StructType(
      Seq(
        StructField("booking_id", LongType, nullable = false),
        StructField("lpc_30_affiliate_id", LongType, nullable = false),
        StructField("lpc_30_affiliate_name", StringType, nullable = false),
        StructField("lpc_30_site_id", LongType, nullable = false),
        StructField("lpc_30_site_name", StringType, nullable = false),
        StructField("is_cancelled", BooleanType, nullable = false),
        StructField("tag", StringType, nullable = false),
        StructField("booking_state_name", StringType, nullable = false),
        StructField("booking_date", StringType, nullable = false),
        StructField("booking_start_date", StringType, nullable = false),
        StructField("booking_end_date", StringType, nullable = false),
        StructField("selling_amount", DoubleType, nullable = false),
        StructField("original_payment_model_name", StringType, nullable = false),
        StructField("upsize_percent", FloatType, nullable = true),
        StructField("cpa_group", StringType, nullable = true)
      )
    )

    val expectedDF = spark.createDataFrame(
      spark.sparkContext.parallelize(expected),
      expectedSchema
    )

    val result = ManualAffiliateCommissionLogic.mergeShopbackActivityData(
      factBookingActivity,
      factBookingAttributionActivity,
      activityGrouping,
      activityUpsize
    )

    result should beEqualTo(expectedDF)
  }

  test("normalizeComstatData should join and select correct columns") {
    import sqlContext.implicits._

    val bookingDF = Seq(
      (
        "b1",
        "confirmed",
        "a1",
        101,
        "2024-12-01",
        "2024-12-10",
        "2024-12-15",
        "2024-12-20",
        "2024-12-25",
        null,
        100.0,
        90.0,
        0.1,
        "online",
        "tag1",
        "202412",
        0.0,
        2.0
      )
    ).toDF(
      "booking_id",
      "booking_status",
      "affiliate_id",
      "attributed_site_id",
      "booking_date",
      "from_date",
      "to_date",
      "cancellation_date",
      "payment_due_date",
      "finalization_date",
      "cust_invoice_amount",
      "cust_invoice_amount_sell_exclusive",
      "processing_percent",
      "origin",
      "tag",
      "master",
      "affiliate_commision",
      "discount_amount"
    )

    val fbDF = Seq(
      ("b1", "Affiliate A", 123, "Platform X", "Country Y", 202412, "Pay Agoda")
    ).toDF(
      "booking_id",
      "affiliate_name",
      "original_supplier_id",
      "platform_group_name",
      "country_name",
      "datamonth",
      "original_payment_model_name"
    )

    val siteDF = Seq(
      (101, "Site 1", "a1", "affiliateName")
    ).toDF("site_id", "site_name", "affiliate_id", "affiliate_name")

    val dmcDF = Seq(
      (123, "DMC-001")
    ).toDF("dmc_id", "dmc_code")

    val expected = Seq(
      (
        "b1",
        "confirmed",
        "a1",
        "affiliateName",
        101,
        "Site 1",
        "2024-12-01",
        "2024-12-10",
        "2024-12-15",
        "2024-12-20",
        "2024-12-25",
        0.0,
        90.0,
        0.1,
        "online",
        "Platform X",
        "tag1",
        "Country Y",
        "DMC-001",
        "Pay Agoda",
        2.0
      )
    ).toDF(
      "booking_id",
      "booking_status",
      "affiliate_id",
      "affiliate_name",
      "attributed_site_id",
      "site_name",
      "booking_date",
      "checkin_date",
      "checkout_date",
      "cancellation_date",
      "payment_due_date",
      "affiliate_commission",
      "cust_invoice_amount_sell_exclusive",
      "processing_percent",
      "origin",
      "platform_group_name",
      "tag",
      "country_name",
      "dmc_code",
      "original_payment_model_name",
      "discount_amount"
    )

    val result = ManualAffiliateCommissionLogic.normalizeComstatData(bookingDF, fbDF, siteDF, dmcDF)

    val resultSelected = result.select(expected.columns.map(col): _*)

    resultSelected should beEqualTo(expected)
  }
}
