package com.agoda.finance.functions.common

import com.agoda.ml.spark.{HiveSupport, SparkSharedLocalTest}
import org.apache.spark.sql.functions._
import org.scalatest.FunSuite
import org.scalatest.Matchers.convertToAnyShouldWrapper
import com.agoda.ml.spark.DataFrameMatchers.beEqualTo
import org.apache.spark.sql.Row
import org.apache.spark.sql.types.{IntegerType, StringType, StructField, StructType}
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat

import java.sql.Timestamp

class CommonDateFunctionSpec extends FunSuite with SparkSharedLocalTest with HiveSupport {
  import sqlContext.implicits._

  test("test change format date for bank Paypal from  mm/dd/yyyy to yyyy-mm-dd") {
    val df = Seq("12/01/2021", "05/06/2021", "07/08/2021").toDF("date")

    val actual = df
      .withColumn("result", CommonDateFunctions.convertStringToDate("mm/dd/yyyy", "yyyy-mm-dd")(col("date")))
      .select("result")

    val expected = Seq("2021-12-01", "2021-05-06", "2021-07-08").toDF("result")

    actual should beEqualTo(expected)
  }

  test("test change format date for Cashback Wise from  yyyy-MM-dd'T'HH:mm:ss.SSS'Z' to yyyy-MM-dd") {

    val df = Seq("2018-04-30T23:59:59.999Z", "2018-04-30T23:59:59.999Z", "2018-04-30T23:59:59.999Z").toDF("date")

    val actual = df
      .withColumn("result", CommonDateFunctions.convertStringToDate("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", "yyyy-MM-dd")(col("date")))
      .select("result")

    val expected = Seq("2018-04-30", "2018-04-30", "2018-04-30").toDF("result")

    actual should beEqualTo(expected)
  }

  test("test change format date for Hopper from MM/dd/yy HH:mm to yyyy-MM-dd HH:mm:ss") {
    val df = Seq("12/1/23 10:30").toDF("date").toDF("date")

    val actual = df
      .withColumn("result", CommonDateFunctions.convertStringToDate("MM/dd/yy HH:mm", "yyyy-MM-dd HH:mm:ss")(col("date")))
      .select("result")

    val expected = Seq("2023-12-01 10:30:00").toDF("result")

    actual.show(false)

    actual should beEqualTo(expected)
  }

  test("test change format date for Hopper from yyyy-MM-dd'T'HH:mm:ss.SSS to yyyy-MM-dd HH:mm:ss") {
    val df = Seq("2023-12-01T10:30:00.000").toDF("date").toDF("date")

    val actual = df
      .withColumn("result", CommonDateFunctions.convertStringToDate("yyyy-MM-dd'T'HH:mm:ss.SSS", "yyyy-MM-dd HH:mm:ss")(col("date")))
      .select("result")

    val expected = Seq("2023-12-01 10:30:00").toDF("result")

    actual.show(false)

    actual should beEqualTo(expected)
  }

  test("convertDateStringToTimestamp") {
    val df = Seq("12/01/21", "05/06/21", "07/08/21").toDF("date")

    val actual = df
      .withColumn("result", CommonDateFunctions.convertDateStringToTimestamp("dd/MM/yy")(col("date")))
      .select("result")

    val expected = Seq("2021-01-12 00:00:00", "2021-06-05 00:00:00", "2021-08-07 00:00:00")
      .toDF("result")
      .withColumn("result", to_timestamp(col("result")))

    actual should beEqualTo(expected)
  }

  test("test change format date for bank SCB from  dd/mm/yyyy to yyyy-mm-dd") {
    val df = Seq("12/01/2021").toDF("date")

    val actual = df
      .withColumn("result", CommonDateFunctions.convertStringToDate("dd/mm/yyyy", "yyyy-mm-dd")(col("date")))
      .select("result")

    val expected = Seq("2021-01-12").toDF("result")

    actual should beEqualTo(expected)
  }

  test("test get first date previous month from datadate") {

    val df = Seq("********").toDF("datadate")

    val actual = df
      .withColumn("result", CommonDateFunctions.getFirstDateOfPreviousMonth(col("datadate")))
      .select("result")

    val expected = Seq("2021-11-01").toDF("result")

    actual should beEqualTo(expected)
  }

  test("test get last date previous month from datadate") {

    val df = Seq("********", "********").toDF("datadate")

    val actual = df
      .withColumn("result", CommonDateFunctions.getLastDateOfPreviousMonth(col("datadate")))
      .select("result")

    val expected = Seq("2021-11-30", "2021-02-28").toDF("result")

    actual should beEqualTo(expected)
  }

  test("test offset timestamp by 1 hour and 30 minutes") {

    val df = Seq(Timestamp.valueOf("2000-01-15 12:00:00")).toDF("timestamp")

    val actual = df
      .withColumn("result", CommonDateFunctions.offsetTimestamp(1, 30)(col("timestamp")))
      .select("result")

    val expected = Seq(Timestamp.valueOf("2000-01-15 13:30:00")).toDF("result")

    actual should beEqualTo(expected)
  }

  test("test offset month by -1") {

    val df = Seq("202401").toDF("datamonth")

    val actual = df
      .withColumn("result", CommonDateFunctions.offsetDatamonth(col("datamonth"), -1))
      .select("result")

    val expected = Seq("202312").toDF("result")

    actual should beEqualTo(expected)
  }

  test("test offset month by +1") {

    val df = Seq("202412").toDF("datamonth")

    val actual = df
      .withColumn("result", CommonDateFunctions.offsetDatamonth(col("datamonth"), 1))
      .select("result")

    val expected = Seq("202501").toDF("result")

    actual should beEqualTo(expected)
  }

  test("isDateInMonthRange should execute correctly") {
    val datadateFormat = DateTimeFormat.forPattern("yyyyMMdd")
    val resultSchema = StructType(
      List(
        StructField("datadate", IntegerType)
      )
    )
    val inputValue = Seq(
      Row(DateTime.now().plusMonths(3).toString(datadateFormat).toInt),
      Row(DateTime.now().plusMonths(1).toString(datadateFormat).toInt),
      Row(DateTime.now().toString(datadateFormat).toInt),
      Row(DateTime.now().minusMonths(1).toString(datadateFormat).toInt),
      Row(DateTime.now().minusMonths(3).toString(datadateFormat).toInt),
      Row(DateTime.now().minusMonths(6).toString(datadateFormat).toInt)
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)

    val actual = df
      .withColumn(
        "result",
        CommonDateFunctions.isDateInMonthRange(col("datadate"), -3, 1, "yyyyMMdd") // between the last 3 months and the next month
      )
      .select("result")

    val expected = Seq(false, true, true, true, true, false).toDF("result")

    actual should beEqualTo(expected)
  }

  test("offsetDateToDatamonth should execute correctly") {
    val datadateFormat = DateTimeFormat.forPattern("yyyyMMdd")
    val resultSchema = StructType(
      List(
        StructField("datadate", StringType)
      )
    )
    val inputValue = Seq(
      Row(DateTime.now().plusMonths(3).toString(datadateFormat)),
      Row(DateTime.now().plusMonths(1).toString(datadateFormat)),
      Row(DateTime.now().toString(datadateFormat)),
      Row(DateTime.now().minusMonths(1).toString(datadateFormat)),
      Row(DateTime.now().minusMonths(3).toString(datadateFormat)),
      Row(DateTime.now().minusMonths(6).toString(datadateFormat))
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(inputValue), resultSchema)

    val expected = df.withColumn("result", date_format(add_months(to_date(col("datadate"), "yyyyMMdd"), 3), "yyyyMM").cast(IntegerType))

    val actual = df
      .withColumn(
        "result",
        CommonDateFunctions.offsetDateToDataMonth(col("datadate"), 3, "yyyyMMdd")
      )
    actual should beEqualTo(expected)
  }

  test("test get process month from processDate") {
    val df = Seq("2023-12-01", "2024-01-15", "2024-02-28").toDF("processDate")

    val actual = df
      .withColumn("result", CommonDateFunctions.getProcessMonth(col("processDate")))
      .select("result")

    val expected = Seq("202312", "202401", "202402").toDF("result")

    actual should beEqualTo(expected)
  }

  test("test combineDateTime") {
    val date =
      Seq(
        (Timestamp.valueOf("2023-12-01 00:00:00"), "10:30:00 AM"),
        (Timestamp.valueOf("2024-01-15 00:00:00"), "12:00:00 AM"),
        (Timestamp.valueOf("2024-02-28 00:00:00"), "03:30:00 PM")
      ).toDF("date", "time")

    val actual = date
      .withColumn("result", CommonDateFunctions.combineDateTime(col("date"), col("time")))
      .select("result")

    val expected = Seq("2023-12-01 10:30:00", "2024-01-15 00:00:00", "2024-02-28 15:30:00")
      .toDF("result")
      .withColumn("result", to_timestamp(col("result")))

    actual should beEqualTo(expected)
  }

  test("test add months and format date") {
    val df = Seq("2023-12-01", "2024-01-15", "2024-02-28").toDF("date")

    val actual = df
      .withColumn("result", CommonDateFunctions.addMonthsAndFormatDate(col("date"), 1, "yyyy-MM-15'T'HH:mm:ss"))
      .select("result")

    val expected = Seq("2024-01-15T00:00:00", "2024-02-15T00:00:00", "2024-03-15T00:00:00").toDF("result")

    actual should beEqualTo(expected)
  }

  test("test offsetTimestampConvertToDate, convert with minute") {
    val df = Seq("2023-12-01 23:30:00").toDF("timestamp")

    val actual = df
      .withColumn("result", CommonDateFunctions.offsetTimestampConvertToDate(0, 30, "yyyy-MM-dd")(col("timestamp")))
      .select("result")

    val expected = Seq("2023-12-02").toDF("result")

    actual should beEqualTo(expected)
  }

  test("test offsetTimestampConvertToDate, convert with hour") {
    val df = Seq("2024-12-15T23:29:35.153000").toDF("timestamp")

    val actual = df
      .withColumn("result", CommonDateFunctions.offsetTimestampConvertToDate(1, 0, "yyyy-MM-dd")(col("timestamp")))
      .select("result")

    val expected = Seq("2024-12-16").toDF("result")

    actual should beEqualTo(expected)
  }

  test("test combineDateTimeWithOffset with 12-hour format and positive offset") {
    val df = Seq(
      (Timestamp.valueOf("2023-12-01 00:00:00"), "10:30:00 AM"),
      (Timestamp.valueOf("2024-01-15 00:00:00"), "12:00:00 AM"),
      (Timestamp.valueOf("2024-02-28 00:00:00"), "03:30:00 PM")
    ).toDF("date", "time")

    val actual = df
      .withColumn("result", CommonDateFunctions.combineDateTimeWithOffset(2, 30, "h:mm:ss a")(col("date"), col("time")))
      .select("result")

    val expected = Seq(
      Timestamp.valueOf("2023-12-01 13:00:00"), // 10:30 AM + 2h30m = 13:00
      Timestamp.valueOf("2024-01-15 02:30:00"), // 12:00 AM + 2h30m = 02:30
      Timestamp.valueOf("2024-02-28 18:00:00")  // 03:30 PM + 2h30m = 18:00
    ).toDF("result")

    actual should beEqualTo(expected)
  }

  test("test combineDateTimeWithOffset with 24-hour format and negative offset") {
    val df = Seq(
      (Timestamp.valueOf("2023-12-01 00:00:00"), "15:30:00"),
      (Timestamp.valueOf("2024-01-15 00:00:00"), "02:45:00"),
      (Timestamp.valueOf("2024-02-28 00:00:00"), "23:15:00")
    ).toDF("date", "time")

    val actual = df
      .withColumn("result", CommonDateFunctions.combineDateTimeWithOffset(-3, -15, "HH:mm:ss")(col("date"), col("time")))
      .select("result")

    val expected = Seq(
      Timestamp.valueOf("2023-12-01 12:15:00"), // 15:30:00 - 3h15m = 12:15:00
      Timestamp.valueOf("2024-01-14 23:30:00"), // 02:45:00 - 3h15m = 23:30:00 (previous day)
      Timestamp.valueOf("2024-02-28 20:00:00")  // 23:15:00 - 3h15m = 20:00:00
    ).toDF("result")

    actual should beEqualTo(expected)
  }

  test("test combineDateTimeWithOffset with 24-hour format with milliseconds") {
    val df = Seq(
      (Timestamp.valueOf("2023-12-01 00:00:00"), "15:57:40.923"),
      (Timestamp.valueOf("2024-01-15 00:00:00"), "08:30:15.456")
    ).toDF("date", "time")

    val actual = df
      .withColumn("result", CommonDateFunctions.combineDateTimeWithOffset(1, 30, "HH:mm:ss.SSS")(col("date"), col("time")))
      .select("result")

    val expected = Seq(
      Timestamp.valueOf("2023-12-01 17:27:40"), // 15:57:40 + 1h30m = 17:27:40 (milliseconds dropped)
      Timestamp.valueOf("2024-01-15 10:00:15")  // 08:30:15 + 1h30m = 10:00:15 (milliseconds dropped)
    ).toDF("result")

    actual should beEqualTo(expected)
  }

  test("test combineDateTimeWithOffset with zero offset") {
    val df = Seq(
      (Timestamp.valueOf("2023-12-01 00:00:00"), "10:30:00 AM"),
      (Timestamp.valueOf("2024-01-15 00:00:00"), "03:30:00 PM")
    ).toDF("date", "time")

    val actual = df
      .withColumn("result", CommonDateFunctions.combineDateTimeWithOffset(0, 0, "h:mm:ss a")(col("date"), col("time")))
      .select("result")

    val expected = Seq(
      Timestamp.valueOf("2023-12-01 10:30:00"), // 10:30 AM + 0h0m = 10:30:00
      Timestamp.valueOf("2024-01-15 15:30:00")  // 03:30 PM + 0h0m = 15:30:00
    ).toDF("result")

    actual should beEqualTo(expected)
  }

  test("test combineDateTimeWithOffset with different time formats") {
    val df = Seq(
      (Timestamp.valueOf("2023-12-01 00:00:00"), "15:30", "HH:mm"),
      (Timestamp.valueOf("2024-01-15 00:00:00"), "3:30 PM", "h:mm a"),
      (Timestamp.valueOf("2024-02-28 00:00:00"), "23:15:45", "HH:mm:ss")
    ).toDF("date", "time", "format")

    // Test each format separately since we need different format strings
    val actual1 = df
      .filter(col("format") === "HH:mm")
      .withColumn("result", CommonDateFunctions.combineDateTimeWithOffset(2, 0, "HH:mm")(col("date"), col("time")))
      .select("result")

    val actual2 = df
      .filter(col("format") === "h:mm a")
      .withColumn("result", CommonDateFunctions.combineDateTimeWithOffset(2, 0, "h:mm a")(col("date"), col("time")))
      .select("result")

    val actual3 = df
      .filter(col("format") === "HH:mm:ss")
      .withColumn("result", CommonDateFunctions.combineDateTimeWithOffset(2, 0, "HH:mm:ss")(col("date"), col("time")))
      .select("result")

    val expected1 = Seq(Timestamp.valueOf("2023-12-01 17:30:00")).toDF("result") // 15:30 + 2h = 17:30
    val expected2 = Seq(Timestamp.valueOf("2024-01-15 17:30:00")).toDF("result") // 3:30 PM + 2h = 17:30
    val expected3 = Seq(Timestamp.valueOf("2024-02-29 01:15:45")).toDF("result") // 23:15:45 + 2h = 01:15:45 (next day)

    actual1 should beEqualTo(expected1)
    actual2 should beEqualTo(expected2)
    actual3 should beEqualTo(expected3)
  }

  test("test combineDateTimeWithOffset with day boundary crossing") {
    val df = Seq(
      (Timestamp.valueOf("2023-12-31 00:00:00"), "11:30:00 PM"), // New Year's Eve
      (Timestamp.valueOf("2024-02-28 00:00:00"), "10:30:00 PM")  // Leap year boundary
    ).toDF("date", "time")

    val actual = df
      .withColumn("result", CommonDateFunctions.combineDateTimeWithOffset(2, 45, "h:mm:ss a")(col("date"), col("time")))
      .select("result")

    val expected = Seq(
      Timestamp.valueOf("2024-01-01 02:15:00"), // 11:30 PM + 2h45m = 02:15 AM next day (new year)
      Timestamp.valueOf("2024-02-29 01:15:00")  // 10:30 PM + 2h45m = 01:15 AM next day (leap day)
    ).toDF("result")

    actual should beEqualTo(expected)
  }

}
