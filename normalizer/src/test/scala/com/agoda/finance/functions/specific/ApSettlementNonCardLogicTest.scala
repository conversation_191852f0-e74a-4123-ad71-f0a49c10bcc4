package com.agoda.finance.functions.specific

import com.agoda.finance.common.utils.HadoopUtilsTrait
import com.agoda.finance.preprocess.Arguments
import com.agoda.finance.tables.Table
import com.agoda.ml.spark.DataFrameMatchers.beEqualTo
import com.agoda.ml.spark._
import org.apache.spark.sql._
import org.apache.spark.sql.functions.{col, lit}
import org.apache.spark.sql.types._
import org.mockito.ArgumentMatchers.{any, argThat, eq => argEq}
import org.mockito.Mockito.{atLeastOnce, reset, times, verify}
import org.scalatest.Matchers.convertToAnyShouldWrapper
import org.scalatest.mockito.MockitoSugar.mock
import org.scalatest.{BeforeAndAfter, FunSuite}

import java.sql.Timestamp
import java.text.SimpleDateFormat
import scala.util.{Failure, Success, Try}

class ApSettlementNonCardLogicTest extends FunSuite with SparkSharedLocalTest with BeforeAndAfter with HiveSupport {
  implicit class dfUtils(df: DataFrame) {
    def onlyFieldsFrom(schema: StructType): DataFrame = df.select(schema.fields.map(f => col(f.name).cast(f.dataType)): _*)
  }

  val nullS: String            = null.asInstanceOf[String]
  val D: String                = "DEDUCT"
  val processDate              = 20240118
  implicit val args: Arguments = Arguments(processDate, processDate, "", "", false, "", "", "")

  final val uuid1 = "1c0d820a-1521-4955-ab95-6d0d5d9fad18"
  final val uuid2 = "2b879da0-1b0c-4aa8-8300-7dd66754d353"
  final val uuid3 = "3c8882cf-42ce-4aa6-aa45-4b3f4a1d8e3e"
  final val uuid4 = "47d7e368-67eb-4138-a034-f00e0193c496"
  final val uuid5 = "5df02e71-9775-48d2-9760-231bcd941e33"
  final val uuid6 = "6f1af334-63f8-4ea8-94f5-c4958d473740"
  final val uuid7 = "7f711254-9d0a-4ce0-8d95-9b86161c580d"
  final val uuid8 = "80181354-21da-4447-9476-a10e92f70f95"

  val resultSchema: StructType = StructType(
    List(
      StructField("uuid", StringType),
      StructField("settlement_amount", DoubleType),
      StructField("posting_ex", DoubleType)
    )
  )

  val mockHadoopUtil: HadoopUtilsTrait = mock[HadoopUtilsTrait]

  val processAgpContractPaymentTable: Table = Table(
    "test_schema",
    "processed_agp_contract_payment",
    Some(
      StructType(
        StructField("contract_id", IntegerType) ::
          StructField("payment_amount", DecimalType(24, 2)) ::
          StructField("payment_date", TimestampType) ::
          StructField("contract_payment_id", IntegerType) ::
          StructField("unpaid_payment_amount", DecimalType(24, 2)) ::
          StructField("datadate", IntegerType) ::
          Nil
      )
    )
  )

  val preprocessedAgpSettlement: Table = Table(
    "test_schema",
    "preprocessed_agp_settlement"
  )

  def verifyWriteHadoop(expectedDf: DataFrame): Unit = {
    verify(mockHadoopUtil, atLeastOnce())
      .writeToExternalTable(
        any(),
        argEq(preprocessedAgpSettlement.fullTableName),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any()
      )(any(), any())

    verify(mockHadoopUtil, atLeastOnce())
      .writeToExternalTable(
        argThat { (df: DataFrame) =>
          Try(
            if (df.columns.forall(c => expectedDf.columns.contains(c))) df.onlyFieldsFrom(expectedDf.schema) should beEqualTo(expectedDf) else false
          ) match {
            case Success(false) => false
            case Success(_)     => true
            case Failure(exception) =>
              System.err.printf("Actual\n")
              System.err.println(df.onlyFieldsFrom(expectedDf.schema).show(100, truncate = false))

              System.err.printf("Expected")
              System.err.println(expectedDf.onlyFieldsFrom(expectedDf.schema).show(100, truncate = false))

              throw exception
          }
        },
        argEq(processAgpContractPaymentTable.fullTableName),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any()
      )(any(), any())

    verify(mockHadoopUtil, times(2))
      .writeToExternalTable(
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any()
      )(any(), any())
  }

  private def verifyResult(settlementRaw: DataFrame, exchangeRates: DataFrame, contractPayment: DataFrame, processedContractPayment: DataFrame)(
      expectedProcessedContractPayment: DataFrame,
      expectedSettlement: DataFrame
  ) = {
    val actual =
      ApSettlementNonCardLogic
        .calculateAgpExchangeRate(settlementRaw, exchangeRates, contractPayment, processedContractPayment)(
          args,
          withPreprocessed = true,
          processAgpContractPaymentTable,
          preprocessedAgpSettlement,
          mockHadoopUtil
        )
        .onlyFieldsFrom(resultSchema)

    verifyWriteHadoop(expectedProcessedContractPayment)

    actual should beEqualTo(expectedSettlement)
  }

  before {
    reset(mockHadoopUtil)
  }

  test("process agp correctly") {
    // transaction_date, is_cancelled, is_adjustment, booking_id, source_transaction_code,
    // advance_payment_contract_id, settlement_amount, settlement_currency, uuid, datadate
    val settlementRawData = settlementRaw(
      Row("2023-09-23", false, false, 1L, 1, 1, "500.00", "THB", uuid1, processDate),
      Row("2023-09-24", false, false, 1L, 2, 1, "1000.00", "THB", uuid2, processDate),
      Row("2023-09-25", false, false, 1L, 3, 1, "1500.00", "THB", uuid3, processDate)
    ).withColumn("posting_ex", lit(null))

    // contract_id, payment_date, payment_amount, contract_payment_id, datadate
    val firedrillContractPaymentData = firedrillContractPayment(
      Row(1, ts("2023-08-10"), 1300.00, 1, processDate),
      Row(1, ts("2023-08-12"), 2000.00, 2, processDate),
      Row(2, ts("2023-08-12"), 2000.00, 3, processDate),
      Row(3, ts("2023-08-12"), 3000.00, 4, processDate),
      Row(3, ts("2023-08-12"), 4000.00, 5, processDate)
    )

    // contract_id, payment_amount, payment_date, contract_payment_id, unpaid_payment_amount, datadate
    val processedAgpContractPaymentData = processedAgpContractPayment(
      Row(3, 3000.00, ts("2023-08-10"), 4, 1300.00, processDate)
    )

    // currency_code, real_exchange_rate, datadate
    val ex = exchangeRate(
      Row("THB", 0.0250, "20230810"),
      Row("THB", 0.0300, "20230812")
    )

    // contract_id, payment_amount, payment_date, contract_payment_id, unpaid_payment_amount, datadate
    val resultProcessedAgpContractPaymentData = processedAgpContractPayment(
      Row(1, 1300.00, ts("2023-08-10"), 1, 0.00, processDate),
      Row(1, 2000.00, ts("2023-08-12"), 2, 300.00, processDate),
      Row(2, 2000.00, ts("2023-08-12"), 3, 2000.00, processDate),
      Row(3, 3000.00, ts("2023-08-12"), 4, 1300.00, processDate),
      Row(3, 4000.00, ts("2023-08-12"), 5, 4000.00, processDate)
    )

    // uuid, settlement_amount, posting_ex
    val expect = result(
      Row(uuid1, 500.00, 0.0250),
      Row(uuid2, 1000.00, 0.0260),
      Row(uuid3, 1500.00, 0.0300)
    )

    verifyResult(settlementRawData, ex, firedrillContractPaymentData, processedAgpContractPaymentData)(resultProcessedAgpContractPaymentData, expect)
  }

  test("process agp correctly - over deduct") {
    // transaction_date, is_cancelled, is_adjustment, booking_id, source_transaction_code,
    // advance_payment_contract_id, settlement_amount, settlement_currency, uuid, datadate
    val settlementRawData = settlementRaw(
      Row("2023-09-23", false, false, 1L, 1, 1, "500.00", "THB", uuid1, processDate)
    ).withColumn("posting_ex", lit(null))

    // contract_id, payment_date, payment_amount, contract_payment_id, datadate
    val firedrillContractPaymentData = firedrillContractPayment(
      Row(1, ts("2023-08-10"), 1300.00, 1, processDate),
      Row(1, ts("2023-08-12"), 2000.00, 2, processDate),
      Row(1, ts("2023-08-13"), 2300.00, 3, processDate),
      Row(1, ts("2023-08-14"), 2500.00, 4, processDate)
    )

    // contract_id, payment_amount, payment_date, contract_payment_id, unpaid_payment_amount, datadate
    val processedAgpContractPaymentData = processedAgpContractPayment(
      Row(1, 1300.00, ts("2023-08-10"), 1, 0.00, processDate),
      Row(1, 2000.00, ts("2023-08-12"), 2, -500.00, processDate),
      Row(1, 2300.00, ts("2023-08-13"), 3, 0.00, processDate),
      Row(1, 2500.00, ts("2023-08-14"), 4, 0.00, processDate)
    )

    // currency_code, real_exchange_rate, datadate
    val ex = exchangeRate(
      Row("THB", 0.0250, "20230810"),
      Row("THB", 0.0300, "20230812"),
      Row("THB", 0.0350, "20230813"),
      Row("THB", 0.0400, "20230814")
    )

    // contract_id, payment_amount, payment_date, contract_payment_id, unpaid_payment_amount, datadate
    val resultProcessedAgpContractPaymentData = processedAgpContractPayment(
      Row(1, 1300.00, ts("2023-08-10"), 1, 0.00, processDate),
      Row(1, 2000.00, ts("2023-08-12"), 2, -500.00, processDate),
      Row(1, 2300.00, ts("2023-08-13"), 3, 0.00, processDate),
      Row(1, 2500.00, ts("2023-08-14"), 4, -500.00, processDate)
    )

    // uuid, settlement_amount, posting_ex
    val expect = result(
      Row(uuid1, 500.00, 0.0400)
    )

    verifyResult(settlementRawData, ex, firedrillContractPaymentData, processedAgpContractPaymentData)(resultProcessedAgpContractPaymentData, expect)
  }

  test("process agp correctly - over deduct then refund") {
    // transaction_date, is_cancelled, is_adjustment, booking_id, source_transaction_code,
    // advance_payment_contract_id, settlement_amount, settlement_currency, uuid, datadate
    val settlementRawData = settlementRaw(
      Row("2023-09-23", false, false, 1L, 1, 1, "-1500.00", "THB", uuid1, processDate)
    ).withColumn("posting_ex", lit(null))

    // contract_id, payment_date, payment_amount, contract_payment_id, datadate
    val firedrillContractPaymentData = firedrillContractPayment(
      Row(1, ts("2023-08-10"), 1300.00, 1, processDate),
      Row(1, ts("2023-08-12"), 2000.00, 2, processDate),
      Row(1, ts("2023-08-13"), 2300.00, 3, processDate),
      Row(1, ts("2023-08-14"), 2400.00, 4, processDate),
      Row(1, ts("2023-08-15"), 2500.00, 5, processDate)
    )

    // contract_id, payment_amount, payment_date, contract_payment_id, unpaid_payment_amount, datadate
    val processedAgpContractPaymentData = processedAgpContractPayment(
      Row(1, 1300.00, ts("2023-08-10"), 1, 0.00, processDate),
      Row(1, 2000.00, ts("2023-08-12"), 2, -500.00, processDate),
      Row(1, 2300.00, ts("2023-08-13"), 3, 0.00, processDate),
      Row(1, 2400.00, ts("2023-08-14"), 4, -600.00, processDate),
      Row(1, 2500.00, ts("2023-08-15"), 5, 300.00, processDate)
    )

    // currency_code, real_exchange_rate, datadate
    val ex = exchangeRate(
      Row("THB", 0.0250, "20230810"),
      Row("THB", 0.0300, "20230812"),
      Row("THB", 0.0350, "20230813"),
      Row("THB", 0.0400, "20230814"),
      Row("THB", 0.0450, "20230815")
    )

    // contract_id, payment_amount, payment_date, contract_payment_id, unpaid_payment_amount, datadate
    val resultProcessedAgpContractPaymentData = processedAgpContractPayment(
      Row(1, 1300.00, ts("2023-08-10"), 1, 0.00, processDate),
      Row(1, 2000.00, ts("2023-08-12"), 2, -500.00, processDate),
      Row(1, 2300.00, ts("2023-08-13"), 3, 0.00, processDate),
      Row(1, 2400.00, ts("2023-08-14"), 4, -600.00, processDate),
      Row(1, 2500.00, ts("2023-08-15"), 5, 1800.00, processDate)
    )

    // uuid, settlement_amount, posting_ex
    val expect = result(
      Row(uuid1, -1500.00, 0.0450)
    )

    verifyResult(settlementRawData, ex, firedrillContractPaymentData, processedAgpContractPaymentData)(resultProcessedAgpContractPaymentData, expect)
  }

  test("process agp correctly - match with future contract_payment") {
    // transaction_date, is_cancelled, is_adjustment, booking_id, source_transaction_code,
    // advance_payment_contract_id, settlement_amount, settlement_currency, uuid, datadate
    val settlementRawData = settlementRaw(
      Row("2023-09-23", false, false, 1L, 1, 1, "500.00", "THB", uuid1, processDate),
      Row("2023-09-23", false, false, 2L, 2, 1, "800.00", "THB", uuid2, processDate)
    ).withColumn("posting_ex", lit(null))

    // contract_id, payment_date, payment_amount, contract_payment_id, datadate
    val firedrillContractPaymentData = firedrillContractPayment(
      Row(1, ts("2023-08-10"), 1000.00, 1, processDate),
      Row(1, ts("2999-01-01"), 1300.00, 2, processDate)
    )

    // contract_id, payment_amount, payment_date, contract_payment_id, unpaid_payment_amount, datadate
    val processedAgpContractPaymentData = processedAgpContractPayment(
      Row(1, 1000.00, ts("2023-08-10"), 1, 0.00, processDate)
    )

    // currency_code, real_exchange_rate, datadate
    val ex = exchangeRate(
      Row("THB", 0.0250, "20230810"),
      Row("THB", 0.0450, "20240118")
    )

    // contract_id, payment_amount, payment_date, contract_payment_id, unpaid_payment_amount, datadate
    val resultProcessedAgpContractPaymentData = processedAgpContractPayment(
      Row(1, 1000.00, ts("2023-08-10"), 1, 0.00, processDate),
      Row(1, 1300.00, ts("2999-01-01"), 2, 0.00, processDate)
    )

    // uuid, settlement_amount, posting_ex
    val expect = result(
      Row(uuid1, 500.00, 0.0450),
      Row(uuid2, 800.00, 0.0450)
    )

    verifyResult(settlementRawData, ex, firedrillContractPaymentData, processedAgpContractPaymentData)(resultProcessedAgpContractPaymentData, expect)
  }

  test("process agp correctly - with refund") {
    // transaction_date, is_cancelled, is_adjustment, booking_id, source_transaction_code,
    // advance_payment_contract_id, settlement_amount, settlement_currency, uuid, datadate
    val settlementRawData = settlementRaw(
      Row("2023-09-23", false, false, 1L, 1, 1, "500.00", "THB", uuid1, processDate),
      Row("2023-09-24", false, false, 1L, 2, 1, "1000.00", "THB", uuid2, processDate),
      Row("2023-09-25", true, false, 1L, 3, 1, "-300.00", "THB", uuid3, processDate),
      Row("2023-09-25", false, false, 1L, 4, 1, "1500.00", "THB", uuid4, processDate)
    ).withColumn("posting_ex", lit(null))

    // contract_id, payment_date, payment_amount, contract_payment_id, datadate
    val firedrillContractPaymentData = firedrillContractPayment(
      Row(1, ts("2023-08-10"), 1300.00, 1, processDate),
      Row(1, ts("2023-08-12"), 2000.00, 2, processDate)
    )

    // contract_id, payment_amount, payment_date, contract_payment_id, unpaid_payment_amount, datadate
    val processedAgpContractPaymentData = processedAgpContractPayment()

    // currency_code, real_exchange_rate, datadate
    val ex = exchangeRate(
      Row("THB", 0.0250, "20230810"),
      Row("THB", 0.0300, "20230812")
    )

    // contract_id, payment_amount, payment_date, contract_payment_id, unpaid_payment_amount, datadate
    val resultProcessedAgpContractPaymentData = processedAgpContractPayment(
      Row(1, 1300.00, ts("2023-08-10"), 1, 0.00, processDate),
      Row(1, 2000.00, ts("2023-08-12"), 2, 600.00, processDate)
    )

    // uuid, settlement_amount, posting_ex
    val expect = result(
      Row(uuid1, 500.00, 0.0250),
      Row(uuid2, 1000.00, 0.0260),
      Row(uuid3, -300.00, 0.0300),
      Row(uuid4, 1500.00, 0.0300)
    )

    verifyResult(settlementRawData, ex, firedrillContractPaymentData, processedAgpContractPaymentData)(resultProcessedAgpContractPaymentData, expect)
  }

  test("process agp correctly - with refund and no following transaction") {
    // transaction_date, is_cancelled, is_adjustment, booking_id, source_transaction_code,
    // advance_payment_contract_id, settlement_amount, settlement_currency, uuid, datadate
    val settlementRawData = settlementRaw(
      Row("2023-09-23", false, false, 1L, 1, 1, "500.00", "THB", uuid1, processDate),
      Row("2023-09-24", false, false, 1L, 2, 1, "1000.00", "THB", uuid2, processDate),
      Row("2023-09-25", true, false, 1L, 3, 1, "-300.00", "THB", uuid3, processDate)
    ).withColumn("posting_ex", lit(null))

    // contract_id, payment_date, payment_amount, contract_payment_id, datadate
    val firedrillContractPaymentData = firedrillContractPayment(
      Row(1, ts("2023-08-10"), 1300.00, 1, processDate),
      Row(1, ts("2023-08-12"), 2000.00, 2, processDate)
    )

    // contract_id, payment_amount, payment_date, contract_payment_id, unpaid_payment_amount, datadate
    val processedAgpContractPaymentData = processedAgpContractPayment()

    // currency_code, real_exchange_rate, datadate
    val ex = exchangeRate(
      Row("THB", 0.0250, "20230810"),
      Row("THB", 0.0300, "20230812")
    )

    // contract_id, payment_amount, payment_date, contract_payment_id, unpaid_payment_amount, datadate
    val resultProcessedAgpContractPaymentData = processedAgpContractPayment(
      Row(1, 1300.00, ts("2023-08-10"), 1, 0.00, processDate),
      Row(1, 2000.00, ts("2023-08-12"), 2, 2100.00, processDate)
    )

    // uuid, settlement_amount, posting_ex
    val expect = result(
      Row(uuid1, 500.00, 0.0250),
      Row(uuid2, 1000.00, 0.0260),
      Row(uuid3, -300.00, 0.0300)
    )

    verifyResult(settlementRawData, ex, firedrillContractPaymentData, processedAgpContractPaymentData)(resultProcessedAgpContractPaymentData, expect)
  }

  test("process agp correctly - with only refund") {
    // transaction_date, is_cancelled, is_adjustment, booking_id, source_transaction_code,
    // advance_payment_contract_id, settlement_amount, settlement_currency, uuid, datadate
    val settlementRawData = settlementRaw(
      Row("2023-09-23", false, false, 1L, 1, 1, "-500.00", "THB", uuid1, processDate)
    ).withColumn("posting_ex", lit(null))

    // contract_id, payment_date, payment_amount, contract_payment_id, datadate
    val firedrillContractPaymentData = firedrillContractPayment(
      Row(1, ts("2023-08-10"), 1300.00, 1, processDate),
      Row(1, ts("2023-08-12"), 2000.00, 2, processDate)
    )

    // contract_id, payment_amount, payment_date, contract_payment_id, unpaid_payment_amount, datadate
    val processedAgpContractPaymentData = processedAgpContractPayment()

    // currency_code, real_exchange_rate, datadate
    val ex = exchangeRate(
      Row("THB", 0.0250, "20230810"),
      Row("THB", 0.0300, "20230812")
    )

    // contract_id, payment_amount, payment_date, contract_payment_id, unpaid_payment_amount, datadate
    val resultProcessedAgpContractPaymentData = processedAgpContractPayment(
      Row(1, 1300.00, ts("2023-08-10"), 1, 1800.00, processDate),
      Row(1, 2000.00, ts("2023-08-12"), 2, 2000.00, processDate)
    )

    // uuid, settlement_amount, posting_ex
    val expect = result(
      Row(uuid1, -500.00, 0.0250)
    )

    verifyResult(settlementRawData, ex, firedrillContractPaymentData, processedAgpContractPaymentData)(resultProcessedAgpContractPaymentData, expect)
  }

  test("process agp correctly - with zero amount") {
    // transaction_date, is_cancelled, is_adjustment, booking_id, source_transaction_code,
    // advance_payment_contract_id, settlement_amount, settlement_currency, uuid, datadate
    val settlementRawData = settlementRaw(
      Row("2023-09-22", false, false, 1L, 1, 1, "500.00", "THB", uuid1, processDate),
      Row("2023-09-24", false, false, 1L, 1, 1, "0.00", "THB", uuid2, processDate),
      Row("2023-09-25", false, false, 1L, 1, 1, "800.00", "THB", uuid3, processDate),
      Row("2023-09-26", false, false, 1L, 1, 1, "0.00", "THB", uuid4, processDate),
      Row("2023-09-27", false, false, 1L, 1, 1, "1000.00", "THB", uuid5, processDate)
    ).withColumn("posting_ex", lit(null))

    // contract_id, payment_date, payment_amount, contract_payment_id, datadate
    val firedrillContractPaymentData = firedrillContractPayment(
      Row(1, ts("2023-08-10"), 1300.00, 1, processDate),
      Row(1, ts("2023-08-12"), 2000.00, 2, processDate)
    )

    // contract_id, payment_amount, payment_date, contract_payment_id, unpaid_payment_amount, datadate
    val processedAgpContractPaymentData = processedAgpContractPayment()

    // currency_code, real_exchange_rate, datadate
    val ex = exchangeRate(
      Row("THB", 0.0250, "20230810"),
      Row("THB", 0.0300, "20230812")
    )

    // contract_id, payment_amount, payment_date, contract_payment_id, unpaid_payment_amount, datadate
    val resultProcessedAgpContractPaymentData = processedAgpContractPayment(
      Row(1, 1300.00, ts("2023-08-10"), 1, 0.00, processDate),
      Row(1, 2000.00, ts("2023-08-12"), 2, 1000.00, processDate)
    )

    // uuid, settlement_amount, posting_ex
    val expect = result(
      Row(uuid1, 500.00, 0.0250),
      Row(uuid2, 0.00, 0.0250),
      Row(uuid3, 800.00, 0.0250),
      Row(uuid4, 0.00, 0.0300),
      Row(uuid5, 1000.00, 0.0300)
    )

    verifyResult(settlementRawData, ex, firedrillContractPaymentData, processedAgpContractPaymentData)(resultProcessedAgpContractPaymentData, expect)
  }

  test("process agp correctly - sort transactions correctly") {
    // transaction_date, is_cancelled, is_adjustment, booking_id, source_transaction_code,
    // advance_payment_contract_id, settlement_amount, settlement_currency, uuid, datadate
    val settlementRawData = settlementRaw(
      Row("2023-09-23", true, false, 2L, 2, 1, "1000.00", "THB", uuid1, processDate),
      Row("2023-09-23", false, true, 2L, 2, 1, "900.00", "THB", uuid2, processDate),
      Row("2023-09-23", false, false, 1L, 2, 1, "800.00", "THB", uuid3, processDate),
      Row("2023-09-23", false, false, 2L, 2, 1, "600.00", "THB", uuid4, processDate),
      Row("2023-09-23", false, false, 2L, 1, 1, "700.00", "THB", uuid5, processDate),
      Row("2023-09-24", true, false, 1L, 1, 1, "500.00", "THB", uuid6, processDate)
    ).withColumn("posting_ex", lit(null))

    // contract_id, payment_date, payment_amount, contract_payment_id, datadate
    val firedrillContractPaymentData = firedrillContractPayment(
      Row(1, ts("2023-08-10"), 1000.00, 1, processDate),
      Row(1, ts("2023-08-11"), 900.00, 2, processDate),
      Row(1, ts("2023-08-12"), 800.00, 3, processDate),
      Row(1, ts("2023-08-13"), 600.00, 4, processDate),
      Row(1, ts("2023-08-14"), 700.00, 5, processDate),
      Row(1, ts("2023-08-15"), 500.00, 6, processDate)
    )

    // contract_id, payment_amount, payment_date, contract_payment_id, unpaid_payment_amount, datadate
    val processedAgpContractPaymentData = processedAgpContractPayment()

    // currency_code, real_exchange_rate, datadate
    val ex = exchangeRate(
      Row("THB", 0.0250, "20230810"),
      Row("THB", 0.0260, "20230811"),
      Row("THB", 0.0270, "20230812"),
      Row("THB", 0.0280, "20230813"),
      Row("THB", 0.0290, "20230814"),
      Row("THB", 0.0300, "20230815")
    )

    // contract_id, payment_amount, payment_date, contract_payment_id, unpaid_payment_amount, datadate
    val resultProcessedAgpContractPaymentData = processedAgpContractPayment(
      Row(1, 1000.00, ts("2023-08-10"), 1, 0.00, processDate),
      Row(1, 900.00, ts("2023-08-11"), 2, 0.00, processDate),
      Row(1, 800.00, ts("2023-08-12"), 3, 0.00, processDate),
      Row(1, 600.00, ts("2023-08-13"), 4, 0.00, processDate),
      Row(1, 700.00, ts("2023-08-14"), 5, 0.00, processDate),
      Row(1, 500.00, ts("2023-08-15"), 6, 0.00, processDate)
    )

    // uuid, settlement_amount, posting_ex
    val expect = result(
      Row(uuid1, 1000.00, 0.0250),
      Row(uuid2, 900.00, 0.0260),
      Row(uuid3, 800.00, 0.0270),
      Row(uuid4, 600.00, 0.0280),
      Row(uuid5, 700.00, 0.0290),
      Row(uuid6, 500.00, 0.0300)
    )

    verifyResult(settlementRawData, ex, firedrillContractPaymentData, processedAgpContractPaymentData)(resultProcessedAgpContractPaymentData, expect)
  }

  val exchangeRateSchema: StructType = StructType(
    List(
      StructField("currency_code", StringType),
      StructField("real_exchange_rate", DoubleType),
      StructField("datadate", StringType)
    )
  )

  val settlementRawSchema: StructType = StructType(
    List(
      StructField("transaction_date", StringType),
      StructField("is_cancelled", BooleanType),
      StructField("is_adjustment", BooleanType),
      StructField("booking_id", LongType),
      StructField("source_transaction_code", IntegerType),
      StructField("advance_payment_contract_id", IntegerType),
      StructField("settlement_amount", StringType),
      StructField("settlement_currency", StringType),
      StructField("uuid", StringType),
      StructField("datadate", IntegerType)
    )
  )

  val firedrillContractPaymentSchema: StructType = StructType(
    List(
      StructField("contract_id", IntegerType),
      StructField("payment_date", TimestampType),
      StructField("payment_amount", DoubleType),
      StructField("contract_payment_id", IntegerType),
      StructField("datadate", IntegerType)
    )
  )

  val processedAgpContractPaymentSchema: StructType = StructType(
    List(
      StructField("contract_id", IntegerType),
      StructField("payment_amount", DoubleType),
      StructField("payment_date", TimestampType),
      StructField("contract_payment_id", IntegerType),
      StructField("unpaid_payment_amount", DoubleType),
      StructField("datadate", IntegerType)
    )
  )

  def ts(time: String): Timestamp =
    if (time.length > 10) {
      new Timestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(time).getTime)
    } else { new Timestamp(new SimpleDateFormat("yyyy-MM-dd").parse(time).getTime) }

  def df(rows: Row*)(schema: StructType): DataFrame = spark.createDataFrame(spark.sparkContext.parallelize(rows), schema)

  def exchangeRate(rows: Row*): DataFrame = df(rows: _*)(exchangeRateSchema)

  def settlementRaw(rows: Row*): DataFrame = df(rows: _*)(settlementRawSchema)

  def firedrillContractPayment(rows: Row*): DataFrame = df(rows: _*)(firedrillContractPaymentSchema)

  def processedAgpContractPayment(rows: Row*): DataFrame = df(rows: _*)(processedAgpContractPaymentSchema)

  def result(rows: Row*): DataFrame = df(rows: _*)(resultSchema)

  def dfCols(rows: Row*)(columns: (String, DataType)*): DataFrame =
    spark.createDataFrame(spark.sparkContext.parallelize(rows), StructType(fields = columns.map(c => StructField(c._1, c._2))))
}
