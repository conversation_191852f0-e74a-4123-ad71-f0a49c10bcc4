package com.agoda.finance.functions.specific

import com.agoda.finance.functions.specific.BspLogic.normalizeBspRawData
import com.agoda.ml.spark.{HiveSupport, SparkSharedLocalTest}
import org.scalatest.FunSuite
import org.scalatest.Matchers.{be, convertToAnyShouldWrapper, not}

import java.sql.Timestamp

class BspLogicTest extends FunSuite with SparkSharedLocalTest with HiveSupport {

  import sqlContext.implicits._

  setupAll {

    // Create the database first
    sqlContext.sql("CREATE DATABASE IF NOT EXISTS finance_sftp_downloader_staging")

    // Create mock historical data table for TKTT lookup
    val mockHistoricalData = Seq(
      (
        "T1",
        "D1",
        "2024-01-01",
        Timestamp.valueOf("2024-01-10 12:00:00"),
        20240120,
        "6M9TCI/1A I",
        "TKTT",
        10.0,
        100.0,
        90.0,
        1.0,
        2.0,
        3.0,
        120.0,
        "USD$",
        "file1",
        "hash1"
      ),
      (
        "T3",
        "D3",
        "2024-01-03",
        Timestamp.valueOf("2024-01-12 14:00:00"),
        20240122,
        "ABCDEF/XY Z",
        "TKTT",
        8.0,
        80.0,
        72.0,
        2.0,
        2.0,
        2.0,
        90.0,
        "GBP$",
        "file3",
        "hash4"
      )
    ).toDF(
      "transaction_number",
      "ticket_document_number",
      "date_of_issue",
      "reporting_date",
      "datadate",
      "pnr_reference_and_or_airline_data",
      "transaction_code",
      "commission_amount",
      "commissionable_amount",
      "net_fare_amount",
      "tax_miscellaneous_fee_amount_1",
      "tax_miscellaneous_fee_amount_2",
      "tax_miscellaneous_fee_amount_3",
      "ticket_document_amount",
      "currency_type",
      "file_id",
      "hash_key"
    )

    mockHistoricalData.write.saveAsTable("finance_sftp_downloader_staging.holiday_tour_iata_daily")

    sqlContext.table("finance_sftp_downloader_staging.holiday_tour_iata_daily").show(100)
  }

  teardownAll {
    sqlContext.sql("DROP DATABASE IF EXISTS finance_sftp_downloader_staging CASCADE")
  }

  test("normalizeBspRawData should extract PNR and aggregate as expected") {
    import sqlContext.implicits._

    val input = Seq(
      // D1: Has both TKTT and RFND, PNR in different patterns
      (
        "T1",
        "D1",
        "2024-01-01",
        Timestamp.valueOf("2024-01-10 12:00:00"),
        20240120,
        "6M9TCI/1A I",
        "TKTT",
        10.0,
        100.0,
        90.0,
        1.0,
        2.0,
        3.0,
        120.0,
        "USD$",
        "file1",
        "hash1"
      ),
      (
        "T1",
        "D1",
        "2024-01-01",
        Timestamp.valueOf("2024-01-10 12:00:00"),
        20240120,
        null, // RFND should have null PNR
        "RFND",
        5.0,
        50.0,
        45.0,
        0.0,
        0.0,
        0.0,
        60.0,
        "USD$",
        "file1",
        "hash2"
      ),
      // D2: Only CANX, pnr is null
      (
        "T2",
        "D2",
        "2024-01-02",
        Timestamp.valueOf("2024-01-11 13:00:00"),
        20240121,
        null, // CANX should have null PNR
        "CANX",
        7.0,
        70.0,
        63.0,
        1.0,
        1.0,
        1.0,
        80.0,
        "EUR$",
        "file2",
        "hash3"
      ),
      // D3: Only TKTT, pnr present, to test uniqueTktt
      (
        "T3",
        "D3",
        "2024-01-03",
        Timestamp.valueOf("2024-01-12 14:00:00"),
        20240122,
        "ABCDEF/XY Z",
        "TKTT",
        8.0,
        80.0,
        72.0,
        2.0,
        2.0,
        2.0,
        90.0,
        "GBP$",
        "file3",
        "hash4"
      )
    ).toDF(
      "transaction_number",
      "ticket_document_number",
      "date_of_issue",
      "reporting_date",
      "datadate",
      "pnr_reference_and_or_airline_data",
      "transaction_code",
      "commission_amount",
      "commissionable_amount",
      "net_fare_amount",
      "tax_miscellaneous_fee_amount_1",
      "tax_miscellaneous_fee_amount_2",
      "tax_miscellaneous_fee_amount_3",
      "ticket_document_amount",
      "currency_type",
      "file_id",
      "hash_key"
    )

    val result = normalizeBspRawData(input)

    // Verify the result structure
    result should not be null
    val resultArray = result.collect()

    // Should have 3 aggregated records (one for each distinct ticket_document_number)
    resultArray.length shouldBe 3

    // Test D1 aggregation (T1 - combining TKTT and RFND)
    val d1Result = resultArray.find(_.getAs[String]("ticket_document_number") == "D1")
    d1Result should not be None
    val d1 = d1Result.get

    // Verify D1 fields
    d1.getAs[String]("transaction_number") shouldBe "T1"
    d1.getAs[String]("pnr") shouldBe "6M9TCI"                 // Should extract PNR before slash
    d1.getAs[String]("transaction_code") shouldBe "TKTT"      // Should take max (TKTT > RFND lexicographically)
    d1.getAs[Double]("commission_amount") shouldBe 15.0       // 10.0 + 5.0
    d1.getAs[Double]("cost_amount") shouldBe 150.0            // 100.0 + 50.0
    d1.getAs[Double]("net_fare_amount") shouldBe 135.0        // 90.0 + 45.0
    d1.getAs[Double]("tax_amount") shouldBe 6.0               // (1+2+3) + (0+0+0)
    d1.getAs[Double]("ticket_document_amount") shouldBe 180.0 // 120.0 + 60.0
    d1.getAs[String]("currency_type") shouldBe "USD"

    // Test D2 (T2 - CANX only, null PNR)
    val d2Result = resultArray.find(_.getAs[String]("ticket_document_number") == "D2")
    d2Result should not be None
    val d2 = d2Result.get

    d2.getAs[String]("transaction_number") shouldBe "T2"
    d2.getAs[String]("pnr") should (be(null) or be("")) // PNR should be null/empty
    d2.getAs[String]("transaction_code") shouldBe "CANX"
    d2.getAs[Double]("commission_amount") shouldBe 7.0
    d2.getAs[Double]("cost_amount") shouldBe 70.0
    d2.getAs[Double]("net_fare_amount") shouldBe 63.0
    d2.getAs[Double]("tax_amount") shouldBe 3.0 // 1+1+1
    d2.getAs[Double]("ticket_document_amount") shouldBe 80.0
    d2.getAs[String]("currency_type") shouldBe "EUR"

    // Test D3 (T3 - TKTT only)
    val d3Result = resultArray.find(_.getAs[String]("ticket_document_number") == "D3")
    d3Result should not be None
    val d3 = d3Result.get

    d3.getAs[String]("transaction_number") shouldBe "T3"
    d3.getAs[String]("pnr") shouldBe "ABCDEF" // Should extract PNR before slash
    d3.getAs[String]("transaction_code") shouldBe "TKTT"
    d3.getAs[Double]("commission_amount") shouldBe 8.0
    d3.getAs[Double]("cost_amount") shouldBe 80.0
    d3.getAs[Double]("net_fare_amount") shouldBe 72.0
    d3.getAs[Double]("tax_amount") shouldBe 6.0 // 2+2+2
    d3.getAs[Double]("ticket_document_amount") shouldBe 90.0
    d3.getAs[String]("currency_type") shouldBe "GBP"
  }

  test("normalizeBspRawData should handle PNR extraction patterns correctly") {
    val input = Seq(
      // Test PNR before slash pattern
      (
        "T1",
        "D1",
        "2024-01-01",
        Timestamp.valueOf("2024-01-10 12:00:00"),
        20240120,
        "ABC123/1A I",
        "TKTT",
        10.0,
        100.0,
        90.0,
        1.0,
        2.0,
        3.0,
        120.0,
        "USD$",
        "file1",
        "hash1"
      ),
      // Test PNR after slash pattern
      (
        "T2",
        "D2",
        "2024-01-02",
        Timestamp.valueOf("2024-01-11 13:00:00"),
        20240121,
        "1B/XYZ789",
        "TKTT",
        15.0,
        150.0,
        135.0,
        2.0,
        3.0,
        4.0,
        180.0,
        "EUR$",
        "file2",
        "hash2"
      ),
      // Test null PNR
      (
        "T3",
        "D3",
        "2024-01-03",
        Timestamp.valueOf("2024-01-12 14:00:00"),
        20240122,
        null,
        "CANX",
        20.0,
        200.0,
        180.0,
        5.0,
        5.0,
        5.0,
        240.0,
        "GBP$",
        "file3",
        "hash3"
      ),
      // Test empty PNR
      (
        "T4",
        "D4",
        "2024-01-04",
        Timestamp.valueOf("2024-01-13 15:00:00"),
        20240123,
        "",
        "TKTT",
        25.0,
        250.0,
        225.0,
        1.0,
        1.0,
        1.0,
        300.0,
        "CAD$",
        "file4",
        "hash4"
      )
    ).toDF(
      "transaction_number",
      "ticket_document_number",
      "date_of_issue",
      "reporting_date",
      "datadate",
      "pnr_reference_and_or_airline_data",
      "transaction_code",
      "commission_amount",
      "commissionable_amount",
      "net_fare_amount",
      "tax_miscellaneous_fee_amount_1",
      "tax_miscellaneous_fee_amount_2",
      "tax_miscellaneous_fee_amount_3",
      "ticket_document_amount",
      "currency_type",
      "file_id",
      "hash_key"
    )

    val result      = normalizeBspRawData(input)
    val resultArray = result.collect()

    resultArray.length shouldBe 4

    // Test PNR extraction patterns
    val d1 = resultArray.find(_.getAs[String]("ticket_document_number") == "D1").get
    d1.getAs[String]("pnr") shouldBe "ABC123"

    val d2 = resultArray.find(_.getAs[String]("ticket_document_number") == "D2").get
    d2.getAs[String]("pnr") shouldBe "XYZ789"

    val d3 = resultArray.find(_.getAs[String]("ticket_document_number") == "D3").get
    d3.getAs[String]("pnr") should (be(null) or be(""))

    val d4 = resultArray.find(_.getAs[String]("ticket_document_number") == "D4").get
    d4.getAs[String]("pnr") should (be(null) or be(""))
  }

  test("normalizeBspRawData should filter out null ticket_document_number") {
    val input = Seq(
      // Valid record
      (
        "T1",
        "D1",
        "2024-01-01",
        Timestamp.valueOf("2024-01-10 12:00:00"),
        20240120,
        "ABC123/1A",
        "TKTT",
        10.0,
        100.0,
        90.0,
        1.0,
        2.0,
        3.0,
        120.0,
        "USD$",
        "file1",
        "hash1"
      ),
      // Invalid record with null ticket_document_number
      (
        "T2",
        null,
        "2024-01-02",
        Timestamp.valueOf("2024-01-11 13:00:00"),
        20240121,
        null,
        "RFND",
        15.0,
        150.0,
        135.0,
        2.0,
        3.0,
        4.0,
        180.0,
        "EUR$",
        "file2",
        "hash2"
      )
    ).toDF(
      "transaction_number",
      "ticket_document_number",
      "date_of_issue",
      "reporting_date",
      "datadate",
      "pnr_reference_and_or_airline_data",
      "transaction_code",
      "commission_amount",
      "commissionable_amount",
      "net_fare_amount",
      "tax_miscellaneous_fee_amount_1",
      "tax_miscellaneous_fee_amount_2",
      "tax_miscellaneous_fee_amount_3",
      "ticket_document_amount",
      "currency_type",
      "file_id",
      "hash_key"
    )

    val result      = normalizeBspRawData(input)
    val resultArray = result.collect()

    // Should only have 1 record (null ticket_document_number filtered out)
    resultArray.length shouldBe 1
    resultArray(0).getAs[String]("ticket_document_number") shouldBe "D1"
  }
}
