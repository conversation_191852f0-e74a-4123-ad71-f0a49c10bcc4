package com.agoda.finance.processor

import adp.shaded.org.joda.time.DateTime
import adp.shaded.org.joda.time.format.DateTimeFormat
import com.agoda.finance.common.utils.HadoopUtils
import com.agoda.finance.constants.{statusValue, DatabaseConstants}
import com.agoda.finance.preprocess.{Arguments, ExecutionInfo}
import com.agoda.finance.processors.{ExtractProcessor, ResultsStatus}
import com.agoda.ml.spark.DataFrameMatchers.beEqualTo
import com.agoda.ml.spark.{HiveSupport, SparkSharedLocalTest}
import org.apache.spark.sql.Row
import org.apache.spark.sql.types._
import org.scalatest.FunSuite
import org.scalatest.Matchers.{be, convertToAnyShouldWrapper, equal}

import java.time.LocalDate
import java.time.format.DateTimeFormatter

class ExtractProcessorTest extends FunSuite with SparkSharedLocalTest with HiveSupport {
  import sqlContext.implicits._

  setupAll {
    sqlContext.sql(s"CREATE DATABASE IF NOT EXISTS ${DatabaseConstants.SFTPDatabase}")
  }

  teardownAll {
    spark.sql(s"DROP DATABASE IF EXISTS ${DatabaseConstants.SFTPDatabase} CASCADE")
  }

  lazy val processDate = 20200801
  lazy val arg         = Arguments(processDate, processDate, "", "", true, "<EMAIL>", "reprocess", "")
  lazy val execInfo    = new ExecutionInfo(arg)

  val matchingSchemaDatadate = StructType(
    List(
      StructField("a", DoubleType),
      StructField("b", StringType),
      StructField("c", StringType),
      StructField("d", StringType),
      StructField("datadate", IntegerType)
    )
  )

  val matchingSchemaDatamonth = StructType(
    List(
      StructField("a", DoubleType),
      StructField("b", StringType),
      StructField("c", StringType),
      StructField("d", StringType),
      StructField("datamonth", IntegerType)
    )
  )

  test("It should return empty dataframe when table doesn't exist") {
    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply

    assert(actual.head.sourceDf.head == sqlContext.emptyDataFrame)
    assert(actual.head.status.status.id == statusValue.ExtractFailed.id) // 1 Means extracted Failed
  }

  test("It reads data from table correctly when table is empty") {
    val emptyDF = spark.createDataFrame(sc.emptyRDD[Row], matchingSchemaDatadate)

    HadoopUtils.writeToExternalTable(
      emptyDF,
      s"${DatabaseConstants.SFTPDatabase}.test_table",
      "overwrite"
    )
    HadoopUtils.writeToExternalTable(
      emptyDF,
      s"${DatabaseConstants.SFTPDatabase}.test_enrich_table",
      "overwrite"
    )

    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply

    actual.head.sourceDf.head should beEqualTo(emptyDF)
    actual.head.enrichDf.head should beEqualTo(emptyDF)
    assert(actual.head.status.status.id == statusValue.Extracted.id)
  }

  test("It reads data correctly from table without lookback") {

    val data       = Seq(Row(1000.0, "B1", "C1", "D1", 20200801), Row(1000.0, "B2", "C2", "D2", 20200801))
    val expectedDF = spark.createDataFrame(spark.sparkContext.parallelize(data), matchingSchemaDatadate)

    HadoopUtils.writeToExternalTable(
      expectedDF,
      s"${DatabaseConstants.SFTPDatabase}.test_table",
      "overwrite"
    )
    HadoopUtils.writeToExternalTable(
      expectedDF,
      s"${DatabaseConstants.SFTPDatabase}.test_enrich_table",
      "overwrite"
    )

    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply

    actual.head.sourceDf.head should beEqualTo(expectedDF)
    actual.head.enrichDf.head should beEqualTo(expectedDF)
    assert(actual.head.status.status.id == statusValue.Extracted.id)
  }

  test("It reads data correctly from table even when the source datadate type is string") {
    val matchingSchemaStringDatadate = StructType(
      List(
        StructField("a", DoubleType),
        StructField("b", StringType),
        StructField("c", StringType),
        StructField("d", StringType),
        StructField("datadate", StringType)
      )
    )

    val data       = Seq(Row(1000.0, "B1", "C1", "D1", "20200801"), Row(1000.0, "B2", "C2", "D2", "20200801"))
    val expectedDF = spark.createDataFrame(spark.sparkContext.parallelize(data), matchingSchemaStringDatadate)

    HadoopUtils.writeToExternalTable(
      expectedDF,
      s"${DatabaseConstants.SFTPDatabase}.test_table",
      "overwrite"
    )

    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply

    actual.head.sourceDf.head should beEqualTo(expectedDF)
    assert(actual.head.status.status.id == statusValue.Extracted.id)
  }

  test("It reads data correctly from table with lookback") {

    val arg        = Arguments(20200801, 20200801, "", "", false, "<EMAIL>", "prod", "")
    val execInfo   = new ExecutionInfo(arg)
    val data       = Seq(Row(1000.0, "B1", "C1", "D1", 20200801), Row(1000.0, "B2", "C2", "D2", 20200801), Row(100.0, "B1", "C1", "D1", 20200731))
    val expectedDF = spark.createDataFrame(spark.sparkContext.parallelize(data), matchingSchemaDatadate)

    HadoopUtils.writeToExternalTable(
      expectedDF,
      s"${DatabaseConstants.SFTPDatabase}.test_table",
      "overwrite"
    )
    HadoopUtils.writeToExternalTable(
      expectedDF,
      s"${DatabaseConstants.SFTPDatabase}.test_enrich_table",
      "overwrite"
    )

    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply

    actual.head.sourceDf.head.unionByName(actual(actual.length / 2).sourceDf.head) should beEqualTo(expectedDF)

    if (actual.nonEmpty) {
      (1 until actual.length).filterNot(_ == actual.length / 2).foreach { i =>
        assert(actual(i).status.status.id == statusValue.ExtractFailed.id)
      }

      assert(actual(actual.length / 2).status.status.id == statusValue.Extracted.id)
    }

  }

  test("It reads data correctly from table using extract function") {

    val data       = Seq(Row(1000.0, "B1", "C1", "D1", 20200801), Row(1000.0, "B2", "C2", "D2", 20200801))
    val expectedDF = spark.createDataFrame(spark.sparkContext.parallelize(data), matchingSchemaDatadate)

    HadoopUtils.writeToExternalTable(
      expectedDF,
      s"${DatabaseConstants.SFTPDatabase}.test_table7",
      "overwrite"
    )
    HadoopUtils.writeToExternalTable(
      expectedDF,
      s"${DatabaseConstants.SFTPDatabase}.test_enrich_table7",
      "overwrite"
    )

    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply

    actual(6).sourceDf.head should beEqualTo(expectedDF.filter("b='B1'"))
    actual(6).enrichDf.head should beEqualTo(expectedDF.filter("b='B2'"))
    assert(actual(6).status.status.id == statusValue.Extracted.id)
  }

  test("It reads data correctly from table with extract functions: marriott_transaction_within_date") {

    val data = Seq(
      Row(1000.0, "B1", "C1", "D1", 20200801),
      Row(1000.0, "B1", "C2", "D2", 20200801),                                           // will be filter out, because col("c") != "C1"
      Row(1000.0, "B2", "C1", "D2", 20200801),                                           // will be filter out, because col("b") != "B1"
      Row(1000.0, null.asInstanceOf[String], null.asInstanceOf[String], "D2", 20200801), // will be filter out, null case
      Row(1001.0, "B1", "C1", "D1", 20180801),
      Row(1002.0, "B1", "C1", "D1", 20180731) // will be filter out, because not met "marriott_transaction_within_date"
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(data), matchingSchemaDatadate)

    /** src/it/resources/application.conf
      * extractfunctions: [
      *   {name: "generic_is_equal_to", constants: ["B1"], columns: ["b"]},
      *   {name: "generic_is_equal_to", constants: ["C1"], columns: ["c"]},
      *   {name: "marriott_transaction_within_date"}
      * ]
      */
    val expectedData = Seq(Row(1000.0, "B1", "C1", "D1", 20200801), Row(1001.0, "B1", "C1", "D1", 20180801))
    val expectedDF   = spark.createDataFrame(spark.sparkContext.parallelize(expectedData), matchingSchemaDatadate)

    HadoopUtils.writeToExternalTable(
      df,
      s"${DatabaseConstants.SFTPDatabase}.test_table4",
      "overwrite"
    )

    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply

    actual(3).sourceDf.head should beEqualTo(expectedDF)
    assert(actual(3).status.status.id == statusValue.Extracted.id)
  }

  test("It reads data correctly from table with extract functions: violet_messaginglog_previous_date") {

    val data = Seq( // will be filter out, because col("c") != "C1"
      Row(1001.0, "B1", "C1", "D1", 20200801),
      Row(1001.0, "B1", "C1", "D1", 20200731),
      Row(1002.0, "B1", "C1", "D1", 20200731),
      Row(1003.0, "B1", "C1", "D1", 20200730)
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(data), matchingSchemaDatadate)

    /** src/it/resources/application.conf
      * extractfunctions: [
      *   {name: "generic_is_equal_to", constants: ["B1"], columns: ["b"]},
      *   {name: "generic_is_equal_to", constants: ["C1"], columns: ["c"]},
      *   {name: "violet_messaginglog_previous_date"}
      * ]
      */
    val expectedData = Seq(Row(1001.0, "B1", "C1", "D1", 20200731), Row(1002.0, "B1", "C1", "D1", 20200731))
    val expectedDF   = spark.createDataFrame(spark.sparkContext.parallelize(expectedData), matchingSchemaDatadate)

    HadoopUtils.writeToExternalTable(
      df,
      s"${DatabaseConstants.SFTPDatabase}.test_table5",
      "overwrite"
    )
    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply

    actual(4).sourceDf.head should beEqualTo(expectedDF)
    assert(actual(4).status.status.id == statusValue.Extracted.id)
  }

  test("It reads loyalty transactions data correctly with extract functions: fact_booking_within_month") {
    val arg      = Arguments(20230501, 20230501, "", "", false, "<EMAIL>", "prod", "")
    val execInfo = new ExecutionInfo(arg)

    val data = Seq(
      Row(1001.0, "B1", "C1", "D1", 202305),
      Row(1002.0, "B1", "C1", "D1", 202304),
      Row(1003.0, "B1", "C1", "D1", 202101) // will be filtered out because transaction is older than 1 year
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(data), matchingSchemaDatamonth)

    /** src/it/resources/application.conf
      * extractfunctions: [
      * {name: "generic_is_equal_to", constants: ["B1"], columns: ["b"]},
      * {name: "generic_is_equal_to", constants: ["C1"], columns: ["c"]},
      * {name: "fact_booking_within_month"}
      * ]
      */

    val expectedData = Seq(Row(1001.0, "B1", "C1", "D1", 202305), Row(1002.0, "B1", "C1", "D1", 202304))
    val expected     = spark.createDataFrame(spark.sparkContext.parallelize(expectedData), matchingSchemaDatamonth)

    HadoopUtils.writeToExternalTable(
      df,
      s"${DatabaseConstants.SFTPDatabase}.test_table6",
      "overwrite"
    )

    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply

    actual(5).sourceDf.head.sort("a") should beEqualTo(expected)
    assert(actual(5).status.status.id == statusValue.Extracted.id)
  }

  test("It reads data correctly from table with date format") {

    val data       = Seq(Row(1000.0, "B1", "C1", "D1", 202008), Row(1000.0, "B2", "C2", "D2", 202009))
    val expectedDF = spark.createDataFrame(spark.sparkContext.parallelize(Seq(data.head)), matchingSchemaDatadate)

    HadoopUtils.writeToExternalTable(
      expectedDF,
      s"${DatabaseConstants.SFTPDatabase}.test_table_partition_format",
      "overwrite"
    )
    HadoopUtils.writeToExternalTable(
      expectedDF,
      s"${DatabaseConstants.SFTPDatabase}.test_enrich_table_partition_format",
      "overwrite"
    )

    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply

    actual(7).sourceDf.head should beEqualTo(expectedDF)
    actual(7).enrichDf.head should beEqualTo(expectedDF)
    assert(actual(7).status.status.id == statusValue.Extracted.id)
  }

  test("It reads data correctly from table with extract functions: generic_datadate_lte_today") {

    val data = Seq(
      Row(1001.0, "B1", "C1", "D1", 20200802),
      Row(1001.0, "B1", "C1", "D1", 20200801),
      Row(1002.0, "B1", "C1", "D1", 20200801),
      Row(1003.0, "B1", "C1", "D1", 20200731)
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(data), matchingSchemaDatadate)

    /** src/it/resources/application.conf
      * extractfunctions: [
      *         {name: "generic_datadate_lte_today", columns: ["datadate"]}
      * ]
      */
    val expectedData = Seq(Row(1001.0, "B1", "C1", "D1", 20200801), Row(1002.0, "B1", "C1", "D1", 20200801), Row(1003.0, "B1", "C1", "D1", 20200731))
    val expectedDF   = spark.createDataFrame(spark.sparkContext.parallelize(expectedData), matchingSchemaDatadate)

    HadoopUtils.writeToExternalTable(
      df,
      s"${DatabaseConstants.SFTPDatabase}.test_table8",
      "overwrite"
    )
    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply

    actual(8).sourceDf.head should beEqualTo(expectedDF)
    assert(actual(8).status.status.id == statusValue.Extracted.id)
  }

  test("It reads data correctly from table with extract functions: generic_offset_datadate") {

    val data = Seq(
      Row(1001.0, "B1", "C1", "D1", 20200802),
      Row(1001.0, "B1", "C1", "D1", 20200801),
      Row(1002.0, "B1", "C1", "D1", 20200801),
      Row(1003.0, "B1", "C1", "D1", 20200731)
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(data), matchingSchemaDatadate)

    /** src/it/resources/application.conf
      * extractfunctions: [
      *     {name: "generic_offset_datadate", constants: ["1"], columns: ["datadate"]}
      * ]
      */
    val expectedData = Seq(Row(1003.0, "B1", "C1", "D1", 20200731))
    val expectedDF   = spark.createDataFrame(spark.sparkContext.parallelize(expectedData), matchingSchemaDatadate)

    HadoopUtils.writeToExternalTable(
      df,
      s"${DatabaseConstants.SFTPDatabase}.test_table9",
      "overwrite"
    )
    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply

    actual(9).sourceDf.head should beEqualTo(expectedDF)
    assert(actual(9).status.status.id == statusValue.Extracted.id)
  }

  test("It reads data correctly from table with extract functions: generic_offset_datamonth_gte") {
    val tableName = "test_generic_offset_datamonth_gte"
    val arg       = Arguments(20240101, 20240101, "", "", true, "<EMAIL>", "reprocess", "")
    val execInfo  = new ExecutionInfo(arg)

    val data = Seq(
      Row(1001.0, "B1", "C1", "D1", 202305), // too old, skipping
      Row(1002.0, "B1", "C1", "D1", 202306), // too old, skipping
      Row(1003.0, "B1", "C1", "D1", 202307),
      Row(1004.0, "B1", "C1", "D1", 202312),
      Row(1005.0, "B1", "C1", "D1", 202401)
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(data), matchingSchemaDatamonth)

    /** src/it/resources/application.conf
      * extractfunctions: [
      *  { name: "generic_offset_datamonth_gte", constants: ["6"], columns: ["datamonth"] }
      * ]
      */
    val expectedData = Seq(
      Row(1003.0, "B1", "C1", "D1", 202307),
      Row(1004.0, "B1", "C1", "D1", 202312),
      Row(1005.0, "B1", "C1", "D1", 202401)
    )
    val expectedDF = spark.createDataFrame(spark.sparkContext.parallelize(expectedData), matchingSchemaDatamonth)

    HadoopUtils.writeToExternalTable(
      df,
      tableName,
      "overwrite"
    )
    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply.filter(r => r.sourceTable.sourceName == tableName).head

    actual.sourceDf.head should beEqualTo(expectedDF)
    assert(actual.status.status.id == statusValue.Extracted.id)
  }

  test("It reads data correctly from table with extract functions: generic_offset_datamonth") {
    val tableName = "test_generic_offset_datamonth"
    val arg       = Arguments(20240101, 20240101, "", "", true, "<EMAIL>", "reprocess", "")
    val execInfo  = new ExecutionInfo(arg)

    val data = Seq(
      Row(1001.0, "B1", "C1", "D1", 202401),
      Row(1001.0, "B1", "C1", "D1", 202402),
      Row(1002.0, "B1", "C1", "D1", 202403),
      Row(1003.0, "B1", "C1", "D1", 202312)
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(data), matchingSchemaDatamonth)

    /** src/it/resources/application.conf
      * extractfunctions: [
      *     {name: "generic_offset_datamonth", constants: ["1"], columns: ["datamonth"]}
      * ]
      */
    val expectedData = Seq(Row(1003.0, "B1", "C1", "D1", 202312))
    val expectedDF   = spark.createDataFrame(spark.sparkContext.parallelize(expectedData), matchingSchemaDatamonth)

    HadoopUtils.writeToExternalTable(
      df,
      tableName,
      "overwrite"
    )
    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply.filter(r => r.sourceTable.sourceName == tableName).head

    actual.sourceDf.head should beEqualTo(expectedDF)
    assert(actual.status.status.id == statusValue.Extracted.id)
  }

  test("It reads data correctly from table with extract functions: generic_is_date_in_month_range") {
    val tableName      = "test_generic_is_date_in_month_range"
    val datadateFormat = DateTimeFormat.forPattern("yyyyMMdd")

    val today          = DateTime.now().toString(datadateFormat).toInt
    val oneMonthAgo    = DateTime.now().minusMonths(1).toString(datadateFormat).toInt
    val threeMonthsAgo = DateTime.now().minusMonths(3).withDayOfMonth(1).toString(datadateFormat).toInt
    val fourMonthsAgo  = DateTime.now().minusMonths(4).toString(datadateFormat).toInt
    val data = Seq(
      Row(1001.0, "B1", "C1", "D1", today), // out of range
      Row(1002.0, "B1", "C1", "D1", oneMonthAgo),
      Row(1003.0, "B1", "C1", "D1", threeMonthsAgo),
      Row(1004.0, "B1", "C1", "D1", fourMonthsAgo) // out of range
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(data), matchingSchemaDatadate)

    /** src/it/resources/application.conf
      * extractfunctions: [
      *     { name: "generic_is_date_in_month_range", constants: ["-3", "-1"], columns: ["datadate"] }
      * ]
      */
    val expectedData = Seq(
      Row(1002.0, "B1", "C1", "D1", oneMonthAgo),
      Row(1003.0, "B1", "C1", "D1", threeMonthsAgo)
    )
    val expectedDF = spark.createDataFrame(spark.sparkContext.parallelize(expectedData), matchingSchemaDatadate)

    HadoopUtils.writeToExternalTable(
      df,
      tableName,
      "overwrite"
    )
    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply.filter(r => r.sourceTable.sourceName == tableName).head

    actual.sourceDf.head should beEqualTo(expectedDF)
    assert(actual.status.status.id == statusValue.Extracted.id)
  }

  test("It reads data correctly from table with extract functions: generic_offset_date_greater/less_than_process_month") {
    val tableName      = "test_generic_is_in_range_relative_to_process_date"
    val datadateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    val matchingSchemaStringDatadate = StructType(
      List(
        StructField("a", DoubleType),
        StructField("b", StringType),
        StructField("c", StringType),
        StructField("d", StringType),
        StructField("datadate", StringType)
      )
    )

    val processDateVal = LocalDate.parse(processDate.toString, DateTimeFormatter.BASIC_ISO_DATE)
    val current        = processDateVal.format(datadateFormat)
    val oneMonthAgo    = processDateVal.minusMonths(1).format(datadateFormat)
    val threeMonthsAgo = processDateVal.minusMonths(3).withDayOfMonth(1).format(datadateFormat)
    val fourMonthsAgo  = processDateVal.minusMonths(4).format(datadateFormat)
    val data = Seq(
      Row(1001.0, "B1", "C1", "D1", current), // out of range
      Row(1002.0, "B1", "C1", "D1", oneMonthAgo),
      Row(1003.0, "B1", "C1", "D1", threeMonthsAgo),
      Row(1004.0, "B1", "C1", "D1", fourMonthsAgo) // out of range
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(data), matchingSchemaStringDatadate)

    /** src/it/resources/application.conf
      * extractfunctions: [
      *        { name: "generic_offset_date_greater_than_process_month", constants: ["3", "yyyy-MM-dd"], columns: ["datadate"] },
      *        { name: "generic_offset_date_less_than_process_month", constants: ["1", "yyyy-MM-dd"], columns: ["datadate"] }
      *     ]
      */
    val expectedData = Seq(
      Row(1002.0, "B1", "C1", "D1", oneMonthAgo),
      Row(1003.0, "B1", "C1", "D1", threeMonthsAgo)
    )
    val expectedDF = spark.createDataFrame(spark.sparkContext.parallelize(expectedData), matchingSchemaStringDatadate)

    HadoopUtils.writeToExternalTable(
      df,
      tableName,
      "overwrite"
    )
    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply.filter(r => r.sourceTable.sourceName == tableName).head

    actual.sourceDf.head.show()

    actual.sourceDf.head should beEqualTo(expectedDF)
    assert(actual.status.status.id == statusValue.Extracted.id)
  }

  test("It reads data correctly from table with extract functions: test_generic_datadate_lt_today") {
    val tableName      = "test_generic_datadate_lt_today"
    val datadateFormat = DateTimeFormatter.ofPattern("yyyyMMdd")
    val matchingSchemaDatadate = StructType(
      List(
        StructField("a", DoubleType),
        StructField("b", StringType),
        StructField("c", StringType),
        StructField("d", StringType),
        StructField("datadate", IntegerType)
      )
    )

    val processDateVal = LocalDate.parse(processDate.toString, DateTimeFormatter.BASIC_ISO_DATE)
    val current        = processDateVal.format(datadateFormat).toInt
    val tomorrow       = processDateVal.plusDays(1).format(datadateFormat).toInt
    val yesterday      = processDateVal.minusDays(1).format(datadateFormat).toInt
    val data = Seq(
      Row(1001.0, "B1", "C1", "D1", current), // out of range
      Row(1003.0, "B1", "C1", "D1", tomorrow),
      Row(1004.0, "B1", "C1", "D1", yesterday) // out of range
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(data), matchingSchemaDatadate)

    /** src/it/resources/application.conf
      * extractfunctions: [
      *        { name: "generic_datadate_lt_today", columns: ["datadate"] },
      *     ]
      */
    val expectedData = Seq(
      Row(1004.0, "B1", "C1", "D1", yesterday)
    )
    val expectedDF = spark.createDataFrame(spark.sparkContext.parallelize(expectedData), matchingSchemaDatadate)

    HadoopUtils.writeToExternalTable(
      df,
      tableName,
      "overwrite"
    )
    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply.filter(r => r.sourceTable.sourceName == tableName).head

    actual.sourceDf.head.show()

    actual.sourceDf.head should beEqualTo(expectedDF)
    assert(actual.status.status.id == statusValue.Extracted.id)
  }

  test("It reads data correctly from table with extract functions: generic_timestamp_lt_than_datadate") {
    val tableName = "test_generic_timestamp_lt_datadate"
    val matchingSchema = StructType(
      List(
        StructField("a", DoubleType),
        StructField("b", StringType),
        StructField("c", StringType),
        StructField("d", StringType),
        StructField("event_time", StringType)
      )
    )

    // Suppose datadate = 20200801, so only rows with event_time < "2020-08-01" should be kept
    val data = Seq(
      Row(1001.0, "B1", "C1", "D1", "2020-07-31T23:59:59"), // should be kept
      Row(1002.0, "B1", "C1", "D1", "2020-08-02T00:00:00"), // should be filtered out
      Row(1003.0, "B1", "C1", "D1", "2020-07-30T12:00:00")  // should be kept
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(data), matchingSchema)

    HadoopUtils.writeToExternalTable(
      df,
      tableName,
      "overwrite"
    )

    // Expected: only rows with event_time < "2020-08-01"
    val expectedData = Seq(
      Row(1001.0, "B1", "C1", "D1", "2020-07-31T23:59:59"),
      Row(1003.0, "B1", "C1", "D1", "2020-07-30T12:00:00")
    )
    val expectedDF = spark.createDataFrame(spark.sparkContext.parallelize(expectedData), matchingSchema)

    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply.filter(r => r.sourceTable.sourceName == tableName).head

    actual.sourceDf.head.show()

    actual.sourceDf.head should beEqualTo(expectedDF)
    assert(actual.status.status.id == statusValue.Extracted.id)
  }

  test("It reads data correctly from table with selectColumn using selectExpr") {
    val tableName = "test_select_expr_table"
    val data = Seq(
      Row(1000.0, "B1", "C1", "D1", 20200801),
      Row(2000.0, "B2", "C2", "D2", 20200801)
    )
    val df = spark.createDataFrame(spark.sparkContext.parallelize(data), matchingSchemaDatadate)

    HadoopUtils.writeToExternalTable(
      df,
      s"${DatabaseConstants.SFTPDatabase}.$tableName",
      "overwrite"
    )

    val extractProcessor = new ExtractProcessor(execInfo, arg)
    val actual           = extractProcessor.apply.find(_.sourceTable.sourceName == tableName).get

    // Verify the selectExpr functionality works correctly
    val resultDf = actual.sourceDf.head

    // Check that the columns match what's specified in selectcolumn: ["a", "b", "c", "a * 2 as double_a", "concat(b, '_', c) as b_c"]
    assert(resultDf.columns.sorted === Array("a", "b", "b_c", "c", "double_a").sorted)

    // Verify the expressions were evaluated correctly
    val firstRow = resultDf.first()
    assert(firstRow.getAs[Double]("a") === 1000.0)
    assert(firstRow.getAs[Double]("double_a") === 2000.0) // a * 2
    assert(firstRow.getAs[String]("b_c") === "B1_C1")     // concat(b, '_', c)

    assert(actual.status.status.id == statusValue.Extracted.id)
  }

}
