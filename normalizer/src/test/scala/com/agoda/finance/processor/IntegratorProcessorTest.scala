package com.agoda.finance.processor

import com.agoda.finance.common.utils.HadoopUtilsTrait
import com.agoda.finance.constants.{statusValue, DatabaseConstants, Status}
import com.agoda.finance.preprocess._
import com.agoda.finance.processors.{IntegratorProcessor, ResultsStatus}
import com.agoda.ml.spark.DataFrameMatchers.beEqualTo
import com.agoda.ml.spark.{HiveSupport, SparkSharedLocalTest}
import org.apache.spark.sql.Row
import org.apache.spark.sql.types._
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.doThrow
import org.scalatest.FunSuite
import org.scalatest.Matchers.convertToAnyShouldWrapper
import org.scalatest.mockito.MockitoSugar.mock

import java.sql.Timestamp
import java.time.LocalDate
import java.time.format.DateTimeFormatter

class IntegratorProcessorTest extends FunSuite with SparkSharedLocalTest with HiveSupport {

  setupAll {
    sqlContext.sql(s"CREATE DATABASE IF NOT EXISTS ${DatabaseConstants.SFTPDatabase}")
    sqlContext.sql(s"CREATE DATABASE IF NOT EXISTS ${DatabaseConstants.NormalizedOutputDatabase}")

  }

  teardownAll {
    spark.sql(s"DROP DATABASE IF EXISTS ${DatabaseConstants.SFTPDatabase} CASCADE")
    spark.sql(s"DROP DATABASE IF EXISTS ${DatabaseConstants.NormalizedOutputDatabase} CASCADE")
  }

  lazy val arg      = Arguments(********, ********, "", "", true, "<EMAIL>", "reprocess", "")
  lazy val execInfo = new ExecutionInfo(arg)
  lazy val successStatus =
    Status(
      "test_table",
      s"${DatabaseConstants.SFTPDatabase}.test_table",
      statusValue.Transformed,
      "Transformed",
      false,
      1,
      "********",
      new Timestamp(DateTime.now.getMillis)
    )

  val normalizedSchema = StructType(
    List(
      StructField("ax", DoubleType),
      StructField("bx", StringType),
      StructField("cx", StringType),
      StructField("dx", StringType),
      StructField("datadate", IntegerType),
      StructField("source", StringType)
    )
  )

  test("it should integrate 1 empty df correctly") {
    lazy val resultStatus =
      ResultsStatus(
        List(sqlContext.emptyDataFrame),
        List(),
        successStatus,
        Source(
          "test_table",
          List(TableList(s"${DatabaseConstants.SFTPDatabase}.test_table", Some("datadate"), None, None, None, List.empty)),
          List(),
          List(),
          List(OutputPartition("source", Some("AA"), None))
        ),
        ********
      )

    val integratorProcessor = new IntegratorProcessor(execInfo, arg)
    val result              = integratorProcessor.apply(Seq(resultStatus))

    assert(result.result == sqlContext.emptyDataFrame)
    assert(result.status.head.status.status.id == statusValue.Integrated.id)
  }

  test("it should integrate multiple empty dfs correctly") {
    lazy val resultStatus =
      ResultsStatus(
        List(sqlContext.emptyDataFrame),
        List(),
        successStatus,
        Source(
          "test_table",
          List(TableList(s"${DatabaseConstants.SFTPDatabase}.test_table", Some("datadate"), None, None, None, List.empty)),
          List(),
          List(),
          List(OutputPartition("source", Some("AA"), None))
        ),
        ********
      )
    lazy val resultStatus2 =
      ResultsStatus(
        List(sqlContext.emptyDataFrame),
        List(),
        successStatus,
        Source(
          "test_table",
          List(TableList(s"${DatabaseConstants.SFTPDatabase}.test_table", Some("datadate"), None, None, None, List.empty)),
          List(),
          List(),
          List(OutputPartition("source", Some("AB"), None))
        ),
        ********
      )

    val integratorProcessor = new IntegratorProcessor(execInfo, arg)
    val result              = integratorProcessor.apply(Seq(resultStatus, resultStatus2))

    assert(result.result == sqlContext.emptyDataFrame)
    assert(result.status.head.status.status.id == statusValue.Integrated.id)
  }

  test("it should integrate 1 normalized input source correctly") {
    val data       = Seq(Row(1000.0, "B1", "C1", "D1", ********, "AA"), Row(1000.0, "B2", "C2", "D2", ********, "AA"))
    val inputDF    = spark.createDataFrame(spark.sparkContext.parallelize(data), normalizedSchema)
    val expectedDF = spark.createDataFrame(spark.sparkContext.parallelize(data), normalizedSchema)

    lazy val resultStatus = ResultsStatus(
      List(inputDF),
      List(),
      successStatus,
      Source(
        "test_table",
        List(TableList(s"${DatabaseConstants.SFTPDatabase}.test_table", Some("datadate"), None, None, None, List.empty)),
        List(),
        List(),
        List(OutputPartition("source", Some("AA"), None))
      ),
      ********
    )

    val integratorProcessor = new IntegratorProcessor(execInfo, arg)
    val result              = integratorProcessor.apply(Seq(resultStatus))

    result.result.drop("accounting_date") should beEqualTo(expectedDF)
    assert(result.status.head.status.status.id == statusValue.Integrated.id)
  }

  test("it should integrate input source with accounting_date correctly") {
    val formatter = DateTimeFormatter.ofPattern("yyyyMMdd")
    val date      = LocalDate.parse("********", formatter)
    val data = Seq(
      Row(1000.0, "B1", "C1", "D1", ********, "AA", Timestamp.valueOf(date.atStartOfDay)),
      Row(1000.0, "B2", "C2", "D2", ********, "AA", Timestamp.valueOf(date.atStartOfDay))
    )
    val inputDF    = spark.createDataFrame(spark.sparkContext.parallelize(data), normalizedSchema.add("accounting_date", TimestampType))
    val expectedDF = spark.createDataFrame(spark.sparkContext.parallelize(data), normalizedSchema.add("accounting_date", TimestampType))

    lazy val resultStatus = ResultsStatus(
      List(inputDF),
      List(),
      successStatus,
      Source(
        "test_table",
        List(TableList(s"${DatabaseConstants.SFTPDatabase}.test_table", Some("datadate"), None, None, None, List.empty)),
        List(),
        List(),
        List(OutputPartition("source", Some("AA"), None))
      ),
      ********
    )

    val integratorProcessor = new IntegratorProcessor(execInfo, arg)
    val result              = integratorProcessor.apply(Seq(resultStatus))

    result.result should beEqualTo(expectedDF)
    assert(result.status.head.status.status.id == statusValue.Integrated.id)
  }

  test("it should integrate multiple normalized input sources correctly") {
    val data    = Seq(Row(1000.0, "B1", "C1", "D1", ********, "AA"), Row(1000.0, "B2", "C2", "D2", ********, "AA"))
    val inputDF = spark.createDataFrame(spark.sparkContext.parallelize(data), normalizedSchema)

    val data2    = Seq(Row(1000.0, "B1", "C1", "D1", ********, "AB"), Row(1000.0, "B2", "C2", "D2", ********, "AB"))
    val inputDF2 = spark.createDataFrame(spark.sparkContext.parallelize(data2), normalizedSchema)

    val expectedDF = inputDF.unionByName(inputDF2)

    lazy val resultStatus = ResultsStatus(
      List(inputDF),
      List(),
      successStatus,
      Source(
        "test_table",
        List(TableList(s"${DatabaseConstants.SFTPDatabase}.test_table", Some("datadate"), None, None, None, List.empty)),
        List(),
        List(),
        List(OutputPartition("source", Some("AA"), None))
      ),
      ********
    )
    lazy val resultStatus2 =
      ResultsStatus(
        List(inputDF2),
        List(),
        successStatus,
        Source(
          "test_table2",
          List(TableList(s"${DatabaseConstants.SFTPDatabase}.test_table2", Some("datadate"), None, None, None, List.empty)),
          List(),
          List(),
          List(OutputPartition("source", Some("AB"), None))
        ),
        ********
      )

    val integratorProcessor = new IntegratorProcessor(execInfo, arg)
    val result              = integratorProcessor.apply(Seq(resultStatus, resultStatus2))

    result.result.drop("accounting_date") should beEqualTo(expectedDF)
    assert(result.status.head.status.status.id == statusValue.Integrated.id)
  }

  test("integration test failed and should return integrationFailed for every input") {
    val data    = Seq(Row(1000.0, "B1", "C1", "D1", ********, "AA"), Row(1000.0, "B2", "C2", "D2", ********, "AA"))
    val inputDF = spark.createDataFrame(spark.sparkContext.parallelize(data), normalizedSchema)

    val data2    = Seq(Row(1000.0, "B1", "C1", "D1", ********, "AB"), Row(1000.0, "B2", "C2", "D2", ********, "AB"))
    val inputDF2 = spark.createDataFrame(spark.sparkContext.parallelize(data2), normalizedSchema)

    lazy val resultStatus = ResultsStatus(
      List(inputDF),
      List(),
      successStatus,
      Source(
        "test_table",
        List(TableList(s"${DatabaseConstants.SFTPDatabase}.test_table", Some("datadate"), None, None, None, List.empty)),
        List(),
        List(),
        List(OutputPartition("source", Some("AA"), None))
      ),
      ********
    )
    lazy val resultStatus2 =
      ResultsStatus(
        List(inputDF2),
        List(),
        successStatus,
        Source(
          "test_table2",
          List(TableList(s"${DatabaseConstants.SFTPDatabase}.test_table2", Some("datadate"), None, None, None, List.empty)),
          List(),
          List(),
          List(OutputPartition("source", Some("AB"), None))
        ),
        ********
      )
    val mockHadoopUtil = mock[HadoopUtilsTrait]
    doThrow(new RuntimeException("something wrong"))
      .when(mockHadoopUtil)
      .writeToExternalTable(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())(any(), any())
    val integratorProcessor = new IntegratorProcessor(execInfo, arg, hadoopUtils = mockHadoopUtil)
    val result              = integratorProcessor.apply(Seq(resultStatus, resultStatus2))
    val status = Seq(
      ("test_table", statusValue.IntegrateFailed.id),
      ("test_table2", statusValue.IntegrateFailed.id)
    )

    assert(result.status.map(x => (x.status.sourceName, x.status.status.id)) == status)

  }

  test("it should integrate multiple normalized input sources but different schema") {
    val data    = Seq(Row(1000.0, "B1", "C1", "D1", ********, "AA"), Row(1000.0, "B2", "C2", "D2", ********, "AA"))
    val inputDF = spark.createDataFrame(spark.sparkContext.parallelize(data), normalizedSchema)

    val data2 = Seq(Row(10.0, "B1", "C1", "D1", ********, "AB"), Row(300.0, "B2", "C2", "D2", ********, "AB"))
    // To make it different schema
    val inputDF2 = spark
      .createDataFrame(spark.sparkContext.parallelize(data2), normalizedSchema)
      .drop("dx")

    val data3    = Seq(Row(2000.0, "B1", "C1", "D1", ********, "AC"), Row(100.0, "B2", "C2", "D2", ********, "AC"))
    val inputDF3 = spark.createDataFrame(spark.sparkContext.parallelize(data3), normalizedSchema)

    val data4 = Seq(Row(500.0, "B1", "C1", "D1", ********, "AD"), Row(20.0, "B2", "C2", "D2", ********, "AD"))
    // To make it different schema which is the partition key
    val inputDF4 = spark
      .createDataFrame(spark.sparkContext.parallelize(data4), normalizedSchema)
      .drop("source")

    val expectedDF = inputDF.unionByName(inputDF3)

    lazy val resultStatus = ResultsStatus(
      List(inputDF),
      List(),
      successStatus,
      Source(
        "test_table",
        List(TableList(s"${DatabaseConstants.SFTPDatabase}.test_table", Some("datadate"), None, None, None, List.empty)),
        List(),
        List(),
        List(OutputPartition("source", Some("AA"), None))
      ),
      ********
    )
    lazy val resultStatus2 =
      ResultsStatus(
        List(inputDF2),
        List(),
        successStatus,
        Source(
          "test_table2",
          List(TableList(s"${DatabaseConstants.SFTPDatabase}.test_table2", Some("datadate"), None, None, None, List.empty)),
          List(),
          List(),
          List(OutputPartition("source", Some("AB"), None))
        ),
        ********
      )

    lazy val resultStatus3 =
      ResultsStatus(
        List(inputDF3),
        List(),
        successStatus,
        Source(
          "test_table3",
          List(TableList(s"${DatabaseConstants.SFTPDatabase}.test_table3", Some("datadate"), None, None, None, List.empty)),
          List(),
          List(),
          List(OutputPartition("source", Some("AC"), None))
        ),
        ********
      )

    lazy val resultStatus4 =
      ResultsStatus(
        List(inputDF4),
        List(),
        successStatus,
        Source(
          "test_table4",
          List(TableList(s"${DatabaseConstants.SFTPDatabase}.test_table4", Some("datadate"), None, None, None, List.empty)),
          List(),
          List(),
          List(OutputPartition("source", Some("AD"), None))
        ),
        ********
      )
    val integratorProcessor = new IntegratorProcessor(execInfo, arg)
    val result              = integratorProcessor.apply(Seq(resultStatus, resultStatus2, resultStatus3, resultStatus4))

    result.result.drop("accounting_date") should beEqualTo(expectedDF)

    val status = Seq(
      ("test_table", statusValue.Integrated.id),
      ("test_table2", statusValue.IntegrateFailed.id),
      ("test_table3", statusValue.Integrated.id),
      ("test_table4", statusValue.IntegrateFailed.id)
    )

    assert(result.status.head.status.status.id == statusValue.Integrated.id)
    assert(result.status.map(x => (x.status.sourceName, x.status.status.id)) == status)

  }
}
