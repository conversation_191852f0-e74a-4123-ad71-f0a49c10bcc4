project-name = "delta-normalizer-ap-settlement" # Use in generic_prod.conf and generic_settings.conf
app-name = "DeltaApSettlementNormalizer" # Use in generic_prod.conf

normalizer {
  include "normalizer_mapper.conf"
  sources: [
    "tutuka_upc_reprocess"
  ]

  tutuka_upc_reprocess {
    sourceName: "tutuka_upc_reprocess"
    tableList: [
      {
        tablename: ${reprocess_source_schema}".settlements_reprocess"
        partitiondatekey: "datadate"
      }
    ]
    enrichTableList: [
      {
        tablename: ${old_settlement_schema}".settlements" # Will select by 'report_date' column in datasetlogicfunction 'delta_settlement_calculation'.
      }
    ]
    datasetlogicfunction: [
      {name: "delta_settlement_calculation"},
    ]
    outputpartitionkey: [
      {partitionname: "payment_method_id", colName: "payment_method_id"},
      {partitionname: "product_type", colName: "product_type"},
      {partitionname: "source_name", colName: "source_name"},
      {partitionname: "report_date", colName: "report_date"}
    ]
  }

  table {
    name: ${output_settlement_schema}".settlements"
    mode: "overwrite" // "default is append"
    trackColumns: ["datadate"]
    partition: [
      {
        key: "datadate"
        value: "0" // "[-t,..,0,..,+t]" (default is 0 (current date)) or "accountingdate"
      },
      {
        value: "outputpartitionkey" // Default is outputpartitionkey from each file
      }
    ]
  }
}

tracking-status.tablename: ${tracker_schema}".delta_ap_settlement_normalizer_status"

result.mail {
  alert-sender = "<EMAIL> (<EMAIL>)"
  alert-recipients = "<EMAIL>"
}

hadoop.spark {
  executor {
    count = 15
    cores-per-executor = 6
    memory-per-executor = "10GB"
  }
  driver {
    memory = "20g"
    memory-overhead = 16384
  }
  use-smart-table-loader = true
  knox.enabled = true
}

