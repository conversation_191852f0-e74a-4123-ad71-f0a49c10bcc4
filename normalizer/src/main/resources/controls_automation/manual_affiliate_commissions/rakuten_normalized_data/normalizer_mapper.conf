mappers: [
  {
    aliasColName: "affiliate_id", dataType: "LongType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "affiliate_id" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "affiliate_id" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "affiliate_id" }
    ]
  },
  {
    aliasColName: "affiliate_commission", dataType: "DoubleType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, value: null }
      { sourceName: ${rakuten_pah.sourceName}, value: null }
      { sourceName: ${rakuten_carried_forward.sourceName}, value: null }
    ]
  },
  {
    aliasColName: "affiliate_name", dataType: "StringType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "affiliate_name" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "affiliate_name" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "affiliate_name" }
    ]
  },
  {
    aliasColName: "booking_date", dataType: "StringType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "booking_date" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "booking_date" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "booking_date" }
    ]
  },
  {
    aliasColName: "booking_id", dataType: "LongType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "booking_id" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "booking_id" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "booking_id" }
    ]
  },
  {
    aliasColName: "booking_status", dataType: "StringType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "booking_status" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "booking_status" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "booking_status" }
    ]
  },
  {
    aliasColName: "cancellation_date", dataType: "StringType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "cancellation_date" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "cancellation_date" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "cancellation_date" }
    ]
  },
  {
    aliasColName: "checkin_date", dataType: "StringType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "checkin_date" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "checkin_date" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "checkin_date" }
    ]
  },
  {
    aliasColName: "checkout_date", dataType: "StringType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "checkout_date" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "checkout_date" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "checkout_date" }
    ]
  },
  {
    aliasColName: "commission_base", dataType: "DecimalType", precision: 18, scale: 2,
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "cta" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "cta" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "commission_base" }
    ]
  },
  {
    aliasColName: "country_name", dataType: "StringType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "country_name" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "country_name" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "country_name" }
    ]
  },
  {
    aliasColName: "cta_before_tax_after_discount", dataType: "DecimalType", precision: 18, scale: 2,
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "cta" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "cta" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "cta_before_tax_after_discount" }
    ]
  },
  {
    aliasColName: "discount_amount", dataType: "DoubleType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, value: null }
      { sourceName: ${rakuten_pah.sourceName}, value: null }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "discount_amount" }
    ]
  },
  {
    aliasColName: "hotel_id", dataType: "LongType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "hotel_id" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "hotel_id" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "hotel_id" }
    ]
  },
  {
    aliasColName: "hotel_name", dataType: "StringType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "hotel_name" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "hotel_name" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "hotel_name" }
    ]
  },
  {
    aliasColName: "margin", dataType: "DoubleType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "margin" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "margin" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "margin" }
    ]
  },
  {
    aliasColName: "origin_country_name", dataType: "StringType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "origin_country_name" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "origin_country_name" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "origin_country_name" }
    ]
  },
  {
    aliasColName: "payment_method", dataType: "StringType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "original_payment_model_name" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "original_payment_model_name" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "payment_method" }
    ]
  },
  {
    aliasColName: "partner_currency", dataType: "StringType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, value: "USD" }
      { sourceName: ${rakuten_pah.sourceName}, value: "USD" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "partner_currency" }
    ]
  },
  {
    aliasColName: "selling_amount", dataType: "DoubleType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "selling_amount" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "selling_amount" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "selling_amount" }
    ]
  },
  {
    aliasColName: "site_id", dataType: "LongType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "site_id" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "site_id" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "site_id" }
    ]
  },
  {
    aliasColName: "site_name", dataType: "StringType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "site_name" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "site_name" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "site_name" }
    ]
  },
  {
    aliasColName: "tag", dataType: "StringType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, colName: "tag" }
      { sourceName: ${rakuten_pah.sourceName}, colName: "tag" }
      { sourceName: ${rakuten_carried_forward.sourceName}, colName: "tag" }
    ]
  },
  {
    aliasColName: "accrued_datamonth", dataType: "IntegerType",
    colMapper: [
      { sourceName: ${rakuten_paa.sourceName}, value: null }
      { sourceName: ${rakuten_pah.sourceName}, value: null }
      { sourceName: ${rakuten_carried_forward.sourceName}, function: {
          name: "generic_coalesce", columns: ["accrued_datamonth", "datamonth"]
        }
      }
    ]
  }
]