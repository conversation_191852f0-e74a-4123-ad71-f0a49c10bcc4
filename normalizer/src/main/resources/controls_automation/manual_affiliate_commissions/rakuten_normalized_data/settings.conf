project-name = "manual-affiliate-commissions" 
app-name = "Rakuten_Normalizer"

normalizer: {
  include "normalizer_mapper.conf"
  sources: [
    "rakuten_paa"
    ,"rakuten_pah"
    ,"rakuten_carried_forward"
  ]

  rakuten_paa {
    sourceName: "rakuten_paa"
    tableList: [
      {
        tablename: "bi_dw.fact_booking"
        selectcolumn: ["booking_id", "original_payment_model_name", "margin"
          "cancellation_date", "checkin_date", "checkout_date",
          "booking_date", "country_name", "hotel_id", "hotel_name",
          "origin_country_name", "booking_status", "selling_amount", "datamonth"]
        extractfunctions: [
          { name: "generic_offset_datamonth_gte", constants: ["18"], columns: ["datamonth"] }, # up to 18 months before cycle month
          { name: "generic_is_not_contain", constants: ["Pay Hotel"], columns: ["original_payment_model_name"] },
          # extracting dates withing the processed month
          { name: "generic_offset_date_greater_than_process_month", constants: ["1", "yyyy-MM-dd"], columns: ["checkout_date"] },
          { name: "generic_offset_date_less_than_process_month", constants: ["1", "yyyy-MM-dd"], columns: ["checkout_date"] }
        ]
      },
      {
        tablename: "bi_dw.fact_booking_attribution"
        extractfunctions: [
          { name: "generic_is_in", constants: ["22"], columns: ["model_id"] },
            { name: "generic_offset_datamonth_gte", constants: ["18"], columns: ["datamonth"] }
        ]
      },
      {
        tablename: "bi_dw.fact_booking_financial_breakdown"
        extractfunctions: [
          { name: "generic_is_positive", columns: ["quantity"] },
          { name: "generic_offset_datamonth_gte", constants: ["18"], columns: ["datamonth"] },
          # The combination of the following conditions was the denormalization result of a single OR function
          { name: "generic_is_in", constants: ["11", "6", "17"], columns: ["item_id"] },
          { name: "generic_multiconstants_or_logical", columns: ["item_id", "type_id"], multiconstants = [["11"], ["5"]] }
        ]
      },
      {
        tablename: "bi_dw.dim_site"
        extractfunctions: [
          { name: "generic_is_in", constants: ["185817", "217645"], columns: ["affiliate_id"] }
        ]
      }
    ]

    datasetlogicfunction: [
      {
        name: "generic_join",
        args: {
          select: [
            {
              table: "bi_dw.fact_booking",
              columns: ["booking_id", "original_payment_model_name", "margin"
                "cancellation_date", "checkin_date", "checkout_date",
                "booking_date", "country_name", "hotel_id", "hotel_name",
                "origin_country_name", "booking_status", "selling_amount"]
            },
            {
              table: "bi_dw.dim_site"
              columns: ["affiliate_id", "affiliate_name", "site_id", "site_name"]
            },
            {
              table: "bi_dw.fact_booking_attribution"
              columns: ["tag", "model_id"]
            },
            {
              table: "bi_dw.fact_booking_financial_breakdown"
              columns: ["usd_amount", "quantity", "item_id", "type_id"]
            }
          ],
          joins: [
            {
              left: "bi_dw.fact_booking"
              right: "bi_dw.fact_booking_attribution"
              conditions: [
                { left: "booking_id", right: "booking_id" },
                { left: "datamonth", right: "datamonth" }
              ]
              joinType: "inner"
            },
            {
              left: "bi_dw.fact_booking"
              right: "bi_dw.fact_booking_financial_breakdown"
              conditions: [
                { left: "booking_id", right: "booking_id" },
                { left: "datamonth", right: "datamonth" }
              ]
              joinType: "inner"
            },
            {
              left: "bi_dw.fact_booking_attribution"
              right: "bi_dw.dim_site"
              conditions: [
                { left: "site_id", right: "site_id" }
              ]
              joinType: "inner"
            }
          ]
        }
      },
      {
        name: "generic_aggregate",
        args: {
          aggregators: [
            {name: "sum", function: { name: "generic_multiply", columns: ["usd_amount", "quantity"] }, alias: "cta" }
          ],
          groupBy: [
            "booking_id", "affiliate_id", "affiliate_name",
            "site_id", "site_name", "original_payment_model_name",
            "margin", "tag", "cancellation_date",
            "checkin_date", "checkout_date", "booking_date",
            "country_name", "hotel_id", "hotel_name", "origin_country_name",
            "booking_status", "selling_amount"
          ]
        }
      }
    ]
  }

  rakuten_pah {
    sourceName: "rakuten_pah"
    tableList: [
      {
        tablename: "bi_dw.fact_booking"
        selectcolumn: ["booking_id", "original_payment_model_name", "margin"
          "cancellation_date", "checkin_date", "checkout_date",
          "booking_date", "country_name", "hotel_id", "hotel_name",
          "origin_country_name", "booking_status", "selling_amount", "datamonth"]
        extractfunctions: [
          { name: "generic_offset_datamonth_gte", constants: ["18"], columns: ["datamonth"] }, # up to 18 months before cycle month
          { name: "generic_is_in", constants: ["Pay Hotel"], columns: ["original_payment_model_name"] },
          # extracting dates withing the processed month
          { name: "generic_offset_date_greater_than_process_month", constants: ["2", "yyyy-MM-dd"], columns: ["checkout_date"] },
          { name: "generic_offset_date_less_than_process_month", constants: ["2", "yyyy-MM-dd"], columns: ["checkout_date"] }
        ]
      },
      {
        tablename: "bi_dw.fact_booking_attribution"
        extractfunctions: [
          { name: "generic_is_in", constants: ["22"], columns: ["model_id"] },
          { name: "generic_offset_datamonth_gte", constants: ["18"], columns: ["datamonth"] }
        ]
      },
      {
        tablename: "bi_dw.fact_booking_financial_breakdown"
        extractfunctions: [
          { name: "generic_offset_datamonth_gte", constants: ["18"], columns: ["datamonth"] },
          { name: "generic_is_positive", columns: ["quantity"] },
          # The combination of the following conditions was the denormalization result of a single OR function
          { name: "generic_is_in", constants: ["11", "6", "17"], columns: ["item_id"] },
          { name: "generic_multiconstants_or_logical", columns: ["item_id", "type_id"], multiconstants = [["11"], ["5"]] }
        ]
      },
      {
        tablename: "bi_dw.dim_site"
        extractfunctions: [
          { name: "generic_is_in", constants: ["185817", "217645"], columns: ["affiliate_id"] }
        ]
      }
    ]

    datasetlogicfunction: [
      {
        name: "generic_join",
        args: {
          select: [
            {
              table: "bi_dw.fact_booking",
              columns: ["booking_id", "original_payment_model_name", "margin"
                "cancellation_date", "checkin_date", "checkout_date",
                "booking_date", "country_name", "hotel_id", "hotel_name",
                "origin_country_name", "booking_status", "selling_amount"]
            },
            {
              table: "bi_dw.dim_site"
              columns: ["affiliate_id", "affiliate_name", "site_id", "site_name"]
            },
            {
              table: "bi_dw.fact_booking_attribution"
              columns: ["tag", "model_id"]
            },
            {
              table: "bi_dw.fact_booking_financial_breakdown"
              columns: ["usd_amount", "quantity", "item_id", "type_id"]
            }
          ],
          joins: [
            {
              left: "bi_dw.fact_booking"
              right: "bi_dw.fact_booking_attribution"
              conditions: [
                { left: "booking_id", right: "booking_id" },
                { left: "datamonth", right: "datamonth" }
              ]
              joinType: "inner"
            },
            {
              left: "bi_dw.fact_booking"
              right: "bi_dw.fact_booking_financial_breakdown"
              conditions: [
                { left: "booking_id", right: "booking_id" },
                { left: "datamonth", right: "datamonth" }
              ]
              joinType: "inner"
            },
            {
              left: "bi_dw.fact_booking_attribution"
              right: "bi_dw.dim_site"
              conditions: [
                { left: "site_id", right: "site_id" }
              ]
              joinType: "inner"
            }
          ]
        }
      },
      {
        name: "generic_aggregate",
        args: {
          aggregators: [
            {name: "sum", function: { name: "generic_multiply", columns: ["usd_amount", "quantity"] }, alias: "cta" }
          ],
          groupBy: [
            "booking_id", "affiliate_id", "affiliate_name",
            "site_id", "site_name", "original_payment_model_name",
            "margin", "tag", "cancellation_date",
            "checkin_date", "checkout_date", "booking_date",
            "country_name", "hotel_id", "hotel_name", "origin_country_name",
            "booking_status", "selling_amount"
          ]
        }
      }
    ]
  }

  rakuten_carried_forward {
    sourceName: "rakuten_carried_forward"
    tableList: [
      {
        tablename: ${schema}".network_partner_transactions"
        extractfunctions: [
          { name: "generic_offset_datamonth", constants: ["1"], columns: ["datamonth"] }, # previous cycle month
          { name: "generic_is_in", constants: ["payment"], columns: ["transaction_type"] },
          { name: "generic_is_in", constants: ["rakuten"], columns: ["common_affiliate_name"] }
        ]
      },
      {
        tablename: ${schema}".final_accrual_payment_transactions"
        selectcolumn: ["booking_id", "datamonth"]
        extractfunctions: [
          { name: "generic_offset_datamonth", constants: ["1"], columns: ["datamonth"] }, # previous cycle month
          { name: "generic_is_in", constants: ["rakuten"], columns: ["common_affiliate_name"] },
          { name: "generic_is_in", constants: ["payment"], columns: ["transaction_type"] },
          { name: "generic_is_null_or_empty", columns: ["partner_commission_amount"] },
          { name: "generic_is_not_null", columns: ["agoda_commission_amount_usd"] }
        ]
      }
    ]

    datasetlogicfunction: [
      {
        name: "generic_join",
        args: {
          select: [
            {
              table: ${schema}".network_partner_transactions",
              columns: [
                "affiliate_id", "affiliate_commission", "affiliate_name", "booking_date", "booking_id",
                "booking_status", "cancellation_date", "checkin_date", "checkout_date", "commission_base",
                "country_name", "cta_before_tax_after_discount", "discount_amount", "hotel_id", "hotel_name",
                "margin", "origin_country_name", "payment_method", "partner_currency", "selling_amount",
                "site_id", "site_name", "tag", "accrued_datamonth"
              ]
            },
            {
              table: ${schema}".final_accrual_payment_transactions",
              columns: ["datamonth"]
            }
          ],
          joins: [
            {
              left: ${schema}".network_partner_transactions"
              right: ${schema}".final_accrual_payment_transactions"
              conditions: [
                { left: "booking_id", right: "booking_id" }
              ]
              joinType: "inner"
            }
          ]
        }
      }
    ]
  }

#================= Output table ===============
  table: {
    name: ${schema}".network_partner_transactions_staging"
    mode: "overwrite" // "default is append"
    trackColumns: ["datamonth"]
    partition: [
      {
        key: "datamonth"
        value: "accountingdate"
      },
      {
        key: "transaction_type"
        value: ${transaction_type}
      },
      {
        key: "common_affiliate_name"
        value: "rakuten"
      }
    ]
  }
  #=============================================
}

tracking-status {
  tablename: ${schema}".network_partner_transactions_staging_status"
}

result {
  mail {
    alert-sender = "<EMAIL> (<EMAIL>)"
    alert-recipients = "<EMAIL>"
  }
}