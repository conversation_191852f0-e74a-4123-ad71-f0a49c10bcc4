mappers: [
  {
    aliasColName: "booking_id", dataType: "LongType",
    colMapper: [
      { sourceName: ${pah_reports_staging.sourceName}, colName: "booking_id"}
    ]
  },
  {
      aliasColName: "booking_status", dataType: "StringType",
      colMapper: [
        { sourceName: ${pah_reports_staging.sourceName}, colName: "booking_status"}
      ]
  },
  {
      aliasColName: "affiliate_id", dataType: "LongType",
      colMapper: [
        { sourceName: ${pah_reports_staging.sourceName}, colName: "affiliate_id"}
      ]
  },
  {
      aliasColName: "affiliate_name", dataType: "StringType",
      colMapper: [
        { sourceName: ${pah_reports_staging.sourceName}, colName: "affiliate_name"}
      ]
  },
  {
      aliasColName: "site_id", dataType: "LongType",
      colMapper: [
        { sourceName: ${pah_reports_staging.sourceName}, colName: "site_id"}
      ]
  },
  {
      aliasColName: "origin_country_name", dataType: "StringType",
      colMapper: [
        { sourceName: ${pah_reports_staging.sourceName}, colName: "origin_country_name"}
      ]
  },
  {
      aliasColName: "hotel_name", dataType: "StringType",
      colMapper: [
        { sourceName: ${pah_reports_staging.sourceName}, colName: "hotel_name"}
      ]
  },
  {
      aliasColName: "booking_date", dataType: "StringType",
      colMapper: [
        { sourceName: ${pah_reports_staging.sourceName}, colName: "booking_date"}
      ]
  },
  {
      aliasColName: "checkin_date", dataType: "StringType",
      colMapper: [
        { sourceName: ${pah_reports_staging.sourceName}, colName: "checkin_date"}
      ]
  },
  {
      aliasColName: "checkout_date", dataType: "StringType",
      colMapper: [
        { sourceName: ${pah_reports_staging.sourceName}, colName: "checkout_date"}
      ]
  },
  {
      aliasColName: "payment_model", dataType: "StringType",
      colMapper: [
        { sourceName: ${pah_reports_staging.sourceName}, colName: "payment_model"}
      ]
  },
  {
      aliasColName: "margin", dataType: "DoubleType",
      colMapper: [
        { sourceName: ${pah_reports_staging.sourceName}, colName: "margin"}
      ]
  },
  {
      aliasColName: "supplier_name", dataType: "StringType",
      colMapper: [
        { sourceName: ${pah_reports_staging.sourceName}, colName: "supplier_name"}
      ]
  },
  {
      aliasColName: "tag", dataType: "StringType",
      colMapper: [
        { sourceName: ${pah_reports_staging.sourceName}, colName: "tag"}
      ]
  },
  {
      aliasColName: "destination", dataType: "StringType",
      colMapper: [
        { sourceName: ${pah_reports_staging.sourceName}, colName: "destination"}
      ]
  },
  {
      aliasColName: "currency", dataType: "StringType",
      colMapper: [
        { sourceName: ${pah_reports_staging.sourceName}, value: "USD" }
      ]
  },
  {
      aliasColName: "commission_amount", dataType: "DoubleType"
      colMapper: [
        { sourceName: ${pah_reports_staging.sourceName}, colName: "commission_amount"}
      ]
  },
  {
      aliasColName: "common_affiliate_name", dataType: "StringType",
      colMapper: [
        { sourceName: ${pah_reports_staging.sourceName},
          function: {name: "mapping_pah_affiliate_name" , columns: ["affiliate_id"]}
        }
     ]
  },
]