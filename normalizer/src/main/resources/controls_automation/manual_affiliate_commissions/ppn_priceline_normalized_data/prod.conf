include classpath("_generic_prod.conf")
include "settings.conf"

current_env="prod"
schema="affiliate_commission_accrual_control"

app.adpMessaging.appName = "normalizer.prod"

hadoop {
  hdfs.user = "hk-conaut-svc"
  knox.enabled = true
  hive.username = "hk-conaut-svc"
  credentials = "hadoop/hk-conaut-svc/credentials"
}


slack-config.sources = {
    allSupplier: ["orchestration-platform-support"]
}
