project-name = "manual-affiliate-commissions"
app-name = "Flight_Normalizer"

normalizer: {
  include "normalizer_mapper.conf"

  # Required by alert guidelines: https://gitlab.agodadev.io/FinancePlatform/reconciliation-platform/external-data-pipeline/-/blob/master/ag-docs/normalizer/developer_guidelines/alert_guideline.md
  job_frequency = Monthly

  sources: [
    "shopback_flight",
    "shopback_flight_351908"
  ]

  shopback_flight {
    sourceName: "shopback_flight"
    tableList: [
      {
         tablename: "bi_dw.fact_booking_flights",
         extractfunctions: [
            { name: "generic_offset_datamonth_gte", constants: ["25"], columns: ["datamonth"] }, # up to 2 years before process date
            { name: "generic_is_equal_to", constants: ["0"], columns: ["booking_flag"]},
            { name: "generic_is_equal_to", constants: ["1"], columns: ["is_completed_booking"]},
            { name: "generic_is_equal_to", constants: ["false"], columns: ["is_cancelled"]},
            { name: "generic_is_in", constants: ["215579"], columns: ["lpc_30_affiliate_id"]},
            { name: "generic_is_not_match_with_regex", constants: ["(?i).*PACKAGE.*"], columns: ["multi_product_type_name"]},
            { name: "generic_offset_date_greater_than_process_month", constants: ["1", "yyyy-MM-dd"], columns: ["trip_end_date"] },
            { name: "generic_offset_date_less_than_process_month", constants: ["1", "yyyy-MM-dd"], columns: ["trip_end_date"] }
         ]
      }
    ]
  }

  shopback_flight_351908 {
      sourceName: "shopback_flight_351908"
      tableList: [
        {
           tablename: "bi_dw.fact_booking_flights",
           extractfunctions: [
              { name: "generic_offset_datamonth_gte", constants: ["25"], columns: ["datamonth"] }, # up to 2 years before process date
              { name: "generic_is_equal_to", constants: ["0"], columns: ["booking_flag"]},
              { name: "generic_is_equal_to", constants: ["1"], columns: ["is_completed_booking"]},
              { name: "generic_is_equal_to", constants: ["false"], columns: ["is_cancelled"]},
              { name: "generic_is_in", constants: ["351908"], columns: ["lpc_30_affiliate_id"]},
              { name: "generic_is_in", constants: ["1943995","1943996","1943997","1943998"], columns: ["lpc_30_site_id"]},
              { name: "generic_is_not_match_with_regex", constants: ["(?i).*PACKAGE.*"], columns: ["multi_product_type_name"]},
              { name: "generic_offset_date_greater_than_process_month", constants: ["1", "yyyy-MM-dd"], columns: ["trip_end_date"] },
              { name: "generic_offset_date_less_than_process_month", constants: ["1", "yyyy-MM-dd"], columns: ["trip_end_date"] }
           ]
        }
      ]
    }

#================= Output table ===============
  table: {
    name: ${schema}".flight_transactions_staging"
    mode: "overwrite" # "default is append"
    trackColumns: ["datamonth"]
    partition: [
       { key: "datamonth", value: "accountingdate" },
       { key: "transaction_type", value: ${transaction_type} }
    ]
  }
  #=============================================
}

tracking-status {
  tablename: ${schema}".flight_transactions_staging_status"
}

result {
  mail {
    alert-sender = "<EMAIL> (<EMAIL>)"
    alert-recipients = "<EMAIL>"
  }
}