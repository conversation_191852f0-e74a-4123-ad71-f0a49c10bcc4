mappers: [
  {
    aliasColName: "booking_id", dataType: "LongType",
    colMapper: [
      { sourceName: ${shopback_flight.sourceName}, colName: "booking_id"},
      { sourceName: ${shopback_flight_351908.sourceName}, colName: "booking_id"}
    ]
  },
  {
    aliasColName: "affiliate_id", dataType: "LongType",
    colMapper: [
      { sourceName: ${shopback_flight.sourceName}, colName: "lpc_30_affiliate_id"},
      { sourceName: ${shopback_flight_351908.sourceName}, colName: "lpc_30_affiliate_id"}
    ]
  },
  {
    aliasColName: "affiliate_name", dataType: "StringType",
    colMapper: [
      { sourceName: ${shopback_flight.sourceName}, colName: "lpc_30_affiliate_name"},
      { sourceName: ${shopback_flight_351908.sourceName}, colName: "lpc_30_affiliate_name"}
    ]
  },
  {
    aliasColName: "site_id", dataType: "LongType",
    colMapper: [
      { sourceName: ${shopback_flight.sourceName}, colName: "lpc_30_site_id"},
      { sourceName: ${shopback_flight_351908.sourceName}, colName: "lpc_30_site_id"}
    ]
  },
  {
    aliasColName: "site_name", dataType: "StringType",
    colMapper: [
      { sourceName: ${shopback_flight.sourceName}, colName: "lpc_30_site_name"},
      { sourceName: ${shopback_flight_351908.sourceName}, colName: "lpc_30_site_name"}
    ]
  },
  {
    aliasColName: "itinerary_id", dataType: "LongType",
    colMapper: [
      { sourceName: ${shopback_flight.sourceName}, colName: "itinerary_id"},
      { sourceName: ${shopback_flight_351908.sourceName}, colName: "itinerary_id"}
    ]
  },
  {
    aliasColName: "flight_state", dataType: "StringType",
    colMapper: [
      { sourceName: ${shopback_flight.sourceName}, colName: "flight_state"},
      { sourceName: ${shopback_flight_351908.sourceName}, colName: "flight_state"}
    ]
  },
  {
    aliasColName: "is_cancelled", dataType: "BooleanType",
    colMapper: [
      { sourceName: ${shopback_flight.sourceName}, colName: "is_cancelled"},
      { sourceName: ${shopback_flight_351908.sourceName}, colName: "is_cancelled"}
    ]
  },
  {
    aliasColName: "tag", dataType: "StringType",
    colMapper: [
      { sourceName: ${shopback_flight.sourceName}, colName: "lpc_30_tag"},
      { sourceName: ${shopback_flight_351908.sourceName}, colName: "lpc_30_tag"}
    ]
  },
  {
    aliasColName: "booking_date", dataType: "StringType",
    colMapper: [
      { sourceName: ${shopback_flight.sourceName}, colName: "booking_date"},
      { sourceName: ${shopback_flight_351908.sourceName}, colName: "booking_date"}
    ]
  },
  {
    aliasColName: "trip_start_date", dataType: "StringType",
    colMapper: [
      { sourceName: ${shopback_flight.sourceName}, colName: "trip_start_date"},
      { sourceName: ${shopback_flight_351908.sourceName}, colName: "trip_start_date"}
    ]
  },
  {
    aliasColName: "trip_end_date", dataType: "StringType",
    colMapper: [
      { sourceName: ${shopback_flight.sourceName}, colName: "trip_end_date"},
      { sourceName: ${shopback_flight_351908.sourceName}, colName: "trip_end_date"}
    ]
  },
  {
    aliasColName: "origin_country_name", dataType: "StringType",
    colMapper: [
      { sourceName: ${shopback_flight.sourceName}, colName: "origin_country_name"},
      { sourceName: ${shopback_flight_351908.sourceName}, colName: "origin_country_name"}
    ]
  },
  {
    aliasColName: "trip_destination_country_name", dataType: "StringType",
    colMapper: [
      { sourceName: ${shopback_flight.sourceName}, colName: "trip_destination_country_name"},
      { sourceName: ${shopback_flight_351908.sourceName}, colName: "trip_destination_country_name"}
    ]
  },
  {
    aliasColName: "original_selling_amount_exclusive", dataType: "DoubleType",
    colMapper: [
      { sourceName: ${shopback_flight.sourceName}, colName: "original_selling_amount_exclusive"},
      { sourceName: ${shopback_flight_351908.sourceName}, colName: "original_selling_amount_exclusive"}
    ]
  },
  {
      aliasColName: "supplier_amount", dataType: "DoubleType",
      colMapper: [
        { sourceName: ${shopback_flight.sourceName}, colName: "supplier_amount"},
        { sourceName: ${shopback_flight_351908.sourceName}, colName: "supplier_amount"}
      ]
  },
  {
      aliasColName: "original_selling_amount", dataType: "DoubleType",
      colMapper: [
        { sourceName: ${shopback_flight.sourceName}, colName: "original_selling_amount"},
        { sourceName: ${shopback_flight_351908.sourceName}, colName: "original_selling_amount"}
      ]
  },
  {
      aliasColName: "tax_amount", dataType: "DoubleType",
      colMapper: [
        { sourceName: ${shopback_flight.sourceName}, colName: "tax_amount"},
        { sourceName: ${shopback_flight_351908.sourceName}, colName: "tax_amount"}
      ]
  },
  {
      aliasColName: "supplier_basefare_amount", dataType: "DoubleType",
      colMapper: [
        { sourceName: ${shopback_flight.sourceName}, colName: "supplier_basefare_amount"},
        { sourceName: ${shopback_flight_351908.sourceName}, colName: "supplier_basefare_amount"}
      ]
  },
  {
    aliasColName: "commission_base", dataType: "DoubleType",
    colMapper: [
        { sourceName: ${shopback_flight.sourceName}, colName: "original_selling_amount_exclusive"},
        { sourceName: ${shopback_flight_351908.sourceName}, colName: "original_selling_amount_exclusive"}
    ]
  },
  {
    aliasColName: "common_affiliate_name", dataType: "StringType",
    colMapper: [
        { sourceName: ${shopback_flight.sourceName}, value: "shopback flight"},
        { sourceName: ${shopback_flight_351908.sourceName}, value: "shopback flight"}
    ]
  },
  {
    aliasColName: "booking_status", dataType: "StringType",
    colMapper: [
       { sourceName: ${shopback_flight.sourceName}, value: "Departed"},
       { sourceName: ${shopback_flight_351908.sourceName}, value: "Departed"}
    ]
  },
  {
    aliasColName: "payment_method", dataType: "StringType",
    colMapper: [
       { sourceName: ${shopback_flight.sourceName}, colName: "original_payment_method_name"},
       { sourceName: ${shopback_flight_351908.sourceName}, colName: "original_payment_method_name"}
    ]
  },
  {
    aliasColName: "datamonth", dataType: "IntegerType",
    colMapper: [
       { sourceName: ${shopback_flight.sourceName}, colName: "datamonth"},
       { sourceName: ${shopback_flight_351908.sourceName}, colName: "datamonth"}
    ]
  }
]