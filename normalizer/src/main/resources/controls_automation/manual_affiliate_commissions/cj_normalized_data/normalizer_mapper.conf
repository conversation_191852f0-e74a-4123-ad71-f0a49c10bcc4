mappers: [
  {
    aliasColName: "affiliate_id", dataType: "LongType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "affiliate_id" },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "affiliate_id" }
    ]
  },
  {
    aliasColName: "affiliate_commission", dataType: "DoubleType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "affiliate_commision" }, # col name has typo in source table
      { sourceName: ${cj_carried_forward.sourceName}, colName: "affiliate_commission" }
    ]
  },
  {
    aliasColName: "affiliate_name", dataType: "StringType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "affiliate_name" },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "affiliate_name" }
    ]
  },
  {
    aliasColName: "booking_date", dataType: "StringType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "booking_date" },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "booking_date" }
    ]
  },
  {
    aliasColName: "booking_id", dataType: "LongType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "booking_id" },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "booking_id" }
    ]
  },
  {
    aliasColName: "booking_status", dataType: "StringType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "booking_status" },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "booking_status" }
    ]
  },
  {
    aliasColName: "cancellation_date", dataType: "StringType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "cancellation_date" },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "cancellation_date" }
    ]
  },
  {
    aliasColName: "checkin_date", dataType: "StringType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "checkin_date" },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "checkin_date" }
    ]
  },
  {
    aliasColName: "checkout_date", dataType: "StringType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "checkout_date" },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "checkout_date" }
    ]
  },
  {
    aliasColName: "commission_base", dataType: "DecimalType", precision: 18, scale: 2,
    colMapper: [
      { sourceName: ${cj.sourceName}, function: { name: "generic_advance_sum", columns: ["selling_amount_exclusive", "discount_amount"], constants: ["+,-"] } },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "commission_base" }
    ]
  },
  {
    aliasColName: "country_name", dataType: "StringType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "country_name" },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "country_name" }
    ]
  },
  {
    aliasColName: "cta_before_tax_after_discount", dataType: "DecimalType", precision: 18, scale: 2,
    colMapper: [
      { sourceName: ${cj.sourceName}, function: { name: "generic_advance_sum", columns: ["selling_amount_exclusive", "discount_amount"], constants: ["+,-"] } },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "cta_before_tax_after_discount" }
    ]
  },
  {
    aliasColName: "discount_amount", dataType: "DoubleType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "discount_amount" },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "discount_amount" }
    ]
  },
  {
    aliasColName: "hotel_id", dataType: "LongType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "hotel_id" },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "hotel_id" }
    ]
  },
  {
    aliasColName: "hotel_name", dataType: "StringType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "hotel_name" },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "hotel_name" }
    ]
  },
  {
    aliasColName: "margin", dataType: "DoubleType",
    colMapper: [
      { sourceName: ${cj.sourceName}, value: null },
      { sourceName: ${cj_carried_forward.sourceName}, value: null }
    ]
  },
  {
    aliasColName: "origin_country_name", dataType: "StringType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "origin_country_name" },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "origin_country_name" }
    ]
  },
  {
    aliasColName: "payment_method", dataType: "StringType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "original_payment_model_name" },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "payment_method" }
    ]
  },
  {
    aliasColName: "partner_currency", dataType: "StringType",
    colMapper: [
      { sourceName: ${cj.sourceName}, value: "USD" },
      { sourceName: ${cj_carried_forward.sourceName}, value: "USD" }
    ]
  },
  {
    aliasColName: "selling_amount", dataType: "DoubleType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "selling_amount" },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "selling_amount" }
    ]
  },
  {
    aliasColName: "site_id", dataType: "LongType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "lpc_30_site_id" },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "site_id" }
    ]
  },
  {
    aliasColName: "site_name", dataType: "StringType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "site_name" },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "site_name" }
    ]
  },
  {
    aliasColName: "tag", dataType: "StringType",
    colMapper: [
      { sourceName: ${cj.sourceName}, colName: "tag" },
      { sourceName: ${cj_carried_forward.sourceName}, colName: "tag" }
    ]
  },
  {
    aliasColName: "accrued_datamonth", dataType: "IntegerType",
    colMapper: [
      { sourceName: ${cj.sourceName}, value: null },
      { sourceName: ${cj_carried_forward.sourceName}, function: {
          name: "generic_coalesce", columns: ["accrued_datamonth", "datamonth"]
        }
      }
    ]
  }
]