project-name = "manual-affiliate-commissions" 
app-name = "CJ_Normalizer"

normalizer: {
  include "normalizer_mapper.conf"
  sources: [
    "cj",
    "cj_carried_forward"
  ]

  cj {
    sourceName: "cj"
    tableList: [
      {
        tablename: "affiliate.afl_booking_report_sql_casper"
        extractfunctions: [
          # extracting all records from past 25 months
          { name: "generic_offset_date_greater_than_process_month", constants: ["18", "yyyyMM"], columns: ["master"] },
          { name: "generic_offset_date_less_than_process_month", constants: ["0", "yyyyMM"], columns: ["master"] },
          # extracting dates withing the processed month
          { name: "generic_offset_date_greater_than_process_month", constants: ["0", "yyyy-MM-dd"], columns: ["payment_due_date"] },
          { name: "generic_offset_date_less_than_process_month", constants: ["0", "yyyy-MM-dd"], columns: ["payment_due_date"] },
          { name: "generic_is_in", constants: ["22"], columns: ["attribution_model_id"] }
        ]
      },
      {
        tablename: "bi_dw.fact_booking"
        selectcolumn: ["lpc_30_site_id", "booking_date", "checkin_date",
                "checkout_date", "selling_amount", "selling_amount_exclusive", "discount_amount",
                "booking_status", "booking_id", "origin_country_name", "original_payment_model_name",
                "country_name", "hotel_id", "hotel_name", "cancellation_date", "datamonth"]
        extractfunctions: [
          { name: "generic_offset_datamonth_gte", constants: ["18"], columns: ["datamonth"] }, # up to 18 months before cycle month
          { name: "generic_is_not_in", constants: ["10"], columns: ["booking_status_id"] },
          { name: "generic_is_positive", columns: ["booking_id"] },
          { name: "generic_is_not_in", constants: ["2", "3"], columns: ["booking_flag"] }
        ]
      },
      {
        tablename: "bi_dw.dim_site"
      },
      {
        tablename: "bi_pricing.pricing_bookings_downliftrule"
        extractfunctions: [
          # extracting all relevant historical records
          { name: "generic_offset_date_greater_than_process_month", constants: ["18", "yyyyMMdd"], columns: ["datadate"] },
          { name: "generic_offset_date_less_than_process_month", constants: ["0", "yyyyMMdd"], columns: ["datadate"] },
          { name: "generic_is_not_in", constants: ["5", "7"], columns: ["trafficgroup"] }
        ]
      }
    ]

    datasetlogicfunction: [
      {
        name: "generic_join",
        args: {
          select: [
            {
              table: "bi_dw.dim_site",
              columns: ["affiliate_id", "affiliate_name", "site_name"]
            },
            {
              table: "bi_dw.fact_booking",
              columns: ["lpc_30_site_id", "booking_date", "checkin_date",
                "checkout_date", "selling_amount", "selling_amount_exclusive", "discount_amount",
                "booking_status", "booking_id", "origin_country_name", "original_payment_model_name",
                "country_name", "hotel_id", "hotel_name", "cancellation_date"]
            },
            {
              table: "affiliate.afl_booking_report_sql_casper",
              columns: ["tag", "affiliate_commision"]
            }
          ],
          joins: [
            {
              left: "affiliate.afl_booking_report_sql_casper"
              right: "bi_dw.fact_booking"
              conditions: [
                { left: "booking_id", right: "booking_id" },
                { left: "master", right: "datamonth" }
              ]
              joinType: "inner"
            },
            {
              left: "bi_dw.fact_booking"
              right: "bi_dw.dim_site"
              conditions: [
                { left: "lpc_30_site_id", right: "site_id" }
              ]
              joinType: "inner"
            },
            {
              left: "bi_dw.fact_booking"
              right: "bi_pricing.pricing_bookings_downliftrule"
              conditions: [
                { left: "booking_id", right: "booking_id" }
              ]
              joinType: "inner"
            },
          ]
        }
      },
      {
        name: "generic_filter",
        args: {
          filters: [
            { 
              name: "generic_multiconstants_or_logical", 
              columns: ["lpc_30_site_id", "affiliate_id"], 
              multiconstants: [
                ["1841938","1841941","1841944","1902203",
                "1902205","1902206","1917463","1917464",
                "1917477","1841945","1841940","1919200",
                "1841943","1920572"],
                ["261856","305804"]
              ]
            }
          ]
        }
      }
    ]
  }

  cj_carried_forward {
    sourceName: "cj_carried_forward"
    tableList: [
      {
        tablename: ${schema}".network_partner_transactions"
        extractfunctions: [
          { name: "generic_offset_datamonth", constants: ["1"], columns: ["datamonth"] }, # previous cycle month
          { name: "generic_is_in", constants: ["payment"], columns: ["transaction_type"] },
          { name: "generic_is_in", constants: ["cj"], columns: ["common_affiliate_name"] }
        ]
      },
      {
        tablename: ${schema}".final_accrual_payment_transactions"
        selectcolumn: ["booking_id", "datamonth"]
        extractfunctions: [
          { name: "generic_offset_datamonth", constants: ["1"], columns: ["datamonth"] }, # previous cycle month
          { name: "generic_is_in", constants: ["cj"], columns: ["common_affiliate_name"] },
          { name: "generic_is_in", constants: ["payment"], columns: ["transaction_type"] },
          { name: "generic_is_null_or_empty", columns: ["partner_commission_amount"] },
          { name: "generic_is_not_null", columns: ["agoda_commission_amount_usd"] }
        ]
      }
    ]

    datasetlogicfunction: [
      {
        name: "generic_join",
        args: {
          select: [
            {
              table: ${schema}".network_partner_transactions",
              columns: [
                "affiliate_id", "affiliate_commission", "affiliate_name", "booking_date", "booking_id",
                "booking_status", "cancellation_date", "checkin_date", "checkout_date", "commission_base",
                "country_name", "cta_before_tax_after_discount", "discount_amount", "hotel_id", "hotel_name",
                "margin", "origin_country_name", "payment_method", "partner_currency", "selling_amount",
                "site_id", "site_name", "tag", "accrued_datamonth"
              ]
            },
            {
              table: ${schema}".final_accrual_payment_transactions",
              columns: ["datamonth"]
            }
          ],
          joins: [
            {
              left: ${schema}".network_partner_transactions"
              right: ${schema}".final_accrual_payment_transactions"
              conditions: [
                { left: "booking_id", right: "booking_id" }
              ]
              joinType: "inner"
            }
          ]
        }
      }
    ]
  }

#================= Output table ===============
  table: {
    name: ${schema}".network_partner_transactions_staging"
    mode: "overwrite" // "default is append"
    trackColumns: ["datamonth"]
    partition: [
      {
        key: "datamonth"
        value: "accountingdate"
      },
      {
        key: "transaction_type"
        value: ${transaction_type}
      },
      {
        key: "common_affiliate_name" ## New partition
        value: "cj"
      }
    ]
  }
  #=============================================
}

tracking-status {
  tablename: ${schema}".network_partner_transactions_staging_status"
}

result {
  mail {
    alert-sender = "<EMAIL> (<EMAIL>)"
    alert-recipients = "<EMAIL>"
  }
}