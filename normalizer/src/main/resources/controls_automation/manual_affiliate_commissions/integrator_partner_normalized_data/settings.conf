project-name = "manual-affiliate-commissions"
app-name = "Integrator_Partner_Normalizer"


normalizer: {
  include "normalizer_mapper.conf"

  # Required by alert guidelines: https://gitlab.agodadev.io/FinancePlatform/reconciliation-platform/external-data-pipeline/-/blob/master/ag-docs/normalizer/developer_guidelines/alert_guideline.md
  job_frequency = Monthly

  sources: [
    "integrator_partners"
  ]

  integrator_partners {
    sourceName: "integrator_partners"
    tableList: [
      {
         tablename: "pandora.booking"
         extractfunctions: [
            { name: "generic_offset_datamonth_gte", constants: ["24"], columns: ["datamonth"] }, # up to 2 years before process date
            { name: "generic_multiconstants_or_logical", columns: ["attribution_affiliate_id", "attribution_site_id"],
            multiconstants:
                [
                    [
                        "218861", "205840", "224829", "219813", "255727", "184015", "265736", "263224", "260486", "282887", # Gimmonix AID
                        "136384","148808","181855","191937","202982","221956","226230","298404","299907","228312","232130", # Juniper AID
                        "234223","248748","252328","258502","259018","259389","268910","280321","241843","237026","249238","250781","248665","268412","288613", # Juniper AID
                        "300255","300253","300267","232323", "320252", "211269", # Juniper AID
                        "201786","300174","300171","305201", #DCS+ AID
                        "292272","292274","279766","279599" #Peakwork AID
                    ],
                    [
                        "1830365","1816554","1830384","1802573", # Gimmonix CID
                        "1837728","1838037" # Juniper CID
                    ]
                ]
            },
            { name: "generic_offset_date_greater_than_process_month", constants: ["1", "yyyy-MM-dd"], columns: ["checkout_date"]},
            { name: "generic_offset_date_less_than_process_month", constants: ["1", "yyyy-MM-dd"], columns: ["checkout_date"]}
         ]
         selectcolumn: ["booking_id", "datamonth",
            "booking_date", "checkin_date", "checkout_date", "attribution_affiliate_id", "attribution_site_id",
            "cancellation_date", "booking_status_id"]
      },
      {
         tablename: "bi_dw.fact_booking_all"
         extractfunctions: [
            { name: "generic_offset_datamonth_gte", constants: ["24"], columns: ["datamonth"] }, # up to 2 years before process date
            { name: "generic_is_not_match_with_regex", constants: ["(?i).*Cxl.*"], columns: ["booking_status"]},
            { name: "generic_offset_date_greater_than_process_month", constants: ["1", "yyyy-MM-dd"], columns: ["checkout_date"] },
            { name: "generic_offset_date_less_than_process_month", constants: ["1", "yyyy-MM-dd"], columns: ["checkout_date"] }
         ]
         selectcolumn: ["booking_id", "datamonth", "booking_status", "affiliate_name", "site_name", "original_payment_method_name"]
      }
    ]
    datasetlogicfunction: [
      {
        name: "generic_join"
        args: {
          select: [
              {
                table: "pandora.booking",
                columns: [
                    "booking_id", "datamonth",
                    "booking_date", "checkin_date", "checkout_date", "attribution_affiliate_id", "attribution_site_id",
                    "cancellation_date", "booking_status_id"
                ]
              },
              {
                table: "bi_dw.fact_booking_all",
                columns: ["booking_status", "affiliate_name", "site_name", "original_payment_method_name"]
              }
          ],
          joins: [
            {
              left: "pandora.booking"
              right: "bi_dw.fact_booking_all"
              conditions: [
                { left: "datamonth", right: "datamonth"},
                { left: "booking_id", right: "booking_id"}
              ]
              joinType: "inner"
            }
          ]
        }
      },
      {name: "append_integrator_additional_data"}
    ]
  }

#================= Output table ===============
  table: {
    name: ${schema}".integrator_partner_transactions_staging"
    mode: "overwrite" # "default is append"
    trackColumns: ["datamonth"]
    partition: [
       { key: "datamonth", value: "accountingdate" },
       { key: "transaction_type", value: ${transaction_type}}
    ]
  }
  #=============================================
}

tracking-status {
  tablename: ${schema}".integrator_partner_transactions_staging_status"
}

result {
  mail {
    alert-sender = "<EMAIL> (<EMAIL>)"
    alert-recipients = "<EMAIL>"
  }
}
