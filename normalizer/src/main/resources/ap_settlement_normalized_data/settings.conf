
project-name = "normalizer-ap-settlement"
app-name = "ApSettlementNormalizer"

normalizer: {
  include "normalizer_mapper.conf"
  job_frequency = Daily
  lookbackdate = 2 // number of the date to lookback eg 7 means 7 days
  sources: [
     "tutuka_upc"
    , "enett_upc"
    , "wex_upc"
    , "adjustment"
    , "agp"
    , "checkout_upc"
  ]

  tutuka_upc {
    sourceName : "tutuka_upc"
    tableList:[
        {
          tablename: ${source_schema}".tutuka_settlement"
          partitiondatekey: "datadate"
          extractfunctions: [
             {name: "generic_is_not_null", columns: ["transaction_id", "tracking_number", "transaction_description", "wallet_reference", "file_name"]}
           ]
        }
    ]
    enrichTableList:[
        {
          tablename: ${source_upc_trans_schema}".minimized_upcapitransaction"
          selectcolumn: ["cardidentifier", "metadata", "logtime", "datadate", "partition_key"]
        },
        {
          tablename: ${source_master_schema}".currency"
          selectcolumn: ["numeric_code", "currency_code"]
        },
        {
          tablename: ${source_hotel_history_schema}".hotel_history"
          selectcolumn: ["hotel_id", "rate_currency_code", "datadate"]
        },
        {
          tablename: ${source_finance}".exchange_rates"
          selectcolumn: ["currency_code", "real_exchange_rate", "datadate"]
        },
        {
           tablename: ${source_ap_schema}".campaign_details"
           selectcolumn: ["campaign_name", "product_name", "provider_campaign_reference"]
        }
    ]
    datasetlogicfunction : [{name: "append_tutuka_upc_additional_info"}, {name: "generic_trim"}]
    outputpartitionkey : [
      {partitionname:"payment_method_id", colName: "payment_method_id"},
      {partitionname:"product_type", colName:"product_type"},
      {partitionname:"source_name", colName:"source_name"},
      {partitionname:"report_date", colName:"report_date"}
    ]
  }

  enett_upc {
    sourceName : "enett_upc"
    tableList:[
      {
        tablename : ${source_schema}".enett_upc"
        partitiondatekey : "datadate"
      }
    ]
    enrichTableList:[
      {
        tablename: ${source_upc_trans_schema}".minimized_upcapitransaction"
        selectcolumn: ["cardidentifier", "metadata", "logtime", "datadate", "partition_key"]
      },
      {
          tablename: ${source_hotel_history_schema}".hotel_history"
          selectcolumn: ["hotel_id", "rate_currency_code", "datadate"]
      },
      {
        tablename: ${source_finance}".exchange_rates"
        selectcolumn: ["currency_code", "real_exchange_rate", "datadate"]
      }
    ]
    datasetlogicfunction : [{name: "append_enett_upc_additional_info"}, {name: "generic_trim"}]
    outputpartitionkey : [
      {partitionname:"payment_method_id", colName: "payment_method_id"},
      {partitionname:"product_type", colName:"product_type"},
      {partitionname:"source_name", colName:"source_name"},
      {partitionname:"report_date", colName:"report_date"}
    ]
  }

  wex_upc {
     sourceName : "wex_upc"
     tableList:[
         {
           tablename: ${source_schema}".wex_upc"
           partitiondatekey: "datadate"
         }
     ]
    enrichTableList:[
      {
          tablename: ${source_hotel_history_schema}".hotel_history"
          selectcolumn: ["hotel_id", "rate_currency_code", "datadate"]
      },
      {
        tablename: ${source_finance}".exchange_rates"
        selectcolumn: ["currency_code", "real_exchange_rate", "datadate"]
      }
    ]
     datasetlogicfunction : [{name: "append_wex_upc_additional_info"}, {name: "generic_trim"}]
     outputpartitionkey : [
       {partitionname:"payment_method_id", colName: "payment_method_id"},
       {partitionname:"product_type", colName:"product_type"},
       {partitionname:"source_name", colName:"source_name"},
       {partitionname:"report_date", colName:"report_date"}
     ]
  }

  adjustment {
     sourceName : "adjustment"
     tableList:[
         {
           tablename: ${source_schema}".approvals"
           partitiondatekey: "datadate"
           extractfunctions: [
             {name: "generic_is_in", constants: ["47"], columns: ["adjustment_reason_id"]},
             {name: "generic_is_in", constants: ["2","5"], columns: ["payment_method_id"]}
           ]
         }
     ]
    enrichTableList:[
      {
        tablename: ${source_finance}".exchange_rates"
        selectcolumn: ["currency_code", "real_exchange_rate", "datadate"]
      }
    ]
     datasetlogicfunction : [{name: "append_approval_exchange_rate"}, {name: "generic_trim"}]
     outputpartitionkey : [
       {partitionname:"payment_method_id", colName: "payment_method_id"},
       {partitionname:"product_type", colName:"product_type"},
       {partitionname:"source_name", colName:"source_name"},
       {partitionname:"report_date", colName:"report_date"}
     ]
  }

  agp {
    sourceName: "agp"
    tableList: [
        {
            tablename: ${source_schema}".settlements_raw"
            partitiondatekey: "datadate"
            extractfunctions: [
                { name: "generic_is_in", constants: ["7"], columns: ["payment_method_id"] }
            ]
        }
    ]

    enrichTableList: [
        {
            tablename: ${source_finance}".exchange_rates"
            selectcolumn: ["currency_code", "real_exchange_rate", "datadate"]
        },
        {
            tablename: ${source_schema}".firedrill_contract_payment"
            partitiondatekey: "datadate"
            selectcolumn: ["contract_id", "payment_date", "payment_amount", "contract_payment_id", "datadate"]
        },
        {
            tablename: ${source_schema}".processed_agp_contract_payment"
            selectcolumn: ["contract_id", "payment_date", "payment_amount", "contract_payment_id", "unpaid_payment_amount", "datadate"]
            extractfunctions: [
                { name: "generic_offset_datadate", constants: ["1"], columns: ["datadate"] }
            ]
        }
    ]

    datasetlogicfunction: [{ name: "calculate_agp_exchange_rate" }, {name: "generic_trim"}]
    outputpartitionkey: [
        { partitionname: "payment_method_id", colName: "payment_method_id"},
        { partitionname: "product_type", colName: "product_type" },
        { partitionname: "source_name", colName: "source_name" },
        { partitionname: "report_date", colName: "report_date" },
    ]
  }

  checkout_upc {
    sourceName : "checkout_upc"
    tableList:[
      {
        tablename: ${source_schema}".checkout_settlement"
        partitiondatekey: "datadate"
        extractfunctions: [
          {name: "generic_is_not_null", columns: ["transaction_id", "card_reference"]}
        ]
      }
    ]
    enrichTableList:[
      {
        tablename: ${source_upc_trans_schema}".minimized_upcapitransaction"
        selectcolumn: ["cardidentifier", "metadata", "logtime", "datadate", "partition_key"]
      },
      {
        tablename: ${source_hotel_history_schema}".hotel_history"
        selectcolumn: ["hotel_id", "rate_currency_code", "datadate"]
      },
      {
        tablename: ${source_finance}".exchange_rates"
        selectcolumn: ["currency_code", "real_exchange_rate", "datadate"]
      },
      {
        tablename: ${source_ap_schema}".campaign_details"
        selectcolumn: ["campaign_name", "product_name", "provider_campaign_reference"]
      }
    ]
    datasetlogicfunction : [{name: "append_checkout_upc_additional_info"}, {name: "generic_trim"}]
    outputpartitionkey : [
      {partitionname:"payment_method_id", colName: "payment_method_id"},
      {partitionname:"product_type", colName:"product_type"},
      {partitionname:"source_name", colName:"source_name"},
      {partitionname:"report_date", colName:"report_date"}
    ]
  }
  table: {
    name: ${schema}".settlements"
    mode: "overwrite" //"default is append"
    trackColumns: ["datadate"]
    partition: [
      {
        key: "datadate"
        value: "0" // "[-t,..,0,..,+t]" (default is 0 (current date))) or "accountingdate"
      },
      {
        value: "outputpartitionkey" // (default is outputpartitionkey from each file)
      }
    ]
  }
}

tracking-status {
  tablename: ${schema}".ap_settlement_normalizer_status"
}

result {
  mail {
    alert-sender = "<EMAIL> (<EMAIL>)"
    alert-recipients = "<EMAIL>"
  }
}

hadoop.spark {
   executor {
        count = 25
        cores-per-executor = 5
        memory-per-executor = "40g"
        memory-overhead = 16384
   }
   driver {
        memory = "40g"
        memory-overhead = 16384
   }
}