# If making any change to mapping or addding any column please make the necessary changes to the following :
# Delta  AP Settlement Normalizer : https://gitlab.agodadev.io/FinancePlatform/reconciliation-platform/external-data-pipeline/-/tree/master/normalizer/src/main/resources/delta_ap_settlement_normalized_data?ref_type=heads
# Enriched AP Settlement Normalizer : https://gitlab.agodadev.io/FinancePlatform/reconciliation-platform/external-data-pipeline/-/tree/master/normalizer/src/main/resources/ap_settlement_enriched_data?ref_type=heads
# Non card settlement ETL :https://gitlab.agodadev.io/etl-workflows/wf-service-accounts/hk-fin-ap-prod-svc/-/tree/main/ap_processor-settlement_non_card

mappers: [
  {
    aliasColName: "type", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, value: "Payment"},
      {sourceName: ${enett_upc.sourceName}, value: "Payment"},
      {sourceName: ${wex_upc.sourceName}, value: "Payment"},
      {sourceName: ${adjustment.sourceName}, value: "Payment"},
      {sourceName: ${agp.sourceName}, colName: "type"},
      {sourceName: ${checkout_upc.sourceName}, value: "Payment"}

    ]
  },
  {
    aliasColName: "sub_type", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "transaction_description"},
      {sourceName: ${enett_upc.sourceName}, colName: "activity_type"},
      {sourceName: ${wex_upc.sourceName}, value: "Settlement"},
      {sourceName: ${adjustment.sourceName}, value: "Adjusted Import"},
      {sourceName: ${agp.sourceName}, colName: "sub_type"},
      {sourceName: ${checkout_upc.sourceName}, colName: "transaction_description"}
    ]
  },
  {
    aliasColName: "source", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, value: "Paymentology"},
      {sourceName: ${enett_upc.sourceName}, value: "Enett"},
      {sourceName: ${wex_upc.sourceName}, value: "Wex"},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "source"},
      {sourceName: ${checkout_upc.sourceName}, value: "Checkout"}
    ]
  },
  {
    aliasColName: "transaction_id", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "transaction_id"},
      {sourceName: ${enett_upc.sourceName}, colName: "ttrans_id"},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}},
      {sourceName: ${checkout_upc.sourceName}, colName: "transaction_id"}
    ]
  },
  {
    aliasColName: "booking_id", dataType: "LongType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "booking_id"},
      {sourceName: ${enett_upc.sourceName}, colName: "booking_id"},
      {sourceName: ${wex_upc.sourceName}, colName: "booking_id"},
      {sourceName: ${adjustment.sourceName}, colName: "booking_id"},
      {sourceName: ${agp.sourceName}, colName: "booking_id"},
      {sourceName: ${checkout_upc.sourceName}, colName: "booking_id"}
    ]
  },
  {
    aliasColName: "sub_supplier_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "sub_supplier_id"},
      {sourceName: ${enett_upc.sourceName}, colName: "sub_supplier_id"},
      {sourceName: ${wex_upc.sourceName}, colName: "sub_supplier_id"},
      {sourceName: ${adjustment.sourceName}, colName: "sub_supplier_id"},
      {sourceName: ${agp.sourceName}, colName: "sub_supplier_id"},
      {sourceName: ${checkout_upc.sourceName}, colName: "sub_supplier_id"}
    ]
  },
  {
    aliasColName: "supplier_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "dmc_id"},
      {sourceName: ${enett_upc.sourceName}, colName: "dmc_id"},
      {sourceName: ${wex_upc.sourceName}, colName: "dmc_id"},
      {sourceName: ${adjustment.sourceName}, colName: "supplier_id"},
      {sourceName: ${agp.sourceName}, colName: "supplier_id"},
      {sourceName: ${checkout_upc.sourceName}, colName: "dmc_id"}
    ]
  },
  {
    aliasColName: "approval_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "batch_id"},
      {sourceName: ${enett_upc.sourceName}, colName: "batch_id"},
      {sourceName: ${wex_upc.sourceName}, colName: "batch_id"},
      {sourceName: ${adjustment.sourceName}, colName: "approval_id"},
      {sourceName: ${agp.sourceName}, colName: "approval_id"},
      {sourceName: ${checkout_upc.sourceName}, colName: "batch_id"}
    ]
  },
  {
    aliasColName: "approval_reference", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "approval_reference"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "payment_method", dataType: "StringType",
    colMapper: [
      {
        sourceName: ${tutuka_upc.sourceName},
        function: {
          name: "get_payment_method_name", columns: ["booking_id", "batch_id", "is_valid_transaction"]
        }
      },
      {
        sourceName: ${enett_upc.sourceName},
        function: {
          name: "get_payment_method_name", columns: ["booking_id", "batch_id", "is_valid_transaction"]
        }
      },
      {
        sourceName: ${wex_upc.sourceName},
        function: {
          name: "get_payment_method_name", columns: ["booking_id", "batch_id", "is_valid_transaction"]
        }
      },
      {
        sourceName: ${adjustment.sourceName}, colName: "payment_method_id",
        mapper: [
          {key: "0", value: "None"},
          {key: "1", value: "TelexTransfer"},
          {key: "2", value: "PlasticCard"},
          {key: "3", value: "UniversalPlasticCard"},
          {key: "4", value: "Manual"},
          {key: "5", value: "UpcOnEPass"},
          {key: "6", value: "PayPal"},
          {key: "7", value: "AgpConsumption"},
          {key: "DEFAULT_MAP_VALUE", value: "None"}
        ]
      },
      {sourceName: ${agp.sourceName}, colName: "payment_method"},
      {
        sourceName: ${checkout_upc.sourceName},
        function: {
          name: "get_payment_method_name", columns: ["booking_id", "batch_id", "is_valid_transaction"]
        }
      }
    ]
  },
  {
    aliasColName: "payment_model", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "payment_model"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "accounting_date", dataType: "TimestampType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "posting_datetime"},
      {sourceName: ${enett_upc.sourceName}, colName: "posting_datetime"},
      {sourceName: ${wex_upc.sourceName}, colName: "posting_datetime"},
      {sourceName: ${adjustment.sourceName}, colName: "accounting_date"},
      {sourceName: ${agp.sourceName}, colName: "accounting_date"},
      {sourceName: ${checkout_upc.sourceName}, colName: "posting_datetime"}
    ]
  },
  {
    aliasColName: "transaction_date", dataType: "TimestampType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "transaction_datetime"},
      {sourceName: ${enett_upc.sourceName}, colName: "transaction_datetime"},
      {sourceName: ${wex_upc.sourceName}, colName: "transaction_datetime"},
      {sourceName: ${adjustment.sourceName}, colName: "transaction_date"},
      {sourceName: ${agp.sourceName}, colName: "transaction_date"},
      {sourceName: ${checkout_upc.sourceName}, colName: "transaction_datetime"}
    ]
  },
  {
    aliasColName: "original_settlement_currency", dataType: "StringType",
    colMapper: [
      {
        sourceName: ${tutuka_upc.sourceName}, colName: "original_settlement_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {
        sourceName: ${enett_upc.sourceName}, colName: "original_settlement_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {
        sourceName: ${wex_upc.sourceName}, colName: "original_settlement_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {
        sourceName: ${adjustment.sourceName}, colName: "local_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {sourceName: ${agp.sourceName}, colName: "original_settlement_currency"},
      {
        sourceName: ${checkout_upc.sourceName}, colName: "original_settlement_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      }
    ]
  },
  {
    aliasColName: "settlement_currency", dataType: "StringType",
    colMapper: [
      {
        sourceName: ${tutuka_upc.sourceName}, colName: "settlement_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {
        sourceName: ${enett_upc.sourceName}, colName: "settlement_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {
        sourceName: ${wex_upc.sourceName}, colName: "settlement_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {
        sourceName: ${adjustment.sourceName}, colName: "local_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {sourceName: ${agp.sourceName}, colName: "settlement_currency"},
      {
        sourceName: ${checkout_upc.sourceName}, colName: "settlement_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      }
    ]
  },
  {
    aliasColName: "transaction_currency", dataType: "StringType",
    colMapper: [
      {
        sourceName: ${tutuka_upc.sourceName}, colName: "settlement_currency_code",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {
        sourceName: ${enett_upc.sourceName}, colName: "reconciliation_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {
        sourceName: ${wex_upc.sourceName}, colName: "source_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {
        sourceName: ${adjustment.sourceName}, colName: "local_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {sourceName: ${agp.sourceName}},
      {
        sourceName: ${checkout_upc.sourceName}, colName: "reconciliation_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      }
    ]
  },
  {
    aliasColName: "posting_currency", dataType: "StringType",
    colMapper: [
      {
        sourceName: ${tutuka_upc.sourceName}, colName: "cardholder_currency_code",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {
        sourceName: ${enett_upc.sourceName}, colName: "post_currency_code",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {
        sourceName: ${wex_upc.sourceName}, colName: "billing_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {
        sourceName: ${adjustment.sourceName}, colName: "local_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {sourceName: ${agp.sourceName}, colName: "posting_currency"},
      {
        sourceName: ${checkout_upc.sourceName}, colName: "billing_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      }
    ]
  },
  {
    aliasColName: "original_settlement_amount", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "original_settlement_amount"},
      {sourceName: ${enett_upc.sourceName}, colName: "original_settlement_amount"},
      {sourceName: ${wex_upc.sourceName}, colName: "original_settlement_amount"},
      {sourceName: ${adjustment.sourceName}, colName: "local_amount"},
      {sourceName: ${agp.sourceName}, colName: "original_settlement_amount"},
      {sourceName: ${checkout_upc.sourceName}, colName: "original_settlement_amount"}
    ]
  },
  {
    aliasColName: "settlement_amount", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "settlement_amount"},
      {sourceName: ${enett_upc.sourceName}, colName: "settlement_amount"},
      {sourceName: ${wex_upc.sourceName}, colName: "settlement_amount"},
      {sourceName: ${adjustment.sourceName}, colName: "local_amount"},
      {sourceName: ${agp.sourceName}, colName: "settlement_amount"},
      {sourceName: ${checkout_upc.sourceName}, colName: "settlement_amount"}
    ]
  },
  {
    aliasColName: "settlement_amount_usd", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "settlement_amount_usd"},
      {sourceName: ${enett_upc.sourceName}, colName: "settlement_amount_usd"},
      {sourceName: ${wex_upc.sourceName}, colName: "settlement_amount_usd"},
      {sourceName: ${adjustment.sourceName}, colName: "usd_amount"},
      {sourceName: ${agp.sourceName}, colName: "settlement_amount_usd"},
      {sourceName: ${checkout_upc.sourceName}, colName: "settlement_amount_usd"}
    ]
  },
  {
    aliasColName: "transaction_amount", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "transaction_settlement_amount"},
      {sourceName: ${enett_upc.sourceName}, colName: "reconciliation_ammount"},
      {sourceName: ${wex_upc.sourceName}, colName: "source_amount"},
      {sourceName: ${adjustment.sourceName}, colName: "local_amount"}
      {sourceName: ${agp.sourceName}},
      {sourceName: ${checkout_upc.sourceName}, colName: "reconciliation_amount"},
    ]
  },
  {
    aliasColName: "posting_amount", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "transaction_cardholder_amount"},
      {sourceName: ${enett_upc.sourceName}, colName: "post_amount"},
      {sourceName: ${wex_upc.sourceName}, colName: "transaction_amount"},
      {sourceName: ${adjustment.sourceName}, colName: "local_amount"},
      {sourceName: ${agp.sourceName}, colName: "posting_amount"},
      {sourceName: ${checkout_upc.sourceName}, colName: "billing_amount"}
    ]
  },
  {
    aliasColName: "source_transaction_code", dataType: "StringType",
    colMapper: [
      {
        sourceName: ${tutuka_upc.sourceName},
        function: {
          name: "get_tutuka_source_transaction_code", columns: ["transaction_id", "tracking_number", "transaction_description"]
        }
      },
      {sourceName: ${enett_upc.sourceName}, colName: "reconcile_txn_id"},
      {sourceName: ${wex_upc.sourceName}, colName: "reference_number"},
      {sourceName: ${adjustment.sourceName}, colName: "adjustment_reference"},
      {sourceName: ${agp.sourceName}, colName: "source_transaction_code"},
      {sourceName: ${checkout_upc.sourceName}, colName: "message_id"}
    ]
  },
  {
    aliasColName: "uuid", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "uuid"},
      {sourceName: ${enett_upc.sourceName}, colName: "uuid"},
      {sourceName: ${wex_upc.sourceName}, colName: "uuid"},
      {sourceName: ${adjustment.sourceName}, function: {name: "generic_uuid"}},
      {sourceName: ${agp.sourceName}, colName: "uuid"},
      {sourceName: ${checkout_upc.sourceName}, colName: "uuid"},
    ]
  },
  {
    aliasColName: "rate_currency", dataType: "StringType",
    colMapper: [
      {
        sourceName: ${tutuka_upc.sourceName}, colName: "rate_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {
        sourceName: ${enett_upc.sourceName}, colName: "rate_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {
        sourceName: ${wex_upc.sourceName}, colName: "rate_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {
        sourceName: ${adjustment.sourceName}, colName: "local_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      },
      {sourceName: ${agp.sourceName}, colName: "rate_currency"},
      {
        sourceName: ${checkout_upc.sourceName}, colName: "rate_currency",
        mapper: [
          {key: "RMB", value: "CNY"},
          {key: "CNH", value: "CNY"}
        ]
      }
    ]
  },
  {
    aliasColName: "rate_ex", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "rate_ex"},
      {sourceName: ${enett_upc.sourceName}, colName: "rate_ex"},
      {sourceName: ${wex_upc.sourceName}, colName: "rate_ex"},
      {sourceName: ${adjustment.sourceName}, colName: "rate_ex"},
      {sourceName: ${agp.sourceName}, colName: "rate_ex"},
      {sourceName: ${checkout_upc.sourceName}, colName: "rate_ex"}
    ]
  },
  {
    aliasColName: "settlement_ex", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "settlement_ex"},
      {sourceName: ${enett_upc.sourceName}, colName: "settlement_ex"},
      {sourceName: ${wex_upc.sourceName}, colName: "settlement_ex"},
      {sourceName: ${adjustment.sourceName}, colName: "settlement_ex"},
      {sourceName: ${agp.sourceName}, colName: "settlement_ex"},
      {sourceName: ${checkout_upc.sourceName}, colName: "settlement_ex"},
    ]
  },
  {
    aliasColName: "posting_ex", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "posting_ex"},
      {sourceName: ${enett_upc.sourceName}, colName: "posting_ex"},
      {sourceName: ${wex_upc.sourceName}, colName: "posting_ex"},
      {sourceName: ${adjustment.sourceName}, colName: "posting_ex"},
      {sourceName: ${agp.sourceName}, colName: "posting_ex"},
      {sourceName: ${checkout_upc.sourceName}, colName: "posting_ex"},
    ]
  },
  {
    aliasColName: "destination_bank_country_name", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "destination_bank_country_name"},
      {sourceName: ${checkout_upc.sourceName}},
    ]
  },
  {
    aliasColName: "destination_bank_country_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "destination_bank_country_id"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "vat_rate", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "vat_rate"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "value_date", dataType: "TimestampType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "value_date"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "agoda_bank_account_number", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "agoda_bank_account_number"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "cid", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "cid"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "is_violet", dataType: "BooleanType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "is_violet"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "is_cancelled", dataType: "BooleanType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "is_cancelled"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "is_allotment_reject", dataType: "BooleanType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "is_allotment_reject"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "is_advance_guarantee", dataType: "BooleanType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "is_advance_guarantee"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "is_agency", dataType: "BooleanType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "is_agency"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "is_adjustment", dataType: "BooleanType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "is_adjustment"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "advance_payment_contract_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "advance_payment_contract_id"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "merchant_of_record_entity", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "merchant_of_record_entity"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "merchant_of_record_entity_type", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "merchant_of_record_entity_type"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "revenue_entity", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "revenue_entity"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "revenue_entity_type", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "revenue_entity_type"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "rate_contract_entity", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "rate_contract_entity"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "rate_contract_entity_type", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "rate_contract_entity_type"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "adjustment_reference", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "adjustment_reference"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "adjustment_reason_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "adjustment_reason_id"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "adjustment_reason", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "adjustment_reason"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "adjustment_remark", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "adjustment_remark"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "payment_method_id", dataType: "IntegerType",
    colMapper: [
      {
        sourceName: ${tutuka_upc.sourceName},
        function: {
          name: "get_payment_method_id", columns: ["booking_id", "batch_id", "is_valid_transaction"]
        }
      },
      {
        sourceName: ${enett_upc.sourceName},
        function: {
          name: "get_payment_method_id", columns: ["booking_id", "batch_id", "is_valid_transaction"]
        }
      },
      {
        sourceName: ${wex_upc.sourceName},
        function: {
          name: "get_payment_method_id", columns: ["booking_id", "batch_id", "is_valid_transaction"]
        }
      },
      {sourceName: ${adjustment.sourceName}, colName: "payment_method_id"},
      {sourceName: ${agp.sourceName}, colName: "payment_method_id"},
      {
        sourceName: ${checkout_upc.sourceName},
        function: {
          name: "get_payment_method_id", columns: ["booking_id", "batch_id", "is_valid_transaction"]
        }
      }
    ]
  },
  {
    aliasColName: "product_type", dataType: "StringType",
    colMapper: [
      {
        sourceName: ${tutuka_upc.sourceName}, colName: "product_type"
      },
      {
        sourceName: ${enett_upc.sourceName}, colName: "sub_supplier_id",
        mapper: [
          {key: "999", value: "VEHICLE"},
          {key: "DEFAULT_MAP_VALUE", value: "HOTEL"}
        ]
      },
      {
        sourceName: ${wex_upc.sourceName}, colName: "sub_supplier_id",
        mapper: [
          {key: "999", value: "VEHICLE"},
          {key: "DEFAULT_MAP_VALUE", value: "HOTEL"}
        ]
      },
      {
        sourceName: ${adjustment.sourceName}, colName: "sub_supplier_id",
        mapper: [
          {key: "999", value: "VEHICLE"},
          {key: "DEFAULT_MAP_VALUE", value: "HOTEL"}
        ]
      },
      {sourceName: ${agp.sourceName}, colName: "product_type"},
      {sourceName: ${checkout_upc.sourceName}, colName: "product_type"}
    ]
  },
  {
    aliasColName: "source_name", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, value: "tutuka_upc"},
      {sourceName: ${enett_upc.sourceName}, value: "enett_upc"},
      {sourceName: ${wex_upc.sourceName}, value: "wex_upc"},
      {sourceName: ${adjustment.sourceName}, value: "adjustment"},
      {sourceName: ${agp.sourceName}, value: "agp"},
      {sourceName: ${checkout_upc.sourceName}, value: "checkout_upc"}
    ]
  },
  {
    aliasColName: "batch_paypal_pay_amount", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}},
      {sourceName: ${checkout_upc.sourceName}}

    ]
  },
  {
    aliasColName: "batch_paypal_pay_amount_usd", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "batch_paypal_fee_amount", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "batch_paypal_fee_amount_usd", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "booking_transaction_date", dataType: "TimestampType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}, colName: "booking_transaction_date"},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "report_date", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "report_date"},
      {sourceName: ${enett_upc.sourceName}, colName: "report_date"},
      {sourceName: ${wex_upc.sourceName}, colName: "report_date"},
      {sourceName: ${adjustment.sourceName}, colName: "report_date"},
      {sourceName: ${agp.sourceName}, colName: "datadate"},
      {sourceName: ${checkout_upc.sourceName}, colName: "report_date"}
    ]
  },
  {
    aliasColName: "interchange_amount", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "interchange_amount"},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}},
      {sourceName: ${checkout_upc.sourceName}, colName: "reconciliation_interchange_amount"}
    ]
  },
  {
    aliasColName: "upc_product_name", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "product_name"},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}},
      {sourceName: ${checkout_upc.sourceName}, colName: "product_name"}
    ]
  },
  {
    aliasColName: "provider_card_classification_id", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "provider_card_classification_id"},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}},
      {sourceName: ${checkout_upc.sourceName}, colName: "provider_card_classification_id"}
    ]
  },
  {
    aliasColName: "acquirer_reference_number", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "acquirer_reference_number"},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}},
      {sourceName: ${checkout_upc.sourceName}, colName: "acquirer_reference_number"}
    ]
  },
  {
    aliasColName: "transaction_authorisation_number", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "transaction_authorisation_number"},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "merchant_country", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "merchant_country"},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "merchant_name", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "merchant_name"},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "merchant_address", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "merchant_address"},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "merchant_city", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "merchant_city"},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  },
  {
    aliasColName: "merchant_post_code", dataType: "StringType",
    colMapper: [
      {sourceName: ${tutuka_upc.sourceName}, colName: "merchant_post_code"},
      {sourceName: ${enett_upc.sourceName}},
      {sourceName: ${wex_upc.sourceName}},
      {sourceName: ${adjustment.sourceName}},
      {sourceName: ${agp.sourceName}},
      {sourceName: ${checkout_upc.sourceName}}
    ]
  }
]