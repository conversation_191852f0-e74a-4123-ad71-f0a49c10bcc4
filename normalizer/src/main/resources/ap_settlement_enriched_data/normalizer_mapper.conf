mappers: [
  {
    aliasColName: "type", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "type"},
      {sourceName: ${plastic_card.sourceName}, colName: "type"},
      {sourceName: ${other_payment_method.sourceName}, colName: "type"},
    ]
  },
  {
    aliasColName: "sub_type", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "sub_type"},
      {sourceName: ${plastic_card.sourceName}, colName: "sub_type"},
      {sourceName: ${other_payment_method.sourceName}, colName: "sub_type"},
    ]
  },
  {
    aliasColName: "source", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "source"},
      {sourceName: ${plastic_card.sourceName}, colName: "source"},
      {sourceName: ${other_payment_method.sourceName}, colName: "source"},
    ]
  },
  {
    aliasColName: "source_name", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "source_name"},
      {sourceName: ${plastic_card.sourceName}, colName: "source_name"},
      {sourceName: ${other_payment_method.sourceName}, colName: "source_name"},
    ]
  },
  {
    aliasColName: "detail_id", dataType: "LongType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "detail_id"},
      {sourceName: ${plastic_card.sourceName}, colName: "detail_id"},
      {sourceName: ${other_payment_method.sourceName}},  # No detail_id for other payment_method
    ]
  },
  {
    aliasColName: "booking_id", dataType: "LongType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "booking_id"},
      {sourceName: ${plastic_card.sourceName}, colName: "booking_id"},
      {sourceName: ${other_payment_method.sourceName}, colName: "booking_id"},
    ]
  },
  {
    aliasColName: "sub_supplier_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "sub_supplier_id"},
      {sourceName: ${plastic_card.sourceName}, colName: "sub_supplier_id"},
      {sourceName: ${other_payment_method.sourceName}, colName: "sub_supplier_id"},
    ]
  },
  {
    aliasColName: "supplier_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "supplier_id"},
      {sourceName: ${plastic_card.sourceName}, colName: "supplier_id"},
      {sourceName: ${other_payment_method.sourceName}, colName: "supplier_id"},
    ]
  },
  {
    aliasColName: "transaction_id", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "transaction_id"},
      {sourceName: ${plastic_card.sourceName}, colName: "transaction_id"},
      {sourceName: ${other_payment_method.sourceName}, colName: "transaction_id"},
    ]
  },
  {
    aliasColName: "cid", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "cid"},
      {sourceName: ${plastic_card.sourceName}, colName: "cid"},
      {sourceName: ${other_payment_method.sourceName}, colName: "cid"},
    ]
  },
  {
    aliasColName: "approval_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "approval_id"},
      {sourceName: ${plastic_card.sourceName}, colName: "approval_id"},
      {sourceName: ${other_payment_method.sourceName}, colName: "approval_id"},
    ]
  },
  {
    aliasColName: "approval_reference", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "approval_reference"},
      {sourceName: ${plastic_card.sourceName}, colName: "approval_reference"},
      {sourceName: ${other_payment_method.sourceName}, colName: "approval_reference"},
    ]
  },
  {
    aliasColName: "accounting_date", dataType: "TimestampType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "accounting_date"},
      {sourceName: ${plastic_card.sourceName}, colName: "accounting_date"},
      {sourceName: ${other_payment_method.sourceName}, colName: "accounting_date"},
    ]
  },
  {
    aliasColName: "transaction_date", dataType: "TimestampType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "transaction_date"},
      {sourceName: ${plastic_card.sourceName}, colName: "transaction_date"},
      {sourceName: ${other_payment_method.sourceName}, colName: "transaction_date"},
    ]
  },
  {
    aliasColName: "reporting_date", dataType: "TimestampType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "reporting_date"},
      {sourceName: ${plastic_card.sourceName}, colName: "reporting_date"},
      {sourceName: ${other_payment_method.sourceName}, colName: "reporting_date"},
    ]
  },
  {
    aliasColName: "value_date", dataType: "TimestampType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "value_date"},
      {sourceName: ${plastic_card.sourceName}, colName: "value_date"},
      {sourceName: ${other_payment_method.sourceName}, colName: "value_date"},
    ]
  },
  {
    aliasColName: "booking_transaction_date", dataType: "TimestampType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "booking_transaction_date"},
      {sourceName: ${plastic_card.sourceName}, colName: "booking_transaction_date"},
      {sourceName: ${other_payment_method.sourceName}, colName: "booking_transaction_date"},
    ]
  },
  {
    aliasColName: "settlement_currency", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "settlement_currency"},
      {sourceName: ${plastic_card.sourceName}, colName: "settlement_currency"},
      {sourceName: ${other_payment_method.sourceName}, colName: "settlement_currency"},
    ]
  },
  {
    aliasColName: "original_settlement_currency", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "original_settlement_currency"},
      {sourceName: ${plastic_card.sourceName}, colName: "original_settlement_currency"},
      {sourceName: ${other_payment_method.sourceName}, colName: "original_settlement_currency"},
    ]
  },
  {
    aliasColName: "posting_currency", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "posting_currency"},
      {sourceName: ${plastic_card.sourceName}, colName: "posting_currency"},
      {sourceName: ${other_payment_method.sourceName}, colName: "posting_currency"},
    ]
  },
  {
    aliasColName: "transaction_currency", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "transaction_currency"},
      {sourceName: ${plastic_card.sourceName}, colName: "transaction_currency"},
      {sourceName: ${other_payment_method.sourceName}, colName: "transaction_currency"},
    ]
  },
  {
    aliasColName: "rate_currency", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "rate_currency"},
      {sourceName: ${plastic_card.sourceName}, colName: "rate_currency"},
      {sourceName: ${other_payment_method.sourceName}, colName: "rate_currency"},
    ]
  },
  {
    aliasColName: "settlement_amount", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "settlement_amount"},
      {sourceName: ${plastic_card.sourceName}, colName: "settlement_amount"},
      {sourceName: ${other_payment_method.sourceName}, colName: "settlement_amount"},
    ]
  },
  {
    aliasColName: "split_settlement_amount", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "split_settlement_amount"},
      {sourceName: ${plastic_card.sourceName}, colName: "split_settlement_amount"},
      {sourceName: ${other_payment_method.sourceName}, colName: "settlement_amount"},
    ]
  },
  {
    aliasColName: "settlement_amount_usd", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "settlement_amount_usd"},
      {sourceName: ${plastic_card.sourceName}, colName: "settlement_amount_usd"},
      {sourceName: ${other_payment_method.sourceName}, colName: "settlement_amount_usd"},
    ]
  },
  {
    aliasColName: "split_settlement_amount_usd", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "split_settlement_amount_usd"},
      {sourceName: ${plastic_card.sourceName}, colName: "split_settlement_amount_usd"},
      {sourceName: ${other_payment_method.sourceName}, colName: "settlement_amount_usd"},
    ]
  },
  {
    aliasColName: "original_settlement_amount", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "original_settlement_amount"},
      {sourceName: ${plastic_card.sourceName}, colName: "original_settlement_amount"},
      {sourceName: ${other_payment_method.sourceName}, colName: "original_settlement_amount"},
    ]
  },
  {
    aliasColName: "posting_amount", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "posting_amount"},
      {sourceName: ${plastic_card.sourceName}, colName: "posting_amount"},
      {sourceName: ${other_payment_method.sourceName}, colName: "posting_amount"},
    ]
  },
  {
    aliasColName: "transaction_amount", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "transaction_amount"},
      {sourceName: ${plastic_card.sourceName}, colName: "transaction_amount"},
      {sourceName: ${other_payment_method.sourceName}, colName: "transaction_amount"},
    ]
  },
  {
    aliasColName: "interchange_amount", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "interchange_amount"},
      {sourceName: ${plastic_card.sourceName}, colName: "interchange_amount"},
      {sourceName: ${other_payment_method.sourceName}, colName: "interchange_amount"},
    ]
  },
  {
    aliasColName: "settlement_ex", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "settlement_ex"},
      {sourceName: ${plastic_card.sourceName}, colName: "settlement_ex"},
      {sourceName: ${other_payment_method.sourceName}, colName: "settlement_ex"},
    ]
  },
  {
    aliasColName: "posting_ex", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "posting_ex"},
      {sourceName: ${plastic_card.sourceName}, colName: "posting_ex"},
      {sourceName: ${other_payment_method.sourceName}, colName: "posting_ex"},
    ]
  },
  {
    aliasColName: "rate_ex", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "rate_ex"},
      {sourceName: ${plastic_card.sourceName}, colName: "rate_ex"},
      {sourceName: ${other_payment_method.sourceName}, colName: "rate_ex"},
    ]
  },
  {
    aliasColName: "source_transaction_code", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "source_transaction_code"},
      {sourceName: ${plastic_card.sourceName}, colName: "source_transaction_code"},
      {sourceName: ${other_payment_method.sourceName}, colName: "source_transaction_code"},
    ]
  },
  {
    aliasColName: "vat_rate", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "vat_rate"},
      {sourceName: ${plastic_card.sourceName}, colName: "vat_rate"},
      {sourceName: ${other_payment_method.sourceName}, colName: "vat_rate"},
    ]
  },
  {
    aliasColName: "uuid", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "uuid"},
      {sourceName: ${plastic_card.sourceName}, colName: "uuid"},
      {sourceName: ${other_payment_method.sourceName}, colName: "uuid"},
    ]
  },
  {
    aliasColName: "parent_uuid", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "parent_uuid"},
      {sourceName: ${plastic_card.sourceName}, colName: "parent_uuid"},
      {sourceName: ${other_payment_method.sourceName}, colName: "uuid"},
    ]
  },
  {
    aliasColName: "destination_bank_country_name", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "destination_bank_country_name"},
      {sourceName: ${plastic_card.sourceName}, colName: "destination_bank_country_name"},
      {sourceName: ${other_payment_method.sourceName}, colName: "destination_bank_country_name"},
    ]
  },
  {
    aliasColName: "destination_bank_country_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "destination_bank_country_id"},
      {sourceName: ${plastic_card.sourceName}, colName: "destination_bank_country_id"},
      {sourceName: ${other_payment_method.sourceName}, colName: "destination_bank_country_id"},
    ]
  },
  {
    aliasColName: "agoda_bank_account_number", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "agoda_bank_account_number"},
      {sourceName: ${plastic_card.sourceName}, colName: "agoda_bank_account_number"},
      {sourceName: ${other_payment_method.sourceName}, colName: "agoda_bank_account_number"},
    ]
  },
  {
    aliasColName: "is_violet", dataType: "BooleanType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "is_violet"},
      {sourceName: ${plastic_card.sourceName}, colName: "is_violet"},
      {sourceName: ${other_payment_method.sourceName}, colName: "is_violet"},
    ]
  },
  {
    aliasColName: "is_allotment_reject", dataType: "BooleanType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "is_allotment_reject"},
      {sourceName: ${plastic_card.sourceName}, colName: "is_allotment_reject"},
      {sourceName: ${other_payment_method.sourceName}, colName: "is_allotment_reject"},
    ]
  },
  {
    aliasColName: "is_advance_guarantee", dataType: "BooleanType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "is_advance_guarantee"},
      {sourceName: ${plastic_card.sourceName}, colName: "is_advance_guarantee"},
      {sourceName: ${other_payment_method.sourceName}, colName: "is_advance_guarantee"},
    ]
  },
  {
    aliasColName: "is_agency", dataType: "BooleanType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "is_agency"},
      {sourceName: ${plastic_card.sourceName}, colName: "is_agency"},
      {sourceName: ${other_payment_method.sourceName}, colName: "is_agency"},
    ]
  },
  {
    aliasColName: "is_adjustment", dataType: "BooleanType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "is_adjustment"},
      {sourceName: ${plastic_card.sourceName}, colName: "is_adjustment"},
      {sourceName: ${other_payment_method.sourceName}, colName: "is_adjustment"},
    ]
  },
  {
    aliasColName: "is_cancelled", dataType: "BooleanType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "is_cancelled"},
      {sourceName: ${plastic_card.sourceName}, colName: "is_cancelled"},
      {sourceName: ${other_payment_method.sourceName}, colName: "is_cancelled"},
    ]
  },
  {
    aliasColName: "is_last_fifo_overcharge", dataType: "BooleanType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "is_last_fifo_overcharge"},
      {sourceName: ${plastic_card.sourceName}, colName: "is_last_fifo_overcharge"},
      {sourceName: ${other_payment_method.sourceName}, value: 0},
    ]
  },
  {
    aliasColName: "advance_payment_contract_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "advance_payment_contract_id"},
      {sourceName: ${plastic_card.sourceName}, colName: "advance_payment_contract_id"},
      {sourceName: ${other_payment_method.sourceName}, colName: "advance_payment_contract_id"},
    ]
  },
  {
    aliasColName: "merchant_of_record_entity", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "merchant_of_record_entity"},
      {sourceName: ${plastic_card.sourceName}, colName: "merchant_of_record_entity"},
      {sourceName: ${other_payment_method.sourceName}, colName: "merchant_of_record_entity"},
    ]
  },
  {
    aliasColName: "merchant_of_record_entity_type", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "merchant_of_record_entity_type"},
      {sourceName: ${plastic_card.sourceName}, colName: "merchant_of_record_entity_type"},
      {sourceName: ${other_payment_method.sourceName}, colName: "merchant_of_record_entity_type"},
    ]
  },
  {
    aliasColName: "revenue_entity", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "revenue_entity"},
      {sourceName: ${plastic_card.sourceName}, colName: "revenue_entity"},
      {sourceName: ${other_payment_method.sourceName}, colName: "revenue_entity"},
    ]
  },
  {
    aliasColName: "revenue_entity_type", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "revenue_entity_type"},
      {sourceName: ${plastic_card.sourceName}, colName: "revenue_entity_type"},
      {sourceName: ${other_payment_method.sourceName}, colName: "revenue_entity_type"},
    ]
  },
  {
    aliasColName: "rate_contract_entity", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "rate_contract_entity"},
      {sourceName: ${plastic_card.sourceName}, colName: "rate_contract_entity"},
      {sourceName: ${other_payment_method.sourceName}, colName: "rate_contract_entity"},
    ]
  },
  {
    aliasColName: "rate_contract_entity_type", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "rate_contract_entity_type"},
      {sourceName: ${plastic_card.sourceName}, colName: "rate_contract_entity_type"},
      {sourceName: ${other_payment_method.sourceName}, colName: "rate_contract_entity_type"},
    ]
  },
  {
    aliasColName: "payment_model", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "payment_model"},
      {sourceName: ${plastic_card.sourceName}, colName: "payment_model"},
      {sourceName: ${other_payment_method.sourceName}, colName: "payment_model"},
    ]
  },
  {
    aliasColName: "adjustment_reference", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "adjustment_reference"},
      {sourceName: ${plastic_card.sourceName}, colName: "adjustment_reference"},
      {sourceName: ${other_payment_method.sourceName}, colName: "adjustment_reference"},
    ]
  },
  {
    aliasColName: "adjustment_reason_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "adjustment_reason_id"},
      {sourceName: ${plastic_card.sourceName}, colName: "adjustment_reason_id"},
      {sourceName: ${other_payment_method.sourceName}, colName: "adjustment_reason_id"},
    ]
  },
  {
    aliasColName: "adjustment_reason", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "adjustment_reason"},
      {sourceName: ${plastic_card.sourceName}, colName: "adjustment_reason"},
      {sourceName: ${other_payment_method.sourceName}, colName: "adjustment_reason"},
    ]
  },
  {
    aliasColName: "adjustment_remark", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "adjustment_remark"},
      {sourceName: ${plastic_card.sourceName}, colName: "adjustment_remark"},
      {sourceName: ${other_payment_method.sourceName}, colName: "adjustment_remark"},
    ]
  },
  {
    aliasColName: "batch_paypal_pay_amount", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "batch_paypal_pay_amount"},
      {sourceName: ${plastic_card.sourceName}, colName: "batch_paypal_pay_amount"},
      {sourceName: ${other_payment_method.sourceName}, colName: "batch_paypal_pay_amount"},
    ]
  },
  {
    aliasColName: "batch_paypal_pay_amount_usd", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "batch_paypal_pay_amount_usd"},
      {sourceName: ${plastic_card.sourceName}, colName: "batch_paypal_pay_amount_usd"},
      {sourceName: ${other_payment_method.sourceName}, colName: "batch_paypal_pay_amount_usd"},
    ]
  },
  {
    aliasColName: "batch_paypal_fee_amount", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "batch_paypal_fee_amount"},
      {sourceName: ${plastic_card.sourceName}, colName: "batch_paypal_fee_amount"},
      {sourceName: ${other_payment_method.sourceName}, colName: "batch_paypal_fee_amount"},
    ]
  },
  {
    aliasColName: "batch_paypal_fee_amount_usd", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "batch_paypal_fee_amount_usd"},
      {sourceName: ${plastic_card.sourceName}, colName: "batch_paypal_fee_amount_usd"},
      {sourceName: ${other_payment_method.sourceName}, colName: "batch_paypal_fee_amount_usd"},
    ]
  },
  {
    aliasColName: "upc_product_name", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "upc_product_name"},
      {sourceName: ${plastic_card.sourceName}, colName: "upc_product_name"},
      {sourceName: ${other_payment_method.sourceName}, colName: "upc_product_name"},
    ]
  },
  {
    aliasColName: "payment_method", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "payment_method"},
      {sourceName: ${plastic_card.sourceName}, colName: "payment_method"}
      {sourceName: ${other_payment_method.sourceName}, colName: "payment_method"},
    ]
  },
  {
    aliasColName: "payment_method_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "payment_method_id"},
      {sourceName: ${plastic_card.sourceName}, colName: "payment_method_id"},
      {sourceName: ${other_payment_method.sourceName}, colName: "payment_method_id"},
    ]
  },
  {
    aliasColName: "product_type", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "product_type"},
      {sourceName: ${plastic_card.sourceName}, colName: "product_type"},
      {sourceName: ${other_payment_method.sourceName}, colName: "product_type"},
    ]
  },
  {
    aliasColName: "report_date", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "report_date"},
      {sourceName: ${plastic_card.sourceName}, colName: "report_date"},
      {sourceName: ${other_payment_method.sourceName}, colName: "report_date"},
    ]
  },
  {
    aliasColName: "reprocess", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "reprocess"},
      {sourceName: ${plastic_card.sourceName}, colName: "reprocess"},
      {sourceName: ${other_payment_method.sourceName}, value: 0}, # reprocess count - 0 for other_payment_method
    ]
  },
  {
    aliasColName: "acquirer_reference_number", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "acquirer_reference_number"},
      {sourceName: ${plastic_card.sourceName}, colName: "acquirer_reference_number"},
      {sourceName: ${other_payment_method.sourceName}, colName: "acquirer_reference_number"},
    ]
  },
  {
    aliasColName: "transaction_authorisation_number", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "transaction_authorisation_number"},
      {sourceName: ${plastic_card.sourceName}, colName: "transaction_authorisation_number"},
      {sourceName: ${other_payment_method.sourceName}, colName: "transaction_authorisation_number"},
    ]
  },
  {
    aliasColName: "merchant_country", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "merchant_country"},
      {sourceName: ${plastic_card.sourceName}, colName: "merchant_country"}
      {sourceName: ${other_payment_method.sourceName}, colName: "merchant_country"},
    ]
  },
  {
    aliasColName: "merchant_name", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "merchant_name"},
      {sourceName: ${plastic_card.sourceName}, colName: "merchant_name"},
      {sourceName: ${other_payment_method.sourceName}, colName: "merchant_name"},
    ]
  },
  {
    aliasColName: "merchant_address", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "merchant_address"},
      {sourceName: ${plastic_card.sourceName}, colName: "merchant_address"},
      {sourceName: ${other_payment_method.sourceName}, colName: "merchant_address"},
    ]
  },
  {
    aliasColName: "merchant_city", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "merchant_city"},
      {sourceName: ${plastic_card.sourceName}, colName: "merchant_city"},
      {sourceName: ${other_payment_method.sourceName}, colName: "merchant_city"},
    ]
  },
  {
    aliasColName: "merchant_post_code", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}, colName: "merchant_post_code"},
      {sourceName: ${plastic_card.sourceName}, colName: "merchant_post_code"},
      {sourceName: ${other_payment_method.sourceName}, colName: "merchant_post_code"},
    ]
  },
  {
    aliasColName: "booking_settlement_amount", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}},
      {sourceName: ${plastic_card.sourceName}},
      {sourceName: ${other_payment_method.sourceName}}
    ]
  },
  {
    aliasColName: "booking_settlement_amount_usd", dataType: "DoubleType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}},
      {sourceName: ${plastic_card.sourceName}},
      {sourceName: ${other_payment_method.sourceName}}
    ]
  },
  {
    aliasColName: "booking_settlement_currency", dataType: "StringType",
    colMapper: [
      {sourceName: ${upc_on_epass.sourceName}},
      {sourceName: ${plastic_card.sourceName}},
      {sourceName: ${other_payment_method.sourceName}}
    ]
  }
]