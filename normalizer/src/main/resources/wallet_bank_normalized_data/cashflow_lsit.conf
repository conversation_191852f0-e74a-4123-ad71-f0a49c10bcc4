include classpath("_generic_staging.conf")
include "settings.conf"
source_schema="finance_wallet_lsit_cashflow"
schema="finance_wallet_lsit_cashflow"
current_env="qa"

app.adpMessaging.appName: "Wallet_Bank_Normalizer.qa.processor"

result {
  mail {
    alert-sender = "<EMAIL> (<EMAIL>)"
    alert-recipients = "<EMAIL>"
  }
}

hadoop {
  hdfs.user = "hk-fin-wal-svc--dev"
  knox.enabled = true
  hive.username = "hk-fin-wal-svc--dev"
  credentials = "hadoop/hk-fin-wal-svc--dev/credentials"
  credentials_from_env = true
  use-smart-table-loader = true
}

slack-config.sources = {
    allSupplier : ["o_fintech-wallet-dev"]
}

interactive-slackbot { # TODO FINWAL-507
  request-group = "AR-Adj-Approver"
  approve-group = "AR-Adj-Approver"
  slack-channel = "C07HQNRL16C" # TODO FINWAL-507 to be change later
}
