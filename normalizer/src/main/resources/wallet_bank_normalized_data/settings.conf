project-name = "normalizer-wallet-bank"
app-name = "WalletBankNormalizer"

normalizer: {
  include "normalizer_mapper.conf"
  job_frequency = Daily
  lookbackdate = 2 // number of the date to lookback eg 7 means 7 days
  sources: ["normalized_uk_adyen_data","refund_uk_interco_data","end_of_day_uk_report", "intra_day_uk_report"]

  normalized_uk_adyen_data {
    sourceName : "normalized_uk_adyen_data"
    tableList:[
      {
        tablename: ${source_schema}".wallet_gateway_settlement"
        partitiondatekey: "datadate"
        extractfunctions: [
          {name: "generic_is_equal_to", constants: ["8"], columns: ["gateway_id"]}
          {name: "generic_is_equal_to", constants: ["BHFS_UK"], columns: ["wallet_entity"]}
          {name: "generic_is_in", constants: ["TOPUP", "CHARGEBACK"], columns: ["wallet_event_type"]}
        ]
      }
    ]
    outputpartitionkey : [
      {partitionname:"wallet_entity",  colName: "wallet_entity"},
      {partitionname:"transfer_party", value: "ADYEN"},
    ]
  }

  refund_uk_interco_data {
    sourceName: "refund_uk_interco_data"
    tableList: [
      {
        tablename: ${source_schema}".preprocessed_wallet_interco_reports"
        partitiondatekey: "datadate"
        extractfunctions: [
            {name: "generic_is_equal_to", constants: ["REFUND"], columns: ["transaction_type"]}
            {name: "generic_is_equal_to", constants: ["BHFS_UK"], columns: ["wallet_entity"]}
            {name: "generic_is_equal_to", constants: ["AR-BHFS-SUSPENSE"], columns: ["description_key"]}
        ]
      }
    ]
    outputpartitionkey: [
      {partitionname: "wallet_entity", colName: "wallet_entity"},
      {partitionname: "transfer_party", value: "REFUND_INTERCO"},
    ]
  }

  end_of_day_uk_report {
    sourceName: "end_of_day_uk_report"
    tableList: [
      {
        tablename: ${source_schema}".cashflow_report"
        partitiondatekey: "datadate"
        extractfunctions: [
            {name: "generic_is_equal_to", constants: ["end_of_day"], columns: ["report"]}
            {name: "generic_is_equal_to", constants: ["BHFS_UK"], columns: ["wallet_entity"]}
        ]
      }
    ]
    outputpartitionkey: [
      {partitionname: "wallet_entity", colName: "wallet_entity"},
      {partitionname: "transfer_party", value: "END_OF_DAY_CASHFLOW_REPORT"},
    ]
  }


  intra_day_uk_report {
    sourceName: "intra_day_uk_report"
    tableList: [
      {
        tablename: ${source_schema}".cashflow_report"
        extractfunctions: [
            {name: "generic_offset_datadate", constants: ["1"], columns: ["datadate"]}
            {name: "generic_is_equal_to", constants: ["intra_day"], columns: ["report"]}
            {name: "generic_is_equal_to", constants: ["BHFS_UK"], columns: ["wallet_entity"]}
        ]
      }
    ]
    outputpartitionkey: [
      {partitionname: "wallet_entity", colName: "wallet_entity"},
      {partitionname: "transfer_party", value: "INTRA_DAY_CASHFLOW_REPORT"},
    ]
  }


  table: {
    name: ${schema}".normalized_wallet_bank"
    mode: "overwrite" //"default is overwrite"
    trackColumns: ["datadate"]
    partition: [
      {
        key: "datadate"
        value: "0" // "[-t,..,0,..,+t]" (default is 0 (current date))) or "accountingdate"
      },
      {
        value: "outputpartitionkey" // (default is outputpartitionkey from each file)
      }
    ]
  }
}

tracking-status {
  tablename: ${schema}".normalized_wallet_bank_status"
}

result {
  mail {
    alert-sender = "<EMAIL> (<EMAIL>)"
    alert-recipients = "<EMAIL>"
  }
}
