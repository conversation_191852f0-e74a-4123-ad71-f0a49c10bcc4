mappers: [
  {
    aliasColName: "source_datadate", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${normalized_uk_adyen_data.sourceName}, colName: "datadate"   },
      {sourceName: ${refund_uk_interco_data.sourceName},   colName: "datadate"   },
      {sourceName: ${end_of_day_uk_report.sourceName},     colName: "datadate"   },
      {sourceName: ${intra_day_uk_report.sourceName},      colName: "datadate"   }
    ]
  },
  {
    aliasColName: "gateway_merchant_reference", dataType: "StringType",
    colMapper: [
      {sourceName: ${normalized_uk_adyen_data.sourceName}, colName: "merchant_reference"},
      {sourceName: ${refund_uk_interco_data.sourceName}                                 },
      {sourceName: ${end_of_day_uk_report.sourceName}                                   },
      {sourceName: ${intra_day_uk_report.sourceName}                                    }
    ]
  },
  {
    aliasColName: "wallet_event_type", dataType: "StringType",
    colMapper: [
      {sourceName: ${normalized_uk_adyen_data.sourceName},  colName: "wallet_event_type" },
      {sourceName: ${refund_uk_interco_data.sourceName},    colName: "transaction_type"  },
      {sourceName: ${end_of_day_uk_report.sourceName}                                    },
      {sourceName: ${intra_day_uk_report.sourceName}                                     }
    ]
  },
  {
    aliasColName: "source_amount", dataType: "DecimalType", precision: 24, scale: 8,
    colMapper: [
      {sourceName: ${normalized_uk_adyen_data.sourceName}, colName: "source_amount"                 },
      {sourceName: ${refund_uk_interco_data.sourceName},   colName: "local_amount"                  },
      {sourceName: ${end_of_day_uk_report.sourceName},     colName: "safeguarding_adjustment_amount"},
      {sourceName: ${intra_day_uk_report.sourceName},      colName: "safeguarding_adjustment_amount"}
    ]
  },
  {
    aliasColName: "source_currency", dataType: "StringType",
    colMapper: [
      {sourceName: ${normalized_uk_adyen_data.sourceName},  colName: "source_currency"},
      {sourceName: ${refund_uk_interco_data.sourceName},    colName: "local_currency" },
      {sourceName: ${end_of_day_uk_report.sourceName},      value: "GBP"              },
      {sourceName: ${intra_day_uk_report.sourceName},       value: "GBP"              }
    ]
  },
  {
    aliasColName: "reference",  dataType: "StringType",
    colMapper: [
      {sourceName: ${normalized_uk_adyen_data.sourceName}, colName: "batch_number"            },
      {sourceName: ${refund_uk_interco_data.sourceName}                                       },
      {sourceName: ${end_of_day_uk_report.sourceName},     colName: "adjustment_reference"    },
      {sourceName: ${intra_day_uk_report.sourceName},      colName: "adjustment_reference"    }
    ]
  },
  {
    aliasColName: "wallet_account_id", dataType: "StringType",
    colMapper: [
      {sourceName: ${normalized_uk_adyen_data.sourceName},                             },
      {sourceName: ${refund_uk_interco_data.sourceName}, colName: "wallet_account_id"  },
      {sourceName: ${end_of_day_uk_report.sourceName}, colName: "wallet_account_id"    },
      {sourceName: ${intra_day_uk_report.sourceName}, colName: "wallet_account_id"     }
    ]
  },
  {
    aliasColName: "source_uuid", dataType: "StringType",
    colMapper: [
      {sourceName: ${normalized_uk_adyen_data.sourceName},    colName: "uuid"},
      {sourceName: ${refund_uk_interco_data.sourceName},      colName: "uuid"},
      {sourceName: ${end_of_day_uk_report.sourceName},        colName: "uuid"},
      {sourceName: ${intra_day_uk_report.sourceName},         colName: "uuid"}
    ]
  },
  {
    aliasColName: "transfer_reason", dataType: "StringType",
    colMapper: [
      {sourceName: ${normalized_uk_adyen_data.sourceName},   value: "GATEWAY_IN" },
      {sourceName: ${refund_uk_interco_data.sourceName},     value: "REFUND_IN"  },
      {sourceName: ${end_of_day_uk_report.sourceName},       value: "ADJUSTMENT" },
      {sourceName: ${intra_day_uk_report.sourceName},        value: "ADJUSTMENT" }
    ]
  },
  {
      aliasColName: "wallet_entity", dataType: "StringType",
      colMapper: [
        {sourceName: ${normalized_uk_adyen_data.sourceName},   colName: "wallet_entity" },
        {sourceName: ${refund_uk_interco_data.sourceName},     colName: "wallet_entity" },
        {sourceName: ${end_of_day_uk_report.sourceName},       colName: "wallet_entity" },
        {sourceName: ${intra_day_uk_report.sourceName},        colName: "wallet_entity" }
      ]
  },
  {
    aliasColName: "source_label", dataType: "StringType",
    colMapper: [
      {sourceName: ${normalized_uk_adyen_data.sourceName}                             },
      {sourceName: ${refund_uk_interco_data.sourceName},     colName: "source_label"  },
      {sourceName: ${end_of_day_uk_report.sourceName},                                },
      {sourceName: ${intra_day_uk_report.sourceName},                                 }
    ]
  },
  {
    aliasColName: "transaction_id", dataType: "StringType",
    colMapper: [
      {sourceName: ${normalized_uk_adyen_data.sourceName}, colName: "transaction_id"   },
      {sourceName: ${refund_uk_interco_data.sourceName},                               },
      {sourceName: ${end_of_day_uk_report.sourceName},                                 },
      {sourceName: ${intra_day_uk_report.sourceName},                                  }
    ]
  }

]
