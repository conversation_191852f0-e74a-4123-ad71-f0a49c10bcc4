include classpath("_generic_prod.conf")
include "settings.conf"

source_schema="finance_wallet"
schema="finance_wallet"
current_env="prod"

app.adpMessaging.appName = "normalizer.prod"

hadoop {
  hdfs.user = "hk-fin-wal-svc"
  knox.enabled = true
  hive.username = "hk-fin-wal-svc"
  credentials = "hadoop/hk-fin-wal-svc/credentials"
}

slack-config.sources = {
    allSupplier : ["o_fintech-wallet"]
}

interactive-slackbot {
  request-group = "finance-backoffice-wallet" // TODO FINWAL-507 need to create this AD group later
  approve-group = "finance-backoffice-wallet"
  slack-channel = "C07HQNRL16C" // fintech-wallet
}
