include classpath("_generic_staging.conf")
include "settings.conf"

source_schema="finance_wallet_uat"
schema="finance_wallet_uat"
current_env="uat"

app.adpMessaging.appName: "Wallet_Gateway_Normalizer.uat.processor"

normalizer: {
  wise_uk_downloader_report {
    tableList: [
      {
        tablename: ${source_schema}".wise_downloader"
        partitiondatekey: "datadate"
        extractfunctions: [
          {name: "generic_is_equal_to", constants: ["28721608"], columns: ["request_profile_id"]}
          {name: "generic_is_equal_to", constants: ["UAT"], columns: ["transactions_details_payment_reference"]}
          {name: "generic_is_equal_to", constants: ["TRANSFER"], columns: ["transactions_details_type"]}
        ]
      }
    ]
  }
  adyen_uk_downloader_report {
    tableList: [
      {
        tablename: ${source_schema}".wallet_adyen_daily_report"
        partitiondatekey: "datadate"
        extractfunctions: [
          // TODO update to actual merchant account for uat
          {name: "generic_is_equal_to", constants: ["AgodaTH_Wallet_UAT_UK_TEST"], columns: ["merchant_account"]}
        ]
      }
    ]
  }
}

result {
  mail {
    alert-sender = "<EMAIL> (<EMAIL>)"
    alert-recipients = "<EMAIL>"
  }
}

hadoop {
  hdfs.user = "hk-fin-wal-svc"
  knox.enabled = true
  hive.username = "hk-fin-wal-svc"
  credentials = "hadoop/hk-fin-wal-svc/credentials"
  credentials_from_env = true
  use-smart-table-loader = true
}

slack-config.sources = {
    allSupplier : ["o_fintech-wallet-uat"]
}

interactive-slackbot { # TODO FINWAL-507
  request-group = "AR-Adj-Approver"
  approve-group = "AR-Adj-Approver"
  slack-channel = "C07HQNRL16C" # TODO FINWAL-507 to be change later
}
