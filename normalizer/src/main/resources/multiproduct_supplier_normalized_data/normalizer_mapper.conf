whypaymore_refund_transaction_types = "(REFUND|Ticket Voided|Ticket Refunded)"
mappers: [
  {
    aliasColName: "agoda_booking_id", dataType: "LongType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, colName: "booking_reference_no"},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "partner_transaction_id"},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName}},
      {sourceName: ${priceline_daily_gordian_file.sourceName}},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "confirmation_num"},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}, colName:"agoda_booking_id"},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}, colName:"booking_confirmation_no"},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}, colName: "partner_order_no"},
      {sourceName: ${bemyguest_daily_report.sourceName}, colName:"agoda_booking_id"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, colName:"agoda_booking_id"},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}, colName: "booking_id"},
      {sourceName: ${cfar_covergenius_premium.sourceName}, colName: "reservation_number"},
      {sourceName: ${cfar_covergenius_claim.sourceName}, colName: "reservation_number"},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}, colName: "client_reference_id"},
    ]
  },
  {
    aliasColName: "agoda_transaction_id", dataType: "LongType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName}},
      {sourceName: ${priceline_daily_gordian_file.sourceName}},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}, colName: "agoda_transaction_id"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, colName: "agoda_transaction_id"},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "supplier_transaction_id", dataType: "StringType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, colName: "policy_no"},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, colName: "transaction_id"},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "priceline_transaction_id"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "priceline_transaction_id"},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}, colName: "order_num"},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "confo_number"},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}, colName: "payment_id"},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, colName: "payment_id"},
      {sourceName: ${hotel_hyatt_file.sourceName}, colName: "recover_pro_transaction_id"},
      {sourceName: ${fy_daily_file.sourceName}, colName: "transaction_id_1"},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}, colName: "supplier_transaction_id"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, colName: "supplier_transaction_id"},
      {sourceName: ${hotel_hilton_file.sourceName}, colName: "confirmation"},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "supplier_booking_id", dataType: "StringType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName},
        function: {name: "generic_exclude_suffix", columns: ["policy_no"], constants: ["C"]}},
      {sourceName: ${covergenius_daily_report.sourceName}, colName: "policy_no"},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "policy_no"},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, colName: "bid"},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "offer_number"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "offer_number"},
      {sourceName: ${tbo_daily_file.sourceName}, colName: "pnr"},
      {sourceName: ${tbo_monthly_file.sourceName}, colName: "pnr"},
      {sourceName: ${indigo_daily_file.sourceName},
        function: {name: "generic_coalesce", columns: ["parent_pnr", "record_locator"]}},
      {sourceName: ${vietjet_daily_file.sourceName}, colName: "record_locator"},
      {sourceName: ${thaivietjet_daily_file.sourceName}, colName: "record_locator"},
      {sourceName: ${lionair_daily_file.sourceName}, colName: "booking_reloc"},
      {sourceName: ${lionair_idr_daily_file.sourceName}, colName: "booking_reloc"},
      {sourceName: ${pkfare_daily_file.sourceName},
        function: {name: "generic_replace_last_characters", constants: ["2", "01"], columns: ["order_num"]}},
      {sourceName: ${nusa_daily_file.sourceName}, colName: "nusatrip_code"},
      {sourceName: ${spicejet_daily_file.sourceName}, function: {name: "generic_coalesce", columns: ["parentpnr", "pnr"]}},
      {sourceName: ${whypaymore_daily_file.sourceName}, colName: "airline_pnr"},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "confirmation_num"},
      {sourceName: ${jetstar_daily_file.sourceName},
        function: {name: "generic_check_defined_null_or_whitespace", columns: ["original_pnr", "pnr"]}},
      {sourceName: ${gofirst_daily_file.sourceName},
        function: {name: "generic_check_defined_null_or_whitespace", columns: ["parent_pnr", "pnr"]}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}, colName: "reference"},
      {sourceName: ${scoot_daily_file.sourceName}, colName: "pnr"},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, colName: "pnr"},
      {sourceName: ${hotel_hyatt_file.sourceName}, colName: "unique_booking_id"},
      {sourceName: ${fy_daily_file.sourceName}, colName: "account_reference"},
      {sourceName: ${viator_daily_report.sourceName}, colName: "itinerary_item_id"},
      {sourceName: ${kkday_daily_report.sourceName}, colName: "kkday_order_no"},
      {sourceName: ${bemyguest_daily_report.sourceName}, colName: "supplier_booking_id"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, colName: "supplier_booking_id"},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}, colName: "reference"},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}, colName: "policy_number"},
      {sourceName: ${cfar_covergenius_claim.sourceName}, colName: "policy_number"},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, colName: "id"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, colName: "pnr"},
      {sourceName: ${tway_daily_file.sourceName},
        function: {name: "generic_coalesce", columns: ["in_connection_with_ticket_number", "electronic_settlement_authorisation_code"]}},
      {sourceName: ${akasa_daily_file.sourceName}, function: {name: "generic_coalesce", columns: ["parent_pnr", "pnr"]}},
      {sourceName: ${emt_daily_file.sourceName}, colName: "pnr"},
      {sourceName: ${unififi_daily_file.sourceName},
        function: {name: "generic_check_defined_null_or_whitespace", columns: ["original_order_number", "order_number"]}
      },
      {sourceName: ${fr24_daily_file.sourceName},
        function: {name: "generic_check_defined_null_or_whitespace", columns: ["original_order_number", "order_no"]}
      },
      {sourceName: ${tripjack_daily_file.sourceName}, colName: "booking_id"},
      {sourceName: ${dida_daily_file.sourceName}, colName: "gds_pnr"},
      {sourceName: ${nokair_daily_file.sourceName}, colName: "reference"},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, colName: "ticket_document_number"},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "transaction_type", dataType: "StringType",
    colMapper: [
      {
        sourceName: ${chubb_daily_report.sourceName}, colName: "policy_status",
        mapper: [
          {key: "Sold", value: "SALE"},
          {key: "Cancelled Accepted", value: "REFUND"}
        ]
      },
      {sourceName: ${covergenius_daily_report.sourceName}, colName: "policy_status",
         mapper: [
           {key: "Booking", value: "SALE"},
           {key: "Modification", value: "ADJUSTMENT"},
           {key: "Cancel", value: "REFUND"}
         ]},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "policy_status",
         mapper: [
           {key: "Booking", value: "SALE"},
           {key: "Modification", value: "ADJUSTMENT"},
           {key: "Cancel", value: "REFUND"}
         ]},
      {
        sourceName: ${kiwi_daily_and_monthly_file.sourceName}, colName: "transaction_type",
        mapper: [
          {key: "payment", value: "SALE"},
          {key: "refund", value: "REFUND"},
          {key: "top up", value: "ADD_DEPOSIT"},
          {key: "withdrawal", value: "DEDUCT_DEPOSIT"},
          {key: "manual adjustment", value: "ADJUSTMENT"}
        ]
      },
      {
        sourceName: ${priceline_daily_file.sourceName}, colName: "transaction_type",
        mapper: [
          {key: "SALE", value: "SALE"},
          {key: "REFUND", value: "REFUND"},
          {key: "VOID", value: "REFUND"},
          {key: "ADJUSTMENT_RESERVATION", value: "ADJUSTMENT"},
          {key: "EMD", value: "EMD"},
          {key: "EXCHANGE", value: "EXCHANGE"},
          {key: "PRS_ADJUSTMENT", value: "PRS_ADJUSTMENT"},
          {key: "ANCILLARY", value: "ANCILLARY"}
        ]
      },
      {
        sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "transaction_type",
        mapper: [
          {key: "SALE", value: "SALE"},
          {key: "REFUND", value: "REFUND"},
          {key: "VOID", value: "REFUND"},
          {key: "ADJUSTMENT_RESERVATION", value: "ADJUSTMENT"},
          {key: "EMD", value: "EMD"},
          {key: "EXCHANGE", value: "EXCHANGE"},
          {key: "PRS_ADJUSTMENT", value: "PRS_ADJUSTMENT"}
        ]
      },
      {
        sourceName: ${tbo_daily_file.sourceName},
        function: {name: "generic_set_matching_value", constants: ["REFUND", "SALE", "([A-Za-z]{2})/(\\d{4})/(\\d+)"], columns: ["invoice_no"]}
      },
      {
        sourceName: ${tbo_monthly_file.sourceName},
        function: {name: "generic_set_matching_value", constants: ["REFUND", "SALE", "0.00"], columns: ["debit_amount"]}
      },
      {
        sourceName: ${indigo_daily_file.sourceName},
        function: {name: "generic_text_from_decimal_comparison", constants: ["SALE", "REFUND"], columns: ["payment_amount"]}
      },
      {
        sourceName: ${vietjet_daily_file.sourceName},
        function: {name: "generic_text_from_decimal_comparison", constants: ["SALE", "REFUND"], columns: ["payment_amount"]}
      },
      {
        sourceName: ${thaivietjet_daily_file.sourceName},
        function: {name: "generic_text_from_decimal_comparison", constants: ["SALE", "REFUND"], columns: ["payment_amount"]}
      },
      {
        sourceName: ${lionair_daily_file.sourceName}, colName: "transaction_type"
        mapper: [
          {key: "NTA", value: "SALE"},
          {key: "Reverse", value: "ADJUSTMENT"},
          {key: "Refund", value: "REFUND"},
          {key: "Change", value: "ADJUSTMENT"},
          {key: "Adjustment", value: "ADJUSTMENT"},
          {key: "Top-Up", value: "ADD_DEPOSIT"}
        ]
      },
      {
        sourceName: ${lionair_idr_daily_file.sourceName}, colName: "transaction_type"
        mapper: [
          {key: "NTA", value: "SALE"},
          {key: "Reverse", value: "ADJUSTMENT"},
          {key: "Refund", value: "REFUND"},
          {key: "Change", value: "ADJUSTMENT"},
          {key: "Adjustment", value: "ADJUSTMENT"},
          {key: "Top-Up", value: "ADD_DEPOSIT"}
        ]
      },
      {
        sourceName: ${pkfare_daily_file.sourceName}, colName: "payment_type"
        mapper: [
          {key: "Purchase", value: "SALE"},
          {key: "Refund", value: "REFUND"},
          {key: "Balance Adjustment", value: "Balance Adjustment"}
        ]
      },
      {
        sourceName: ${nusa_daily_file.sourceName}, colName: "transaction_type",
        mapper: [
          {key: "ISSUE", value: "SALE"},
          {key: "issue", value: "SALE"},
          {key: "TICKET REFUNDED", value: "REFUND"},
          {key: "Ticket Refunded", value: "REFUND"},
          {key: "TICKET EXCHANGED", value: "ADJUSTMENT"},
          {key: "Ticket Exchanged", value: "ADJUSTMENT"},
          {key: "OTHER", value: "ADJUSTMENT"},
          {key: "Other", value: "ADJUSTMENT"},
          {key: "VOID", value: "REFUND"},
          {key: "Void", value: "REFUND"}
        ]
      },
      {sourceName: ${spicejet_daily_file.sourceName}, function: {name: "generic_text_from_decimal_comparison", constants: ["SALE", "REFUND"], columns: ["amount"]}},
      {
        sourceName: ${whypaymore_daily_file.sourceName}, colName: "transaction_type",
        mapper: [
          {key: "Ticket Issued", value: "SALE"},
          {key: "Ticket Voided", value: "REFUND"},
          {key: "Ticket Refunded", value: "REFUND"},
        ]
      },
      {sourceName: ${hotel_violet_file.sourceName}, colName: "cares_class",
        mapper: [
          {key: "(?i)^accepted$(?i)", value: "SALE"},
          {key: "(?i)^accepted(?i)-.*", value: "ADJUSTMENT"},
          {key: "(?i)^cancel(?i).*", value: "REFUND"},
          {key: "DEFAULT_MAP_VALUE", value: "UNKNOWN"}
        ]
      },
      {sourceName: ${jetstar_daily_file.sourceName}, colName: "status", mapper: [
        {key: "Purchase", value: "SALE"},
        {key: "Refund", value: "REFUND"},
        {key: "DEFAULT_MAP_VALUE", value: "ADJUSTMENT"}
      ]},
      {
        sourceName: ${gofirst_daily_file.sourceName},
        function: {
          name: "generic_text_from_decimal_comparison",
          constants: ["SALE", "REFUND"],
          columns: ["booking_amount"]
        }
      },
      {sourceName: ${hotel_marriott_file.sourceName}, value: "SALE"},
      {sourceName: ${citilink_daily_file.sourceName}, colName: "transaction_type", mapper: [
        {key: "PPAccountDebitForPayment", value: "SALE"},
        {key: "PPAccountCredit-Topup", value: "REFUND"},
        {key: "DEFAULT_MAP_VALUE", value: "UNKNOWN"}
      ]},
      {sourceName: ${scoot_daily_file.sourceName}, function: {
        name: "generic_set_matching_value",
        constants: ["REFUND", "SALE", "-.+"],
        columns: ["booking_amount"]
      }},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, function: {
        name: "generic_set_matching_value",
        constants: ["REFUND", "SALE", "-.+"],
        columns: ["booking_amount"]
      }},
      {sourceName: ${hotel_hyatt_file.sourceName}, colName: "transaction_type"},
      {sourceName: ${fy_daily_file.sourceName}, colName: "transaction_type", mapper: [
        {key: "CreditAccountDebitForPayment", value: "SALE"},
        {key: "CreditAccountCredit", value: "REFUND"},
        {key: "DEFAULT_MAP_VALUE", value: "UNKNOWN"}
      ]},
      {sourceName: ${viator_daily_report.sourceName}, function: {
        name: "generic_set_number_is_null",
        constants: ["REFUND", "SALE"],
        columns: ["cancellation_usd"]
      }},
      {sourceName: ${kkday_daily_report.sourceName}, function: {
        name: "generic_set_matching_value",
        constants: ["SALE", "REFUND", "RCP"],
        columns: ["type_of_document"]
      }},
      {sourceName: ${bemyguest_daily_report.sourceName}, colName: "transaction_type"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, colName: "transaction_type"},
      {sourceName: ${hotel_hilton_file.sourceName}, value: "SALE"},
      {
        sourceName: ${airasia_daily_file.sourceName}, colName: "account_transaction_type",
        mapper: [
          {key: "PPAccountDebitForPayment", value: "SALE"},
          {key: "PPAccountCredit", value: "REFUND"},
          {key: "DEFAULT_MAP_VALUE", value: "UNKNOWN"}
        ]
      },
      {sourceName: ${direct_comm_file.sourceName}, value: "SALE"},
      {sourceName: ${cfar_covergenius_premium.sourceName}, colName: "transaction_type" },
      {sourceName: ${cfar_covergenius_claim.sourceName}, value: "Claim" },
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, colName: "transaction_type" },
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, colName: "transaction_type" },
      {sourceName: ${tway_daily_file.sourceName}, colName: "billing_class",
          mapper: [
            {key: "ISSUES", value: "SALE"},
            {key: "REFUNDS", value: "REFUND"}
          ]
      },
      {sourceName: ${akasa_daily_file.sourceName}, function:
          {name: "generic_text_from_decimal_comparison", constants: ["SALE", "REFUND"], columns: ["amount"]} },
      {sourceName: ${emt_daily_file.sourceName}, colName: "transaction_type",
        mapper: [
          {key: "AirBooking", value: "SALE"},
          {key: "Credit", value: "REFUND"},
          {key: "Refund", value: "REFUND"},
          {key: "offline refund", value: "REFUND"},
          {key: "Rejected", value: "SALE"},
          {key: "Offline Charge", value: "SALE"}
        ]
      },
      {sourceName: ${unififi_daily_file.sourceName}, colName: "order_type",
        mapper: [
          {key: "Purchase", value: "SALE"},
          {key: "Ancillary Purchase", value: "SALE"},
          {key: "Exchange", value: "SALE"},
          {key: "Refund", value: "REFUND"},
          {key: "Void Payment", value: "REFUND"}
        ]
      },
      {sourceName: ${fr24_daily_file.sourceName}, colName: "fund_type",
        mapper: [
          {key: "Baggage", value: "SALE"},
          {key: "Issued Tickets", value: "SALE"},
          {key: "Void Fee", value: "SALE"},
          {key: "Rescheduled", value: "EXCHANGE"},
          {key: "Refund", value: "REFUND"},
          {key: "Baggage Refund", value: "REFUND"},
          {key: "Void", value: "REFUND"},
          {key: "Cancel Tickets", value: "REFUND"},
        ]
      },
      {sourceName: ${tripjack_daily_file.sourceName},
        function: {
          name: "get_tripjack_transaction_type",
          columns: ["amendment_type"]
        }
      },
      {sourceName: ${dida_daily_file.sourceName}, colName: "transaction_type",
        mapper: [
          {key: "Ticket Issued", value: "SALE"},
          {key: "Ticket Refunded", value: "REFUND"},
          {key: "Ticket Exchanged", value: "EXCHANGE"},
        ]
      },
      {
        sourceName: ${nokair_daily_file.sourceName},
        function: {
                name: "generic_text_from_first_not_null",
                columns: ["debit_amount", "credit_amount"],
                constants: ["SALE", "REFUND"]
        }
      },
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, colName: "transaction_code",
        mapper: [
          {key: "TKTT", value: "SALE"},
          {key: "CANX", value: "SALE"},
          {key: "EMDA", value: "SALE"},
          {key: "EMDS", value: "SALE"},
          {key: "TASF", value: "SALE"},
          {key: "ADMA", value: "ADM"},
          {key: "ADMD", value: "ADM"},
          {key: "ADNT", value: "ADM"},
          {key: "SPDR", value: "ADM"},
          {key: "SSAD", value: "ADM"},
          {key: "RFNC", value: "REFUND"},
          {key: "RFND", value: "REFUND"},
          {key: "ACMA", value: "ACM"},
          {key: "ACMD", value: "ACM"},
          {key: "ACNT", value: "ACM"},
          {key: "SPCR", value: "ACM"},
          {key: "SSAC", value: "ACM"},
        ]
      },
      {
        sourceName: ${tripadd_daily_file.sourceName}, colName: "transaction",
        mapper: [
          {key: "sale", value: "SALE"},
          {key: "cancelation", value: "REFUND"},
        ]
      },
    ]
  },
  {
    aliasColName: "ticket_number", dataType: "StringType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "ticket_number"}
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "ticket_number"}
      {sourceName: ${tbo_daily_file.sourceName}, colName: "ticket_no"},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}, function: {name: "generic_coalesce", columns: ["parent_pnr", "record_locator"]}},
      {sourceName: ${vietjet_daily_file.sourceName}, function: {name: "generic_set_matching_value_with_null", constants: ["([nN]ull)|^$|\\s+"], columns: ["e_tkt"]}},
      {sourceName: ${thaivietjet_daily_file.sourceName}, function: {name: "generic_set_matching_value_with_null", constants: ["([nN]ull)|^$|\\s+"], columns: ["e_tkt"]}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}, colName: "ticket_no"},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName},
        function: {name: "generic_check_defined_null_or_whitespace", columns: ["original_pnr", "pnr"]}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}, colName: "account_reference"},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, colName: "ticket_number"},
      {sourceName: ${tway_daily_file.sourceName}, colName: "ticket_number"},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}, colName: "ticket_number"},
      {sourceName: ${fr24_daily_file.sourceName}, colName: "ticket_no"},
      {sourceName: ${tripjack_daily_file.sourceName}, colName: "ticket_number"},
      {sourceName: ${dida_daily_file.sourceName},
        function: {name: "generic_check_defined_null_or_whitespace", columns: ["old_ticket_no", "ticket_no"]}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, colName: "ticket_document_number"},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "currency", dataType: "StringType",
    colMapper: [
      {
        sourceName: ${chubb_daily_report.sourceName},
        function: {name: "generic_upper_case", columns: ["currency"]}
      },
      {sourceName: ${covergenius_daily_report.sourceName}, colName: "settlement_currency"},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "settlement_currency"},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, colName: "currency"},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "payable_currency"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "payable_currency"},
      {sourceName: ${tbo_daily_file.sourceName}, colName: "preferred_currency"},
      {sourceName: ${tbo_monthly_file.sourceName}, colName: "currency"},
      {sourceName: ${indigo_daily_file.sourceName}, colName: "payment_currency"},
      {sourceName: ${vietjet_daily_file.sourceName}, value: "USD"},
      {sourceName: ${thaivietjet_daily_file.sourceName}, value: "THB"},
      {sourceName: ${lionair_daily_file.sourceName}, colName: "currency_code"},
      {sourceName: ${lionair_idr_daily_file.sourceName}, colName: "currency_code"},
      {sourceName: ${pkfare_daily_file.sourceName}, colName: "currency"},
      {sourceName: ${nusa_daily_file.sourceName}, colName: "currency"},
      {sourceName: ${spicejet_daily_file.sourceName}, colName: "currency"},
      {sourceName: ${whypaymore_daily_file.sourceName}, colName: "client_currency"},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "supplier_currency"},
      {sourceName: ${jetstar_daily_file.sourceName}, colName: "currency"},
      {sourceName: ${gofirst_daily_file.sourceName}, colName: "booking_currency"},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "currency_code"},
      {sourceName: ${citilink_daily_file.sourceName}, colName: "curr"},
      {sourceName: ${scoot_daily_file.sourceName}, colName: "booking_currency"},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, colName: "booking_currency"},
      {sourceName: ${hotel_hyatt_file.sourceName}, colName: "conf_currency"},
      {sourceName: ${fy_daily_file.sourceName}, colName: "currency"},
      {sourceName: ${viator_daily_report.sourceName}, value: "USD"},
      {sourceName: ${kkday_daily_report.sourceName}, colName: "currency"},
      {sourceName: ${bemyguest_daily_report.sourceName}, colName: "currency"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, colName: "local_currency"},
      {sourceName: ${hotel_hilton_file.sourceName}, colName: "revenue_report_currency"},
      {sourceName: ${airasia_daily_file.sourceName}, colName: "currency_code"},
      {sourceName: ${direct_comm_file.sourceName}, colName: "receipt_currency"},
      {sourceName: ${cfar_covergenius_premium.sourceName}, colName: "customer_currency" },
      {sourceName: ${cfar_covergenius_claim.sourceName}, colName: "customer_currency" },
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, colName: "Currency"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, colName: "pay_currency"},
      {sourceName: ${tway_daily_file.sourceName},
        function: {name: "generic_exclude_suffix", columns: ["currency_code"], constants: ["0"]}},
      {sourceName: ${akasa_daily_file.sourceName}, value: "INR"},
      {sourceName: ${emt_daily_file.sourceName}, value: "INR"},
      {sourceName: ${unififi_daily_file.sourceName}, colName: "currency"},
      {sourceName: ${fr24_daily_file.sourceName}, colName: "currency"},
      {sourceName: ${tripjack_daily_file.sourceName}, value: "INR"},
      {sourceName: ${dida_daily_file.sourceName}, colName: "client_currency"},
      {sourceName: ${nokair_daily_file.sourceName}, value: "THB"},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, colName: "currency_type"},
      {sourceName: ${tripadd_daily_file.sourceName}, colName: "settlement_currency"},
    ]
  },
  {
    aliasColName: "cost", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, colName: "net_premium"},
      {
        sourceName: ${kiwi_daily_and_monthly_file.sourceName},
        function: {
          name: "generic_revert", columns: ["original_amount"]
        }
      },
      {sourceName: ${covergenius_daily_report.sourceName}, colName: "total_sale_sgd"},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "total_sale_usd"},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "cost"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "cost"},
      {
        sourceName: ${tbo_daily_file.sourceName},
        function: {name: "generic_revert_with_condition", constants: ["([A-Za-z]{2})/(\\d{4})/(\\d+)"], columns: ["invoice_no", "fare"]}
      },
      {sourceName: ${tbo_monthly_file.sourceName}, value:0},
      {sourceName: ${indigo_daily_file.sourceName}, value: 0},
      {sourceName: ${vietjet_daily_file.sourceName}, value: 0},
      {sourceName: ${thaivietjet_daily_file.sourceName}, value: 0},
      {sourceName: ${lionair_daily_file.sourceName}, value: 0},
      {sourceName: ${lionair_idr_daily_file.sourceName}, value: 0},
      {sourceName: ${pkfare_daily_file.sourceName}, value: 0},
      {sourceName: ${nusa_daily_file.sourceName}, value: 0},
      {sourceName: ${spicejet_daily_file.sourceName}, value: 0},
      {sourceName: ${whypaymore_daily_file.sourceName}, colName: "base_fare"},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "pcln_total_room_cost_rate"},
      {sourceName: ${jetstar_daily_file.sourceName}, value: 0},
      {sourceName: ${gofirst_daily_file.sourceName}, value: 0},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}, value: 0},
      {sourceName: ${scoot_daily_file.sourceName}, value: 0},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, value: 0},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}, value: 0},
      {
        sourceName: ${viator_daily_report.sourceName},
        function: {name: "generic_sum", columns: ["gross_booking_usd", "cancellation_usd"]}
      },
      {sourceName: ${kkday_daily_report.sourceName}, colName: "amount"},
      {
        sourceName: ${bemyguest_daily_report.sourceName},
        function: {name: "generic_revert_with_condition", constants: ["REFUND"], columns: ["transaction_type", "cost"]}
      },
      {
        sourceName: ${bemyguest_cm_daily_report.sourceName},
        function: {name: "generic_revert_with_condition", constants: ["REFUND"], columns: ["transaction_type", "cost_local"]}
      },
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}, value: 0},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}, colName: "total_sale" },
      {sourceName: ${cfar_covergenius_claim.sourceName}, value: 0},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, colName: "ticket_price" },
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, colName: "cost"},
      {sourceName: ${tway_daily_file.sourceName}, colName: "commissionable_amount"},
      {sourceName: ${akasa_daily_file.sourceName}, value: 0},
      {sourceName: ${emt_daily_file.sourceName}, value: 0},
      {sourceName: ${unififi_daily_file.sourceName}, value: 0},
      {sourceName: ${fr24_daily_file.sourceName}, value: 0},
      {sourceName: ${tripjack_daily_file.sourceName}, colName: "base_fare"},
      {
        sourceName: ${dida_daily_file.sourceName},
        function: {name: "generic_revert_with_condition", constants: ["REFUND"], columns: ["transaction_type", "base_fare"]}
      },
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, colName: "cost_amount"},
      {sourceName: ${tripadd_daily_file.sourceName}, colName: "total_gross_price"},
    ]
  },
  {
    aliasColName: "cost_usd", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName},},
      {sourceName: ${priceline_daily_gordian_file.sourceName},},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "pcln_total_room_cost_rate_usd"},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {
        sourceName: ${bemyguest_daily_report.sourceName},
        function: {name: "generic_revert_with_condition", constants: ["REFUND"], columns: ["transaction_type", "cost_usd"]}
      },
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}, colName: "total_sale_usd" },
      {sourceName: ${cfar_covergenius_claim.sourceName}, value: 0},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "margin", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName},},
      {sourceName: ${priceline_daily_gordian_file.sourceName},},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}, colName: "commission" },
      {sourceName: ${cfar_covergenius_claim.sourceName}, value: 0},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "margin_usd", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName},},
      {sourceName: ${priceline_daily_gordian_file.sourceName},},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}, colName: "commission_usd" },
      {sourceName: ${cfar_covergenius_claim.sourceName}, value: 0},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "commission", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, colName: "commission"},
      {sourceName: ${covergenius_daily_report.sourceName}, colName: "commission_sgd"},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "commission_usd"},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, value: 0},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "commission_detail_actual"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "commission_detail_actual"},
      {
        sourceName: ${tbo_daily_file.sourceName},
        function: {name: "generic_advance_sum", columns: ["h_charge_commission_rev_h_charge", "plb_amount", "tds"], constants: ["+,+,-"]}
      },
      {sourceName: ${tbo_monthly_file.sourceName}, value: 0},
      {sourceName: ${indigo_daily_file.sourceName}, value: 0},
      {sourceName: ${vietjet_daily_file.sourceName}, value: 0},
      {sourceName: ${thaivietjet_daily_file.sourceName}, value: 0},
      {sourceName: ${lionair_daily_file.sourceName}, value: 0},
      {sourceName: ${lionair_idr_daily_file.sourceName}, value: 0},
      {sourceName: ${pkfare_daily_file.sourceName}, value: 0},
      {sourceName: ${nusa_daily_file.sourceName}, value: 0},
      {sourceName: ${spicejet_daily_file.sourceName}, value: 0},
      {sourceName: ${whypaymore_daily_file.sourceName}, colName: "commission"},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}, value: 0},
      {sourceName: ${gofirst_daily_file.sourceName}, value: 0},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "comm_amt_ctac_num"},
      {sourceName: ${citilink_daily_file.sourceName}, value: 0},
      {sourceName: ${scoot_daily_file.sourceName}, value: 0},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, value: 0},
      {sourceName: ${hotel_hyatt_file.sourceName}, colName: "paid_commission"},
      {sourceName: ${fy_daily_file.sourceName}, value: 0},
      {sourceName: ${viator_daily_report.sourceName}, value: 0},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, colName: "commission"},
      {sourceName: ${hotel_hilton_file.sourceName}, colName: "report_com"},
      {sourceName: ${airasia_daily_file.sourceName}, value: 0},
      {sourceName: ${direct_comm_file.sourceName}, colName: "gross_commission_amount"},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}}
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, colName: "commission"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, value: 0},
      {sourceName: ${tway_daily_file.sourceName}, value: 0},
      {sourceName: ${akasa_daily_file.sourceName}, value: 0},
      {
        sourceName: ${emt_daily_file.sourceName},
        function: {name: "generic_revert_with_condition", constants: ["REFUND"], columns: ["transaction_type", "commission"]}
      },
      {sourceName: ${unififi_daily_file.sourceName}, value: 0},
      {sourceName: ${fr24_daily_file.sourceName}, value: 0},
      {sourceName: ${tripjack_daily_file.sourceName}, value: 0},
      {
        sourceName: ${dida_daily_file.sourceName},
        function: {name: "generic_revert_with_condition", constants: ["REFUND"], columns: ["transaction_type", "commission"]}
      },
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, colName: "commission_amount"},
      {
        sourceName: ${tripadd_daily_file.sourceName},
        function: {name: "generic_multiply", columns: ["unit_markup", "quantity"]}
      },
    ]
  },
  {
    aliasColName: "commission_usd", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName},},
      {sourceName: ${priceline_daily_gordian_file.sourceName},},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "comm_amt_pay_num"},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}, colName: "paid_commission"},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}, colName: "pay_com"},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}, colName: "gross_commission_amount_usd"},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "fee", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, value: 0},
      {sourceName: ${covergenius_daily_report.sourceName}, value: 0},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, value: 0},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, value: 0},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "fees_gds_detail_aggregator_actual"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "fees_gds_detail_aggregator_actual"},
      {
        sourceName: ${tbo_daily_file.sourceName},
        function: {name: "generic_sum", columns: ["service_charges", "cancellation_amount", "total_gst"]}
      },
      {sourceName: ${tbo_monthly_file.sourceName}, value: 0},
      {sourceName: ${indigo_daily_file.sourceName}, value: 0},
      {sourceName: ${vietjet_daily_file.sourceName}, value: 0},
      {sourceName: ${thaivietjet_daily_file.sourceName}, value: 0},
      {sourceName: ${lionair_daily_file.sourceName}, value: 0},
      {sourceName: ${lionair_idr_daily_file.sourceName}, value: 0},
      {sourceName: ${pkfare_daily_file.sourceName}, value: 0},
      {sourceName: ${nusa_daily_file.sourceName}, value: 0},
      {sourceName: ${spicejet_daily_file.sourceName}, value: 0},
      {
        sourceName: ${whypaymore_daily_file.sourceName},
        function: {name: "generic_sum", columns: ["ticketing_fee", "gst", "cancellation_charge"]}
      },
      {sourceName: ${hotel_violet_file.sourceName}, colName: "pcln_total_fee_cost"},
      {sourceName: ${jetstar_daily_file.sourceName}, value: 0},
      {sourceName: ${gofirst_daily_file.sourceName}, value: 0},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}, value: 0},
      {sourceName: ${scoot_daily_file.sourceName}, value: 0},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, value: 0},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}, value: 0},
      {sourceName: ${viator_daily_report.sourceName}, value: 0},
      {sourceName: ${kkday_daily_report.sourceName}, value: 0},
      {sourceName: ${bemyguest_daily_report.sourceName}, value: 0},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, value: 0},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}, value: 0},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, colName: "flight_change_fee"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, value: 0},
      {sourceName: ${tway_daily_file.sourceName}, value: 0},
      {sourceName: ${akasa_daily_file.sourceName}, value: 0},
      {sourceName: ${emt_daily_file.sourceName}, value: 0},
      {sourceName: ${unififi_daily_file.sourceName}, value: 0},
      {sourceName: ${fr24_daily_file.sourceName}, value: 0},
      {sourceName: ${tripjack_daily_file.sourceName}, value: 0},
      {
        sourceName: ${dida_daily_file.sourceName},
        function: {name: "generic_sum", columns: ["ticketing_fee", "cancellation_charge"]}
      },
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, value: 0},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "fee_usd", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName},},
      {sourceName: ${priceline_daily_gordian_file.sourceName},},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "pcln_total_fee_cost_usd"},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "admin_fee", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, value: 0},
      {sourceName: ${covergenius_daily_report.sourceName}, value: 0},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, value: 0},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, value: 0},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "admin_fee"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "admin_fee"},
      {sourceName: ${tbo_daily_file.sourceName}, value: 0},
      {sourceName: ${tbo_monthly_file.sourceName}, value: 0},
      {sourceName: ${indigo_daily_file.sourceName}, value: 0},
      {sourceName: ${vietjet_daily_file.sourceName}, value: 0},
      {sourceName: ${thaivietjet_daily_file.sourceName}, value: 0},
      {sourceName: ${lionair_daily_file.sourceName}, value: 0},
      {sourceName: ${lionair_idr_daily_file.sourceName}, value: 0},
      {sourceName: ${pkfare_daily_file.sourceName}, value: 0},
      {sourceName: ${nusa_daily_file.sourceName}, value: 0},
      {sourceName: ${spicejet_daily_file.sourceName}, value: 0},
      {sourceName: ${whypaymore_daily_file.sourceName}, colName: "admin_fee"},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}, value: 0},
      {sourceName: ${gofirst_daily_file.sourceName}, value: 0},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}, value: 0},
      {sourceName: ${scoot_daily_file.sourceName}, value: 0},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, value: 0},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}, value: 0},
      {sourceName: ${viator_daily_report.sourceName}, value: 0},
      {sourceName: ${kkday_daily_report.sourceName}, value: 0},
      {sourceName: ${bemyguest_daily_report.sourceName}, value: 0},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, value: 0},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}, value: 0},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, colName: "ticketing_fee"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, value: 0},
      {sourceName: ${tway_daily_file.sourceName}, value: 0},
      {sourceName: ${akasa_daily_file.sourceName}, value: 0},
      {sourceName: ${emt_daily_file.sourceName}, value: 0},
      {sourceName: ${unififi_daily_file.sourceName},
        function: {
          name: "generic_revert", columns: ["service_fee"]
        }
      },
      {sourceName: ${fr24_daily_file.sourceName}, value: 0},
      {sourceName: ${tripjack_daily_file.sourceName}, colName: "payment_fee"},
      {
        sourceName: ${dida_daily_file.sourceName},
        function: {name: "generic_revert_with_condition", constants: ["REFUND"], columns: ["transaction_type", "admin_fee"]}
      },
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, value: 0},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "admin_fee_usd", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName},},
      {sourceName: ${priceline_daily_gordian_file.sourceName},},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "penalty", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, value: 0},
      {sourceName: ${covergenius_daily_report.sourceName}, value: 0},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, value: 0},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, value: 0},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "penalty"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "penalty"},
      {sourceName: ${tbo_daily_file.sourceName}, value: 0},
      {sourceName: ${tbo_monthly_file.sourceName}, value: 0},
      {sourceName: ${indigo_daily_file.sourceName}, value: 0},
      {sourceName: ${vietjet_daily_file.sourceName}, value: 0},
      {sourceName: ${thaivietjet_daily_file.sourceName}, value: 0},
      {sourceName: ${lionair_daily_file.sourceName}, value: 0},
      {sourceName: ${lionair_idr_daily_file.sourceName}, value: 0},
      {sourceName: ${pkfare_daily_file.sourceName}, value: 0},
      {sourceName: ${nusa_daily_file.sourceName}, value: 0},
      {sourceName: ${spicejet_daily_file.sourceName}, value: 0},
      {sourceName: ${whypaymore_daily_file.sourceName}, colName: "penalty"},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "pcln_total_penalty"},
      {sourceName: ${jetstar_daily_file.sourceName}, value: 0},
      {sourceName: ${gofirst_daily_file.sourceName}, value: 0},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}, value: 0},
      {sourceName: ${scoot_daily_file.sourceName}, value: 0},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, value: 0},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}, value: 0},
      {sourceName: ${viator_daily_report.sourceName}, value: 0},
      {sourceName: ${kkday_daily_report.sourceName}, value: 0},
      {sourceName: ${bemyguest_daily_report.sourceName}, value: 0},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, value: 0},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}, value: 0},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, colName: "cancellation_fee"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, colName: "canx_fee"},
      {sourceName: ${tway_daily_file.sourceName}, value: 0},
      {sourceName: ${akasa_daily_file.sourceName}, value: 0},
      {sourceName: ${emt_daily_file.sourceName}, value: 0},
      {sourceName: ${unififi_daily_file.sourceName}, value: 0},
      {sourceName: ${fr24_daily_file.sourceName}, value: 0},
      {sourceName: ${tripjack_daily_file.sourceName}, value: 0},
      {
        sourceName: ${dida_daily_file.sourceName},
        function: {name: "generic_revert_with_condition", constants: ["REFUND"], columns: ["transaction_type", "penalty"]}
      },
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, value: 0},
      {sourceName: ${tripadd_daily_file.sourceName}, value: 0},
    ]
  },
  {
    aliasColName: "penalty_usd", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName},},
      {sourceName: ${priceline_daily_gordian_file.sourceName},},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "pcln_total_penalty_usd"},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "tax", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {
        sourceName: ${chubb_daily_report.sourceName},
        function: {name: "generic_sum", columns: ["stamp_duty", "service_tax", "gst"]}
      },
      {sourceName: ${covergenius_daily_report.sourceName}, colName: "total_tax_sgd"},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "total_tax_usd"},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, value: 0},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "tax"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "tax"},
      {
        sourceName: ${tbo_daily_file.sourceName},
        function: {name: "generic_revert_with_condition", constants: ["([A-Za-z]{2})/(\\d{4})/(\\d+)"], columns: ["invoice_no", "tax"]}
      },
      {sourceName: ${tbo_monthly_file.sourceName}, value: 0},
      {sourceName: ${indigo_daily_file.sourceName}, value: 0},
      {sourceName: ${vietjet_daily_file.sourceName}, value: 0},
      {sourceName: ${thaivietjet_daily_file.sourceName}, value: 0},
      {sourceName: ${lionair_daily_file.sourceName}, value: 0}
      {sourceName: ${lionair_idr_daily_file.sourceName}, value: 0}
      {sourceName: ${pkfare_daily_file.sourceName}, value: 0},
      {sourceName: ${nusa_daily_file.sourceName}, value: 0},
      {sourceName: ${spicejet_daily_file.sourceName}, value: 0},
      {sourceName: ${whypaymore_daily_file.sourceName}, colName: "tax"},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "pcln_total_room_tax_rate"},
      {sourceName: ${jetstar_daily_file.sourceName}, value: 0},
      {sourceName: ${gofirst_daily_file.sourceName}, value: 0},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "tax_amt_ctac_num"},
      {sourceName: ${citilink_daily_file.sourceName}, value: 0},
      {sourceName: ${scoot_daily_file.sourceName}, value: 0},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, value: 0},
      {sourceName: ${hotel_hyatt_file.sourceName}, colName: "vat_rate_total"},
      {sourceName: ${fy_daily_file.sourceName}, value: 0},
      {sourceName: ${viator_daily_report.sourceName}, value: 0},
      {sourceName: ${kkday_daily_report.sourceName}, value: 0},
      {sourceName: ${bemyguest_daily_report.sourceName}, colName: "gst_amount"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, value: 0},
      {sourceName: ${hotel_hilton_file.sourceName}, colName: "report_tax"},
      {sourceName: ${airasia_daily_file.sourceName}, value: 0},
      {sourceName: ${direct_comm_file.sourceName}, value: 0},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, value: 0},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, colName: "total_tax"},
      {sourceName: ${tway_daily_file.sourceName}, function: { name: "generic_advance_sum", constants: ["+,-"], columns: ["cash_amount", "commissionable_amount"] }},
      {sourceName: ${akasa_daily_file.sourceName}, colName: "taxes"},
      {sourceName: ${emt_daily_file.sourceName}, value: 0},
      {sourceName: ${unififi_daily_file.sourceName},
        function: {
          name: "generic_revert", columns: ["tax"]
        }
      },
      {sourceName: ${fr24_daily_file.sourceName}, value: 0},
      {sourceName: ${tripjack_daily_file.sourceName}, colName: "other_taxes"},
      {
        sourceName: ${dida_daily_file.sourceName},
        function: {name: "generic_revert_with_condition", constants: ["REFUND"], columns: ["transaction_type", "tax"]}
      },
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, colName: "tax_amount"},
      {sourceName: ${tripadd_daily_file.sourceName}, value: 0},
    ]
  },
  {
    aliasColName: "tax_usd", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName},},
      {sourceName: ${priceline_daily_gordian_file.sourceName},},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "pcln_total_room_tax_rate_usd"},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "tax_amt_pay_num"},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}, colName: "vat_rate_total"},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}, colName: "pay_tax"},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}, value: 0},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "tax_on_commission_pass_to_property", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName},},
      {sourceName: ${priceline_daily_gordian_file.sourceName},},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}, colName: "b2b_service_tax"},
      {sourceName: ${cfar_covergenius_claim.sourceName}, value: 0},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "tax_on_commission_pass_to_property_usd", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName},},
      {sourceName: ${priceline_daily_gordian_file.sourceName},},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}, colName: "b2b_service_tax_usd"},
      {sourceName: ${cfar_covergenius_claim.sourceName}, value: 0},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "claim", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName},},
      {sourceName: ${priceline_daily_gordian_file.sourceName},},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}, value: 0},
      {sourceName: ${cfar_covergenius_claim.sourceName}, colName: "cfar_refund"},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "claim_usd", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName},},
      {sourceName: ${priceline_daily_gordian_file.sourceName},},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}, value: 0},
      {sourceName: ${cfar_covergenius_claim.sourceName}, colName: "cfar_refund_usd"},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "total_amount", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, colName: "total_premium"},
      {sourceName: ${covergenius_daily_report.sourceName}, colName: "total_sale_sgd"},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "total_sale_usd"},
      {
        sourceName: ${kiwi_daily_and_monthly_file.sourceName},
        function: {
          name: "generic_revert", columns: ["original_amount"]
        }
      },
      {sourceName: ${priceline_daily_file.sourceName}, colName: "total_amount"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "total_amount"},
      {
        sourceName: ${tbo_daily_file.sourceName},
        function: {name: "generic_revert_with_condition", constants: ["([A-Za-z]{2})/(\\d{4})/(\\d+)"], columns: ["invoice_no", "total_amount_preferred_curr"]}
      },
      {
        sourceName: ${tbo_monthly_file.sourceName},
        function: {name: "generic_revert_sum_with_condition", constants: ["0.00"], columns: ["debit_amount", "debit_amount", "credit_amount"]}
      },
      {sourceName: ${indigo_daily_file.sourceName}, colName: "payment_amount"},
      {sourceName: ${vietjet_daily_file.sourceName}, colName: "converted_amount_usd"},
      {sourceName: ${thaivietjet_daily_file.sourceName}, colName: "converted_amount_thb"},
      {
        sourceName: ${lionair_daily_file.sourceName},
        function: {
          name: "get_lionair_total_amount", columns: ["transaction_type", "transaction_amount", "ancillaries"]
        }
      },
      {
        sourceName: ${lionair_idr_daily_file.sourceName},
        function: {
          name: "get_lionair_total_amount", columns: ["transaction_type", "transaction_amount", "ancillaries"]
        }
      },
      {
        sourceName: ${pkfare_daily_file.sourceName},
        function: {
          name: "generic_revert", columns: ["amount"]
        }
      },
      {
        sourceName: ${nusa_daily_file.sourceName},
        function: {
          name: "generic_revert", columns: ["amount"]
        }
      },
      {sourceName: ${spicejet_daily_file.sourceName}, colName: "amount"},
      {sourceName: ${whypaymore_daily_file.sourceName}, colName: "total_amount"},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "pcln_total_trans_amount"},
      {
        sourceName: ${jetstar_daily_file.sourceName},
        function: {name: "generic_get_normalized_amount_within_brackets", columns: ["payment_value_in_aud"]}
      },
      {sourceName: ${gofirst_daily_file.sourceName}, colName: "booking_amount"},
      {
        sourceName: ${hotel_marriott_file.sourceName},
        function: {name: "generic_sum", columns: ["comm_amt_ctac_num", "tax_amt_ctac_num"]}
      },
      {
        sourceName: ${citilink_daily_file.sourceName},
        function: {name: "get_citilink_total_amount", columns: ["transaction_type", "credit_amount", "debit_amount"]}
      },
      {
        sourceName: ${scoot_daily_file.sourceName},
        function: {name: "generic_convert_string_to_big_decimal", columns: ["booking_amount"]}
      },
      {
        sourceName: ${scoot_ndc_daily_file.sourceName},
        function: {name: "generic_convert_string_to_big_decimal", columns: ["booking_amount"]}
      },
      {sourceName: ${hotel_hyatt_file.sourceName}, colName: "amount"},
      {
        sourceName: ${fy_daily_file.sourceName},
        function: {name: "generic_revert", columns: ["amount"]}
      },
      {
        sourceName: ${viator_daily_report.sourceName},
        function: {name: "generic_sum", columns: ["gross_booking_usd", "cancellation_usd"]}
      },
      {sourceName: ${kkday_daily_report.sourceName}, colName: "amount"},
      {
        sourceName: ${bemyguest_daily_report.sourceName},
        function: {name: "generic_revert_with_condition", constants: ["REFUND"], columns: ["transaction_type", "total_amount"]}
      },
      {
        sourceName: ${bemyguest_cm_daily_report.sourceName},
        function: {name: "generic_revert_with_condition", constants: ["REFUND"], columns: ["transaction_type", "total_amount"]}
      },
      {sourceName: ${hotel_hilton_file.sourceName}, colName: "report_revenue"},
      {
        sourceName: ${airasia_daily_file.sourceName},
        function: {
          name: "generic_revert", columns: ["amount"]
        }
      },
      {sourceName: ${direct_comm_file.sourceName}, colName: "gross_commission_amount"},
      {sourceName: ${cfar_covergenius_premium.sourceName}, colName: "total_sale" },
      {sourceName: ${cfar_covergenius_claim.sourceName}, value: 0},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, colName: "amount_billed"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, colName: "total_amount" },
      {sourceName: ${tway_daily_file.sourceName}, colName: "cash_amount"},
      {sourceName: ${akasa_daily_file.sourceName}, colName: "amount"},
      {
        sourceName: ${emt_daily_file.sourceName},
        function: {name: "generic_revert_with_condition", constants: ["REFUND"], columns: ["transaction_type", "transaction_amount"]}
      },
      {sourceName: ${unififi_daily_file.sourceName},
        function: {
          name: "generic_revert", columns: ["total_price"]
        }
      },
      {sourceName: ${fr24_daily_file.sourceName},
        function: {
          name: "generic_revert", columns: ["amount"]
        }
      },
      {sourceName: ${tripjack_daily_file.sourceName}, colName: "net_fare"},
      {
        sourceName: ${dida_daily_file.sourceName},
        function: {
          name: "generic_revert", columns: ["total_amount"]
        }
      },
      {
        sourceName: ${nokair_daily_file.sourceName},
        function: {name: "generic_advance_sum", constants: ["+,-"], columns: ["debit_amount", "credit_amount"]}
      },
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, colName: "ticket_document_amount"},
      {sourceName: ${tripadd_daily_file.sourceName}, colName: "invoice_amount"},
    ]
  },
  {
    aliasColName: "total_amount_usd", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName},},
      {sourceName: ${priceline_daily_gordian_file.sourceName},},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "pcln_total_trans_amount_usd"},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {
        sourceName: ${hotel_marriott_file.sourceName},
        function: {name: "generic_sum", columns: ["comm_amt_pay_num", "tax_amt_pay_num"]}
      },
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}, colName: "amount"},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {
        sourceName: ${bemyguest_daily_report.sourceName},
        function: {name: "generic_revert_with_condition", constants: ["REFUND"], columns: ["transaction_type", "total_amount_usd"]}
      },
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}, colName: "report_revenue"},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}, colName: "gross_commission_amount_usd"},
      {sourceName: ${cfar_covergenius_premium.sourceName}, colName: "total_sale_usd" },
      {sourceName: ${cfar_covergenius_claim.sourceName}, value: 0},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "supplier_name", dataType: "StringType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, value: "Chubb"},
      {sourceName: ${covergenius_daily_report.sourceName}, value: "XCover"},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "product_type", mapper: [
        {key: "Rocket Travel - Premium Post Departure Protection", value: "XCover"},
        {key: "DEFAULT_MAP_VALUE", value: "RentalCover"}
      ]},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, value: "Kiwi.com s.r.o."},
      {sourceName: ${priceline_daily_file.sourceName}, value: "Priceline"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, value: "Priceline"},
      {sourceName: ${tbo_daily_file.sourceName}, value: "Travelboutiqueonline"},
      {sourceName: ${tbo_monthly_file.sourceName}, value: "Travelboutiqueonline"},
      {sourceName: ${indigo_daily_file.sourceName}, value: "Indigo - Direct Connect"},
      {sourceName: ${vietjet_daily_file.sourceName}, value: "Vietjet - Direct Connect"},
      {sourceName: ${thaivietjet_daily_file.sourceName}, value: "Thai Vietjet - Direct Connect"},
      {sourceName: ${lionair_daily_file.sourceName}, value: "LionAir-Direct connect"},
      {sourceName: ${lionair_idr_daily_file.sourceName}, value: "LionAir (IDR) - Direct Connect"},
      {sourceName: ${pkfare_daily_file.sourceName}, value: "PKFare - Flights"},
      {sourceName: ${nusa_daily_file.sourceName}, value: "NUSATRIP-direct connect"},
      {sourceName: ${spicejet_daily_file.sourceName}, value: "SpiceJet - Direct Connect"},
      {sourceName: ${whypaymore_daily_file.sourceName}, value: "WhyPayMore - Domestic"},
      {sourceName: ${hotel_violet_file.sourceName}, value: "Priceline Violet Hotel"},
      {sourceName: ${jetstar_daily_file.sourceName}, value: "Jetstar - Direct Connect"},
      {sourceName: ${gofirst_daily_file.sourceName}, value: "GoFirst – Direct Connect"},
      {sourceName: ${hotel_marriott_file.sourceName}, value: "Marriott by Derbysoft"},
      {sourceName: ${citilink_daily_file.sourceName}, value: "Citilink - Direct Connect"},
      {sourceName: ${scoot_daily_file.sourceName}, value: "Scoot - Direct Connect"},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, value: "Scoot NDC - Direct Connect"},
      {sourceName: ${hotel_hyatt_file.sourceName}, value: "Hyatt Direct Connect"},
      {sourceName: ${fy_daily_file.sourceName}, value: "FY - Direct Connect"},
      {sourceName: ${viator_daily_report.sourceName}, value: "Viator"},
      {sourceName: ${kkday_daily_report.sourceName}, value: "KKday"},
      {sourceName: ${bemyguest_daily_report.sourceName}, value: "Bemyguest"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, value: "BemyguestChannelManager"},
      {sourceName: ${hotel_hilton_file.sourceName}, value: "Hilton by Derbysoft"},
      {sourceName: ${airasia_daily_file.sourceName}, value: "AirAsia REST - Agoda Connect"},
      {sourceName: ${direct_comm_file.sourceName}, colName: "invoice_product_id", mapper: [
        {key: 1, value: "Hilton by Derbysoft"}
      ]},
      { sourceName: ${cfar_covergenius_premium.sourceName}, value: "XCover" },
      { sourceName: ${cfar_covergenius_claim.sourceName}, value: "XCover" },
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, value: "Airtrip"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, value: "JejuAir - Direct Connect"},
      {sourceName: ${tway_daily_file.sourceName}, value: "TWAY AIR - Direct Connect" },
      {sourceName: ${akasa_daily_file.sourceName}, value: "Akasa Air - Direct Connect" },
      {sourceName: ${emt_daily_file.sourceName}, value: "EaseMyTrip" },
      {sourceName: ${unififi_daily_file.sourceName}, value: "Unififi" },
      {sourceName: ${fr24_daily_file.sourceName}, value: "Flightroutes24" },
      {sourceName: ${tripjack_daily_file.sourceName}, value: "Tripjack" },
      {sourceName: ${dida_daily_file.sourceName}, value: "DidaTravel - Agoda Connect"},
      {sourceName: ${nokair_daily_file.sourceName}, value: "Nok Air - Direct connect"},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, value: "Holiday Tour IATA" },
      {sourceName: ${tripadd_daily_file.sourceName}, value: "TripAdd"},
    ]
  },
  {
    aliasColName: "sub_supplier_name", dataType: "StringType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, value: "Chubb"},
      {sourceName: ${covergenius_daily_report.sourceName}, value: "XCover"},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "product_type", mapper: [
        {key: "Rocket Travel - Premium Post Departure Protection", value: "XCover"},
        {key: "DEFAULT_MAP_VALUE", value: "RentalCover"}
      ]},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, value: "Kiwi.com s.r.o."},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "processing_partner"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "processing_partner"},
      {sourceName: ${tbo_daily_file.sourceName}, value: "Travelboutiqueonline"},
      {sourceName: ${tbo_monthly_file.sourceName}, value: "Travelboutiqueonline"},
      {sourceName: ${indigo_daily_file.sourceName}, value: "Indigo - Direct Connect"},
      {sourceName: ${vietjet_daily_file.sourceName}, value: "Vietjet - Direct Connect"},
      {sourceName: ${thaivietjet_daily_file.sourceName}, value: "Thai Vietjet - Direct Connect"},
      {sourceName: ${lionair_daily_file.sourceName}, value: "LionAir-Direct connect"},
      {sourceName: ${lionair_idr_daily_file.sourceName}, value: "LionAir (IDR) - Direct Connect"},
      {sourceName: ${pkfare_daily_file.sourceName}, value: "PKFare - Flights"},
      {sourceName: ${nusa_daily_file.sourceName}, value: "NUSATRIP-direct connect"},
      {sourceName: ${spicejet_daily_file.sourceName}, value: "SpiceJet - Direct Connect"},
      {sourceName: ${whypaymore_daily_file.sourceName}, value: "WhyPayMore - Domestic"},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "parent_brand_name"},
      {sourceName: ${jetstar_daily_file.sourceName}, value: "Jetstar - Direct Connect"},
      {sourceName: ${gofirst_daily_file.sourceName}, value: "GoFirst - Direct Connect"},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "prop_name"},
      {sourceName: ${citilink_daily_file.sourceName}, value: "Citilink - Direct Connect"},
      {sourceName: ${scoot_daily_file.sourceName}, value: "Scoot - Direct Connect"},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, value: "Scoot NDC - Direct Connect"},
      {sourceName: ${hotel_hyatt_file.sourceName}, value: "Hyatt Direct Connect"},
      {sourceName: ${fy_daily_file.sourceName}, value: "FY - Direct Connect"},
      {sourceName: ${viator_daily_report.sourceName}, value: "Viator"},
      {sourceName: ${kkday_daily_report.sourceName}, value: "KKday"},
      {sourceName: ${bemyguest_daily_report.sourceName}, value: "Bemyguest"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, colName: "operator_name"},
      {sourceName: ${hotel_hilton_file.sourceName}, colName: "property_name"},
      {sourceName: ${airasia_daily_file.sourceName}, value: "AirAsia REST - Agoda Connect"},
      {sourceName: ${direct_comm_file.sourceName}, colName: "invoice_product_id", mapper: [
        {key: 1, value: "Hilton by Derbysoft Direct Sub Supplier"}
      ]},
      { sourceName: ${cfar_covergenius_premium.sourceName}, value: "XCover" },
      { sourceName: ${cfar_covergenius_claim.sourceName}, value: "XCover" },
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, value: "Airtrip"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, value: "JejuAir - Direct Connect"},
      {sourceName: ${tway_daily_file.sourceName}, value: "TWAY AIR - Direct Connect" },
      {sourceName: ${akasa_daily_file.sourceName}, value: "Akasa Air - Direct Connect" },
      {sourceName: ${emt_daily_file.sourceName}, value: "EaseMyTrip" },
      {sourceName: ${unififi_daily_file.sourceName}, value: "Unififi" },
      {sourceName: ${fr24_daily_file.sourceName}, value: "Flightroutes24" },
      {sourceName: ${tripjack_daily_file.sourceName}, value: "Tripjack" },
      {sourceName: ${dida_daily_file.sourceName}, value: "DidaDC"},
      {sourceName: ${nokair_daily_file.sourceName}, value: "Nok Air - Direct connect"},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, value: "Holiday Tour IATA" },
      {sourceName: ${tripadd_daily_file.sourceName}, value: "TripAdd"},
    ]
  },
  {
    aliasColName: "sub_supplier_short_name", dataType: "StringType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, value: "CHUBB"},
      {sourceName: ${covergenius_daily_report.sourceName}, value: "XCOVER"},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "product_type", mapper: [
        {key: "Rocket Travel - Premium Post Departure Protection", value: "XCOVER"},
        {key: "DEFAULT_MAP_VALUE", value: "RENTALCOVER"}
      ]},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, value: "KIWI"},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "processing_partner"}, // Confirm column
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "processing_partner"}, // Confirm column
      {sourceName: ${tbo_daily_file.sourceName}, value: "TBO"},
      {sourceName: ${tbo_monthly_file.sourceName}, value: "TBO"},
      {sourceName: ${indigo_daily_file.sourceName}, value: "IND"},
      {sourceName: ${vietjet_daily_file.sourceName}, value: "VIET"},
      {sourceName: ${thaivietjet_daily_file.sourceName}, value: "VZ"},
      {sourceName: ${lionair_daily_file.sourceName}, value: "LA"},
      {sourceName: ${lionair_idr_daily_file.sourceName}, value: "LAIDR"},
      {sourceName: ${pkfare_daily_file.sourceName}, value: "PKF"},
      {sourceName: ${nusa_daily_file.sourceName}, value: "NTR1"},
      {sourceName: ${spicejet_daily_file.sourceName}, value: "SJ"},
      {sourceName: ${whypaymore_daily_file.sourceName}, value: "WPM"},
      {sourceName: ${hotel_violet_file.sourceName}, value: "PVH"},
      {sourceName: ${jetstar_daily_file.sourceName}, value: "JS"},
      {sourceName: ${gofirst_daily_file.sourceName}, value: "GF"},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "prop_code"},
      {sourceName: ${citilink_daily_file.sourceName}, value: "CL"},
      {sourceName: ${scoot_daily_file.sourceName}, value: "SC"},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, value: "ScootNDC"},
      {sourceName: ${hotel_hyatt_file.sourceName}, value: "HYA"},
      {sourceName: ${fy_daily_file.sourceName}, value: "FY"},
      {sourceName: ${viator_daily_report.sourceName}, value: "VIATOR"},
      {sourceName: ${kkday_daily_report.sourceName}, value: "KKDAY"},
      {sourceName: ${bemyguest_daily_report.sourceName}, value: "BEMYGUEST"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, colName: "operator_name"},
      {sourceName: ${hotel_hilton_file.sourceName}, colName: "property_code"},
      {sourceName: ${airasia_daily_file.sourceName}, value: "AAREST"},
      {sourceName: ${direct_comm_file.sourceName}, colName: "invoice_product_id", mapper: [
        {key: 1, value: "HILTONDIRECT"}
      ]}
      { sourceName: ${cfar_covergenius_premium.sourceName}, value: "XCOVER" },
      { sourceName: ${cfar_covergenius_claim.sourceName}, value: "XCOVER" },
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, value: "Airtrip"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, value: "JejuAir"},
      {sourceName: ${tway_daily_file.sourceName}, value: "TWAYAIR" },
      {sourceName: ${akasa_daily_file.sourceName}, value: "AKASAAIR" },
      {sourceName: ${emt_daily_file.sourceName}, value: "EMT" },
      {sourceName: ${unififi_daily_file.sourceName}, value: "Unififi" },
      {sourceName: ${fr24_daily_file.sourceName}, value: "FR24" },
      {sourceName: ${tripjack_daily_file.sourceName}, value: "TJ" },
      {sourceName: ${dida_daily_file.sourceName}, value: "DIDA"},
      {sourceName: ${nokair_daily_file.sourceName}, value: "NOK"}, // TODO: check
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, value: "HTIATA" },
      {sourceName: ${dida_daily_file.sourceName}, value: "DidaDC"},
      {sourceName: ${nokair_daily_file.sourceName}, value: "NOK"},
      {sourceName: ${tripadd_daily_file.sourceName}, value: "TRIPADD"},
    ]
  },
  {
    aliasColName: "payment_model", dataType: "StringType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, colName: "payment_model"
        mapper: [
            {key: "Merchant_Model", value: "MERCHANT"},
            {key: "Merchant_Commission", value: "MERCHANTCOM"}
        ]
      },
      {sourceName: ${covergenius_daily_report.sourceName}, value: "MERCHANTCOM"},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, value: "AGENCY"},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "ticket_type"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "ticket_type"},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {
        sourceName: ${whypaymore_daily_file.sourceName}, colName: "payment_type",
        mapper: [
          {key: "CARD", value: "MERCHANT"},
          {key: "CASH", value: "MERCHANT"},
        ]
      },
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "payment_model",
           mapper: [
              {key: "1", value: "MERCHANT"},
              {key: "2", value: "AGENCY"},
              {key: "4", value: "MERCHANTCOM"},
              {key: "0", value: "UNKNOWN"},
           ]
      },
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}, value: "MERCHANTCOM"},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}, value: "MERCHANTCOM"},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}, value: "MERCHANT"},
    ]
  },
  {
    # date when transactions happen
    aliasColName: "transaction_date_utc", dataType: "TimestampType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, colName: "transaction_date"},
      {sourceName: ${covergenius_daily_report.sourceName}, colName: "transaction_date"},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "transaction_date"},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, colName: "transaction_timestamp"},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "transaction_date_utc"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "transaction_date_utc"},
      {sourceName: ${tbo_daily_file.sourceName}, colName: "date"},
      {sourceName: ${tbo_monthly_file.sourceName}, colName: "transaction_date"},
      {sourceName: ${indigo_daily_file.sourceName}, colName: "created_date"},
      {sourceName: ${vietjet_daily_file.sourceName}, colName: "pmt_date"},
      {sourceName: ${thaivietjet_daily_file.sourceName}, colName: "pmt_date"},
      {sourceName: ${lionair_daily_file.sourceName}, colName: "transaction_timestamp"},
      {sourceName: ${lionair_idr_daily_file.sourceName}, colName: "transaction_timestamp"},
      {sourceName: ${pkfare_daily_file.sourceName}, colName: "time"},
      {sourceName: ${nusa_daily_file.sourceName}, colName: "transaction_date"},
      {sourceName: ${spicejet_daily_file.sourceName}, colName: "payment_date"},
      {sourceName: ${whypaymore_daily_file.sourceName}, colName: "transaction_date"},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "pcln_last_trans_date"},
      {sourceName: ${jetstar_daily_file.sourceName}, colName: "payment_date"},
      # Need to convert from IST to UTC
      {sourceName: ${gofirst_daily_file.sourceName},
        function: {
          name: "generic_offset_timestamp",
          constants: ["-5", "-30"],
          columns: ["payment_date"]
        }
      },
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}, colName: "date"},
      # Need to convert from SGT to UTC
      {sourceName: ${scoot_daily_file.sourceName},
        function: {
          name: "generic_offset_timestamp",
          constants: ["-8", "0"],
          columns: ["payment_date"]
        }
      },
      {sourceName: ${scoot_ndc_daily_file.sourceName},
        function: {
          name: "generic_offset_timestamp",
          constants: ["-8", "0"],
          columns: ["payment_date"]
        }
      },
      # hyatt send only date, no timestamp
      {sourceName: ${hotel_hyatt_file.sourceName}, colName: "payment_date"},
      {sourceName: ${fy_daily_file.sourceName}, colName: "date"},
      {sourceName: ${viator_daily_report.sourceName},
        function: {
          name: "generic_offset_timestamp",
          constants: ["+7", "0"],
          columns: ["order_date"]
        }
      },
      {sourceName: ${kkday_daily_report.sourceName}, colName: "order_date"},
      {sourceName: ${bemyguest_daily_report.sourceName}, colName: "transaction_date_utc"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, colName: "transaction_date_utc"},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}, colName: "transaction_date"},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}, colName: "transaction_date" },
      {sourceName: ${cfar_covergenius_claim.sourceName}, colName: "claim_date" },
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, colName: "date_of_issue"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, colName: "transaction_date_utc"},
      {sourceName: ${tway_daily_file.sourceName},
        function: {
          name: "generic_offset_timestamp",
          constants: ["-9", "0"], // KST to UTC
          columns: ["date_of_issue"]
        }
      },
      {sourceName: ${akasa_daily_file.sourceName}, colName: "transaction_date_time"},
      {sourceName: ${emt_daily_file.sourceName},
        function: {
          name: "generic_combine_datetime",
          columns: ["transaction_date", "transaction_time"]
        }
      },
      {
        sourceName: ${unififi_daily_file.sourceName},
        function: {
          name: "generic_offset_timestamp",
          constants: ["-8", "0"], // GMT+8 to UTC
          columns: ["ticketing_time"]
        }
      },
      {
        sourceName: ${fr24_daily_file.sourceName},
        function: {
          name: "generic_offset_timestamp",
          constants: ["-8", "0"], // GMT+8 to UTC
          columns: ["transaction_time_of_the_order"]
        }
      },
      {
        sourceName: ${tripjack_daily_file.sourceName},
        function: {
          name: "get_tripjack_transaction_date",
          columns: ["transaction_type", "booking_date", "booking_time", "amendment_date"]
        }
      },
      {
        sourceName: ${dida_daily_file.sourceName},
        function: {
          name: "generic_offset_timestamp",
          constants: ["-8", "0"], // GMT+8 to UTC
          columns: ["transaction_date"]
        }
      },
       {sourceName: ${nokair_daily_file.sourceName},
         function: {
           name: "generic_offset_timestamp",
           constants: ["-7", "0"], // GMT+7 to UTC
           columns: ["date"]
         }
       },
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, colName: "date_of_issue"},
      {
        sourceName: ${nokair_daily_file.sourceName},
        function: {
          name: "generic_offset_timestamp",
          constants: ["-7", "0"], // GMT+7 to UTC
          columns: ["date"]
        }
      },
      {
        sourceName: ${tripadd_daily_file.sourceName},
        function: {
          name: "generic_offset_timestamp",
          constants: ["-7", "0"], // GMT+7 to UTC
          columns: ["created_at"]
        }
      },
    ]
  },
  {
    aliasColName: "pnr", dataType: "StringType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, colName: "bid"},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "pnr"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "pnr"},
      {sourceName: ${tbo_daily_file.sourceName}, colName: "pnr"},
      {sourceName: ${tbo_monthly_file.sourceName}, colName: "pnr"},
      {sourceName: ${indigo_daily_file.sourceName}, function: {name: "generic_coalesce", columns: ["parent_pnr", "record_locator"]}},
      {sourceName: ${vietjet_daily_file.sourceName}, colName: "record_locator"},
      {sourceName: ${thaivietjet_daily_file.sourceName}, colName: "record_locator"},
      {sourceName: ${lionair_daily_file.sourceName}, colName: "booking_reloc"},
      {sourceName: ${lionair_idr_daily_file.sourceName}, colName: "booking_reloc"},
      {sourceName: ${pkfare_daily_file.sourceName},
        function: {name: "generic_replace_last_characters", constants: ["2", "01"], columns: ["order_num"]}
      },
      {sourceName: ${nusa_daily_file.sourceName}, colName: "airline_pnr"},
      {sourceName: ${spicejet_daily_file.sourceName}, function: {name: "generic_coalesce", columns: ["parentpnr", "pnr"]}},
      {sourceName: ${whypaymore_daily_file.sourceName}, colName: "airline_pnr"},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName},
        function: {name: "generic_check_defined_null_or_whitespace", columns: ["original_pnr", "pnr"]}},
      {sourceName: ${gofirst_daily_file.sourceName},
        function: {name: "generic_check_defined_null_or_whitespace", columns: ["parent_pnr", "pnr"]}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}, colName: "reference"},
      {sourceName: ${scoot_daily_file.sourceName}, colName: "pnr"},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, colName: "pnr"},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}, colName: "account_reference"},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}, colName: "reference"},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, colName: "id"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, colName: "pnr"},
      {sourceName: ${tway_daily_file.sourceName},
        function: {name: "generic_coalesce", columns: ["in_connection_with_ticket_number", "electronic_settlement_authorisation_code"]}},
      {sourceName: ${akasa_daily_file.sourceName}, function: {name: "generic_check_defined_null_or_whitespace", columns: ["parent_pnr", "pnr"]}},
      {
        sourceName: ${emt_daily_file.sourceName},
        function: {name: "generic_split_and_get", constants: ["\\|", "0"], columns: ["pnr"]}
      },
      {sourceName: ${unififi_daily_file.sourceName},
        function: {name: "generic_check_defined_null_or_whitespace", columns: ["original_order_number", "order_number"]}
      },
      {sourceName: ${fr24_daily_file.sourceName},
        function: {name: "generic_check_defined_null_or_whitespace", columns: ["original_order_number", "order_no"]}
      },
      {sourceName: ${tripjack_daily_file.sourceName}, colName: "pnr"},
      {sourceName: ${dida_daily_file.sourceName}, colName: "gds_pnr"},
      {sourceName: ${nokair_daily_file.sourceName}, colName: "reference"},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, colName: "pnr"},
      {sourceName: ${tripadd_daily_file.sourceName}, colName: "booking_reference"},
    ]
  },
  {
    aliasColName: "booking_source", dataType: "StringType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, value: "FSA"},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "booking_source"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "booking_source"},
      {sourceName: ${tbo_daily_file.sourceName}, value: "FSA"},
      {sourceName: ${tbo_monthly_file.sourceName}, value: "FSA"},
      {sourceName: ${indigo_daily_file.sourceName}, value: "FSA"},
      {sourceName: ${vietjet_daily_file.sourceName}, value: "FSA"},
      {sourceName: ${thaivietjet_daily_file.sourceName}, value: "FSA"},
      {sourceName: ${lionair_daily_file.sourceName}, value: "FSA"},
      {sourceName: ${lionair_idr_daily_file.sourceName}, value: "FSA"},
      {sourceName: ${pkfare_daily_file.sourceName}, value: "FSA"},
      {sourceName: ${nusa_daily_file.sourceName}, value: "FSA"},
      {sourceName: ${spicejet_daily_file.sourceName}, value: "FSA"},
      {sourceName: ${whypaymore_daily_file.sourceName}, value: "FSA"},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}, value: "FSA"},
      {sourceName: ${gofirst_daily_file.sourceName}, value: "FSA"},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}, value: "FSA"},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, value: "FSA"},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}, value: "FSA"},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}, value: "FSA"},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "exceptional", dataType: "BooleanType",
    colMapper: [
      {
        sourceName: ${chubb_daily_report.sourceName},
        function: {name: "generic_or_logical", columns: ["source_system", "cancelled_by"], constants: ["B2B", "CHUBB"], operators: ["==", "=="]}
      },
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, value: "false"},
      {
        sourceName: ${priceline_daily_file.sourceName},
        function: {
          name: "generic_or_logical", columns: ["ticket_type"], constants: ["AGENCY"], operators: ["=="]
        }
      },
      {
        sourceName: ${priceline_daily_gordian_file.sourceName},
        function: {
          name: "generic_or_logical", columns: ["ticket_type"], constants: ["AGENCY"], operators: ["=="]
        }
      },
      {sourceName: ${tbo_daily_file.sourceName}, value: "false"},
      {sourceName: ${tbo_monthly_file.sourceName}, value: "false"},
      {sourceName: ${indigo_daily_file.sourceName}, value: "false"},
      {sourceName: ${vietjet_daily_file.sourceName}, value: "false"},
      {sourceName: ${thaivietjet_daily_file.sourceName}, value: "false"},
      {sourceName: ${lionair_daily_file.sourceName}, value: "false"},
      {sourceName: ${lionair_idr_daily_file.sourceName}, value: "false"},
      {sourceName: ${pkfare_daily_file.sourceName}, value: "false"},
      {sourceName: ${nusa_daily_file.sourceName}, value: "false"},
      {sourceName: ${spicejet_daily_file.sourceName}, value: "false"},
      {sourceName: ${whypaymore_daily_file.sourceName}, value: "false"},
      {sourceName: ${hotel_violet_file.sourceName}, value: "false"},
      {
        sourceName:${hotel_marriott_file.sourceName},
        function:{
          name: "generic_multiconstants_or_logical",
          columns: ["prop_code","payment_model", "res_reason_code_desc"],
          multiconstants: [
            ["CTACB"]
            , ["MERCHANT","AGENCY","UNKNOWN"]
            , ["PAID DIRECTLY BY THE HOTEL", "COMMISSION DEDUCTED BY A PREPAYMENT", "COMMISSION DEDUCTED FROM DIRECT BILL", "PREVCOMMISSION PREVIOUSLY PAID", "PDSSAPPROVED PAYMENT"]
          ]
        }
      },
      {sourceName: ${jetstar_daily_file.sourceName}, value: "false"},
      {sourceName: ${gofirst_daily_file.sourceName}, value: "false"},
      {sourceName: ${hotel_marriott_file.sourceName}, value: "false"},
      {sourceName: ${citilink_daily_file.sourceName}, value: "false"},
      {sourceName: ${scoot_daily_file.sourceName}, value: "false"},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, value: "false"},
      {sourceName: ${hotel_hyatt_file.sourceName}, value: "false"},
      {sourceName: ${fy_daily_file.sourceName}, value: "false"},
      {sourceName: ${viator_daily_report.sourceName}, value: "false"},
      {sourceName: ${kkday_daily_report.sourceName}, value: "false"},
      {sourceName: ${bemyguest_daily_report.sourceName}, value: "false"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, value: "false"},
      {sourceName: ${hotel_hilton_file.sourceName}, value: "false"},
      {sourceName: ${airasia_daily_file.sourceName}, value: "false"},
      {sourceName: ${direct_comm_file.sourceName}, value: "false"},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, value: "false"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, value: "false"},
      {sourceName: ${tway_daily_file.sourceName}, value: "false"},
      {sourceName: ${akasa_daily_file.sourceName}, value: "false"},
      {sourceName: ${emt_daily_file.sourceName}, value: "false"},
      {sourceName: ${unififi_daily_file.sourceName}, value: "false"},
      {sourceName: ${fr24_daily_file.sourceName}, value: "false"},
      {sourceName: ${tripjack_daily_file.sourceName}, value: "false"},
      {sourceName: ${dida_daily_file.sourceName}, value: "false"},
      {sourceName: ${nokair_daily_file.sourceName}, value: "false"},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}, value: "false"},
    ]
  },
  {
    aliasColName: "invoice_date", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName}},
      {sourceName: ${priceline_daily_gordian_file.sourceName}},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}, colName: "invoice_date"},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "supplier_invoice_number", dataType: "StringType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName}},
      {sourceName: ${priceline_daily_gordian_file.sourceName}},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "balance", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {
        sourceName: ${kiwi_daily_and_monthly_file.sourceName}, colName: "deposit_balance"
      },
      {sourceName: ${priceline_daily_file.sourceName}},
      {sourceName: ${priceline_daily_gordian_file.sourceName}},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}, colName: "available_balance"},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName},
        function: {name: "generic_convert_string_to_big_decimal", columns: ["balance"]}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}, colName: "available"},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "payment_type", dataType: "StringType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, value: "Prepaid"},
      {sourceName: ${priceline_daily_file.sourceName}},
      {sourceName: ${priceline_daily_gordian_file.sourceName}},
      {
        sourceName: ${tbo_daily_file.sourceName}, colName: "gsd_lcc",
        mapper: [
          {key: "GDS", value: "Postpaid"},
          {key: "LCC", value: "Prepaid"}
        ]
      },
      {sourceName: ${tbo_monthly_file.sourceName}},
      {
        sourceName: ${indigo_daily_file.sourceName}, colName: "payment_method_code",
        mapper: [
          {key: "AG", value: "Prepaid"}
        ]
      },
      {sourceName: ${vietjet_daily_file.sourceName}, value: "Prepaid"},
      {sourceName: ${thaivietjet_daily_file.sourceName}, value: "Prepaid"},
      {sourceName: ${lionair_daily_file.sourceName}, value: "Prepaid"},
      {sourceName: ${lionair_idr_daily_file.sourceName}, value: "Prepaid"},
      {sourceName: ${pkfare_daily_file.sourceName}, value: "Postpaid"},
      {sourceName: ${nusa_daily_file.sourceName}, value: "Prepaid"},
      {
        sourceName: ${spicejet_daily_file.sourceName}, colName: "type",
        mapper: [
          {key: "AG", value: "Prepaid"}
        ]
      },
      {sourceName: ${whypaymore_daily_file.sourceName}, value: "Prepaid"},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}, value: "Prepaid"},
      {sourceName: ${gofirst_daily_file.sourceName}, value: "Prepaid"},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}, value: "Prepaid"},
      {sourceName: ${scoot_daily_file.sourceName}, value: "Postpaid"},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, value: "Postpaid"},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}, value: "Prepaid"},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}, value: "Prepaid"},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, value: "Postpaid"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, value: "Postpaid"},
      {sourceName: ${tway_daily_file.sourceName}, value: "Prepaid"},
      {sourceName: ${akasa_daily_file.sourceName}, value: "Prepaid"},
      {sourceName: ${emt_daily_file.sourceName}, value: "PostPaid"},
      {sourceName: ${unififi_daily_file.sourceName}, value: "PostPaid"},
      {sourceName: ${fr24_daily_file.sourceName}, value: "PostPaid"},
      {sourceName: ${tripjack_daily_file.sourceName}, value: "Prepaid"},
      {sourceName: ${dida_daily_file.sourceName}, value: "PostPaid"},
      {sourceName: ${nokair_daily_file.sourceName}, value: "Prepaid"},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, value: "Postpaid"},
      {sourceName: ${tripadd_daily_file.sourceName}, value: "PostPaid"},
    ]
  },
  {
    aliasColName: "source_file_id", dataType: "LongType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, colName: "file_id"},
      {sourceName: ${covergenius_daily_report.sourceName}, colName: "file_id"},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "file_id"},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, colName: "file_id"},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "file_id"},
      {sourceName: ${tbo_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${tbo_monthly_file.sourceName}, colName: "file_id"},
      {sourceName: ${indigo_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${vietjet_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${thaivietjet_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${lionair_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${lionair_idr_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${pkfare_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${nusa_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${spicejet_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${whypaymore_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "file_id"},
      {sourceName: ${jetstar_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${gofirst_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "file_id"},
      {sourceName: ${citilink_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${scoot_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${hotel_hyatt_file.sourceName}, colName: "file_id"},
      {sourceName: ${fy_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${viator_daily_report.sourceName}, colName: "file_id"},
      {sourceName: ${kkday_daily_report.sourceName}, colName: "file_id"},
      {sourceName: ${bemyguest_daily_report.sourceName}, colName: "file_id"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, colName: "file_id"},
      {sourceName: ${hotel_hilton_file.sourceName}, colName: "file_id"},
      {sourceName: ${airasia_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}, colName: "file_id"},
      {sourceName: ${cfar_covergenius_claim.sourceName}, colName: "file_id"},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, value: "file_id"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${tway_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${akasa_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${emt_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${unififi_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${fr24_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${tripjack_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${dida_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${nokair_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, colName: "file_id"},
      {sourceName: ${tripadd_daily_file.sourceName}, colName: "file_id"},
    ]
  },
  {
    aliasColName: "source_line_number", dataType: "LongType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, colName: "line_number"},
      {sourceName: ${covergenius_daily_report.sourceName}, colName: "line_number"},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "line_number"},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, colName: "line_number"},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "line_number"},
      {sourceName: ${tbo_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${tbo_monthly_file.sourceName}, colName: "line_number"},
      {sourceName: ${indigo_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${vietjet_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${thaivietjet_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${lionair_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${lionair_idr_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${pkfare_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${nusa_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${spicejet_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${whypaymore_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "line_number"},
      {sourceName: ${jetstar_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${gofirst_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "line_number"},
      {sourceName: ${citilink_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${scoot_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${hotel_hyatt_file.sourceName}, colName: "line_number"},
      {sourceName: ${fy_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${viator_daily_report.sourceName}, colName: "line_number"},
      {sourceName: ${kkday_daily_report.sourceName}, colName: "line_number"},
      {sourceName: ${bemyguest_daily_report.sourceName}, colName: "line_number"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, colName: "line_number"},
      {sourceName: ${hotel_hilton_file.sourceName}, colName: "line_number"},
      {sourceName: ${airasia_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}, colName: "line_number"},
      {sourceName: ${cfar_covergenius_claim.sourceName}, colName: "line_number"},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${tway_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${akasa_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${emt_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${unififi_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${fr24_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${tripjack_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${dida_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${nokair_daily_file.sourceName}, colName: "line_number"},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, colName: "transaction_number"},
      {sourceName: ${tripadd_daily_file.sourceName}, colName: "line_number"},
    ]
  },
  {
    aliasColName: "source_reporting_date", dataType: "TimestampType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, colName: "reporting_date"},
      {sourceName: ${covergenius_daily_report.sourceName}, colName: "reporting_date"},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "reporting_date"},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${tbo_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${tbo_monthly_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${indigo_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${vietjet_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${thaivietjet_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${lionair_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${lionair_idr_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${pkfare_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${nusa_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${spicejet_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${whypaymore_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${jetstar_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${gofirst_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${citilink_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${scoot_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${hotel_hyatt_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${fy_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${viator_daily_report.sourceName}, colName: "reporting_date"},
      {sourceName: ${kkday_daily_report.sourceName}, colName: "reporting_date"},
      {sourceName: ${bemyguest_daily_report.sourceName}, colName: "reporting_date"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, colName: "reporting_date"},
      {sourceName: ${hotel_hilton_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${airasia_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}, colName: "reporting_date"},
      {sourceName: ${cfar_covergenius_claim.sourceName}, colName: "reporting_date"},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${tway_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${akasa_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${emt_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${unififi_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${fr24_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${tripjack_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${dida_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${nokair_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, colName: "reporting_date"},
      {sourceName: ${tripadd_daily_file.sourceName}, colName: "reporting_date"},
    ]
  },
  {
    aliasColName: "source_datadate", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, colName: "datadate"},
      {sourceName: ${covergenius_daily_report.sourceName}, colName: "datadate"},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "datadate"},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, colName: "datadate"},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "datadate"},
      {sourceName: ${tbo_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${tbo_monthly_file.sourceName}, colName: "datadate"},
      {sourceName: ${indigo_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${vietjet_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${thaivietjet_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${lionair_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${lionair_idr_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${pkfare_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${nusa_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${spicejet_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${whypaymore_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "datadate"},
      {sourceName: ${jetstar_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${gofirst_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "datadate"},
      {sourceName: ${citilink_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${scoot_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${hotel_hyatt_file.sourceName}, colName: "datadate"},
      {sourceName: ${fy_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${viator_daily_report.sourceName}, colName: "datadate"},
      {sourceName: ${kkday_daily_report.sourceName}, colName: "datadate"},
      {sourceName: ${bemyguest_daily_report.sourceName}, colName: "datadate"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, colName: "datadate"},
      {sourceName: ${hotel_hilton_file.sourceName}, colName: "datadate"},
      {sourceName: ${airasia_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${direct_comm_file.sourceName}, colName: "datadate"},
      {sourceName: ${cfar_covergenius_premium.sourceName}, colName: "datadate"},
      {sourceName: ${cfar_covergenius_claim.sourceName}, colName: "datadate"},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${tway_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${akasa_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${emt_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${unififi_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${fr24_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${tripjack_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${dida_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${nokair_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, colName: "datadate"},
      {sourceName: ${tripadd_daily_file.sourceName}, colName: "datadate"},
    ]
  },
  {
    aliasColName: "supplier_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, value: 40003},
      {sourceName: ${covergenius_daily_report.sourceName}, value: 40004},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "product_type", mapper: [
        {key: "Rocket Travel - Premium Post Departure Protection", value: 40004},
        {key: "DEFAULT_MAP_VALUE", value: 40005}
      ]},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, value: 30010},
      {sourceName: ${priceline_daily_file.sourceName}, value: 30001},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, value: 30001},
      {sourceName: ${tbo_daily_file.sourceName}, value: 30011},
      {sourceName: ${tbo_monthly_file.sourceName}, value: 30011},
      {sourceName: ${indigo_daily_file.sourceName}, value: 30012},
      {sourceName: ${vietjet_daily_file.sourceName}, value: 30014},
      {sourceName: ${thaivietjet_daily_file.sourceName}, value: 30046},
      {sourceName: ${lionair_daily_file.sourceName}, value: 30017},
      {sourceName: ${lionair_idr_daily_file.sourceName}, value: 30066},
      {sourceName: ${pkfare_daily_file.sourceName}, value: 30018},
      {sourceName: ${nusa_daily_file.sourceName}, value: 30019},
      {sourceName: ${spicejet_daily_file.sourceName}, value: 30021},
      {sourceName: ${whypaymore_daily_file.sourceName}, value: 30020},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}, value: 30026},
      {sourceName: ${gofirst_daily_file.sourceName}, value: 30028},
      {sourceName: ${hotel_marriott_file.sourceName}, value: 29002},
      {sourceName: ${citilink_daily_file.sourceName}, value: 30025},
      {sourceName: ${scoot_daily_file.sourceName}, value: 30027},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, value: 30065},
      {sourceName: ${hotel_hyatt_file.sourceName}, value: 29003},
      {sourceName: ${fy_daily_file.sourceName}, value: 30034},
      {sourceName: ${viator_daily_report.sourceName}, value: 50001},
      {sourceName: ${kkday_daily_report.sourceName}, value: 50002},
      {sourceName: ${bemyguest_daily_report.sourceName}, value: 50004},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, value: 50007},
      {sourceName: ${hotel_hilton_file.sourceName}, value: 29005},
      {sourceName: ${airasia_daily_file.sourceName}, value: 30072},
      {sourceName: ${direct_comm_file.sourceName}, colName: "invoice_product_id", mapper: [
        {key: 1, value: 29005}
      ]},
      {sourceName: ${cfar_covergenius_premium.sourceName}, value: 40004},
      {sourceName: ${cfar_covergenius_claim.sourceName}, value: 40004},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, value: 30013},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, value: 30038},
      {sourceName: ${tway_daily_file.sourceName}, value: 30058},
      {sourceName: ${akasa_daily_file.sourceName}, value: 30056},
      {sourceName: ${emt_daily_file.sourceName}, value: 30084},
      {sourceName: ${unififi_daily_file.sourceName}, value: 30085},
      {sourceName: ${fr24_daily_file.sourceName}, value: 30086},
      {sourceName: ${tripjack_daily_file.sourceName}, value: 30087},
      {sourceName: ${dida_daily_file.sourceName}, value: 30091},
      {sourceName: ${nokair_daily_file.sourceName}, value: 30083},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, value: 30088},
      {sourceName: ${tripadd_daily_file.sourceName}, value: 49999},
    ]
  },
  {
    // ******* REQUIRED FIELD FOR RECONCILIATION *******
    aliasColName: "recon_partner_id", dataType: "StringType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, value: "40003"},
      {sourceName: ${covergenius_daily_report.sourceName}, value: "40004"},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "product_type", mapper: [
        {key: "Rocket Travel - Premium Post Departure Protection", value: "40004"},
        {key: "DEFAULT_MAP_VALUE", value: "40005"}
      ]},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, value: "30010"},
      {sourceName: ${priceline_daily_file.sourceName}, value: "30001"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, value: "30001"},
      {sourceName: ${tbo_daily_file.sourceName}, value: "30011"},
      {sourceName: ${tbo_monthly_file.sourceName}, value: "30011"},
      {sourceName: ${indigo_daily_file.sourceName}, value: "30012"},
      {sourceName: ${vietjet_daily_file.sourceName}, value: "30014"},
      {sourceName: ${thaivietjet_daily_file.sourceName}, value: "30046"},
      {sourceName: ${lionair_daily_file.sourceName}, value: "30017"},
      {sourceName: ${lionair_idr_daily_file.sourceName}, value: "30066"},
      {sourceName: ${pkfare_daily_file.sourceName}, value: "30018"},
      {sourceName: ${nusa_daily_file.sourceName}, value: "30019"},
      {sourceName: ${spicejet_daily_file.sourceName}, value: "30021"},
      {sourceName: ${whypaymore_daily_file.sourceName}, value: "30020"},
      {sourceName: ${hotel_violet_file.sourceName}, value: "pcln_hotel_violet"},
      {sourceName: ${jetstar_daily_file.sourceName}, value: "30026"},
      {sourceName: ${gofirst_daily_file.sourceName}, value: "30028"},
      {sourceName: ${hotel_marriott_file.sourceName}, value: "29002"},
      {sourceName: ${citilink_daily_file.sourceName}, value: "30025"},
      {sourceName: ${scoot_daily_file.sourceName}, value: "30027"},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, value: "30065"},
      {sourceName: ${hotel_hyatt_file.sourceName}, value: "29003"},
      {sourceName: ${fy_daily_file.sourceName}, value: "30034"},
      {sourceName: ${viator_daily_report.sourceName}, value: "50001"},
      {sourceName: ${kkday_daily_report.sourceName}, value: "50002"},
      {sourceName: ${bemyguest_daily_report.sourceName}, value: "50004"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, value: "50007"},
      {sourceName: ${hotel_hilton_file.sourceName}, value: "29005"},
      {sourceName: ${airasia_daily_file.sourceName}, value: "30072"},
      {sourceName: ${direct_comm_file.sourceName}, colName: "invoice_product_id", mapper: [
        {key: 1, value: 29005}
      ]},
      {sourceName: ${cfar_covergenius_premium.sourceName}, value: 40004},
      {sourceName: ${cfar_covergenius_claim.sourceName}, value: 40004},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, value: "30013"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, value: 30038},
      {sourceName: ${tway_daily_file.sourceName}, value: 30058},
      {sourceName: ${akasa_daily_file.sourceName}, value: 30056},
      {sourceName: ${emt_daily_file.sourceName}, value: 30084},
      {sourceName: ${unififi_daily_file.sourceName}, value: 30085},
      {sourceName: ${fr24_daily_file.sourceName}, value: 30086},
      {sourceName: ${tripjack_daily_file.sourceName}, value: 30087},
      {sourceName: ${dida_daily_file.sourceName}, value: "30091"},
      {sourceName: ${nokair_daily_file.sourceName}, value: 30083},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, value: 30088},
      {sourceName: ${tripadd_daily_file.sourceName}, value: 49999},
    ]
  },
  {
    // ******* FOR EXCEPTION CATEGORIZER *******
    aliasColName: "supplier_additional_info", dataType: "StringType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, colName: "remark"}, //special flight case
      {sourceName: ${priceline_daily_file.sourceName}},
      {sourceName: ${priceline_daily_gordian_file.sourceName}},
      {sourceName: ${tbo_daily_file.sourceName},
        json: [
          {key: "performance_linked_bonus", value: "plb_amount"},
          {key: "tds", value: "tds"}
        ]
      },
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName},
        json: [
          {key : "offer_number", value : "offer_num"},
          {key : "confirmation_status", value : "confcancelstatus"},
          {key : "current_offer_status", value : "current_offer_status"},
          {key : "cares_class", value : "cares_class"},
          {key : "priceline_bucket", value : "priceline_bucket"},
          {key : "priceline_team", value : "priceline_team"}
          {key : "pcln_canc_api_status", value : "pcln_canc_api_status"},
          {key : "pcln_adj_bkng_price_api_status", value : "pcln_adj_bkng_price_api_status"},
          {key : "pcln_cancelar_api_status", value : "pcln_cancelar_api_status"},
          {key : "allotment_alert_chrg_thru_brkg", value : "priceline_team"}
        ]
      },
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName},
        json: [
          {key : "booking_iata", value : "booking_iata"},
          {key : "reason_code", value : "res_reason_code_desc"},
          {key : "statement_date", value : "statement_date"},
          {key : "arrival_date", value : "arrival_date"},
          {key : "rn_number", value : "rn_number"},
          {key : "paid_iata", value : "paid_iata"},
          {key : "matched_booking_id_date", value : "matched_booking_id_date"},
          {key : "record_type", value : "record_type"},
          {key : "city", value : "city"},
          {key : "state", value : "state"},
          {key : "brand", value : "brand"},
          {key : "country_code", value : "country_code"},
          {key : "comm_rate_num", value : "comm_rate_num"},
          {key : "room_rate_ctac_num", value : "room_rate_ctac_num"},
          {key : "comm_rev_ctac_num", value : "comm_rev_ctac_num"},
          {key : "room_rate_usd_num", value : "room_rate_usd_num"},
          {key : "comm_rev_usd_num", value : "comm_rev_usd_num"},
          {key : "comm_amt_usd_num", value : "comm_amt_usd_num"},
          {key : "tax_amt_usd_num", value : "tax_amt_usd_num"},
          {key : "ta_preferred_curr", value : "ta_preferred_curr"},
          {key : "ta_pref_curr_desc", value : "ta_pref_curr_desc"},
          {key : "check_number", value : "check_number"},
          {key : "check_date", value : "check_date"},
          {key : "action_code_desc", value : "action_code_desc"},
          {key : "reason_code_short", value : "res_reason_code"},
          {key : "prop_phone", value : "prop_phone"}
        ]
      },
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}
        json: [
          {key : "booking_iata", value : "iata"},
          {key : "arrival_date", value : "arrival"},
          {key : "checkout_date", value : "departure"},
        ]
      },
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "hotel_id", dataType: "StringType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName}},
      {sourceName: ${priceline_daily_gordian_file.sourceName}},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "hotel_id"},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}, colName: "hotel_id"}
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "booking_date", dataType: "TimestampType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName}},
      {sourceName: ${priceline_daily_gordian_file.sourceName}},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "booking_date"},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}, colName: "booking_create_date"},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}, colName: "booking_date_utc"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, colName: "booking_date_utc"},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}, colName: "booking_date"},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "hash_key", dataType: "StringType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, colName: "hash_key"},
      {sourceName: ${covergenius_daily_report.sourceName}, colName: "hash_key"},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "hash_key"},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, colName: "hash_key"},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "hash_key"},
      {sourceName: ${tbo_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${tbo_monthly_file.sourceName}, colName: "hash_key"},
      {sourceName: ${indigo_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${vietjet_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${thaivietjet_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${lionair_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${lionair_idr_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${pkfare_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${nusa_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${spicejet_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${whypaymore_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${hotel_violet_file.sourceName}, colName: "hash_key"},
      {sourceName: ${jetstar_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${gofirst_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "hash_key"},
      {sourceName: ${citilink_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${scoot_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${hotel_hyatt_file.sourceName}, colName: "hash_key"},
      {sourceName: ${fy_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${viator_daily_report.sourceName}, colName: "hash_key"},
      {sourceName: ${kkday_daily_report.sourceName}, colName: "hash_key"},
      {sourceName: ${bemyguest_daily_report.sourceName}, colName: "hash_key"},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}, colName: "hash_key"},
      {sourceName: ${hotel_hilton_file.sourceName}, colName: "hash_key"},
      {sourceName: ${airasia_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${direct_comm_file.sourceName}, colName: "hash_key"},
      {sourceName: ${cfar_covergenius_premium.sourceName}, colName: "hash_key"},
      {sourceName: ${cfar_covergenius_claim.sourceName}, colName: "hash_key"},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${tway_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${akasa_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${emt_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${unififi_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${fr24_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${tripjack_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${dida_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${nokair_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}, colName: "hash_key"},
      {sourceName: ${tripadd_daily_file.sourceName}, colName: "hash_key"},
    ]
  },
  {
    aliasColName: "start_date", dataType: "TimestampType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName}},
      {sourceName: ${priceline_daily_gordian_file.sourceName}},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "arrival_date"},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}, colName: "arrival"},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}, colName: "checkin_date"},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "end_date", dataType: "TimestampType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName}},
      {sourceName: ${priceline_daily_gordian_file.sourceName}},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}, colName: "departure"},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}, colName: "checkout_date"},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    # date when supplier paid agoda
    aliasColName: "statement_date", dataType: "TimestampType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName}},
      {sourceName: ${priceline_daily_gordian_file.sourceName}},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "statement_date"},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {
        sourceName: ${hotel_hilton_file.sourceName},
        function: {name: "generic_convert_date_format", columns: ["pay_date"], constants: ["yyyy-MM-dd'T'HH:mm:ss.SSS", "yyyy-MM-dd HH:mm:ss"]}
      },
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}, colName: "bank_receipt_date"},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "settlement_entity", dataType: "StringType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName}},
      {sourceName: ${priceline_daily_gordian_file.sourceName}},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}, value: "CENTRAL"},
      {sourceName: ${direct_comm_file.sourceName}, value: "DIRECT"},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    aliasColName: "invoice_currency", dataType: "StringType",
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}},
      {sourceName: ${covergenius_daily_report.sourceName}},
      {sourceName: ${covergenius_agency_daily_report.sourceName}},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}},
      {sourceName: ${priceline_daily_file.sourceName}},
      {sourceName: ${priceline_daily_gordian_file.sourceName}},
      {sourceName: ${tbo_daily_file.sourceName}},
      {sourceName: ${tbo_monthly_file.sourceName}},
      {sourceName: ${indigo_daily_file.sourceName}},
      {sourceName: ${vietjet_daily_file.sourceName}},
      {sourceName: ${thaivietjet_daily_file.sourceName}},
      {sourceName: ${lionair_daily_file.sourceName}},
      {sourceName: ${lionair_idr_daily_file.sourceName}},
      {sourceName: ${pkfare_daily_file.sourceName}},
      {sourceName: ${nusa_daily_file.sourceName}},
      {sourceName: ${spicejet_daily_file.sourceName}},
      {sourceName: ${whypaymore_daily_file.sourceName}},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}},
      {sourceName: ${gofirst_daily_file.sourceName}},
      {sourceName: ${hotel_marriott_file.sourceName}},
      {sourceName: ${citilink_daily_file.sourceName}},
      {sourceName: ${scoot_daily_file.sourceName}},
      {sourceName: ${scoot_ndc_daily_file.sourceName}},
      {sourceName: ${hotel_hyatt_file.sourceName}},
      {sourceName: ${fy_daily_file.sourceName}},
      {sourceName: ${viator_daily_report.sourceName}},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${airasia_daily_file.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}},
      {sourceName: ${direct_comm_file.sourceName}, colName: "receipt_currency"},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  },
  {
    # commission as sent on settlement file
    aliasColName: "commission_actual", dataType: "DecimalType", precision: 18, scale: 8
    colMapper: [
      {sourceName: ${chubb_daily_report.sourceName}, colName: "commission"},
      {sourceName: ${covergenius_daily_report.sourceName}, colName: "commission_sgd"},
      {sourceName: ${covergenius_agency_daily_report.sourceName}, colName: "commission_usd"},
      {sourceName: ${kiwi_daily_and_monthly_file.sourceName}, value: 0},
      {sourceName: ${priceline_daily_file.sourceName}, colName: "commission_detail_actual"},
      {sourceName: ${priceline_daily_gordian_file.sourceName}, colName: "commission_detail_actual"},
      {sourceName: ${tbo_daily_file.sourceName}, colName: "h_charge_commission_rev_h_charge"},
      {sourceName: ${tbo_monthly_file.sourceName}, value: 0},
      {sourceName: ${indigo_daily_file.sourceName}, value: 0},
      {sourceName: ${vietjet_daily_file.sourceName}, value: 0},
      {sourceName: ${thaivietjet_daily_file.sourceName}, value: 0},
      {sourceName: ${lionair_daily_file.sourceName}, value: 0},
      {sourceName: ${lionair_idr_daily_file.sourceName}, value: 0},
      {sourceName: ${pkfare_daily_file.sourceName}, value: 0},
      {sourceName: ${nusa_daily_file.sourceName}, value: 0},
      {sourceName: ${spicejet_daily_file.sourceName}, value: 0},
      {sourceName: ${whypaymore_daily_file.sourceName}, colName: "commission"},
      {sourceName: ${hotel_violet_file.sourceName}},
      {sourceName: ${jetstar_daily_file.sourceName}, value: 0},
      {sourceName: ${gofirst_daily_file.sourceName}, value: 0},
      {sourceName: ${hotel_marriott_file.sourceName}, colName: "comm_amt_ctac_num"},
      {sourceName: ${citilink_daily_file.sourceName}, value: 0},
      {sourceName: ${scoot_daily_file.sourceName}, value: 0},
      {sourceName: ${scoot_ndc_daily_file.sourceName}, value: 0},
      {sourceName: ${hotel_hyatt_file.sourceName}, colName: "paid_commission"},
      {sourceName: ${fy_daily_file.sourceName}, value: 0},
      {sourceName: ${viator_daily_report.sourceName}, value: 0},
      {sourceName: ${kkday_daily_report.sourceName}},
      {sourceName: ${bemyguest_daily_report.sourceName}},
      {sourceName: ${bemyguest_cm_daily_report.sourceName}},
      {sourceName: ${hotel_hilton_file.sourceName}, colName: "report_com"},
      {sourceName: ${airasia_daily_file.sourceName}, value: 0},
      {sourceName: ${direct_comm_file.sourceName}},
      {sourceName: ${cfar_covergenius_premium.sourceName}},
      {sourceName: ${cfar_covergenius_claim.sourceName}},
      {sourceName: ${airtrip_issuance_and_cancellation_daily_file.sourceName}, colName: "commission"},
      {sourceName: ${jejuair_sale_and_refund_daily_file.sourceName}},
      {sourceName: ${tway_daily_file.sourceName}},
      {sourceName: ${akasa_daily_file.sourceName}},
      {sourceName: ${emt_daily_file.sourceName}},
      {sourceName: ${unififi_daily_file.sourceName}},
      {sourceName: ${fr24_daily_file.sourceName}},
      {sourceName: ${tripjack_daily_file.sourceName}},
      {sourceName: ${dida_daily_file.sourceName}},
      {sourceName: ${nokair_daily_file.sourceName}},
      {sourceName: ${holiday_tour_iata_daily_file.sourceName}},
      {sourceName: ${tripadd_daily_file.sourceName}},
    ]
  }
]
