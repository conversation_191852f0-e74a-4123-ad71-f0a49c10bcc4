
project-name = "normalizer-multiproduct-suppliers"
app-name = "MultiproductSupplierNormalizer"

hadoop {
    spark {
        executor {                           // (All exist in Environment)
            count = 12
            cores-per-executor = 5
            memory-per-executor = "23g"
            memory-overhead = 16384
        }
        driver {                             // (All exist in Environment)
            memory = "20g"
            memory-overhead = 16384
        }
    }
}

normalizer: {
  include "normalizer_mapper.conf"
  job_frequency = Daily
  lookbackdate = 2 // number of the date to lookback eg 7 means 7 days
  sources: [
    #   If added Flight supplier, please also update PaymentNormalizer as well
    "chubb_daily_report",
    "covergenius_daily_report",
    "covergenius_agency_daily_report",
    "kiwi_daily_and_monthly_file",
    "priceline_daily_file",
    "priceline_daily_gordian_file",
    "tbo_daily_file",
    "tbo_monthly_file",
    "indigo_daily_file",
    "vietjet_daily_file",
    "thaivietjet_daily_file",
    "lionair_daily_file",
    "lionair_idr_daily_file"
    "pkfare_daily_file",
    "nusa_daily_file",
    "spicejet_daily_file",
    "whypaymore_daily_file",
    "jetstar_daily_file",
    "gofirst_daily_file",
    "hotel_marriott_file",
    "citilink_daily_file",
    "scoot_daily_file",
    "scoot_ndc_daily_file",
    //    "hotel_hyatt_file",
    "fy_daily_file",
    "viator_daily_report",
    "kkday_daily_report",
    "bemyguest_daily_report",
    "bemyguest_cm_daily_report",
    "hotel_hilton_file",
    "airasia_daily_file",
    "direct_comm_file",
    "cfar_covergenius_premium",
    "cfar_covergenius_claim",
    "airtrip_issuance_and_cancellation_daily_file"
    "tway_daily_file",
    "jejuair_sale_and_refund_daily_file",
    "akasa_daily_file",
    "emt_daily_file",
    "unififi_daily_file",
    "fr24_daily_file",
    "nokair_daily_file",
    "dida_daily_file",
    "tripjack_daily_file"
//    "tripadd_daily_file"
//    "holiday_tour_iata_daily_file"
  ]

  chubb_daily_report {
    sourceName : "chubb_daily_report"
    tableList:[{
      tablename: ${source_schema}".chubb_daily_report"
      partitiondatekey: "datadate"
      filterFunction {name: "generic_is_match_with_regex", constants: ["^(?!.*E[0-9]$).*"], columns: ["policy_no"]}
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey : [
      {partitionname:"source",value:"CD"},
      {partitionname:"product_type",value:"PROTECTION"}
    ]
  }

  covergenius_daily_report {
        sourceName : "covergenius_daily_report"
        tableList:[{
          tablename: ${source_schema}".covergenius_daily"
          partitiondatekey: "datadate"
        }]
        datasetlogicfunction : [{name: "generic_trim"}]
        outputpartitionkey: [
          {partitionname: "source", value: "CGD"},
          {partitionname: "product_type", value: "PROTECTION"}
        ]
      }

  covergenius_agency_daily_report {
        sourceName : "covergenius_agency_daily_report"
        tableList:[{
          tablename: ${source_schema}".covergenius_agency_daily"
          partitiondatekey: "datadate"
        }]
        datasetlogicfunction : [{name: "generic_trim"}]
        outputpartitionkey: [
          {partitionname: "source", value: "CGAD"},
          {partitionname: "product_type", value: "PROTECTION"}
        ]
      }

  kiwi_daily_and_monthly_file {
    sourceName : "kiwi_daily_and_monthly_file"
    tableList:[
      {
        tablename : ${source_schema}".kiwi_daily"
        partitiondatekey : "datadate"
        filterFunction {name: "generic_is_not_null", columns: ["bid"]}
      },
      {
        tablename : ${source_schema}".kiwi_monthly"
        partitiondatekey : "datadate"
        filterFunction {name: "generic_is_not_null", columns: ["bid"]}
      },
      {
        tablename : "finance_normalizer.multiproduct_supplier_normalized_data"
        selectcolumn: ["supplier_transaction_id", "datadate"]
        extractfunctions: [
          {name: "generic_is_equal_to", constants: ["FLIGHT"], columns: ["product_type"]},
          {name: "generic_is_equal_to", constants: ["KD"], columns: ["source"]}
          {name: "generic_is_date_in_time_range_of_current_date", constants: ["365"], columns: ["datadate"]}
        ]
      }
    ]
    datasetlogicfunction : [{name: "merge_kiwi_data"}, {name: "generic_trim"}]
    outputpartitionkey : [
      {partitionname:"source",value:"KD"},
      {partitionname:"product_type",value:"FLIGHT"}
    ]
  }

  priceline_daily_file {
    sourceName : "priceline_daily_file"
    tableList:[
      {
        tablename : ${source_schema}."Agoda_Priceline_Airdaily"
        partitiondatekey : "datadate"
      },
      {
        tablename: ${schema}."multiproduct_supplier_normalized_data"
        selectcolumn: ["supplier_booking_id", "sub_supplier_name", "datadate"]
        extractfunctions: [
          {name: "generic_is_in", constants: ["PD"], columns: ["source"]},
          {name: "generic_offset_date_greater_than_process_month", constants: ["12"], columns: ["datadate"]}
        ]
      }
    ]
    datasetlogicfunction : [{name:"overwrite_supplier_not_found"}, {name: "pcln_mapping"}, {name: "generic_trim"}]
    outputpartitionkey : [
      {partitionname:"source",value:"PD"},
      {partitionname:"product_type",value:"FLIGHT"}
    ]
  }

  priceline_daily_gordian_file {
    sourceName : "priceline_daily_gordian_file"
    tableList:[{
      tablename : ${source_schema}."Agoda_Priceline_Airdaily_Gordian"
      partitiondatekey : "datadate"
    }]
    datasetlogicfunction : [{name: "pcln_mapping"}, {name: "generic_trim"}]
    outputpartitionkey : [
      {partitionname:"source",value:"PDG"},
      {partitionname:"product_type",value:"FLIGHT"}
    ]
  }

  tbo_daily_file {
    sourceName : "tbo_daily_file"
    tableList:[{
      tablename : ${source_schema}".Tbo_Daily"
      partitiondatekey : "datadate"
      filterFunction {name: "generic_is_not_null", columns: ["pnr"]}
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey : [
      {partitionname:"source",value:"TD"},
      {partitionname:"product_type",value:"FLIGHT"}
    ]
  }

  tbo_monthly_file {
    sourceName : "tbo_monthly_file"
    tableList:[{
      tablename : ${source_schema}".Tbo_Monthly"
      partitiondatekey : "datadate"
      filterFunction {name: "generic_is_not_null", columns: ["pnr"]}
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey : [
      {partitionname:"source",value:"TM"},
      {partitionname:"product_type",value:"FLIGHT"}
    ]
  }

  indigo_daily_file {
    sourceName : "indigo_daily_file"
    tableList:[
      {
        tablename: ${source_schema}".IndiGo_Daily"
        partitiondatekey: "datadate"
        filterFunction {name: "generic_is_equal_to", constants: ["AG"], columns: ["payment_method_code"]}
      },{
        tablename: ${source_schema}".IndiGo_Divided_PNR_Daily"
        partitiondatekey: "datadate"
      }
    ]
    datasetlogicfunction : [{name: "merge_indigo_data"}, {name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "ID"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  vietjet_daily_file {
    sourceName : "vietjet_daily_file"
    tableList:[{
      tablename: ${source_schema}".Vietjet_Daily"
      partitiondatekey: "datadate",
      filterFunction {name: "generic_is_not_null", columns: ["record_locator"]}
      extractfunctions: [
        {name: "generic_is_date_match_with_file_name", constants: ["yyyyMMdd"], columns: ["pmt_date", "file_name"]}
      ]
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "VD"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  thaivietjet_daily_file {
      sourceName : "thaivietjet_daily_file"
      tableList:[{
        tablename: ${source_schema}".Thaivietjet_Daily"
        partitiondatekey: "datadate",
        filterFunction {name: "generic_is_not_null", columns: ["record_locator"]}
        extractfunctions: [
          {name: "generic_is_date_match_with_file_name", constants: ["yyyyMMdd"], columns: ["pmt_date", "file_name"]}
        ]
      }]
      datasetlogicfunction : ["generic_trim"]
      outputpartitionkey: [
        {partitionname: "source", value: "VZD"},
        {partitionname: "product_type", value: "FLIGHT"}
      ]
    }

  pkfare_daily_file {
    sourceName : "pkfare_daily_file"
    tableList:[{
      tablename : ${source_schema}".PkFare_Daily"
      partitiondatekey : "datadate"
      filterFunction { name: "generic_is_not_null", columns: ["order_num"] } // Confirm with YY or PkFare if we should filter only row with order_num
      extractfunctions: [
        {name: "generic_is_in", constants: ["Purchase", "Refund", "Balance Adjustment"], columns: ["payment_type"]}
      ]
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey : [
      {partitionname: "source", value:"PK"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  lionair_daily_file {
    sourceName: "lionair_daily_file"
    tableList: [
      {
        tablename : ${source_schema}".lionair_transaction_daily"
        partitiondatekey : "datadate",
        filterFunction { name: "generic_is_not_null", columns: ["booking_reloc"] }
      },
      {
        tablename : ${source_schema}".LionAir_Special_Refund"
        partitiondatekey : "datadate"
      }
    ]
    datasetlogicfunction : [{name: "merge_lionair_data"}, {name: "generic_trim"}]
    outputpartitionkey : [
      {partitionname:"source",value:"LD"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  lionair_idr_daily_file {
    sourceName: "lionair_idr_daily_file"
    tableList: [
      {
        tablename : ${source_schema}".lionair_idr_transaction_daily"
        partitiondatekey : "datadate",
        filterFunction { name: "generic_is_not_null", columns: ["booking_reloc"] }
      },
      {
        tablename : ${source_schema}".lionair_idr_special_refund"
        partitiondatekey : "datadate"
      }
    ]
    datasetlogicfunction : [{name: "merge_lionair_data"}, {name: "generic_trim"}]
    outputpartitionkey : [
      {partitionname:"source",value:"LDIDR"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  nusa_daily_file {
    sourceName : "nusa_daily_file"
    tableList:[{
      tablename: ${source_schema}".Nusa_Daily"
      partitiondatekey: "datadate"
      filterFunction {name: "generic_is_not_null", columns: ["agent_booking_id"]}
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "NTR"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  spicejet_daily_file {
    sourceName : "spicejet_daily_file"
    tableList:[{
      tablename: ${source_schema}".SpiceJet_Daily"
      partitiondatekey: "datadate"
      filterFunction {name: "generic_is_equal_to", constants: ["AG"], columns: ["type"]}
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "SD"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  whypaymore_daily_file {
    sourceName : "whypaymore_daily_file"
    tableList:[{
      tablename: ${source_schema}".WhyPayMore_Daily"
      partitiondatekey: "datadate"
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "WD"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  jetstar_daily_file {
    sourceName : "jetstar_daily_file"
    tableList:[{
      tablename: ${source_schema}".jetstar_daily"
      partitiondatekey: "datadate"
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "JD"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  gofirst_daily_file {
      sourceName : "gofirst_daily_file"
      tableList:[{
        tablename: ${source_schema}".GoFirst_Daily"
        partitiondatekey: "datadate"
        filterFunction {name: "generic_is_equal_to", constants: ["AG"], columns: ["type"]}
      }]
      datasetlogicfunction : [{name: "generic_trim"}]
      outputpartitionkey: [
        {partitionname: "source", value: "GD"},
        {partitionname: "product_type", value: "FLIGHT"}
      ]
    }

  hotel_marriott_file {
    sourceName : "hotel_marriott_file"
    tableList:[
      {
        tablename : ${source_schema}."marriott_settlement"
        partitiondatekey : "datadate"
      },
      {
        tablename : ${source_multiproduct_schema}."financial_transactions"
        selectcolumn: ["booking_id", "supplier_booking_id", "booking_date", "sub_supplier_id", "datadate", "start_date", "payment_model"]
        extractfunctions: [
          {name: "generic_is_equal_to", constants: ["29002"], columns: ["supplier_id"]},
          {name: "generic_is_in", constants: ["4","1","2","0"], columns: ["payment_model"]},
          {name: "marriott_transaction_within_date"}
        ],
      },
      {
        tablename : "finance_normalizer.hotel_mapping_master"
        selectcolumn: ["hotel_id", "external_hotel_code"]
      }
    ]
    datasetlogicfunction : [{name: "merge_marriott_data"}, {name: "generic_trim"}]
    outputpartitionkey : [
        {partitionname:"source",value:"MARRIOTT"},
        {partitionname:"product_type",value:"HOTEL"}
    ]
  }

  hotel_hyatt_file {
    sourceName : "hotel_hyatt_file"
    tableList:[
      {
        tablename : ${source_schema}."hyatt_settlement"
        partitiondatekey : "datadate"
        filterFunction {name: "generic_is_equal_to", constants: ["PTA"], columns: ["payment_status_code"]}
      },
      {
        tablename : ${source_schema}."hyatt_settlement"
        partitiondatekey : "datadate"
        filterFunction {name: "generic_is_equal_to", constants: ["PTA"], columns: ["payment_status_code"]}
        extractfunctions: [
            {name: "hyatt_transaction_within_date"}
        ],
      }
    ]
    datasetlogicfunction : [{name: "merge_hyatt_data"}, {name: "generic_trim"}]
    outputpartitionkey : [
        {partitionname:"source", value:"HYATT"},
        {partitionname:"product_type", value:"HOTEL"}
    ]
  }

  citilink_daily_file {
      sourceName : "citilink_daily_file"
      tableList:[
       {
        tablename: ${source_schema}".Citilink_Daily"
        partitiondatekey: "datadate"
        filterFunction {name: "generic_is_match_with_regex", constants: ["^[A-Z0-9]{6}$"], columns: ["reference"]}
        extractfunctions: [
            {name: "generic_is_date_match_with_file_name", constants: ["yyyyMMdd"], columns: ["date", "file_name"]}
        ],
       }
      ]
      datasetlogicfunction : [{name: "generic_trim"}]
      outputpartitionkey: [
        {partitionname: "source", value: "CLD"},
        {partitionname: "product_type", value: "FLIGHT"}
      ]
    }

  scoot_daily_file {
    sourceName : "scoot_daily_file"
    tableList:[{
      tablename: ${source_schema}".scoot_daily"
      filterFunction {name: "generic_is_equal_to", constants: ["AG"], columns: ["payment_method_code"]}
      extractfunctions: [
        {name: "generic_is_not_in", constants: ["NUS2100001"], columns: ["agent"]}
      ]
      partitiondatekey: "datadate"
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "SCD"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  scoot_ndc_daily_file {
    sourceName : "scoot_ndc_daily_file"
    tableList:[{
      tablename: ${source_schema}".scoot_daily"
      filterFunction {name: "generic_is_equal_to", constants: ["AG"], columns: ["payment_method_code"]}
      partitiondatekey: "datadate"
      extractfunctions: [
        {name: "generic_is_equal_to", constants: ["NUS2100001"], columns: ["agent"]}
      ]
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "SCND"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  fy_daily_file {
    sourceName : "fy_daily_file"
    tableList:[{
      tablename: ${source_schema}".fy_daily"
      partitiondatekey: "datadate"
      filterFunction {
        name: "generic_is_in",
        constants: ["CreditAccountDebitForPayment", "CreditAccountCredit"],
        columns: ["transaction_type"]
      }
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "FYD"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  viator_daily_report {
    sourceName: "viator_daily_report"
    tableList:[{
      tablename: ${source_schema}".viator_daily_report"
      partitiondatekey: "datadate"
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "VIATOR"},
      {partitionname: "product_type", value: "ACTIVITY"}
    ]
  }

  kkday_daily_report: {
    sourceName: "kkday_daily_report"
    tableList:[{
      tablename: ${source_schema}".kkday_daily_report"
      partitiondatekey: "datadate"
    }]
    datasetlogicfunction: [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "KKDAY"},
      {partitionname: "product_type", value: "ACTIVITY"}
    ]
  }

  bemyguest_daily_report {
      sourceName: "bemyguest_daily_report"
      tableList:[{
        tablename: ${source_schema}".bemyguest_daily_report"
        partitiondatekey: "datadate"
      }]
      datasetlogicfunction : [{name: "generic_trim"}]
      outputpartitionkey: [
        {partitionname: "source", value: "BEMYGUEST"},
        {partitionname: "product_type", value: "ACTIVITY"}
      ]
    }

  bemyguest_cm_daily_report {
    sourceName: "bemyguest_cm_daily_report"
    tableList:[{
        tablename: ${source_schema}".bemyguest_cm_daily_report"
        partitiondatekey: "datadate"
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
          {partitionname: "source", value: "BEMYGUESTCM"},
          {partitionname: "product_type", value: "ACTIVITY"}
    ]
  }

  hotel_violet_file {
    sourceName : "hotel_violet_file"
    tableList:[
      {
        tablename : ${source_multiproduct_schema}."financial_transactions"
        selectcolumn: ["booking_id", "cid", "product_type", "tracking_tag", "supplier_currency", "datadate"]
        extractfunctions: [
          {name: "generic_is_equal_to", constants: ["1758195"], columns: ["cid"]},
          {name: "generic_is_equal_to", constants: ["HOTEL"], columns: ["product_type"]},
          {name: "generic_is_in", constants: ["BREAKDOWN", "PAYMENT,BREAKDOWN"], columns: ["source_type"]}
        ]
      },
      {
        tablename : ${source_schema}."hotel_violet_booking_activity"
        partitiondatekey : "datadate"
      }
    ]
    datasetlogicfunction : [{name: "merge_violet_data"}, {name: "generic_trim"}]
    outputpartitionkey : [
      {partitionname:"source", value:"HVD"},
      {partitionname:"product_type", value:"HOTEL"}
    ]
  }

  hotel_hilton_file {
    sourceName: "hotel_hilton_file"
    tableList: [
      {
        tablename : ${source_schema}".hilton_statement"
        partitiondatekey : "datadate"
      },
      {
        tablename : ${source_schema}".hilton_tax"
        partitiondatekey : "datadate"
      }
    ]
    datasetlogicfunction : [{name: "merge_hilton_data"}, {name: "generic_trim"}]
    outputpartitionkey : [
      {partitionname:"source", value:"HILTON"},
      {partitionname:"product_type", value: "HOTEL"}
    ]
  }

  airasia_daily_file {
    sourceName : "airasia_daily_file"
    tableList:[{
      tablename: ${source_schema}".AirAsia_Daily"
      partitiondatekey: "datadate"
      extractfunctions: [
        {name: "generic_is_in", constants: ["PPAccountDebitForPayment", "PPAccountCredit"], columns: ["account_transaction_type"]},
        {name: "generic_is_not_equal_to_for_some", constants: ["PPAccountCredit", "APITHAGODA"], columns: ["account_transaction_type", "reference"]}
        {name: "generic_is_not_equal_to_for_some", constants: ["PPAccountCredit", "APITHAGOMY"], columns: ["account_transaction_type", "reference"]}
        {name: "generic_is_not_equal_to_for_some", constants: ["PPAccountCredit", "APITHAGOPH"], columns: ["account_transaction_type", "reference"]}
        {name: "generic_is_not_equal_to_for_some", constants: ["PPAccountCredit", "APITHAGOSG"], columns: ["account_transaction_type", "reference"]}
      ]
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "AAD"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  direct_comm_file {
    sourceName: "direct_comm_file"
    tableList: [
      {
        tablename : ${directcomm_source_schema}".hotel_settlement_detail"
        partitiondatekey : "datadate"
      }
    ]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey : [
      {partitionname:"source", value:"DIRECTCOMM"},
      {partitionname:"product_type", value: "HOTEL"}
    ]
  }

  cfar_covergenius_premium {
    sourceName : "cfar_covergenius_premium"
    datasetlogicfunction : [{name: "generic_trim"}]
    tableList:[
      {
        tablename: ${source_schema}".cfar_covergenius_premium_statement"
        partitiondatekey: "datadate"
      }
    ]
    outputpartitionkey: [
      {partitionname: "source", value: "CGM"},
      {partitionname: "product_type", value: "CANCEL_FOR_ANY_REASON"}
    ]
  }

  cfar_covergenius_claim {
    sourceName : "cfar_covergenius_claim"
    datasetlogicfunction : [{name: "generic_trim"}]
    tableList:[
      {
        tablename: ${source_schema}".cfar_covergenius_claim_statement"
        partitiondatekey: "datadate"
      }
    ]
    outputpartitionkey: [
      {partitionname: "source", value: "CGM"},
      {partitionname: "product_type", value: "CANCEL_FOR_ANY_REASON"}
    ]
  }

  airtrip_issuance_and_cancellation_daily_file {
    sourceName : "airtrip_issuance_and_cancellation_daily_file"
    tableList:[
      {
        tablename : ${source_schema}".AirTrip_Daily_Issuance"
        partitiondatekey : "datadate"
      },
      {
        tablename : ${source_schema}".AirTrip_Daily_Cancellation"
        partitiondatekey : "datadate"
      }
    ]
    datasetlogicfunction : [{name: "merge_airtrip_data"}, {name: "generic_trim"}]
    outputpartitionkey : [
      {partitionname:"source",value:"AT"},
      {partitionname:"product_type",value:"FLIGHT"}
    ]
  }

  jejuair_sale_and_refund_daily_file {
    sourceName : "jejuair_sale_and_refund_daily_file"
    tableList:[
      {
        tablename : ${source_schema}".jejuair_daily_sale"
        partitiondatekey : "datadate"
      },
      {
        tablename : ${source_schema}".jejuair_daily_refund"
        partitiondatekey : "datadate"
      }
    ]
    datasetlogicfunction : [{name: "merge_jejuair_data"}, {name: "generic_trim"}]
    outputpartitionkey : [
      {partitionname:"source",value:"JJD"},
      {partitionname:"product_type",value:"FLIGHT"}
    ]
  }

  tway_daily_file {
    sourceName : "tway_daily_file"
    tableList:[{
      tablename: ${source_schema}".tway_daily"
      partitiondatekey: "datadate"
      filterFunction {name: "generic_is_equal_to", constants: ["DET"], columns: ["record_identifier"]}
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "TWD"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  akasa_daily_file {
    sourceName : "akasa_daily_file"
    tableList:[{
      tablename: ${source_schema}".akasa_daily_transactions"
      partitiondatekey: "datadate"
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "AD"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  emt_daily_file {
    sourceName : "emt_daily_file"
    tableList:[{
      tablename: ${source_schema}".emt_daily"
      partitiondatekey: "datadate"
      filterFunction { name: "generic_is_not_null", columns: ["pnr"] }
      extractfunctions: [
        {name: "generic_is_not_in", constants: ["FlightBookingReject"], columns: ["transaction_type"]},
        {name: "generic_is_not_in", constants: ["----"], columns: ["pnr"]}
      ]
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "EMT"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  unififi_daily_file {
    sourceName : "unififi_daily_file"
    tableList:[{
      tablename: ${source_schema}".Unififi_Daily"
      partitiondatekey: "datadate"
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "UNIFIFI"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  fr24_daily_file {
    sourceName : "fr24_daily_file"
    tableList:[{
      tablename: ${source_schema}".fr24_daily"
      partitiondatekey: "datadate"
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "FR24"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  dida_daily_file {
    sourceName : "dida_daily_file"
    tableList:[{
      tablename: ${source_schema}".dida_daily"
      partitiondatekey: "datadate"
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "DIDA"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  tripjack_daily_file {
    sourceName : "tripjack_daily_file"
    tableList:[{
      tablename: ${source_schema}".tripjack_daily"
      partitiondatekey: "datadate"
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "TJ"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  holiday_tour_iata_daily_file {
    sourceName : "holiday_tour_iata_daily_file"
    tableList: [{
      tablename: ${source_schema}".holiday_tour_iata_daily"
      partitiondatekey: "datadate"
    }]
    datasetlogicfunction : [{name: "bsp_normalized_logic"}]
    outputpartitionkey: [
      {partitionname: "source", value: "HTI"},
      {partitionname: "product_type", value: "FLIGHT"}
    ]
  }

  tripadd_daily_file {
    sourceName : "tripadd_daily_file"
    tableList:[{
      tablename: ${source_schema}".tripadd_daily"
      partitiondatekey: "datadate"
    }]
    datasetlogicfunction : [{name: "generic_trim"}]
    outputpartitionkey: [
      {partitionname: "source", value: "TRIPADD"},
      {partitionname: "product_type", value: "ANCILLARY"}
    ]
  }

  nokair_daily_file {
      sourceName : "nokair_daily_file"
      tableList:[{
        tablename: ${source_schema}".nokair_daily"
        partitiondatekey: "datadate"
        filterFunction { name: "generic_is_not_null", columns: ["reference"] }
      }]
      datasetlogicfunction : [{name: "generic_trim"}]
      outputpartitionkey: [
        {partitionname: "source", value: "NOK"},
        {partitionname: "product_type", value: "FLIGHT"}
      ]
    }

  #================= Output table ===============
  table: {
    name: ${schema}".multiproduct_supplier_normalized_data"
    mode: "overwrite" //"default is append"
    trackColumns: ["datadate"]
    partition: [
      {
        key: "datadate"
        value: "0" // "[-t,..,0,..,+t]" (default is 0 (current date))) or "accountingdate"
      },
      {
        value: "outputpartitionkey" // (default is outputpartitionkey from each file)
      }
    ]
  }
  #=============================================
}

tracking-status {
  tablename: ${schema}".multiproduct_supplier_normalizer_status"
}

result {
  mail {
    alert-sender = "<EMAIL> (<EMAIL>)"
    alert-recipients = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
  }
}