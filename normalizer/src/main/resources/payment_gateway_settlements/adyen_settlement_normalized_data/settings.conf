
app-name = "PaymentSettlementProcess"

normalizer {
  include "normalizer_mapper.conf"
  # Adyen-specific source configurations
  adyen_settlement_report {
    sourceName : "adyen_settlement_report"
    tableList:[
      {
        tablename : ${source_schema}."raw_adyen_settlement_report"
      }
    ]
  }

  adyen_china_settlement_report {
      sourceName : "adyen_china_settlement_report"
      tableList:[
        {
          tablename : ${source_schema}."raw_adyen_china_settlement_report"
          extractfunctions: [
                    {name: "generic_is_not_in", constants: ["MerchantPayout","Balancetransfer","BankInstructionReturned","ManualCorrected","InvoiceDeduction"], columns: ["type"]},
          ]
        }
      ]
  }
}