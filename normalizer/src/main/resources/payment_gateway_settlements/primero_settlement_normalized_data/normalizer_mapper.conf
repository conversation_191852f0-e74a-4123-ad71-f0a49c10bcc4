mappers: [
  {
    aliasColName: "ar_payment_transaction_id", dataType: "StringType",
    colMapper: [
      {sourceName: ${source_file}, colName: "ar_payment_transaction_id"}
    ]
  },
  {
    aliasColName: "ar_gateway_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${source_file}, value: ${gateway_id}}
      ]
  },
  {
    aliasColName: "ar_payment_file_id", dataType: "LongType",
    colMapper: [
      {sourceName: ${source_file}, colName: "file_id"}
      ]
  },
  {
    aliasColName: "ar_file_type_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${source_file}, value: ${file_type_id}}
      ]
  },
  {
    aliasColName: "ar_raw_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${source_file}, colName: "max_line_number"}
      ]
  },
    {
    aliasColName: "ar_transaction_type_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${source_file}, colName: "ar_transaction_type_id"}
      ]
  },
  {
    aliasColName: "prebooking_id", dataType: "LongType",
    colMapper: [
      {sourceName: ${source_file}, colName: "prebooking_id"}
    ]
  },
  {
    aliasColName: "itinerary_id", dataType: "LongType",
    colMapper: [
      {sourceName: ${source_file}, colName: "itinerary_id"}
    ]
  },
  {
    aliasColName: "merchant_reference", dataType: "StringType",
    colMapper: [
      {sourceName: ${source_file}, colName: "merchant_reference"}
    ]
  },
  {
    aliasColName: "source_amount", dataType: "StringType",
    colMapper: [
      {sourceName: ${source_file}, colName: "source_amount"}
    ]
  },
  {
    aliasColName: "source_currency", dataType: "StringType",
    colMapper: [
      {sourceName: ${source_file}, colName: "source_currency"}
    ]
  },
  {
    aliasColName: "received_amount", dataType: "StringType",
    colMapper: [
      {sourceName: ${source_file}, colName: "received_amount"}
    ]
  },
  {
    aliasColName: "received_currency", dataType: "StringType",
    colMapper: [
      {sourceName: ${source_file}, colName: "holding_currency"}
    ]
  },
  {
    aliasColName: "fee_amount", dataType: "StringType",
    colMapper: [
      {sourceName: ${source_file}, colName: "fee_amount"}
    ]
  },
  {
    aliasColName: "fee_currency", dataType: "StringType",
    colMapper: [
       {sourceName: ${source_file}, colName: "holding_currency"}
    ]
  },
  {
    aliasColName: "exchange_rate", dataType: "StringType",
    colMapper: [
      {sourceName: ${source_file}, colName: "exchange_rate"}
    ]
  },
  {
    aliasColName: "payment_method", dataType: "StringType",
    colMapper: [
      {sourceName: ${source_file}, colName: "payment_method"},
    ]
  },
  {
    aliasColName: "auth_amount", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, value: null}]
  },
  {
    aliasColName: "markup_fee_amount", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, value: null}]
  },
  {
    aliasColName: "scheme_fee_amount", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, value: null}]
  },
  {
    aliasColName: "interchange_fee_amount", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, value: null}]
  },
  {
    aliasColName: "rec_status", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value: 1}]
  },
  {
    aliasColName: "event_type", dataType: "StringType",
    colMapper: [
      {sourceName: ${source_file}, colName: "event_type"}
    ]
  },
  {
    aliasColName: "report_date", dataType: "LongType",
    colMapper: [
      {sourceName: ${source_file}, colName: "report_date"}
    ]
  },
 {
    aliasColName: "agoda_posting_date", dataType: "LongType",
    colMapper: [
      {sourceName: ${source_file}, colName: "agoda_posting_date"}
    ]
  },
  {
    aliasColName: "is_gateway_transaction", dataType: "BooleanType",
    colMapper: [{sourceName: ${source_file}, value: true}]
  },
  {
    aliasColName: "mid", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, colName: "client_entity_name"}]
  },
  {
    aliasColName: "revenue_entity", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value: null}]
  },
  {
    aliasColName: "rate_contract_entity", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value: null}]
  },
  {
    aliasColName: "merchant_of_record", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value: ${mor}}]
  },
  {
    aliasColName: "white_label_id", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value: ${white_label_id}}]
  },
  {
    aliasColName: "gateway_unique_id", dataType: "StringType",
    colMapper: [
      {sourceName: ${source_file}, colName: "gateway_unique_id"}
        ]
  },
  {
    aliasColName: "is_used_for_reconciliation", dataType: "BooleanType",
    colMapper: [
      {sourceName: ${source_file}, colName: "is_used_for_reconciliation"}
    ]
  },
  {
    aliasColName: "ar_event_type_id", dataType: "StringType",
    colMapper: [
      {sourceName: ${source_file}, colName: "ar_event_type_id"}
    ]
  },
  {
    aliasColName: "rec_created_when", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, value: 0}]
  },
  {
    aliasColName: "rec_created_by", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, value: ${created_by}}]
  },
]