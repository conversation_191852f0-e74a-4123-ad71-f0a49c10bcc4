mappers: [
  {
    aliasColName: "ar_payment_transaction_id", dataType: "StringType",
    colMapper: [
           {
             sourceName: ${source_file},
             function: { name: "generic_concat_with_delimiter", columns: ["file_id","line_number"], constants: [""] }
           }
    ]
  },
  {
    aliasColName: "ar_gateway_id", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value: ${gateway_id}}]
  },
  {
    aliasColName: "ar_payment_file_id", dataType: "LongType",
    colMapper: [{sourceName: ${source_file}, colName: "file_id"}]
  },
  {
    aliasColName: "ar_file_type_id", dataType: "IntegerType",
     colMapper: [{sourceName: ${source_file}, value: ${file_type_id}}]
  },
  {
    aliasColName: "ar_raw_id", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value:0}]
  },
  {
    aliasColName: "merchant_reference", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, colName: "invoice_id"}]
  },
  {
    aliasColName: "ar_transaction_type_id", dataType: "IntegerType",
    colMapper: [
       {
       sourceName: ${source_file},
       function: { name: "get_transaction_type", columns: ["invoice_id"] }
      }
     ]
   },
  {
    aliasColName: "prebooking_id", dataType: "LongType",
    colMapper: [
      {
       sourceName: ${source_file},
       function: { name: "paypal_get_prebooking_id", columns: ["invoice_id", "transaction_event_code"], constants: [${file_type_id}] }
      }
    ]
  },
  {
    aliasColName: "itinerary_id", dataType: "LongType",
    colMapper: [
      {
       sourceName: ${source_file},
       function: { name: "paypal_get_itinerary_id", columns: ["invoice_id", "transaction_event_code"], constants: [${file_type_id}] }
      }
    ]
  },
  {
    aliasColName: "gateway_unique_id", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, colName: "transaction_id"}]
  },
  {
    aliasColName: "event_type", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, colName: "transaction_event_code"}]
  },
  {
    aliasColName: "report_date", dataType: "LongType",
    colMapper: [
      {
       sourceName: ${source_file},
       function: { name: "convert_datestring_to_bigint", columns: ["transaction_completion_date"] }
      }
    ]
  },
  {
    aliasColName: "auth_amount", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, value:null}]
  },
  {
    aliasColName: "source_currency", dataType: "StringType",
    colMapper: [
       {
        sourceName: ${source_file},
        function: { name: "format_string_to_uppercase", columns: ["gross_transaction_currency"]}
    }
    ]
  },
  {
    aliasColName: "source_amount", dataType: "StringType",
    colMapper: [
       {
        sourceName: ${source_file},
        function: { name: "paypal_get_source_amount", columns: ["transaction_debit_or_credit", "gross_transaction_amount"] }

        }
    ]
  },
  {
    aliasColName: "received_currency", dataType: "StringType",
    colMapper: [
        {
          sourceName: ${source_file},
          function: { name: "paypal_get_received_currency", columns: ["fee_currency","gross_transaction_currency"]}
        }
    ]
  },
  {
    aliasColName: "received_amount", dataType: "StringType",
    colMapper: [
      {
       sourceName: ${source_file},
        function: { name: "paypal_get_received_amount", columns: ["transaction_debit_or_credit", "gross_transaction_amount"] }
      }
    ]
  },
 {
   aliasColName: "exchange_rate", dataType: "StringType",
   colMapper: [
      {
        sourceName: ${source_file},
        function: { name: "paypal_get_exchange_rate", columns: ["gross_transaction_amount"] }
      }
    ]
},
  {
    aliasColName: "payment_method", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, colName: null}]
  },
  {
    aliasColName: "fee_currency", dataType: "StringType",
    colMapper: [
        {
          sourceName: ${source_file},
          function: { name: "paypal_get_fee_currency", columns: ["fee_currency","gross_transaction_currency"]}
        }
    ]
  },
  {
    aliasColName: "fee_amount", dataType: "StringType",
    colMapper: [
      {
        sourceName: ${source_file},
        function: { name: "paypal_get_fee_amount", columns: ["transaction_debit_or_credit", "fee_amount"] }
      }
    ]
  },
  {
    aliasColName: "markup_fee_amount", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, value:null}]
  },
  {
    aliasColName: "scheme_fee_amount", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, value:null}]
  },
  {
    aliasColName: "interchange_fee_amount", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, value:null}]
  },
  {
    aliasColName: "is_used_for_reconciliation", dataType: "BooleanType",
    colMapper: [
      {
         sourceName: ${source_file},
        function: { name: "is_used_for_reconciliation", columns: ["transaction_event_code"], constants: [${file_type_id}] }
      }
    ]
  },
  {
    aliasColName: "is_gateway_transaction", dataType: "BooleanType",
    colMapper: [
      {
        sourceName: ${source_file},
        function: { name: "is_gateway_transaction", columns: ["transaction_event_code"], constants: [${file_type_id}] }
      }
    ]
   },
  {
    aliasColName: "agoda_posting_date", dataType: "LongType",
    colMapper: [{sourceName: ${source_file}, colName: "accounting_date" }]
  },
  {
    aliasColName: "rec_status", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value: 1 }]
  },
  {
    aliasColName: "rec_created_when", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, value: 0}]
  },
  {
    aliasColName: "rec_created_by", dataType: "StringType",
    colMapper: [
      {sourceName: ${source_file}, value: 0}
    ]
  },
  {
    aliasColName: "mid", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, colName: null }]
  },
  {
    aliasColName: "revenue_entity", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value: null}]
  },
  {
    aliasColName: "rate_contract_entity", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value: null}]
  },
  {
    aliasColName: "merchant_of_record", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value: ${mor}}]
  },
  {
    aliasColName: "white_label_id", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value: ${white_label_id}}]
  },
  {
    aliasColName: "ar_event_type_id", dataType: "StringType",
    colMapper: [
      {
       sourceName: ${source_file},
       function: { name: "paypal_get_event_type_id", columns: ["transaction_debit_or_credit", "transaction_event_code"], constants: [${file_type_id}]  }
      }
    ]
  },
]
