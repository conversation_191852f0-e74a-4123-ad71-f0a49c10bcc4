<<<<<<< Updated upstream
=======
include "_common.conf"
project-name = "Payment-Gateway-Settlement-Process"
>>>>>>> Stashed changes
app-name = "AlipaySettlementNormalizer"

normalizer {
  # Override lookback date for Alipay
  lookbackdate = 1
  include "normalizer_mapper.conf"
  # Alipay-specific source configuration
  alipay_osp_settlement {
    sourceName: ${source_file}
    tableList: [
      {
        tablename: ${source_schema}."raw_alipay_osp_settlement"
      }
    ]
    datasetlogicfunction: [
      {name: "generic_trim"}
    ]
  }
}