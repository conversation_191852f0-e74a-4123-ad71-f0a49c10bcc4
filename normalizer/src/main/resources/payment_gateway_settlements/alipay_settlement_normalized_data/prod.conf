include classpath("payment_gateway_settlements/_common_prod.conf")
include "gateway_file.conf"
<<<<<<< Updated upstream
include "settings.conf"
=======

source_schema = "finance_sftp_downloader"
schema = "finance_payment_settlement"
current_env = "prod"

coordinator.freq = "30 10 *"

app.adpMessaging.appName = "normalizer.prod"

hadoop {
  hdfs.user = "hk-it-pymnt-svc"
  knox.enabled = true
  hive.username = "hk-it-pymnt-svc"
  credentials = "hadoop/hk-it-pymnt-svc/credentials"
}

result {
  mail {
    alert-sender = "<EMAIL> (Downloader)"
    alert-recipients = "<EMAIL>"
  }
}

hadoop.use-smart-table-loader = true
>>>>>>> Stashed changes
