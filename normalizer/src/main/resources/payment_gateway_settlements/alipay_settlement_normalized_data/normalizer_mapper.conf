mappers: [
  {
    aliasColName: "ar_payment_transaction_id", dataType: "StringType",
    colMapper: [
           {
             sourceName: ${source_file},
             function: { name: "generic_concat_with_delimiter", columns: ["file_id","line_number"], constants: [""] }
           }
    ]
  },
  {
    aliasColName: "ar_gateway_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${alipay_osp_settlement.sourceName}, value: ${gateway_id}}
      ]
  },
  {
    aliasColName: "ar_payment_file_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${alipay_osp_settlement.sourceName}, colName: "file_id"}
      ]
  },
  {
    aliasColName: "ar_file_type_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${alipay_osp_settlement.sourceName}, value: ${file_type_id}}
      ]
  },
  {
    aliasColName: "ar_raw_id", dataType: "IntegerType",
    colMapper: [
      {sourceName: ${alipay_osp_settlement.sourceName}, value: 0}
      ]
  },
  {
    aliasColName: "ar_transaction_type_id", dataType: "IntegerType",
    colMapper: [
        {
        sourceName: ${alipay_osp_settlement.sourceName},
        function: { name: "get_transaction_type", columns: ["reference_transaction_id"] }
      }
      ]
  },
  {
    aliasColName: "prebooking_id", dataType: "LongType",
    colMapper: [
      {
        sourceName: ${alipay_osp_settlement.sourceName},
        function: { name: "get_prebooking_id", columns: ["reference_transaction_id"] }
      }
    ]
  },
  {
    aliasColName: "itinerary_id", dataType: "LongType",
    colMapper: [
      {
        sourceName: ${alipay_osp_settlement.sourceName},
       function: { name: "get_hotel_itinerary_id", columns: ["reference_transaction_id"] }
      }
    ]
  },
  {
    aliasColName: "merchant_reference", dataType: "StringType",
    colMapper: [
      {sourceName: ${alipay_osp_settlement.sourceName}, colName: "reference_transaction_id"},
    ]
  },
  {
    aliasColName: "source_amount", dataType: "StringType",
    colMapper: [
      {sourceName: ${alipay_osp_settlement.sourceName},
       function: {name: "alipay_osp_get_source_amount", columns: ["transaction_amount_value", "payment_method_type"]}}
    ]
  },
  {
    aliasColName: "source_currency", dataType: "StringType",
    colMapper: [
      {sourceName: ${alipay_osp_settlement.sourceName}, colName: "transaction_currency"}
    ]
  },
  {
    aliasColName: "received_amount", dataType: "StringType",
    colMapper: [
      {sourceName: ${alipay_osp_settlement.sourceName},
       function: {name: "alipay_osp_get_received_amount", columns: ["settlement_amount_value", "fee_amount_value"]}}
    ]
  },
  {
    aliasColName: "received_currency", dataType: "StringType",
    colMapper: [
      {sourceName: ${alipay_osp_settlement.sourceName}, colName: "settlement_currency"}
    ]
  },
  {
    aliasColName: "fee_amount", dataType: "StringType",
    colMapper: [
      {sourceName: ${alipay_osp_settlement.sourceName},
       function: {name: "alipay_osp_get_fee_amount", columns: ["fee_amount_value"]}}
    ]
  },
  {
    aliasColName: "fee_currency", dataType: "StringType",
    colMapper: [
      {sourceName: ${alipay_osp_settlement.sourceName}, colName: "fee_currency"},
    ]
  },
  {
    aliasColName: "exchange_rate", dataType: "StringType",
    colMapper: [
      {
        sourceName: ${alipay_osp_settlement.sourceName},
        function: { name: "convert_double_to_string", columns: ["quote_price"], constants: [8] }
      }
    ]
  },
  {
    aliasColName: "payment_method", dataType: "StringType",
    colMapper: [
      {sourceName: ${alipay_osp_settlement.sourceName}, colName: "payment_method_type"},
    ]
  },
  {
    aliasColName: "auth_amount", dataType: "StringType",
    colMapper: [{sourceName: ${alipay_osp_settlement.sourceName}, value: null}]
  },
  {
    aliasColName: "markup_fee_amount", dataType: "StringType",
    colMapper: [{sourceName: ${alipay_osp_settlement.sourceName}, value: null}]
  },
  {
    aliasColName: "scheme_fee_amount", dataType: "StringType",
    colMapper: [{sourceName: ${alipay_osp_settlement.sourceName}, value: null}]
  },
  {
    aliasColName: "interchange_fee_amount", dataType: "StringType",
    colMapper: [{sourceName: ${alipay_osp_settlement.sourceName}, value: null}]
  },
  {
    aliasColName: "rec_status", dataType: "IntegerType",
    colMapper: [{sourceName: ${alipay_osp_settlement.sourceName}, value: 1}]
  },
  {
    aliasColName: "event_type", dataType: "StringType",
    colMapper: [{sourceName: ${alipay_osp_settlement.sourceName}, colName: "transaction_type"}]
  },
  {
    aliasColName: "report_date", dataType: "LongType",
    colMapper: [
      {
        sourceName: ${alipay_osp_settlement.sourceName},
        function: { name: "convert_datestring_to_bigint", columns: ["settlement_time"] }
      }
    ]
  },
  {
    aliasColName: "agoda_posting_date", dataType: "LongType",
    colMapper: [{sourceName: ${alipay_osp_settlement.sourceName}, colName: "settlement_time"}]
  },
  {
    aliasColName: "is_gateway_transaction", dataType: "BooleanType",
    colMapper: [{sourceName: ${alipay_osp_settlement.sourceName}, value: true}]
  },
  {
    aliasColName: "mid", dataType: "StringType",
    colMapper: [{sourceName: ${alipay_osp_settlement.sourceName}, colName: "reference_merchant_id"}]
  },
  {
    aliasColName: "revenue_entity", dataType: "IntegerType",
    colMapper: [{sourceName: ${alipay_osp_settlement.sourceName}, value: null}]
  },
  {
    aliasColName: "rate_contract_entity", dataType: "IntegerType",
    colMapper: [{sourceName: ${alipay_osp_settlement.sourceName}, value: null}]
  },
  {
    aliasColName: "merchant_of_record", dataType: "IntegerType",
    colMapper: [{sourceName: ${alipay_osp_settlement.sourceName}, value: ${mor}}]
  },
  {
    aliasColName: "white_label_id", dataType: "IntegerType",
    colMapper: [{sourceName: ${alipay_osp_settlement.sourceName}, value: ${white_label_id}}]
  },
  {
    aliasColName: "gateway_unique_id", dataType: "StringType",
    colMapper: [
       {
         sourceName: ${source_file},
         function: { name: "generic_concat_with_delimiter", columns: ["reference_transaction_id","transaction_type"], constants: [""] }
       }
     ]
  },
  {
    aliasColName: "is_used_for_reconciliation", dataType: "BooleanType",
    colMapper: [
      {
        sourceName: ${alipay_osp_settlement.sourceName},
        function: { name: "is_used_for_reconciliation", columns: ["transaction_type"], constants: [${file_type_id}] }
      }
    ]
  },
  {
    aliasColName: "ar_event_type_id", dataType: "StringType",
    colMapper: [
      {
        sourceName: ${alipay_osp_settlement.sourceName},
        function: { name: "get_event_type_id", columns: ["transaction_type"], constants: [${file_type_id}] }
      }
    ]
  },
  {
    aliasColName: "rec_created_when", dataType: "StringType",
    colMapper: [{sourceName: ${alipay_osp_settlement.sourceName}, value: 0}]
  },
  {
    aliasColName: "rec_created_by", dataType: "StringType",
    colMapper: [{sourceName: ${alipay_osp_settlement.sourceName}, value: 0}]
  },
]
