# Configuration for STAGING environment
include classpath("payment_gateway_settlements/_common_staging.conf")
include "settings.conf"
include "gateway_file.conf"
<<<<<<< Updated upstream
=======

source_schema = "finance_sftp_downloader_qa"
schema = "finance_sftp_downloader_qa"
current_env = "staging"

app.adpMessaging.appName: "normalizer.qa"

result {
  mail {
    alert-sender = "<EMAIL> (Downloader)"
    alert-recipients = "<EMAIL>"
  }
}

hadoop {
    hdfs.user = "hk-it-pymnt-svc--dev"
    knox.enabled = true
    hive.username = "hk-it-pymnt-svc--dev"
    credentials = "hadoop/hk-it-pymnt-svc--dev/credentials"
}

hadoop.use-smart-table-loader = true
>>>>>>> Stashed changes
