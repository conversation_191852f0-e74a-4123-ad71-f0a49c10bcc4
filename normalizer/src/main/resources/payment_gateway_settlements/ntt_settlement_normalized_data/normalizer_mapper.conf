mappers: [
  {
    aliasColName: "ar_gateway_id", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value: ${gateway_id}}]
  },
  {
    aliasColName: "ar_payment_file_id", dataType: "LongType",
    colMapper: [{sourceName: ${source_file}, colName: "file_id"}]
  },
  {
    aliasColName: "ar_file_type_id", dataType: "IntegerType",
     colMapper: [{sourceName: ${source_file}, value: ${file_type_id}}]
  },
  {
    aliasColName: "ar_raw_id", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value:0}]
  },
  {
    aliasColName: "merchant_reference", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, colName: "merchant_reference"}]
  },
  {
    aliasColName: "ar_transaction_type_id", dataType: "IntegerType",
    colMapper: [
       {
       sourceName: ${source_file},
       function: { name: "get_transaction_type", columns: ["merchant_reference"] }
      }
     ]
   },
  {
    aliasColName: "prebooking_id", dataType: "LongType",
    colMapper: [
      {
       sourceName: ${source_file},
       function: { name: "get_prebooking_id", columns: ["merchant_reference"] }
      }
    ]
  },
  {
    aliasColName: "itinerary_id", dataType: "LongType",
    colMapper: [
      {
       sourceName: ${source_file},
       function: { name: "get_itinerary_id", columns: ["merchant_reference"] }
      }
    ]
  },
  {
    aliasColName: "gateway_unique_id", dataType: "StringType",
    colMapper: [
       {
         sourceName: ${source_file},
         function: { name: "generic_concat_with_delimiter", columns: ["merchant_reference","record_type"], constants: [""] }
       }
     ]
  },
  {
    aliasColName: "event_type", dataType: "StringType",
    colMapper: [
      {sourceName: ${source_file}, colName: "record_type"}
    ]
  },
  {
    aliasColName: "report_date", dataType: "LongType",
    colMapper: [
      {
       sourceName: ${source_file},
       function: { name: "convert_datestring_to_bigint", columns: ["booking_date"] }
      }
    ]
  },
  {
    aliasColName: "auth_amount", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, value:null}]
  },
  {
    aliasColName: "source_currency", dataType: "StringType",
    colMapper: [
       {
        sourceName: ${source_file},
        function: { name: "format_string_to_uppercase", columns: ["payment_currency"]}
    }
    ]
  },
  {
    aliasColName: "source_amount", dataType: "StringType",
    colMapper: [
       {
        sourceName: ${source_file},
        function: { name: "format_string_to_double_with_rounding", columns: ["captured_pc"], constants: [4] }

        }
    ]
  },
  {
    aliasColName: "received_currency", dataType: "StringType",
    colMapper: [
        {
          sourceName: ${source_file},
          function: { name: "format_string_to_uppercase", columns: ["settlement_currency"]}
        }
    ]
  },
  {
    aliasColName: "received_amount", dataType: "StringType",
    colMapper: [
      {
       sourceName: ${source_file},
       function: { name: "ntt_get_received_amount", columns: ["payable_sc", "commission_sc", "markup_sc", "scheme_fee_sc", "interchange_sc"] }
      }
    ]
  },
 {
   aliasColName: "exchange_rate", dataType: "StringType",
   colMapper: [
      {
        sourceName: ${source_file},
        function: { name: "ntt_get_exchange_rate", columns: ["captured_pc", "payable_sc", "commission_sc", "markup_sc", "scheme_fee_sc", "interchange_sc"] }
      }
    ]
},
  {
    aliasColName: "payment_method", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, colName: "payment_method"}]
  },
  {
    aliasColName: "fee_currency", dataType: "StringType",
    colMapper: [
        {
          sourceName: ${source_file},
          function: { name: "format_string_to_uppercase", columns: ["settlement_currency"]}
        }
    ]
  },
  {
    aliasColName: "fee_amount", dataType: "StringType",
    colMapper: [
      {
        sourceName: ${source_file},
        function: { name: "format_string_to_double_with_rounding", columns: ["commission_sc"], constants: [4] }
      }
    ]
  },
  {
    aliasColName: "markup_fee_amount", dataType: "StringType",
    colMapper: [
          {
            sourceName: ${source_file},
            function: { name: "format_string_to_double_with_rounding", columns: ["markup_sc"], constants: [4] }
          }
    ]
  },
  {
    aliasColName: "scheme_fee_amount", dataType: "StringType",
    colMapper: [
        {
            sourceName: ${source_file},
            function: { name: "format_string_to_double_with_rounding", columns: ["scheme_fee_sc"], constants: [4] }
        }
    ]
  },
  {
    aliasColName: "interchange_fee_amount", dataType: "StringType",
    colMapper: [
        {
            sourceName: ${source_file},
            function: { name: "format_string_to_double_with_rounding", columns: ["interchange_sc"], constants: [4] }
        }
    ]
  },
  {
       aliasColName: "is_used_for_reconciliation", dataType: "BooleanType",
       colMapper: [
         {
            sourceName: ${source_file},
           function: { name: "is_used_for_reconciliation", columns: ["record_type"], constants: [${file_type_id}] }
         }
       ]
  },
    {
       aliasColName: "is_gateway_transaction", dataType: "BooleanType",
       colMapper: [
         {
           sourceName: ${source_file},
           function: { name: "is_gateway_transaction", columns: ["record_type"], constants: [${file_type_id}] }
         }
       ]
   },
  {
    aliasColName: "agoda_posting_date", dataType: "LongType",
    colMapper: [{sourceName: ${source_file}, colName: "accounting_date" }]
  },
  {
    aliasColName: "rec_status", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value: 1 }]
  },
  {
    aliasColName: "rec_created_when", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, value: 0}]
  },
  {
    aliasColName: "rec_created_by", dataType: "StringType",
    colMapper: [
      {sourceName: ${source_file}, value: 0}
    ]
  },
  {
    aliasColName: "mid", dataType: "StringType",
    colMapper: [{sourceName: ${source_file}, colName: "merchant_account" }]
  },
  {
    aliasColName: "revenue_entity", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value: null}]
  },
  {
    aliasColName: "rate_contract_entity", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value: null}]
  },
  {
    aliasColName: "merchant_of_record", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value: ${mor}}]
  },
  {
    aliasColName: "white_label_id", dataType: "IntegerType",
    colMapper: [{sourceName: ${source_file}, value: ${white_label_id}}]
  },
  {
    aliasColName: "ar_event_type_id", dataType: "StringType",
    colMapper: [
      {
       sourceName: ${source_file},
       function: { name: "ntt_get_event_type_id", columns: ["captured_pc", "record_type"], constants: [${file_type_id}]  }
      }
    ]
  },
]
