package com.agoda.finance.functions.specific.PaymentGatewaySettlements

import com.agoda.finance.constants.{PaymentGatewayList, PaymentSettlementReconciliation}
import com.agoda.finance.functions.utils.PaymentGatewaySettlementUtils
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions.udf

import scala.util.Try

object JuspaySettlementLogic extends LazyLogging {

  def getGatewayIdValue(gatewayName: String): Int = {
    val gatewayId = PaymentGatewayList.getGatewayId(gatewayName.toUpperCase())
    if (gatewayId == 0) {
      logger.error(s"Invalid gateway name: $gatewayName while processing juspay settlement file")
      throw new IllegalArgumentException(s"Invalid gateway name: $gatewayName while processing juspay settlement file")
    }
    gatewayId
  }

  def getGatewayId: UserDefinedFunction = udf { (gatewayName: String) =>
    getGatewayIdValue(gatewayName)
  }

  def getSourceAmount: UserDefinedFunction = udf { (grossCredit: String, grossDebit: String) =>
    val result    = calculateSourceAmount(grossCredit, grossDebit)
    val resultVal = BigDecimal(result).setScale(4, BigDecimal.RoundingMode.HALF_UP).toDouble
    PaymentGatewaySettlementUtils.formatDoubleToString(resultVal, 4)
  }
  def calculateSourceAmount: (String, String) => Double = { (grossCredit: String, grossDebit: String) =>
    val convertedGrossCredit = PaymentGatewaySettlementUtils.convertStringToDoubleWithRounding(grossCredit, 4)
    val convertedGrossDebit  = PaymentGatewaySettlementUtils.convertStringToDoubleWithRounding(grossDebit, 4)
    convertedGrossCredit - convertedGrossDebit
  }

  def getFeeAmount: UserDefinedFunction = udf { (commission: String, schemeFees: String, interchange: String, markup: String) =>
    val convertedCommission   = PaymentGatewaySettlementUtils.convertStringToDoubleWithRounding(commission, 4)
    val convertedSchemeFees   = PaymentGatewaySettlementUtils.convertStringToDoubleWithRounding(schemeFees, 4)
    val convertedOInterchange = PaymentGatewaySettlementUtils.convertStringToDoubleWithRounding(interchange, 4)
    val convertedMarkup       = PaymentGatewaySettlementUtils.convertStringToDoubleWithRounding(markup, 4)
    val result                = convertedCommission + convertedSchemeFees + convertedOInterchange + convertedMarkup
    PaymentGatewaySettlementUtils.formatDoubleToString(result, 4)
  }

  def getExchangeRate: UserDefinedFunction = udf {
    (
        exchangeRate: String,
        grossCredit: String,
        grossDebit: String,
        netCredit: String,
        netDebit: String,
        commission: String,
        schemeFees: String,
        interchange: String,
        markup: String
    ) =>
      val sourceAmount          = calculateSourceAmount(grossCredit, grossDebit)
      val convertedExchangeRate = Try(exchangeRate.toDouble).getOrElse(0.0)
      val receivedAmount =
        calculateReceivedAmount(convertedExchangeRate, sourceAmount, netCredit, netDebit, commission, schemeFees, interchange, markup)

      if (sourceAmount != 0.0d && receivedAmount != null) {
        val exchangeRateCalculated = math.abs(receivedAmount / sourceAmount)
        val resultVal              = BigDecimal(exchangeRateCalculated).setScale(8, BigDecimal.RoundingMode.HALF_UP).toDouble
        PaymentGatewaySettlementUtils.formatDoubleToString(resultVal, 8)
      } else {
        exchangeRate
      }
  }

  def getReceivedAmount: UserDefinedFunction = udf {
    (
        exchangeRate: String,
        grossCredit: String,
        grossDebit: String,
        netCredit: String,
        netDebit: String,
        commission: String,
        schemeFees: String,
        interchange: String,
        markup: String
    ) =>
      val sourceAmount          = calculateSourceAmount(grossCredit, grossDebit)
      val convertedExchangeRate = Try(exchangeRate.toDouble).getOrElse(0.0)
      val result                = calculateReceivedAmount(convertedExchangeRate, sourceAmount, netCredit, netDebit, commission, schemeFees, interchange, markup)
      if (result == null) {
        null
      } else {
        PaymentGatewaySettlementUtils.formatDoubleToString(result, 4)
      }
  }

  def calculateReceivedAmount: (Double, Double, String, String, String, String, String, String) => Double = {
    (
        exchangeRate: Double,
        sourceAmount: Double,
        netCredit: String,
        netDebit: String,
        commission: String,
        schemeFees: String,
        interchange: String,
        markup: String
    ) =>
      if (sourceAmount != 0.0d) {
        val convertedNetCredit   = Try(netCredit.toDouble).getOrElse(0.0)
        val convertedNetDebit    = Try(netDebit.toDouble).getOrElse(0.0)
        val convertedCommission  = Try(commission.toDouble).getOrElse(0.0)
        val convertedSchemeFees  = Try(schemeFees.toDouble).getOrElse(0.0)
        val convertedInterchange = Try(interchange.toDouble).getOrElse(0.0)
        val convertedmarkup      = Try(markup.toDouble).getOrElse(0.0)

        val receivedAmount = convertedNetCredit - convertedNetDebit +
          convertedCommission + convertedSchemeFees +
          convertedInterchange + convertedmarkup
        BigDecimal(receivedAmount).setScale(4, BigDecimal.RoundingMode.HALF_UP).toDouble
      } else if (exchangeRate != 0.0d) {
        BigDecimal(0).setScale(4, BigDecimal.RoundingMode.HALF_UP).toDouble
      } else {
        null.asInstanceOf[Double]
      }

  }

  def getMid: UserDefinedFunction = udf { (merchantAccount: String, aquirer: String) =>
    val merchantAccountTrimed = merchantAccount.trim()
    if (!merchantAccountTrimed.contains("AgodaCOM")) {
      merchantAccount
    } else if (aquirer != null && aquirer.contains("_US_")) {
      merchantAccountTrimed + "_US"
    } else if (aquirer != null && aquirer.contains("_SG_")) {
      merchantAccountTrimed + "_SG"
    } else {
      merchantAccountTrimed
    }
  }

  def getEventTypeId(fileTypeId: String): UserDefinedFunction = udf { (grossCredit: String, grossDebit: String, recordType: String) =>
    if (!PaymentSettlementReconciliation.getReconciliationRule(fileTypeId, recordType.toUpperCase))
      null.asInstanceOf[String]
    else {
      val amount = calculateSourceAmount(grossCredit, grossDebit);
      if (amount >= 0) "2"
      else "4"
    }
  }
}
