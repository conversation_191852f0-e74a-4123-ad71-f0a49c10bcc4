package com.agoda.finance.functions.specific.ApSettlementRatioUpcLogic

import org.apache.spark.sql.functions.{coalesce, col, lit, to_timestamp}

object UpcSettlementConstants {

  final val bookingColumnsFromFinancialTransactions = Seq(
    col("bookingDf.booking_id"),
    col("bookingDf.supplier_currency"),
    col("bookingDf.payout_designated_item_id_amount").as("local_total_amount_inc_gst").cast("decimal(24, 8)"),
    col("bookingDf.payout_designated_item_id_amount_usd").as("usd_total_amount_inc_gst").cast("decimal(24, 8)"),
    col("bookingDf.product_type"),
    col("bookingDf.source_type"),
    col("bookingDf.is_test"),
    col("bookingDf.member_id"),
    col("bookingDf.sub_supplier_id"),
    col("bookingDf.supplier_id"),
    col("bookingDf.payment_model"),
    col("bookingDf.booking_month"),
    col("bookingDf.uuid"),
    col("bookingDf.transaction_date"),
    col("bookingDf.vat_rate"),
    col("bookingDf.datadate")
  )

  final val bookingColumnsFromLedgerBookingBalance = Seq(
    col("ledgerBookingBalanceDf.booking_id"),
    col("ledgerBookingBalanceDf.supplier_currency"),
    col("ledgerBookingBalanceDf.product_type"),
    col("ledgerBookingBalanceDf.booking_date"),
    col("ledgerBookingBalanceDf.booking_usd_total_amount_exc_gst"),
    col("ledgerBookingBalanceDf.booking_local_total_amount_exc_gst"),
    col("ledgerBookingBalanceDf.booking_usd_total_amount_inc_gst").as("usd_total_amount_inc_gst").cast("decimal(24, 8)"),
    col("ledgerBookingBalanceDf.booking_local_total_amount_inc_gst").as("local_total_amount_inc_gst").cast("decimal(24, 8)"),
    to_timestamp(col("ledgerBookingBalanceDf.accounting_datadate").cast("string"), "yyyyMMdd").as("transaction_date"),
    col("ledgerBookingBalanceDf.datadate")
  )

  final val settlementColumnsFromSettlements = Seq(
    col("settlementDf.type"),
    col("settlementDf.sub_type"),
    col("settlementDf.source"),
    col("settlementDf.booking_id"),
    col("settlementDf.sub_supplier_id"),
    col("settlementDf.supplier_id"),
    col("settlementDf.approval_id"),
    col("settlementDf.payment_method"),
    col("settlementDf.accounting_date"),
    col("settlementDf.transaction_date"),
    col("settlementDf.settlement_currency"),
    col("settlementDf.posting_currency"),
    col("settlementDf.settlement_amount"),
    col("settlementDf.posting_amount"),
    col("settlementDf.source_transaction_code"),
    col("settlementDf.vat_rate"),
    col("settlementDf.uuid"),
    col("settlementDf.destination_bank_country_name"),
    col("settlementDf.destination_bank_country_id"),
    col("settlementDf.settlement_amount_usd"),
    col("settlementDf.original_settlement_amount"),
    col("settlementDf.original_settlement_currency"),
    col("settlementDf.rate_currency"),
    col("settlementDf.rate_ex"),
    col("settlementDf.settlement_ex"),
    col("settlementDf.posting_ex"),
    col("settlementDf.reporting_date"),
    col("settlementDf.value_date"),
    col("settlementDf.agoda_bank_account_number"),
    col("settlementDf.cid"),
    col("settlementDf.is_violet"),
    col("settlementDf.is_allotment_reject"),
    col("settlementDf.is_advance_guarantee"),
    col("settlementDf.is_agency"),
    col("settlementDf.is_adjustment"),
    col("settlementDf.advance_payment_contract_id"),
    col("settlementDf.merchant_of_record_entity"),
    col("settlementDf.merchant_of_record_entity_type"),
    col("settlementDf.revenue_entity"),
    col("settlementDf.revenue_entity_type"),
    col("settlementDf.rate_contract_entity"),
    col("settlementDf.rate_contract_entity_type"),
    col("settlementDf.adjustment_reference"),
    col("settlementDf.adjustment_reason_id"),
    col("settlementDf.adjustment_reason"),
    col("settlementDf.adjustment_remark"),
    col("settlementDf.approval_reference"),
    col("settlementDf.payment_model"),
    col("settlementDf.batch_paypal_pay_amount"),
    col("settlementDf.batch_paypal_pay_amount_usd"),
    col("settlementDf.batch_paypal_fee_amount"),
    col("settlementDf.batch_paypal_fee_amount_usd"),
    col("settlementDf.booking_transaction_date"),
    col("settlementDf.is_cancelled"),
    col("settlementDf.transaction_currency"),
    col("settlementDf.transaction_amount"),
    col("settlementDf.interchange_amount"),
    col("settlementDf.transaction_id"),
    col("settlementDf.upc_product_name"),
    col("settlementDf.provider_card_classification_id"),
    col("settlementDf.acquirer_reference_number"),
    col("settlementDf.transaction_authorisation_number"),
    col("settlementDf.merchant_country"),
    col("settlementDf.merchant_name"),
    col("settlementDf.merchant_address"),
    col("settlementDf.merchant_city"),
    col("settlementDf.merchant_post_code"),
    col("settlementDf.datadate"),
    col("settlementDf.payment_method_id"),
    col("settlementDf.product_type"),
    col("settlementDf.source_name"),
    col("settlementDf.report_date")
  )

  final val adjustmentColumnsFromManualDetailAdjustment = Seq(
    col("reconManualBookingAdjustment.uuid"),
    col("reconManualBookingAdjustment.booking_id"),
    col("reconManualBookingAdjustment.approval_id"),
    col("reconManualBookingAdjustment.supplier_id"),
    col("reconManualBookingAdjustment.sub_supplier_id"),
    col("reconManualBookingAdjustment.supplier_currency"),
    col("reconManualBookingAdjustment.local_amount_inc_gst"),
    col("reconManualBookingAdjustment.local_commission_inc_gst"),
    col("reconManualBookingAdjustment.local_total_amount_inc_gst").as("local_total_amount_inc_gst").cast("decimal(24, 8)"),
    col("reconManualBookingAdjustment.local_amount_exc_gst"),
    col("reconManualBookingAdjustment.local_commission_exc_gst"),
    col("reconManualBookingAdjustment.local_total_amount_exc_gst"),
    col("reconManualBookingAdjustment.usd_amount_inc_gst"),
    col("reconManualBookingAdjustment.usd_commission_inc_gst"),
    col("reconManualBookingAdjustment.usd_total_amount_inc_gst").as("usd_total_amount_inc_gst").cast("decimal(24, 8)"),
    col("reconManualBookingAdjustment.usd_amount_exc_gst"),
    col("reconManualBookingAdjustment.usd_commission_exc_gst"),
    col("reconManualBookingAdjustment.usd_total_amount_exc_gst"),
    col("reconManualBookingAdjustment.accounting_date"),
    to_timestamp(col("reconManualBookingAdjustment.transaction_date").cast("string"), "yyyy-MM-dd").as("transaction_date"),
    col("reconManualBookingAdjustment.is_agency"),
    col("reconManualBookingAdjustment.is_advance_guarantee"),
    col("reconManualBookingAdjustment.merchant_of_record_entity"),
    col("reconManualBookingAdjustment.revenue_entity"),
    col("reconManualBookingAdjustment.rate_contract_entity"),
    col("reconManualBookingAdjustment.approval_reference"),
    col("reconManualBookingAdjustment.payment_method_id"),
    col("reconManualBookingAdjustment.payment_method"),
    col("reconManualBookingAdjustment.remark"),
    col("reconManualBookingAdjustment.reason"),
    col("reconManualBookingAdjustment.type"),
    col("reconManualBookingAdjustment.sub_type"),
    col("reconManualBookingAdjustment.source"),
    col("reconManualBookingAdjustment.status"),
    col("reconManualBookingAdjustment.reconciliation_instance"),
    col("reconManualBookingAdjustment.side"),
    col("reconManualBookingAdjustment.datadate"),
    col("reconManualBookingAdjustment.product_type"),
    col("reconManualBookingAdjustment.submission_id"),
    col("reconManualBookingAdjustment.submitted_by"),
    col("reconManualBookingAdjustment.submitted_email"),
    col("reconManualBookingAdjustment.adjustment_data_status"),
    col("reconManualBookingAdjustment.adjustment_datadate")
  )

  final val bookingColumnsForRatioCalculation = Seq(
    col("uuid"),
    col("booking_id").cast("bigint"),
    col("supplier_currency").cast("string"),
    coalesce(col("local_total_amount_inc_gst").cast("decimal(24, 8)"), lit(0).cast("decimal(24, 8)")).as("local_total_amount_inc_gst"),
    coalesce(col("usd_total_amount_inc_gst").cast("decimal(24, 8)"), lit(0).cast("decimal(24, 8)")).as("usd_total_amount_inc_gst"),
    col("transaction_date").cast("timestamp"),
    col("source").cast("string"),
    col("from").cast("string").as("source_table"),
    col("rundate").as("datadate")
  )

  final val settlementEnrichedColumns = Seq(
    col("settlementDf.payment_method"),
    col("settlementDf.source_name"),
    lit(null).cast("long").as("detail_id"),
    lit(null).cast("double").as("split_settlement_amount"),
    lit(null).cast("double").as("split_settlement_amount_usd"),
    lit(null).cast("string").as("parent_uuid"),
    lit(null).cast("boolean").as("is_last_fifo_overcharge"),
    lit(null).cast("int").as("reprocess"),
    col("settlementDf.booking_settlement_amount").cast("decimal(24,8)"),
    col("settlementUsdDf.calculated_usd_amount").cast("decimal(24,8)").as("booking_settlement_amount_usd"),
    col("settlementDf.booking_settlement_currency"),
    col("settlementDf.report_date"),
    col("settlementDf.type"),
    col("settlementDf.sub_type"),
    col("settlementDf.source"),
    col("settlementDf.source_transaction_code"),
    col("settlementDf.product_type"),
    col("settlementDf.booking_id"),
    col("settlementDf.payment_method_id"),
    col("settlementDf.sub_supplier_id"),
    col("settlementDf.supplier_id"),
    col("settlementDf.approval_id"),
    col("settlementDf.accounting_date"),
    col("settlementDf.transaction_date"),
    col("settlementDf.is_violet"),
    col("settlementDf.is_allotment_reject"),
    col("settlementDf.cid"),
    col("settlementDf.payment_model"),
    col("settlementDf.approval_reference"),
    col("settlementDf.is_agency"),
    col("settlementDf.is_adjustment"),
    col("settlementDf.is_advance_guarantee"),
    col("settlementDf.merchant_of_record_entity"),
    col("settlementDf.revenue_entity"),
    col("settlementDf.rate_contract_entity"),
    col("settlementDf.merchant_of_record_entity_type"),
    col("settlementDf.revenue_entity_type"),
    col("settlementDf.rate_contract_entity_type"),
    col("settlementDf.advance_payment_contract_id"),
    col("settlementDf.adjustment_reference"),
    col("settlementDf.adjustment_remark"),
    col("settlementDf.adjustment_reason_id"),
    col("settlementDf.adjustment_reason"),
    col("settlementDf.settlement_currency"),
    col("settlementDf.settlement_amount").cast("decimal(24,2)"),
    col("settlementDf.posting_currency"),
    col("settlementDf.posting_amount").cast("decimal(24,2)"),
    col("settlementDf.transaction_currency"),
    col("settlementDf.transaction_amount").cast("decimal(24,2)"),
    col("settlementDf.settlement_amount_usd").cast("decimal(24,2)"),
    col("settlementDf.posting_ex").cast("decimal(24,8)"),
    col("settlementDf.vat_rate").cast("decimal(24,2)"),
    col("settlementDf.destination_bank_country_name"),
    col("settlementDf.destination_bank_country_id"),
    col("settlementDf.agoda_bank_account_number"),
    col("settlementDf.booking_transaction_date"),
    col("settlementDf.batch_paypal_pay_amount").cast("decimal(24,2)"),
    col("settlementDf.batch_paypal_fee_amount").cast("decimal(24,2)"),
    col("settlementDf.batch_paypal_pay_amount_usd").cast("decimal(24,2)"),
    col("settlementDf.batch_paypal_fee_amount_usd").cast("decimal(24,2)"),
    col("settlementDf.value_date"),
    col("settlementDf.uuid")
  )

}
