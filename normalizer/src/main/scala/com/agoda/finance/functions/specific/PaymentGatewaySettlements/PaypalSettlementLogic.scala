package com.agoda.finance.functions.specific.PaymentGatewaySettlements

import com.agoda.finance.constants.{PaymentSettlementEventTypes, PaymentSettlementReconciliation}
import com.agoda.finance.functions.utils.PaymentGatewaySettlementUtils
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions.udf

import scala.util.Try

object PaypalSettlementLogic extends LazyLogging {

  def getSourceAmount: UserDefinedFunction = udf { (transactionDebitOrCredit: String, grossTransactionAmount: String) =>
    calculateAmount(transactionDebitOrCredit, grossTransactionAmount)
  }

  def calculateAmount: (String, String) => String = { (transactionDebitOrCredit: String, amount: String) =>
    val convertedAmount = PaymentGatewaySettlementUtils.convertStringToDoubleWithRounding(amount, 4)
    if (transactionDebitOrCredit.equalsIgnoreCase("DR")) {
      val resultAmount = convertedAmount * -0.01d
      val resultVal    = BigDecimal(resultAmount).setScale(4, BigDecimal.RoundingMode.HALF_UP).toDouble
      PaymentGatewaySettlementUtils.formatDoubleToString(resultVal, 4)
    } else {
      val resultAmount = convertedAmount * 0.01d
      val resultVal    = BigDecimal(resultAmount).setScale(4, BigDecimal.RoundingMode.HALF_UP).toDouble
      PaymentGatewaySettlementUtils.formatDoubleToString(resultVal, 4)
    }
  }

  def getPreBookingId(fileTypeId: String): UserDefinedFunction = udf { (invoice_id: String, transactionType: String) =>
    if (PaymentSettlementReconciliation.getReconciliationRule(fileTypeId, transactionType.toUpperCase)) {
      PaymentGatewaySettlementUtils.getPrebookingIdHelper(invoice_id)
    } else {
      null.asInstanceOf[Long]
    }
  }

  def getItineraryId(fileTypeId: String): UserDefinedFunction = udf { (invoice_id: String, transactionType: String) =>
    if (PaymentSettlementReconciliation.getReconciliationRule(fileTypeId, transactionType.toUpperCase)) {
      PaymentGatewaySettlementUtils.getItineraryIdHelper(invoice_id)
    } else {
      null.asInstanceOf[Long]
    }
  }

  def getReceivedCurrency: UserDefinedFunction = udf { (feeCurrency: String, grossTransactionCurrency: String) =>
    getCurrency(feeCurrency, grossTransactionCurrency)
  }

  def getFeeCurrency: UserDefinedFunction = udf { (feeCurrency: String, grossTransactionCurrency: String) =>
    getCurrency(feeCurrency, grossTransactionCurrency)
  }

  def getCurrency: (String, String) => String = { (feeCurrency: String, grossTransactionCurrency: String) =>
    if (feeCurrency == null || feeCurrency.trim.isEmpty) {
      if (grossTransactionCurrency == null || grossTransactionCurrency.trim.isEmpty) {
        null
      } else {
        grossTransactionCurrency.toUpperCase
      }
    } else {
      feeCurrency.toUpperCase
    }
  }

  def getFeeAmount: UserDefinedFunction = udf { (transactionDebitOrCredit: String, feeAmount: String) =>
    calculateAmount(transactionDebitOrCredit, feeAmount)
  }

  def getReceivedAmount: UserDefinedFunction = udf { (transactionDebitOrCredit: String, grossTransactionAmount: String) =>
    calculateAmount(transactionDebitOrCredit, grossTransactionAmount)
  }

  def getExchangeRate: UserDefinedFunction = udf { (grossTransactionAmount: String) =>
    val convertedGrossTransactionAmount = Try(grossTransactionAmount.toDouble).getOrElse(0.0)
    if (convertedGrossTransactionAmount == 0)
      PaymentGatewaySettlementUtils.formatDoubleToString(0.00000000d, 8)
    else
      PaymentGatewaySettlementUtils.formatDoubleToString(1.00000000d, 8)
  }

  def getEventTypeId(fileTypeId: String): UserDefinedFunction = udf { (transactionDebitOrCredit: String, transactionEventCode: String) =>
    val eventMap = PaymentSettlementEventTypes.events(fileTypeId).asInstanceOf[Map[String, String]]
    eventMap.get(transactionEventCode.toUpperCase) match {
      case Some(eventTypeId) => eventTypeId
      case None =>
        if (!PaymentSettlementReconciliation.getReconciliationRule(fileTypeId, transactionEventCode.toUpperCase))
          null
        else {
          if (!transactionDebitOrCredit.equalsIgnoreCase("DR"))
            "2"
          else "4"
        }
    }
  }
}
