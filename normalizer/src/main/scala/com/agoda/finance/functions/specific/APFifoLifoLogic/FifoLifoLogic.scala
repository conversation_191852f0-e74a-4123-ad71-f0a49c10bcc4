package com.agoda.finance.functions.specific.APFifoLifoLogic

import com.agoda.finance.functions.specific.APFifoLifoLogic.FifoLifoSchema._
import org.apache.spark.rdd.RDD
import org.apache.spark.sql.{Dataset, SQLContext}

import scala.collection.mutable
import scala.util.{Failure, Success, Try}

class FifoLifoLogic(shouldPrintLog: Boolean = false, enableCorrupt: Boolean = true) extends java.io.Serializable {
  private def cPrint(x: Any): Unit           = if (shouldPrintLog) print(x)
  private def cPrintln(x: Any): Unit         = if (shouldPrintLog) println(x)
  private def cPrintList(x: List[Any]): Unit = x.foreach(row => cPrint(s"      $row\n"))

  class InfiniteLoopException(
      groupingKey: String,
      bookingRowDetailId: Long,
      settlementRowSourceTransactionCode: String,
      paymentMethodId: Int,
      datadate: Int
  ) extends Exception {
    def toCorruptSchema: CorruptSchema = CorruptSchema(groupingKey, bookingRowDetailId, settlementRowSourceTransactionCode, paymentMethodId, datadate)
  }

  private def addUsedApprovalAmount(usedApprovalAmount: mutable.Map[Long, Double])(detailId: Long, amount: Double): Unit = {
    val updatedAmount = usedApprovalAmount.getOrElse(detailId, 0d) + amount

    cPrint(s"      Deduct approval amount for detail_id = $detailId \tfor $amount\n")
    cPrint(s"         from ${usedApprovalAmount.getOrElse(detailId, "  -")}\t to $updatedAmount\n")

    usedApprovalAmount.put(detailId, updatedAmount)
  }

  def roundNumber(num: Double): Double = BigDecimal(num).setScale(2, BigDecimal.RoundingMode.HALF_UP).toDouble

  // scalastyle:off cyclomatic.complexity
  def calculateFifoLifoSettlement(
      approval: Dataset[ApprovalSchema],
      settlement: Dataset[SettlementSchema],
      isTagRefundToLastBooking: Boolean = false
  )(implicit SQLContext: SQLContext): (RDD[ResultSchema], List[CorruptSchema]) = {
    val bookingsKeyedRDD = approval.rdd.map(row => (row.groupingKey, row))
    val paymentsKeyedRDD = settlement.rdd.map(row => (row.groupingKey, row))

    val groupedBookingsRDD = bookingsKeyedRDD.aggregateByKey(Seq.empty[ApprovalSchema])(_ :+ _, _ ++ _)
    val groupedPaymentsRDD = paymentsKeyedRDD.aggregateByKey(Seq.empty[SettlementSchema])(_ :+ _, _ ++ _)

    val pairedGroupsRDD = groupedPaymentsRDD.leftOuterJoin(groupedBookingsRDD)
    val corrupts        = List.newBuilder[CorruptSchema]

    val processedData = pairedGroupsRDD
      .map {
        case (groupingKey, (paymentRows, None))              => (groupingKey, (paymentRows.toList, Nil))
        case (groupingKey, (paymentRows, Some(bookingRows))) => (groupingKey, (paymentRows.toList, bookingRows.toList))
      }
      .flatMap { case (groupingKey, (paymentRows, bookingRows)) =>
        cPrintln(s"Processing groupingKey: $groupingKey, taggingRefundToLastLine = $isTagRefundToLastBooking")

        val sortedFifoBookingRows = bookingRows
          .filter(_.fifoOrder.isDefined)
          .sortBy(_.fifoOrder)

        val sortedLifoBookingRows = bookingRows
          .filter(_.lifoOrder.isDefined)
          .sortBy(_.lifoOrder)

        val sortedPaymentRows = paymentRows
          .sortBy(_.order)

        cPrintln("List of transactions")
        cPrintln(s"FIFO Booking row count = ${sortedFifoBookingRows.size}")
        cPrintList(sortedFifoBookingRows)
        cPrintln(s"LIFO Booking row count = ${sortedLifoBookingRows.size}")
        cPrintList(sortedLifoBookingRows)
        cPrintln(s"Payment row count = ${sortedPaymentRows.size}")
        cPrintList(paymentRows)

        val bookingAmountUsedMap = mutable.Map[Long, Double](bookingRows.map(row => (row.detailId, 0.00d)): _*)

        val results                                                                        = List.newBuilder[ResultSchema]
        var remainingFifoBookingAmount, remainingPaymentAmount, remainingLifoBookingAmount = 0.0
        var bookingRow: ApprovalSchema                                                     = null
        val bookingFifoIterator                                                            = sortedFifoBookingRows.iterator
        val bookingLifoIterator                                                            = sortedLifoBookingRows.iterator

        def updateBookingAmountUsed(bookingRow: ApprovalSchema, paymentAmount: Double): Unit = {
          val existingBookingAmount = bookingAmountUsedMap(bookingRow.detailId)
          val updatedAmount         = roundNumber(existingBookingAmount + paymentAmount)
          bookingAmountUsedMap.put(bookingRow.detailId, updatedAmount)

          cPrintln(
            s"""Updating bookingAmountUsedMap for bookingRow.detailId = ${bookingRow.detailId}
               |  existingBookingAmount = $existingBookingAmount, paymentAmount = $paymentAmount
               |  -> updatedAmount = $updatedAmount""".stripMargin
          )
        }

        Try {
          sortedPaymentRows.foreach { paymentRow =>
            remainingPaymentAmount = roundNumber(paymentRow.localAmount)

            // Do FIFO
            if (remainingPaymentAmount > 0) {
              do {
                if ((bookingRow == null || remainingFifoBookingAmount == 0) && bookingFifoIterator.hasNext) {
                  // Proceed to next booking line
                  bookingRow = bookingFifoIterator.next()
                  remainingFifoBookingAmount = roundNumber(bookingRow.localAmount) - bookingAmountUsedMap(bookingRow.detailId)
                }

                if (!bookingFifoIterator.hasNext && sortedFifoBookingRows.nonEmpty) {
                  // No booking line left to FIFO -- tag payment to last booking line
                  bookingRow = sortedFifoBookingRows.last

                  remainingFifoBookingAmount = roundNumber(bookingRow.localAmount) - bookingAmountUsedMap(bookingRow.detailId)
                  // Dummy Fifo Amount for Tagging Last Payment
                  if (remainingFifoBookingAmount == 0) remainingFifoBookingAmount = -1
                }

                if (remainingFifoBookingAmount != 0) {
                  val (paymentAmount: Double, isLastFifoOvercharge: Boolean) =
                    if (remainingPaymentAmount != 0 && !bookingFifoIterator.hasNext && sortedFifoBookingRows.nonEmpty) {
                      (remainingPaymentAmount, true)
                    } else {
                      (Math.min(remainingFifoBookingAmount, remainingPaymentAmount), false)
                    }

                  remainingFifoBookingAmount -= paymentAmount
                  remainingPaymentAmount -= paymentAmount

                  updateBookingAmountUsed(bookingRow, paymentAmount)

                  results += ResultSchema.fromApprovalAndSettlement(bookingRow, paymentRow)(
                    paymentAmount,
                    isLastFifoOvercharge = (remainingPaymentAmount > remainingFifoBookingAmount) && isLastFifoOvercharge
                  )
                }
              } while (
                // Do while still have payment amount and booking amount left
                remainingPaymentAmount != 0 && (remainingFifoBookingAmount != 0 || bookingFifoIterator.hasNext)
              )
            } else if (remainingPaymentAmount < 0) {
              // Do LIFO
              while (
                // While still have payment amount and booking amount left
                remainingPaymentAmount != 0 && (remainingLifoBookingAmount > 0 || bookingLifoIterator.hasNext)
              ) {
                if ((bookingRow == null || remainingLifoBookingAmount == 0) && bookingLifoIterator.hasNext) {
                  // Proceed to next booking line
                  bookingRow = bookingLifoIterator.next()
                  remainingLifoBookingAmount = roundNumber(bookingRow.localAmount) - bookingAmountUsedMap(bookingRow.detailId)
                } else if (remainingLifoBookingAmount < 0 && bookingLifoIterator.hasNext) {
                  cPrintln(s"Grouping ${paymentRow.groupingKey} hit infinite loop while processing $bookingRow")

                  if (enableCorrupt) {
                    throw new InfiniteLoopException(
                      paymentRow.groupingKey,
                      bookingRow.detailId,
                      paymentRow.sourceTransactionCode,
                      paymentRow.paymentMethodId,
                      paymentRow.datadate
                    )
                  } else {
                    while (true) { /* TODO add corrupted table to handle this case */ }
                  }
                }

                if (remainingLifoBookingAmount > 0) {
                  val paymentAmount = {
                    if (remainingPaymentAmount != 0 && !bookingLifoIterator.hasNext && sortedLifoBookingRows.nonEmpty && isTagRefundToLastBooking) {
                      // All booking amount is used, tagging payment amount to last booking line
                      Math.abs(remainingPaymentAmount)
                    } else {
                      Math.min(remainingLifoBookingAmount, Math.abs(remainingPaymentAmount))
                    }
                  }

                  remainingLifoBookingAmount -= paymentAmount
                  remainingPaymentAmount += paymentAmount

                  updateBookingAmountUsed(bookingRow, paymentAmount)

                  results += ResultSchema.fromApprovalAndSettlement(bookingRow, paymentRow)(-1 * paymentAmount)
                }
              }
            }
          }

          cPrintln(s"Finished processing groupingKey: $groupingKey")
          results.result
        } match {
          case Success(value) => value
          case Failure(exception: InfiniteLoopException) =>
            corrupts += exception.toCorruptSchema

            List.empty[ResultSchema]
          case Failure(exception) => throw exception
        }
      }

    (processedData, corrupts.result)
  }
}
