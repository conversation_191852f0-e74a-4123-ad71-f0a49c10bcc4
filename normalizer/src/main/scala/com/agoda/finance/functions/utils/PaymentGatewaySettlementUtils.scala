package com.agoda.finance.functions.utils

import com.agoda.finance.constants.{PaymentSettlementEventTypes, PaymentSettlementGatewayTransaction, PaymentSettlementReconciliation}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions.udf

import java.time.format.{DateTimeFormatter, DateTimeParseException}
import java.time.{LocalDate, LocalDateTime, ZoneOffset}
import java.util.Locale
import scala.util.Try

object PaymentGatewaySettlementUtils extends LazyLogging {

  def getTransactionType = udf { (merchantReferenceNumber: String) =>
    logger.info("merchantReferenceNumber : " + merchantReferenceNumber)
    if (isValidMerchantReference(merchantReferenceNumber)) {
      val parts = splitMerchantReferenceNumber(merchantReferenceNumber)
      if (parts.length == 3) Try(parts(1).toInt).getOrElse(1)
      else 1
    } else 1
  }

  private def isValidMerchantReference(merchantReferenceNumber: String): Boolean =
    merchantReferenceNumber != null && !merchantReferenceNumber
      .isEmpty() && (!isCharNumber(merchantReferenceNumber, "B") || !isCharNumber(merchantReferenceNumber, "P") ||
      splitMerchantReferenceNumber(merchantReferenceNumber).length == 3)

  private def isCharNumber: (String, String) => Boolean = (value: String, checkCharacter: String) => {
    Option(value).exists { v =>
      val upperCaseValue = v.toUpperCase
      val upperCaseCheck = checkCharacter.toUpperCase
      !(upperCaseValue.startsWith(upperCaseCheck) && Try(upperCaseValue.filter(_.isValidChar).toLong).isSuccess)
    }
  }

  def getPrebookingId: UserDefinedFunction = udf { (merchantReferenceNumber: String) =>
    getPrebookingIdHelper(merchantReferenceNumber)
  }

  def getPrebookingIdHelper(merchantReferenceNumber: String): Long = {
    val defaultPreBookingId = null.asInstanceOf[Long]
    if (merchantReferenceNumber == null)
      defaultPreBookingId
    else {
      val upperCaseValue = merchantReferenceNumber.toUpperCase
      if (upperCaseValue.startsWith("P")) {
        Try(upperCaseValue.filter(_.isValidChar).toLong).getOrElse(defaultPreBookingId)
      } else {
        val parts = splitMerchantReferenceNumber(merchantReferenceNumber)
        if (parts.length == 3) {
          if (parts(1) == "1") Try(parts(0).toLong).getOrElse(defaultPreBookingId) else defaultPreBookingId
        } else defaultPreBookingId
      }
    }
  }

  def getItineraryId: UserDefinedFunction = udf { (merchantReferenceNumber: String) =>
    getItineraryIdHelper(merchantReferenceNumber)
  }

  def getItineraryIdHelper(merchantReferenceNumber: String): Long = {
    val defaultItineraryId = null.asInstanceOf[Long]
    if (merchantReferenceNumber == null)
      defaultItineraryId
    else {
      val parts = splitMerchantReferenceNumber(merchantReferenceNumber)
      if (parts.length == 3) {
        if (parts(1) == "3") Try(parts(0).toLong).getOrElse(defaultItineraryId)
        else defaultItineraryId
      } else defaultItineraryId
    }
  }

  def getHotelItineraryId: UserDefinedFunction = udf { (merchantReferenceNumber: String) =>
    val defaultItineraryId = null.asInstanceOf[Long]
    val parts              = splitMerchantReferenceNumber(merchantReferenceNumber)
    if (parts.length == 3) {
      if (parts(1) == "10") Try(parts(0).toLong).getOrElse(defaultItineraryId)
      else defaultItineraryId
    } else defaultItineraryId
  }

  def isUsedForReconciliation(fileTypeId: String): UserDefinedFunction = udf { (eventType: String) =>
    PaymentSettlementReconciliation.getReconciliationRule(fileTypeId, eventType.toUpperCase)
  }

  def isGatewayTransaction(fileTypeId: String): UserDefinedFunction = udf { (eventType: String) =>
    PaymentSettlementGatewayTransaction.getIsGatewayTransactionRule(fileTypeId, eventType.toUpperCase)
  }

  def convertDateStringtoBigInt = udf { (dateString: String) =>
    if (dateString.length == 8) {
      val formatter = DateTimeFormatter.ofPattern("yyyyMMdd")
      val date      = LocalDate.parse(dateString, formatter)
      val dateTime  = date.atStartOfDay()
      dateTime.toInstant(ZoneOffset.UTC).toEpochMilli
    } else if (dateString.length == 25) {
      val dateformat = "yyyy/MM/dd"
      logger.info("dateTime in conversion for paypal : " + dateString)
      val onlyDateString = dateString.substring(0, 10)
      val formatter      = DateTimeFormatter.ofPattern(dateformat)
      val date           = LocalDate.parse(onlyDateString, formatter)
      val dateTime       = date.atStartOfDay()
      dateTime.toInstant(ZoneOffset.UTC).toEpochMilli
    } else {
      val paddedDateString = if (dateString.length == 16) dateString + ":00" else dateString
      val formatter        = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
      val localDateTime    = LocalDateTime.parse(paddedDateString, formatter)
      localDateTime.toInstant(ZoneOffset.UTC).toEpochMilli
    }
  }

  def getEventTypeId(fileTypeId: String): UserDefinedFunction = udf { (eventType: String) =>
    if (PaymentSettlementReconciliation.getReconciliationRule(fileTypeId, eventType)) {
      PaymentSettlementEventTypes.events(fileTypeId).asInstanceOf[Map[String, String]](eventType)
    } else {
      null
    }
  }

  def formatStringToUpperCase: UserDefinedFunction = udf { (value: String) =>
    if (value == null || value.trim.isEmpty) {
      null
    } else {
      value.toUpperCase(Locale.ROOT)
    }
  }

  def formatDoubleToStringWithDecimalPlace(decimal: Int): UserDefinedFunction = udf { (value: String) =>
    formatDoubleToString(Try(value.toDouble).getOrElse(0.0000d), decimal)
  }

  def formatDoubleToString(value: Double, decimals: Int): String =
    f"%%.${decimals}f".format(value)

  def convertStringToDoubleWithRounding(value: String, decimals: Int = 2): Double = {
    val valueD = Try(value.toDouble).getOrElse(0.0000d)
    BigDecimal(valueD).setScale(decimals, BigDecimal.RoundingMode.HALF_UP).toDouble
  }

  def formatStringToDoubleWithRounding(decimals: Int = 2): UserDefinedFunction = udf { (value: String) =>
    val doubleValue = convertStringToDoubleWithRounding(value, decimals)
    formatDoubleToString(doubleValue, decimals)
  }

  private def splitMerchantReferenceNumber(value: String): Array[String] =
    value.split('-')
}
