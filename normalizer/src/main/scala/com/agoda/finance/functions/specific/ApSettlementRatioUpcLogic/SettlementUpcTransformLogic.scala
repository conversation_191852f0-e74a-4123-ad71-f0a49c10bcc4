package com.agoda.finance.functions.specific.ApSettlementRatioUpcLogic

import com.agoda.finance.common.utils.{HadoopUtils, HadoopUtilsTrait}
import com.agoda.finance.functions.specific.ApSettlementRatioUpcLogic.UpcSettlementConstants._
import com.agoda.finance.tables.APSettlementCardUpcTables.{BookingUpc, SettlementsUpc, SettlementsUpcUsd}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{Column, DataFrame, SQLContext}

import java.time.LocalDate
import java.time.format.DateTimeFormatter

class SettlementUpcTransformLogic(
    processedUpcBookingTableNameDaily: String = BookingUpc.table.fullTableName,
    processedUpcSettlementsTableNameDaily: String = SettlementsUpc.table.fullTableName,
    processedUpcSettlementsTableNameDailyWithUsd: String = SettlementsUpcUsd.table.fullTableName,
    dataDate: Int,
    hadoopUtils: HadoopUtilsTrait = HadoopUtils
)(implicit
    SQLContext: SQLContext
) extends LazyLogging {

  import SQLContext.implicits._

  def prepareUpcSettlementData(
      financialTransactionsDf: DataFrame,
      ledgerBookingBalanceDf: DataFrame,
      settlementDf: DataFrame,
      unmatchedSettlementBookingsDf: DataFrame,
      reconManualBookingAdjustment: DataFrame,
      factBookingVehicle: DataFrame,
      factBookingAll: DataFrame,
      exchangeRate: DataFrame
  ): DataFrame = {

    logger.info(s"Starting UPC Settlement data for dataDate=$dataDate")

    val (allBookingForTodaySettlementUpc, allSettlementForTodayMatchedAndUnmatched, allSettlementAdjustmentForToday) =
      prepareUpcBookingSettlementUniverseForRatioCalculation(
        financialTransactionsDf,
        ledgerBookingBalanceDf,
        settlementDf,
        unmatchedSettlementBookingsDf,
        reconManualBookingAdjustment,
        factBookingVehicle,
        factBookingAll,
        exchangeRate
      )

    hadoopUtils.writeToExternalTable(
      allBookingForTodaySettlementUpc,
      processedUpcBookingTableNameDaily,
      "overwrite",
      Seq("datadate")
    )

    hadoopUtils.writeToExternalTable(
      allSettlementForTodayMatchedAndUnmatched,
      processedUpcSettlementsTableNameDaily,
      "overwrite",
      Seq("datadate", "case")
    )

    val settlementDataWithCalculatedUsd = RatioIntermediateForSettlementUpcUsd.calculateUSD(
      allBookingForTodaySettlementUpc,
      allSettlementForTodayMatchedAndUnmatched,
      allSettlementAdjustmentForToday,
      dataDate
    )

    hadoopUtils.writeToExternalTable(
      settlementDataWithCalculatedUsd,
      processedUpcSettlementsTableNameDailyWithUsd,
      "overwrite",
      Seq("datadate", "source", "reference")
    )

    /** *******************************************************************************
      * DataFrame: settlementsEnrichedDataFrameForToday
      * -------------------------------------------------------------------------------
      * Description:
      *   - Enriches today's settlement records by joining all matched and unmatched settlements
      *     (`allSettlementForTodayMatchedAndUnmatched`) with their corresponding calculated USD values
      *     from `settlementDataWithCalculatedUsd`.
      *   - The join is performed on the "uuid" column to ensure each settlement is matched with its
      *     calculated USD data.
      *   - The USD data is filtered to include only records for the current `dataDate`, where the
      *     source is "SETTLEMENT" and the reference is either "TODAY" or "OLD_UNMATCHED".
      *   - Only the columns specified in `settlementEnrichedColumns` are selected for the final output.
      *   - Adds/overrides the "datadate" column to ensure all records are tagged with the current processing date.
      * Purpose:
      *   - Produces a DataFrame of today's settlements_enriched dataframe that will be consumed by Recon
      * *******************************************************************************
      */

    val settlementsEnrichedDataFrameForToday =
      allSettlementForTodayMatchedAndUnmatched
        .alias("settlementDf")
        .join(
          settlementDataWithCalculatedUsd
            .filter(
              col("datadate") === dataDate &&
                col("source") === "SETTLEMENT" &&
                col("reference").isin(Seq("TODAY", "OLD_UNMATCHED"): _*)
            )
            .alias("settlementUsdDf"),
          Seq("uuid"),
          "inner"
        )
        .select(settlementEnrichedColumns: _*)
        .withColumn("datadate", lit(dataDate))

    settlementsEnrichedDataFrameForToday

  }

  def prepareUpcBookingSettlementUniverseForRatioCalculation(
      financialTransactionsDf: DataFrame,
      ledgerBookingBalanceDf: DataFrame,
      settlementDf: DataFrame,
      unmatchedSettlementBookingsDf: DataFrame,
      reconManualBookingAdjustment: DataFrame,
      factBookingVehicle: DataFrame,
      factBookingAll: DataFrame,
      exchangeRate: DataFrame
  ): (DataFrame, DataFrame, DataFrame) = {

    logger.info(s"Starting UPC Booking Settlement Universe preparation for dataDate=$dataDate")

    val formatter  = DateTimeFormatter.ofPattern("yyyyMMdd")
    val refDateInt = ledgerBookingBalanceDf.agg(max("datadate")).as[Int].collect()(0)

    /** *******************************************************************************
      * DataFrame: todaySettlementsUpc
      * -------------------------------------------------------------------------------
      * Description:
      *   - Retrieves all UPC settlements for the today's `dataDate` from `finance_multiproduct_ap.settlements`.
      *   - Filters for records where:
      *       • payment_method_id == 3   (indicating UPC settlements)
      *       • datadate equals the provided `dataDate`
      *   - Selects only the columns listed in `settlementColumnsFromFinancialTransactions`.
      * *******************************************************************************
      */

    val todaySettlementsUpc = settlementDf
      .alias("settlementDf")
      .filter(col("payment_method_id") === 3 && col("datadate") === dataDate)
      .select(settlementColumnsFromSettlements: _*)
      .withColumns(
        Map(
          "reference"    -> lit("TODAY"),
          "source_table" -> lit("SETTLEMENT_TABLE")
        )
      )

    /** *******************************************************************************
      * DataFrame: matchedUnmatchedSettlementBookingsDf
      * -------------------------------------------------------------------------------
      * Description:
      *   - Retrieves unmatched UPC settlement records from unmatchedSettlementsTable, the
      *     schema of this table is same as that of settlements. This table contains all the
      *     unmatched settlements data where the booking information is not available.
      *     The records of carry forwarded to the next in this table. So (today-1) will contain
      *     the snapshot of all the unmatched data .
      *   - Retrieve data for the previous day (which is snapshot)(`datadate = dataDate - 1`).
      *   - Joins with all relevant booking IDs from both `factBookingAll` and `factBookingVehicle`
      *     to ensure only valid bookings are included.
      *   - Selects only the columns specified in `settlementColumnsFromFinancialTransactions`.
      *   - Adds/overrides columns:
      *       • "reference": set to "OLD_UNMATCHED" to indicate these are unmatched settlements from a previous day.
      *       • "source_table": set to "SETTLEMENT_UNMATCHED_TABLE" to indicate the data source.
      *       • "source_transaction_code": if the code starts with "M_" (MISSING), replaces it with "R_"  (REVERSE)
      *         otherwise, keeps the original value. This standardizes transaction codes for downstream logic.
      *
      * Purpose:
      *   - Prepares a DataFrame of unmatched settlements, and identify if booking lines are available for
      *     reconciliation process
      * *******************************************************************************
      */

    val matchedUnmatchedSettlementBookingsDf = unmatchedSettlementBookingsDf
      .alias("settlementDf")
      .filter(col("datadate") === dataDate - 1)
      .join(
        factBookingAll
          .filter(col("datadate") < dataDate)
          .select(col("booking_id"))
          .unionByName(
            factBookingVehicle
              .filter(col("datadate") < dataDate)
              .select(col("booking_id"))
          )
          .alias("factbooking"),
        Seq("booking_id"),
        "inner"
      )
      .select(settlementColumnsFromSettlements: _*)
      .withColumns(
        Map(
          "reference"    -> lit("OLD_UNMATCHED"),
          "source_table" -> lit("SETTLEMENT_UNMATCHED_TABLE"),
          "source_transaction_code" -> when(
            col("source_transaction_code").startsWith("M_"),
            concat(lit("R_"), col("source_transaction_code").substr(3, 1000))
          ).otherwise(col("source_transaction_code"))
        )
      )

    logger.debug(s"todaySettlementsUpc schema: ${todaySettlementsUpc.printSchema()}")

    /** *******************************************************************************
      * DataFrame: todayBookingFromFinancialTransaction
      * -------------------------------------------------------------------------------
      * Description:
      *   - Retrieves today's booking records from `finance_multiproduct.financial_transactions` from
      *      `today-1` as financial_transactions is an hourly job.
      *   - Applies additional booking transaction filters (`bookingTransactionFilter`). Ref: (https://agoda.atlassian.net/wiki/spaces/FINN/pages/685056826/Pre-processing+Expected+Booking+Logic+for+AP+Processor)
      *      - For the addition booking transaction, below is simple snippet to check for detailed analysis:
      *        """
      *        val df = financialTransactionsDf.withColumn("product_type_check", col("product_type").isin("HOTEL", "VEHICLE"))
      *        .withColumn("source_type_check", col("source_type").isin("PAYMENT,BREAKDOWN", "\"PAYMENT,BREAKDOWN\"", "BREAKDOWN") || col("source_type").isNull)
      *        .withColumn("whitelabel_id_check", !col("whitelabel_id").isin(2, 3, 4))
      *        .withColumn("is_test_check", (col("is_test") =!= 1 || col("is_test").isNull))
      *        .withColumn("member_id_check", !col("member_id").isin(3, 945619, 946788, 624172) || col("member_id").isNull)
      *        .withColumn("sub_supplier_id_check", !col("sub_supplier_id").isin(567792, 1161599, 407854, 1200578, 746663, 648375, 958718, 984760, 1199052, 1275000, 287202, 4861890) || col("sub_supplier_id").isNull)
      *        .withColumn("supplier_id_check", !col("supplier_id").isin(27985, 27986, 27987, 27995, 28007, 28008, 28014, 28015, 28034, 28035, 27990, 29030, 28064) || col("supplier_id").isNull)
      *        .withColumn("payment_model_check", col("payment_model") =!= 3)
      *        .withColumn("supplier_id_payment_model_check", !(col("supplier_id") === 3038 && col("payment_model") === 2))
      *        """
      *   - Performs an inner join with `todaySettlementsUpc` on the "booking_id" column,
      *     ensuring only bookings with matching UPC settlements are included.
      *   - Selects only the columns listed in `bookingColumnsFromFinancialTransactions`.
      *   - Adds two new columns:
      *       • "source" set to "BOOKING"
      *       • "from"   set to "FT"
      * *******************************************************************************
      */

    logger.info(
      "Joining todaySettlementsUpc with financialTransactionsDf for getting booking from" +
        "financial_transaction in today-1 partition"
    )

    val todayBookingFromFinancialTransaction = financialTransactionsDf
      .alias("bookingDf")
      .filter(col("datadate").isin(Seq(dataDate - 1): _*))
      .filter(bookingTransactionFilter)
      .join(broadcast(todaySettlementsUpc), Seq("booking_id"), "inner")
      .select(bookingColumnsFromFinancialTransactions: _*)
      .withColumns(
        Map(
          "source"  -> lit("BOOKING"),
          "from"    -> lit("FT"),
          "rundate" -> lit(dataDate)
        )
      )

    /** *******************************************************************************
      * DataFrame: todayBookingForUnmatchedFromFinancialTransaction
      * -------------------------------------------------------------------------------
      * Description:
      *   - Retrieves today's booking records from `finance_multiproduct.financial_transactions` from
      *      `today-1` as financial_transactions. This is similar to todayBookingFromFinancialTransaction
      *      but it identifies the booking_data for the missing bookings in the past
      *
      * *******************************************************************************
      */

    val todayBookingForUnmatchedFromFinancialTransaction = financialTransactionsDf
      .alias("bookingDf")
      .filter(col("datadate").isin(Seq(dataDate - 1): _*))
      .filter(bookingTransactionFilter)
      .join(
        broadcast(
          unmatchedSettlementBookingsDf.filter(col("datadate") === dataDate - 1).alias("settlementDf")
        ),
        Seq("booking_id"),
        "inner"
      )
      .select(bookingColumnsFromFinancialTransactions: _*)
      .withColumns(
        Map(
          "source"  -> lit("BOOKING"),
          "from"    -> lit("FT"),
          "rundate" -> lit(dataDate)
        )
      )

    /** *******************************************************************************
      * DataFrame: historyBookingFromLedgerBookingBalance
      * -------------------------------------------------------------------------------
      * Description:
      *   - Retrieves historical booking data from ` finance_multiproduct_ap.ledger_booking_balance` for the
      *     booking universe over the last 24 months, excluding today's data.
      *   - Filters rows where "datadate" is between (refDateInt - 24 months) and refDateInt.
      *   - Performs an inner join with `todaySettlementsUpc` on "booking_id" to include
      *     only bookings with matching UPC settlements.
      *   - Selects columns specified in `bookingColumnsFromLedgerBookingBalance`.
      *   - Adds two new columns:
      *       • "source" set to "BOOKING"
      *       • "from"   set to "LEDGER_BOOKING"
      * *******************************************************************************
      */

    logger.info(
      "Joining todaySettlementsUpc with ledgerBookingBalanceDf for getting booking from" +
        "ledger_booking_balance considering last 2 years partitions history"
    )

    val historyBookingFromLedgerBookingBalance =
      ledgerBookingBalanceDf
        .alias("ledgerBookingBalanceDf")
        .filter(
          col("datadate").between(
            LocalDate
              .parse(refDateInt.toString, formatter)
              .minusMonths(24)
              .format(formatter)
              .toInt,
            refDateInt
          )
        )
        .join(broadcast(todaySettlementsUpc), Seq("booking_id"), "inner")
        .select(bookingColumnsFromLedgerBookingBalance: _*)
        .withColumns(
          Map(
            "UUID"    -> expr("uuid()"),
            "source"  -> lit("BOOKING"),
            "from"    -> lit("LEDGER_BOOKING"),
            "rundate" -> lit(dataDate.toInt)
          )
        )

    /** *******************************************************************************
      * DataFrame: bookingLedgerAdjustment
      * -------------------------------------------------------------------------------
      * Description:
      *   - Retrieves booking adjustment records from `finance_adjustment.ap_reconciliation_manual_detail_adjustment`
      *     where the "side" is "BOOKING".
      *   - Performs an inner join with `todaySettlementsUpc` on "booking_id" to include
      *     only adjustments related to bookings with matching UPC settlements.
      *   - Selects columns specified in `adjustmentColumnsFromManualDetailAdjustment`.
      *   - Adds two new columns:
      *       • "source" set to "BOOKING"
      *       • "from"   set to "BOOKING_ADJUSTMENTS"
      * *******************************************************************************
      */

    val bookingLedgerAdjustment = reconManualBookingAdjustment
      .alias("reconManualBookingAdjustment")
      .filter(col("side") === "BOOKING")
      .join(broadcast(todaySettlementsUpc), Seq("booking_id"), "inner")
      .select(adjustmentColumnsFromManualDetailAdjustment: _*)
      .withColumns(
        Map(
          "source"  -> lit("BOOKING"),
          "from"    -> lit("BOOKING_ADJUSTMENTS"),
          "rundate" -> lit(dataDate)
        )
      )

    val allBookingDataDataframe = Seq(
      todayBookingFromFinancialTransaction.select(bookingColumnsForRatioCalculation: _*),
      todayBookingForUnmatchedFromFinancialTransaction.select(bookingColumnsForRatioCalculation: _*),
      historyBookingFromLedgerBookingBalance.select(bookingColumnsForRatioCalculation: _*),
      bookingLedgerAdjustment.select(bookingColumnsForRatioCalculation: _*)
    )

    /** *******************************************************************************
      * DataFrame: bookingUniverseForTodaySettlementUpc
      * -------------------------------------------------------------------------------
      * Description:
      *   - Builds the complete booking universe by combining:
      *       • Today's booking transactions (`todayBookingFromFinancialTransaction`)
      *       • Historical booking records from the last 24 months (`historyBookingFromLedgerBookingBalance`)
      *       • Booking adjustment records (`bookingLedgerAdjustment`)
      *   - All DataFrames are combined using `unionByName` to ensure columns are aligned by name.
      *   - The result is a unified DataFrame representing the full state of all relevant bookings for today's UPC settlement bookings.
      *   - The schema for the booking data that will be used for ratio calculation:
      *       • root
      *              |-- booking_id: long (nullable = true)
      *              |-- supplier_currency: string (nullable = true)
      *              |-- local_total_amount_inc_gst: decimal(24,8) (nullable = false)
      *              |-- usd_total_amount_inc_gst: decimal(24,8) (nullable = false)
      *              |-- transaction_date: timestamp (nullable = true)
      *              |-- source: string (nullable = false)
      *              |-- source_table: string (nullable = false)
      *              |-- datadate: integer (nullable = false) // Run date when the job ran
      * *******************************************************************************
      */

    val bookingUniverseForTodaySettlementUpc = allBookingDataDataframe.reduce(_.unionByName(_))

    logger.info("The Booking Universe For Today has been created successfully")

    /** *******************************************************************************
      * DataFrame: factBookingData
      * -------------------------------------------------------------------------------
      * Description:
      *   - Builds a DataFrame to retrieve the booking currency for UPC settlements from the fact booking table as it contains
      *   1 row for 1 booking .
      *   - Steps:
      *       • Joins `bi_dw.fact_booking_all ` for HOTEL data (aliased as "fba") with `todaySettlementsUpc` on "booking_id"
      *         and selects "booking_id" and "original_local_currency" (cast to appropriate types).
      *       • Joins `bi_dw.fact_booking_vehicle_all` for VEHICLE (aliased as "fbav") with `todaySettlementsUpc` on "booking_id"
      *         and selects the same columns.
      *       • Combines both DataFrames using `unionByName` and removes duplicates with `distinct()`.
      *   - The result is a unified DataFrame containing unique booking IDs and their currencies
      *     for all UPC settlements of the day.
      * *******************************************************************************
      */

    val factBookingDataForTodayUpc = factBookingAll
      .filter(col("datadate") < dataDate)
      .alias("fba")
      .join(broadcast(todaySettlementsUpc.unionByName(matchedUnmatchedSettlementBookingsDf)), Seq("booking_id"), "inner")
      .select(col("fba.booking_id").cast("bigint"), col("fba.original_local_currency").cast("string").as("booking_local_currency"))

    // should I use original_payment_currency or original_supplier_currency
    val factBookingVehicleDataForTodayUpc = factBookingVehicle
      .filter(col("datadate") < dataDate)
      .alias("fbav")
      .join(broadcast(todaySettlementsUpc.unionByName(matchedUnmatchedSettlementBookingsDf)), Seq("booking_id"), "inner")
      .select(col("fbav.booking_id").cast("bigint"), col("fbav.original_payment_currency").cast("string").as("booking_local_currency"))

    val factBookingData = factBookingDataForTodayUpc.unionByName(factBookingVehicleDataForTodayUpc).distinct()

    /** *******************************************************************************
      * DataFrame: settlementLedgerAdjustment
      * -------------------------------------------------------------------------------
      * Description:
      *   - Retrieves settlement adjustment records from `finance_adjustment.ap_reconciliation_manual_detail_adjustment`
      *     where the "side" is "BOOKINGSETTLEMENT".
      *   - Performs an inner join with `todaySettlementsUpc` on "booking_id" to include
      *     only adjustments related to bookings with matching UPC settlements.
      *   - Selects columns specified in `adjustmentColumnsFromManualDetailAdjustment`.
      *   - Adds two new columns:
      *       • "source" set to "BOOKING"
      *       • "from"   set to "BOOKING_ADJUSTMENTS"
      * *******************************************************************************
      */

    val settlementLedgerAdjustment = reconManualBookingAdjustment
      .alias("reconManualBookingAdjustment")
      .filter(col("side") === "BOOKINGSETTLEMENT")
      .join(broadcast(todaySettlementsUpc), Seq("booking_id"), "inner")
      .select(adjustmentColumnsFromManualDetailAdjustment: _*)
      .withColumns(
        Map(
          "source"       -> lit("SETTLEMENT"),
          "source_table" -> lit("SETTLEMENT_ADJUSTMENTS"),
          "rundate"      -> lit(dataDate)
        )
      )

    /** *******************************************************************************
      * DataFrame: settlementForMatchedBookings
      * -------------------------------------------------------------------------------
      * Description:
      *   - Builds a DataFrame for settlements of matched bookings with enriched currency
      *     and exchange rate information.
      *   - Steps:
      *       • Joins `todaySettlementsUpc` (aliased as "settlementDf") with `factBookingData`
      *         to get the booking's original local currency.
      *       • Joins with `exchangeRate` (aliased as "ex4") to get the exchange rate for
      *         the booking's original local currency on the transaction date.
      *       • Joins with `exchangeRate` (aliased as "ex5") to get the exchange rate for
      *         the original settlement currency on the transaction date.
      *       • Selects all columns from `settlementColumnsFromFinancialTransactions` plus:
      *           - booking_settlement_currency: the booking's original local currency
      *           - booking_settlement_ex_rate: exchange rate for booking currency
      *           - settlement_settlement_ex_rate: exchange rate for settlement currency
      *           - booking_settlement_amount_usd: initialized to 0 (decimal)
      *           - booking_settlement_amount: calculated using exchange rates and amounts
      *       • Adds columns to indicate case ("MATCHED"), reference ("TODAY"),
      *         source ("SETTLEMENT"), and the current datadate.
      * *******************************************************************************
      */

    val settlementForMatchedBookings = todaySettlementsUpc
      .unionByName(matchedUnmatchedSettlementBookingsDf)
      .alias("settlementDf")
      .join(factBookingData.alias("factbooking"), Seq("booking_id"), "inner")
      .join(
        broadcast(exchangeRate.select(col("source_currency_code"), col("datadate"), col("real_exchange_rate")).distinct()).alias("ex4"),
        expr("""ex4.source_currency_code = factbooking.booking_local_currency AND
                    to_date(from_unixtime(unix_timestamp(ex4.datadate, 'yyyyMMdd'))) = to_date(settlementDf.transaction_date)"""),
        "left"
      )
      .join(
        broadcast(exchangeRate.select(col("source_currency_code"), col("datadate"), col("real_exchange_rate")).distinct()).alias("ex5"),
        expr("""ex5.source_currency_code = settlementDf.original_settlement_currency AND
                    to_date(from_unixtime(unix_timestamp(ex5.datadate, 'yyyyMMdd'))) = to_date(settlementDf.transaction_date)"""),
        "left"
      )
      .select(
        settlementColumnsFromSettlements :+
          col("reference").as("reference") :+
          col("source_table").as("source_table") :+
          col("factbooking.booking_local_currency").as("booking_settlement_currency") :+
          col("ex4.real_exchange_rate").as("booking_settlement_ex_rate") :+
          col("ex5.real_exchange_rate").as("settlement_settlement_ex_rate") :+
          lit(0).cast("decimal(28,8)").as("booking_settlement_amount_usd") :+
          (-(col("settlementDf.original_settlement_amount") * col("ex5.real_exchange_rate") / col("ex4.real_exchange_rate")))
            .as("booking_settlement_amount"): _*
      )
      .withColumns(
        Map(
          "case"     -> lit("MATCHED"),
          "source"   -> lit("SETTLEMENT"),
          "datadate" -> lit(dataDate)
        )
      )

    /** *******************************************************************************
      * DataFrame: settlementForUnmatchedBookings
      * -------------------------------------------------------------------------------
      * Description:
      *   - Builds a DataFrame for settlements that do not have a matching booking record
      *     in `factBookingData` (i.e., unmatched bookings).
      *   - Steps:
      *       • Performs a left anti join between `todaySettlementsUpc` (aliased as "settlementDf")
      *         and `factBookingData` to select only settlements without a corresponding booking.
      *       • Selects all columns from `settlementColumnsFromFinancialTransactions` plus:
      *           - booking_settlement_currency: set to null (string)
      *           - booking_settlement_ex_rate: set to null (double)
      *           - settlement_settlement_ex_rate: set to null (double)
      *           - booking_settlement_amount_usd: set to 0 (decimal)
      *           - booking_settlement_amount: set to 0 (decimal)
      *       • Adds columns to indicate case ("UNMATCHED"), reference ("FUTURE"),
      *         source ("SETTLEMENT"), and the current datadate.
      * *******************************************************************************
      */

    val settlementForUnmatchedBookings = todaySettlementsUpc
      .unionByName(
        unmatchedSettlementBookingsDf
          .filter(col("datadate") === dataDate - 1)
          .withColumns(
            Map(
              "reference"    -> lit("OLD_UNMATCHED"),
              "source_table" -> lit("SETTLEMENT_UNMATCHED_TABLE")
            )
          )
      )
      .alias("settlementDf")
      .join(broadcast(factBookingData).alias("factbooking"), Seq("booking_id"), "left_anti")
      .select(
        settlementColumnsFromSettlements :+
          col("reference").as("reference") :+
          col("source_table").as("source_table") :+
          lit(null).cast("string").as("booking_settlement_currency") :+
          lit(null).cast("double").as("booking_settlement_ex_rate") :+
          lit(null).cast("double").as("settlement_settlement_ex_rate") :+
          lit(0).cast("decimal(28,8)").as("booking_settlement_amount_usd") :+
          lit(0).cast("decimal(28,8)").as("booking_settlement_amount"): _*
      )
      .withColumns(
        Map(
          "case"     -> lit("UNMATCHED"),
          "source"   -> lit("SETTLEMENT"),
          "datadate" -> lit(dataDate),
          "source_transaction_code" -> when(col("settlementDf.source_transaction_code").startsWith("M_"), col("settlementDf.source_transaction_code"))
            .otherwise(concat(lit("M_"), col("settlementDf.source_transaction_code")))
        )
      )

    /** *******************************************************************************
      * DataFrame: historySettlementsUpc
      * -------------------------------------------------------------------------------
      * Description:
      *   - Retrieves historical UPC settlement records from `settlementDf` for the past
      *     two years, excluding today's data only for the MATCHED settlement to bookings.
      *   - Filters for settlements where "datadate" is between (two years ago) excluding today's data.
      *   - Joins with today's UPC settlements matched today (from `settlementForMatchedBookings`) on "booking_id" to include
      *     only those bookings that also exist in today's settlements.
      *   - Selects all columns from `settlementColumnsFromFinancialTransactions` plus:
      *       • booking_settlement_currency: booking's original local currency
      *       • booking_settlement_ex_rate: exchange rate for booking currency
      *       • settlement_settlement_ex_rate: exchange rate for settlement currency
      *       • booking_settlement_amount_usd: initialized to 0 (decimal)
      *       • booking_settlement_amount: calculated using exchange rates and amounts
      *   - Adds columns to indicate case ("MATCHED"), reference ("HISTORY"),
      *     source ("SETTLEMENT"), and the current datadate.
      * *******************************************************************************
      */

    val historySettlementsUpc = settlementDf
      .alias("settlementDf")
      .filter(
        col("datadate").between(
          LocalDate
            .now()
            .minusYears(2)
            .format(formatter)
            .toInt,
          LocalDate.now().format(formatter).toInt - 1
        )
      )
      .join(
        broadcast(settlementForMatchedBookings)
          .filter(col("payment_method_id") === 3 && col("datadate") === dataDate)
          .select("booking_id")
          .distinct(),
        Seq("booking_id"),
        "inner"
      )
      .join(broadcast(factBookingData).alias("factbooking"), Seq("booking_id"), "left")
      .join(
        broadcast(exchangeRate.select(col("source_currency_code"), col("datadate"), col("real_exchange_rate")).distinct()).alias("ex4"),
        expr("""ex4.source_currency_code = factbooking.booking_local_currency AND
                    to_date(from_unixtime(unix_timestamp(ex4.datadate, 'yyyyMMdd'))) = to_date(settlementDf.transaction_date)"""),
        "left"
      )
      .join(
        broadcast(exchangeRate.select(col("source_currency_code"), col("datadate"), col("real_exchange_rate")).distinct()).alias("ex5"),
        expr("""ex5.source_currency_code = settlementDf.original_settlement_currency AND
                    to_date(from_unixtime(unix_timestamp(ex5.datadate, 'yyyyMMdd'))) = to_date(settlementDf.transaction_date)"""),
        "left"
      )
      .select(
        settlementColumnsFromSettlements :+
          col("factbooking.booking_local_currency").as("booking_settlement_currency") :+
          col("ex4.real_exchange_rate").as("booking_settlement_ex_rate") :+
          col("ex5.real_exchange_rate").as("settlement_settlement_ex_rate") :+
          lit(0).cast("decimal(28,8)").as("booking_settlement_amount_usd") :+
          (-(col("settlementDf.original_settlement_amount") * col("ex5.real_exchange_rate") / col("ex4.real_exchange_rate")))
            .as("booking_settlement_amount"): _*
      )
      .withColumns(
        Map(
          "case"         -> lit("MATCHED"),
          "reference"    -> lit("HISTORY"),
          "source"       -> lit("SETTLEMENT"),
          "datadate"     -> lit(dataDate),
          "source_table" -> lit("SETTLEMENT_TABLE_HISTORY")
        )
      )

    /** *******************************************************************************
      * DataFrame: settlementsUpcForMatchedUnmatchedAndHistoryCombined
      * -------------------------------------------------------------------------------
      * Description:
      *   - Combines all UPC settlement records into a single DataFrame by uniting:
      *       • Settlements for matched bookings (`settlementForMatchedBookings`)
      *       • Historical settlements (`historySettlementsUpc`)
      *       • Settlements for unmatched bookings (`settlementForUnmatchedBookings`)
      *   - Uses `unionByName` to ensure columns are aligned by name across all DataFrames.
      *   - The result is a comprehensive DataFrame containing all relevant UPC settlement
      *     records for further processing or analysis.
      * *******************************************************************************
      */

    settlementDf.printSchema()
    settlementForUnmatchedBookings.printSchema()

    val settlementsUpcForMatchedUnmatchedAndHistoryCombined = settlementForMatchedBookings
      .unionByName(historySettlementsUpc)
      .unionByName(settlementForUnmatchedBookings)

    logger.info(s"Completed UPC Booking Settlement Universe preparation for dataDate=$dataDate")

    (bookingUniverseForTodaySettlementUpc, settlementsUpcForMatchedUnmatchedAndHistoryCombined, settlementLedgerAdjustment)

  }

  private def bookingTransactionFilter: Column =
    col("product_type").isin("HOTEL", "VEHICLE") &&
      (col("source_type").isin("PAYMENT,BREAKDOWN", "\"PAYMENT,BREAKDOWN\"", "BREAKDOWN") || col("source_type").isNull) &&
      !col("whitelabel_id").isin(2, 3, 4) &&
      (col("is_test") =!= 1 || col("is_test").isNull) &&
      (!col("member_id").isin(3, 945619, 946788, 624172) || col("member_id").isNull) &&
      (!col("sub_supplier_id").isin(567792, 1161599, 407854, 1200578, 746663, 648375, 958718, 984760, 1199052, 1275000, 287202, 4861890) || col(
        "sub_supplier_id"
      ).isNull) &&
      (!col("supplier_id").isin(27985, 27986, 27987, 27995, 28007, 28008, 28014, 28015, 28034, 28035, 27990, 29030, 28064) || col(
        "supplier_id"
      ).isNull) &&
      col("payment_model") =!= 3 &&
      !(col("supplier_id") === 3038 && col("payment_model") === 2) &&
      (col("product_type") =!= "VEHICLE" ||
        (coalesce(col("supplier_absorption"), lit(0.00)) === 0.00 &&
          col("supplier_id").isin(31001, 31002) &&
          col("payment_model") === 4))

  private def validate(bookingDf: DataFrame, settlementDf: DataFrame): Unit = {

    val exists = bookingDf.select("datadate").distinct().filter($"date" === dataDate).count() > 0

    if (!exists) {
      logger.error(s" [ERROR] Partition for date=$dataDate does not exist TABLE =>  finance_multiproduct.financial_transaction !!!!!")
      throw new Exception(s" [ERROR] Partition for date=$dataDate does not exist TABLE =>  finance_multiproduct.financial_transaction !!!!!")
    } else {
      logger.info(s"Partition for date=$dataDate exists for TABLE =>  finance_multiproduct.financial_transaction")
    }

  }

}
