package com.agoda.finance.functions.specific.PaymentGatewaySettlements

import com.agoda.finance.functions.utils.PaymentGatewaySettlementUtils
import com.agoda.finance.functions.utils.PaymentGatewaySettlementUtils.logger
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions.udf

import java.time.format.DateTimeFormatter
import java.time.{LocalDate, LocalDateTime, ZoneOffset}
import java.util.Locale
import scala.util.Try

object AfterPaySettlementLogic extends LazyLogging {
  def getAmount: UserDefinedFunction = udf { (amount: String) =>
    if (amount == null) {
      PaymentGatewaySettlementUtils.formatDoubleToString(0.0, 4)
    } else {
      var decimalAmount = amount.replace("$", "").replace("£", "")
      val amountNumeric = Try(decimalAmount.toDouble).getOrElse(0.0)

      // Formula: transaction_amount_value / 100
      val result = amountNumeric / 100.0
      PaymentGatewaySettlementUtils.formatDoubleToString(result, 4)
    }
  }

  def getGatewayUniqueId: UserDefinedFunction = udf { (eventType: String, orderId: String, refundId: String) =>
    if (eventType == "Refund")
      s"$orderId-$refundId"
    else
      orderId
  }

  def convertDateStringtoBigInt(dateFormat: String) = udf { (dateString: String) =>
    if (dateString == null || dateString.trim.isEmpty) {
      logger.error(s"Date string cannot be null or empty")
      throw new IllegalArgumentException("Date string cannot be null or empty")
    }

    // Special handling for "dd MMM yy" format
    if (dateString.matches("\\d{2}\\s[A-Za-z]{3}\\s\\d{2}")) {
      val formatter = DateTimeFormatter.ofPattern("dd MMM yy", Locale.ENGLISH)
      val date      = LocalDate.parse(dateString, formatter)
      date.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli
    } else {
      // Check if the dateFormat is a date-only pattern (no time components)
      val isDateOnlyFormat = !dateFormat.contains("H") && !dateFormat.contains("h") &&
        !dateFormat.contains("m") && !dateFormat.contains("s") &&
        !dateFormat.contains("t") && !dateFormat.contains("T")

      if (isDateOnlyFormat) {
        // Parse as LocalDate and convert to LocalDateTime at start of day
        val formatter = DateTimeFormatter.ofPattern(dateFormat, Locale.ENGLISH)
        val date      = LocalDate.parse(dateString, formatter)
        date.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli
      } else {
        // Handle datetime formats
        val paddedDateString = if (dateString.length == 16 && !dateString.endsWith(":00")) {
          dateString + ":00"
        } else dateString

        val formatter     = DateTimeFormatter.ofPattern(dateFormat, Locale.ENGLISH)
        val localDateTime = LocalDateTime.parse(paddedDateString, formatter)
        localDateTime.toInstant(ZoneOffset.UTC).toEpochMilli
      }
    }
  }
}
