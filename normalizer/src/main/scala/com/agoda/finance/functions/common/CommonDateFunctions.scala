package com.agoda.finance.functions.common

import com.typesafe.scalalogging.LazyLogging
import com.agoda.finance.common.utils.DateFormats
import org.apache.spark.sql.Column
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.IntegerType
import org.joda.time.format.DateTimeFormat

import java.sql.Timestamp
import java.text.SimpleDateFormat
import java.time._
import java.time.format.DateTimeFormatter
import java.util.{Calendar, Date}
import java.util.concurrent.TimeUnit
import org.joda.time.DateTime

object CommonDateFunctions extends LazyLogging {

  def getFirstDateOfPreviousMonth: UserDefinedFunction = udf { datadate: String =>
    YearMonth
      .parse(datadate.substring(0, 4) + "-" + datadate.substring(4, 6))
      .minusMonths(1)
      .atDay(1)
      .toString

  }
  def getLastDateOfPreviousMonth: UserDefinedFunction = udf { datadate: String =>
    YearMonth
      .parse(datadate.substring(0, 4) + "-" + datadate.substring(4, 6))
      .minusMonths(1)
      .atEndOfMonth()
      .toString

  }

  def convertStringToDate(format_source: String, format_expect: String): UserDefinedFunction = udf { value_date: String =>
    val formatter: SimpleDateFormat = new SimpleDateFormat(format_source)
    val date: Date                  = formatter.parse(value_date)
    val df                          = new SimpleDateFormat(format_expect)
    df.format(date)
  }

  def offsetTimestamp(hour: Long, minute: Long): UserDefinedFunction = udf { timestamp: Timestamp =>
    val offsetTimeInMillis = TimeUnit.HOURS.toMillis(hour) + TimeUnit.MINUTES.toMillis(minute)
    new Timestamp(timestamp.getTime + offsetTimeInMillis)
  }

  def offsetDatadate(offset: Int): UserDefinedFunction = udf { datadate: Int =>
    val formatter: SimpleDateFormat = new SimpleDateFormat("yyyyMMdd")
    val date: Date                  = formatter.parse(datadate.toString)
    val calendar                    = Calendar.getInstance()

    calendar.setTime(date)
    calendar.add(Calendar.DATE, offset)

    formatter.format(calendar.getTime)
  }

  def offsetDatamonth(datamonth: Column, offset: Int): Column =
    date_format(add_months(to_date(datamonth, "yyyyMM"), offset), "yyyyMM")

  def convertDateStringToTimestamp(format_source: String): UserDefinedFunction = udf { value_date: String =>
    val format = new SimpleDateFormat(format_source)
    val date   = format.parse(value_date)
    new Timestamp(date.getTime)
  }

  // Checks whether a date is included in the time range between the first day of start month and the last day of end month.
  // Start and end are relative to the current datadate
  def isDateInMonthRange(datadate: Column, start: Int, end: Int, format: String): Column = {
    val currentDate         = current_date()
    val firstDayOfDateRange = trunc(add_months(currentDate, start), "MM") // 1st day of the start month
    val lastDayOfDateRange  = last_day(add_months(currentDate, end))      // last day of the ending month

    val formattedDate = date_format(to_date(datadate, format), "yyyy-MM-dd")
    formattedDate.between(firstDayOfDateRange, lastDayOfDateRange)
  }

  def offsetDateToDataMonth(datadate: Column, offset: Int, format: String): Column = {
    val formattedDate = date_format(to_date(datadate, format), "yyyy-MM-dd")
    date_format(add_months(formattedDate, offset), "yyyyMM").cast(IntegerType)
  }

  def getProcessMonth(processDate: Column): Column =
    date_format(processDate, "yyyyMM")

  def combineDateTime(date: Column, time: Column): Column = {
    val dateStr = date_format(date, "yyyy-MM-dd")
    val timeStr = date_format(to_timestamp(time, "h:m:s a"), "HH:mm:ss")
    to_timestamp(concat_ws(" ", dateStr, timeStr), "yyyy-MM-dd HH:mm:ss")
  }

  def combineDateTimeWithOffset(hour: Long, minute: Long, timeFormat: String): UserDefinedFunction =
    udf { (dateVal: java.sql.Date, timeVal: String) =>
      val dateStr            = new SimpleDateFormat("yyyy-MM-dd").format(dateVal)
      val timeStr            = new SimpleDateFormat("HH:mm:ss").format(new SimpleDateFormat(timeFormat).parse(timeVal))
      val combinedDateTime   = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(s"$dateStr $timeStr")
      val offsetTimeInMillis = TimeUnit.HOURS.toMillis(hour) + TimeUnit.MINUTES.toMillis(minute)
      new Timestamp(combinedDateTime.getTime + offsetTimeInMillis)
    }

  def addMonthsAndFormatDate(date: Column, monthsToAdd: Int, dateFormat: String): Column =
    date_format(add_months(date, monthsToAdd), dateFormat)

  def offsetTimestampConvertToDate(hour: Long, minute: Long, format: String): UserDefinedFunction = udf { timestamp: Timestamp =>
    val offsetTimeInMillis = TimeUnit.HOURS.toMillis(hour) + TimeUnit.MINUTES.toMillis(minute)
    val newTimestamp       = new Timestamp(timestamp.getTime + offsetTimeInMillis)
    val dateFormat         = new SimpleDateFormat(format)
    dateFormat.format(newTimestamp)
  }
}
