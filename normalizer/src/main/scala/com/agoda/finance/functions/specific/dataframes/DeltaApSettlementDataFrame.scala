package com.agoda.finance.functions.specific.dataframes

import com.agoda.finance.constants.APReconConstants.APReconPaymentMethod.{ECARD, UPC, UPCONEPASS}
import com.agoda.finance.constants.DeltaApSettlementConstants._
import org.apache.spark.sql.expressions.{Window, WindowSpec}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{IntegerType, StringType}
import org.apache.spark.sql.{Column, DataFrame}

import scala.language.implicitConversions

class DeltaApSettlementDataFrame(df: DataFrame) {

  /** Extracts and decomposes the `source_transaction_code` into distinct columns based on a predefined pattern.
    *
    * Utilizes regular expression to capture groups representing transaction_id, reprocess_number, tracking_number, and transaction_description.
    * Returns a DataFrame with these new columns and drops the original `source_transaction_code` column.
    *
    * Targeting regex pattern to target: {transaction_id}_{ap_recon_internal_reprocess_number}_{tracking_number}_{transaction_description}.
    *
    * Example: 123456_R1_7891011_DD or 123456_7891011_DD
    */
  def extractSourceTransactionCode(): DataFrame = {
    val pattern: String               = "^(\\d+)(?:(?:_\\d+)|(?:_R(\\d+))?)?_(\\d+)_(\\w+)$"
    def getMatchGroup(i: Int): Column = regexp_extract(col(sourceTransactionCodeColumnName), pattern, i)

    df.withColumns(
      Map(
        transactionIdColumnName          -> getMatchGroup(1),
        reprocessNumberColumnName        -> coalesce(getMatchGroup(2).cast(IntegerType), lit(0)),
        trackingNumberColumnName         -> getMatchGroup(3),
        transactionDescriptionColumnName -> getMatchGroup(4)
      )
    ).drop(sourceTransactionCodeColumnName)
  }

  def createGroupingKey(): DataFrame = {
    val ecardCondition      = col("payment_method_id") === ECARD.id
    val upcOnEpassCondition = col("payment_method_id") === UPCONEPASS.id
    val upcCondition        = col("payment_method_id") === UPC.id

    val ecardKey      = concat_ws("_", groupByColumnNamesEcard.map(col): _*)
    val upcOnEpassKey = concat_ws("_", groupByColumnNamesUpcOnEpass.map(col): _*)
    val upcKey        = concat_ws("_", groupByColumnNamesUpc.map(col): _*)

    df.withColumn(
      groupingKeyColumnName,
      when(ecardCondition, ecardKey)
        .when(upcOnEpassCondition, upcOnEpassKey)
        .when(upcCondition, upcKey)
        .otherwise(lit(null).cast(StringType))
    ).filter(col(groupingKeyColumnName).isNotNull) // Filter not supported payment method
  }

  def sumAndPickOnlyLatestByGroupingKey(): DataFrame = {
    val windowByGroupingKey: WindowSpec = Window.partitionBy(col(groupingKeyColumnName))
    val orderingLatestRow: Seq[Column]  = Seq(col(transactionDateColumnName).desc, col(reprocessNumberColumnName).desc)

    val dfWithOnlyLatestRecordForEachGroup: DataFrame = df
      .withColumn("row_num", row_number().over(windowByGroupingKey.orderBy(orderingLatestRow: _*)))
      .filter(col("row_num") === 1)
      .drop(
        Seq("row_num") ++
          allAmountColumnNames: _* // Drop columns which will get join from sumDfByGroupingKey
      )

    val allAggAmounts: Seq[Column] = allAmountColumnNames.map(colName => sum(col(colName)).alias("sum_" + colName))
    val sumDfByGroupingKey: DataFrame = df
      .groupBy(groupingKeyColumnName)
      .agg(allAggAmounts.head, allAggAmounts.tail: _*)

    dfWithOnlyLatestRecordForEachGroup.join(sumDfByGroupingKey, Seq(groupingKeyColumnName), "inner")
  }

  def populateFromBothSide(oldSettlement: DataFrame, newSettlement: DataFrame): DataFrame =
    df.select(
      (Seq(
        coalesce(newSettlement(groupingKeyColumnName), oldSettlement(groupingKeyColumnName)).as(groupingKeyColumnName),
        coalesce(oldSettlement(reprocessNumberColumnName), newSettlement(reprocessNumberColumnName))
          .as(reprocessNumberColumnName) // Only old settlement should have reprocess_number
      ) ++
        allAmountColumnNames.flatMap { columnName =>
          val sumColumnName: String = s"sum_$columnName"
          Seq(
            oldSettlement(sumColumnName).as(s"old_$columnName"),
            newSettlement(sumColumnName).as(s"new_$columnName")
          )
        } ++
        oldSettlement.columns
          .filterNot((Seq(groupingKeyColumnName, reprocessNumberColumnName) ++ allAmountColumnNames).contains(_))
          .map(columnName =>
            (if (newSettlement.columns.contains(columnName)) {
               coalesce(newSettlement(columnName), oldSettlement(columnName))
             } else {
               oldSettlement(columnName)
             }) as columnName
          )): _* // Prefer new settlement column if present
    )

  def calculateAllDeltaAmount(): DataFrame = {

    /** Delta = New - Old */
    def calculateDelta(targetColumnName: String): Column = {
      val oldDeltaColumn: Column = col(s"old_$targetColumnName")
      val newDeltaColumn: Column = col(s"new_$targetColumnName")
      coalesce(newDeltaColumn, lit(0)) - coalesce(oldDeltaColumn, lit(0))
    }

    /** The delta amount on TransactionAmount(CardholderCurrency) -> "original_settlement_amount" in settlements table is 0
      * AND TransactionAmount(SettlementAmount) -> "transaction_amount" in settlements table is changed ONLY
      */
    def overrideWithReversalApproach(df: DataFrame): DataFrame = {
      val reversalFlagName: String = "useReversalApproach"
      val amountColumnsToDrop: Seq[String] = allAmountColumnNames.flatMap { columnName =>
        Seq(s"old_$columnName", s"new_$columnName")
      }

      val taggedReversalDf: DataFrame = {
        val reversalCriteria: Column = col(originalSettlementAmountColumnName) === 0 && col(transactionAmountColumnName) =!= 0
        df.withColumn(
          reversalFlagName,
          when(reversalCriteria, lit(true)).otherwise(lit(false))
        )
      } // Tagged for apply reversal approach

      val excludeReversalCaseDf: DataFrame = taggedReversalDf
        .filter(not(col(reversalFlagName))) // Exclude reversal case
        .drop(Seq(reversalFlagName) ++ amountColumnsToDrop: _*)

      val reversalCaseResultDf: DataFrame = {
        val onlyReversalCaseDf: DataFrame = taggedReversalDf
          .filter(col(reversalFlagName)) // Only reversal case

        onlyReversalCaseDf.isEmpty match {
          case false => // Reversal case
            val reversalLineDf: DataFrame = onlyReversalCaseDf.withColumns(
              allAmountColumnNames.map(columnName => (columnName, -col(s"old_$columnName"))).toMap // Negate the old amount and use for reversal line
            )
            val correctedLineDf: DataFrame = onlyReversalCaseDf.withColumns(
              allAmountColumnNames.map(columnName => (columnName, col(s"new_$columnName"))).toMap // Use the new amount for reversal line
            )
            reversalLineDf.union(correctedLineDf) // Combine reversal line and corrected line
          case true => onlyReversalCaseDf // No reversal case, Skip reversal case
        }
      }.drop(Seq(reversalFlagName) ++ amountColumnsToDrop: _*)

      excludeReversalCaseDf.union(reversalCaseResultDf) // Add reversal case result back
    }

    val filterOutNoDiffCriteria: Column = not(col(originalSettlementAmountColumnName) === 0.0 && col(transactionAmountColumnName) === 0.0)
    val appliedDeltaDf: DataFrame = df
      .withColumns(
        allAmountColumnNames.map(columnName => (columnName, calculateDelta(columnName))).toMap
      )                                // Delta approach
      .filter(filterOutNoDiffCriteria) // Filter out no diff

    overrideWithReversalApproach(appliedDeltaDf) // Reversal approach
  }

  def stampNewSourceTransactionCode(): DataFrame = {
    val windowBySourceTransactionCode: WindowSpec =
      Window.partitionBy(col(transactionIdColumnName), col(trackingNumberColumnName), col(transactionDescriptionColumnName))
    val orderingBySettlementAmount: Seq[Column] = Seq(
      col(settlementAmountColumnName).asc
    )
    /* Ascending order to get the lowest settlement_amount
       (Case sub_supplier_id got change, We'll indicate the revert of the wrong sub_supplier_id to have the first reprocess_number
       then the correct sub_supplier_id will have the following reprocess_number. */

    df.withColumn(
      "max_reprocess_num",
      max(col(reprocessNumberColumnName)).over(windowBySourceTransactionCode)
    ) // Get the max reprocess_number for each source_transaction_code
      .withColumn("row_num", row_number().over(windowBySourceTransactionCode.orderBy(orderingBySettlementAmount: _*)))
      .withColumn(
        sourceTransactionCodeColumnName,
        concat_ws(
          "_",
          col(transactionIdColumnName),
          concat(lit("R"), col("max_reprocess_num") + col("row_num")),
          col(trackingNumberColumnName),
          col(transactionDescriptionColumnName)
        )
      )
      .drop(
        Seq(
          "max_reprocess_num",
          "row_num",
          transactionIdColumnName,
          reprocessNumberColumnName,
          trackingNumberColumnName,
          transactionDescriptionColumnName
        ): _*
      )
  }

  def populateStaticColumn(): DataFrame =
    df.withColumn("source_name", lit("tutuka_reprocess"))

  def stampNewUUID(): DataFrame =
    df.withColumn("uuid", expr("uuid()")) // Prevent duplicate UUID for reversal case

  def dropUtilsColumns(): DataFrame =
    df.drop(groupingKeyColumnName)
}

object DeltaApSettlementDataFrame {
  implicit def dFWithExtraOperations(
      df: DataFrame
  ): DeltaApSettlementDataFrame =
    new DeltaApSettlementDataFrame(df)
}
