package com.agoda.finance.functions

import com.agoda.finance.functions.specific.PaymentGatewaySettlements.{
  AdyenSettlementLogic,
  AfterPaySettlementLogic,
  AlipayOspSettlementLogic,
  JuspaySettlementLogic,
  NttSettlementLogic,
  PaypalSettlementLogic,
  PrimeroSettlementLogic
}
import com.agoda.finance.functions.utils.PaymentGatewaySettlementUtils
import org.apache.spark.sql.Column
import org.apache.spark.sql.functions._

object PaymentGatewaySettlementsFunctionMapper {

  val FIRST   = 0
  val SECOND  = 1
  val THIRD   = 2
  val FOURTH  = 3
  val FIFTH   = 4
  val SIXTH   = 5
  val SEVENTH = 6
  val EIGHT   = 7
  val NINETH  = 8

  /** Generic payment settlement functions that can be used across different payment gateways */
  def genericPaymentSettlementFunction(functionName: Option[String], constants: Vector[String], columns: Vector[Column]): Option[Column] =
    functionName match {
      case Some("get_transaction_type") =>
        Some(PaymentGatewaySettlementUtils.getTransactionType(columns(FIRST)))
      case Some("get_prebooking_id") =>
        Some(PaymentGatewaySettlementUtils.getPrebookingId(columns(FIRST)))
      case Some("get_itinerary_id") =>
        Some(PaymentGatewaySettlementUtils.getItineraryId(columns(FIRST)))
      case Some("get_hotel_itinerary_id") =>
        Some(PaymentGatewaySettlementUtils.getHotelItineraryId(columns(FIRST)))
      case Some("convert_datestring_to_bigint") =>
        Some(
          PaymentGatewaySettlementUtils.convertDateStringtoBigInt(columns(FIRST))
        )
      case Some("get_event_type_id") =>
        Some(PaymentGatewaySettlementUtils.getEventTypeId(constants(FIRST))(columns(FIRST)))
      case Some("format_string_to_uppercase") =>
        Some(PaymentGatewaySettlementUtils.formatStringToUpperCase(columns(FIRST)))
      case Some("convert_double_to_string") =>
        Some(PaymentGatewaySettlementUtils.formatDoubleToStringWithDecimalPlace(scala.util.Try(constants(FIRST).toInt).getOrElse(0))(columns(FIRST)))
      case Some("format_string_to_double_with_rounding") =>
        Some(PaymentGatewaySettlementUtils.formatStringToDoubleWithRounding(scala.util.Try(constants(FIRST).toInt).getOrElse(0))(columns(FIRST)))
      case Some("is_used_for_reconciliation") =>
        Some(PaymentGatewaySettlementUtils.isUsedForReconciliation(constants(FIRST))(columns(FIRST)))
      case Some("is_gateway_transaction") =>
        Some(PaymentGatewaySettlementUtils.isGatewayTransaction(constants(FIRST))(columns(FIRST)))
      case _ => None
    }

  /** Adyen specific payment settlement functions */
  def adyenSettlementFunction(functionName: Option[String], columns: Vector[Column]): Option[Column] =
    functionName match {
      case Some("adyen_get_source_currency") =>
        Some(AdyenSettlementLogic.getSourceCurrency(columns(FIRST), columns(SECOND), columns(THIRD)))
      case Some("adyen_get_source_amount") =>
        Some(AdyenSettlementLogic.getSourceAmount(array(columns: _*)))
      case Some("adyen_get_received_amount") =>
        Some(
          AdyenSettlementLogic.getReceivedAmount(
            columns(FIRST),
            columns(SECOND),
            columns(THIRD),
            columns(FOURTH),
            columns(FIFTH),
            columns(SIXTH),
            columns(SEVENTH)
          )
        )
      case Some("adyen_get_fee_amount") =>
        Some(AdyenSettlementLogic.getFeeAmount(columns(FIRST), columns(SECOND), columns(THIRD), columns(FOURTH)))
      case Some("adyen_get_merchant_id") =>
        Some(AdyenSettlementLogic.getMerchantId(columns(FIRST), columns(SECOND)))
      case _ => None
    }

  /** Alipay OSP specific payment settlement functions */
  def alipayOspSettlementFunction(functionName: Option[String], columns: Vector[Column]): Option[Column] =
    functionName match {
      case Some("alipay_osp_get_source_amount") =>
        Some(AlipayOspSettlementLogic.getSourceAmount(columns(FIRST), columns(SECOND)))
      case Some("alipay_osp_get_fee_amount") =>
        Some(AlipayOspSettlementLogic.getFeeAmount(columns(FIRST)))
      case Some("alipay_osp_get_received_amount") =>
        Some(AlipayOspSettlementLogic.getReceivedAmount(columns(FIRST), columns(SECOND)))
      case _ => None
    }

  /** NTT specific payment settlement functions */
  def nttSettlementFunction(functionName: Option[String], constants: Vector[String], columns: Vector[Column]): Option[Column] =
    functionName match {
      case Some("ntt_get_event_type_id") =>
        Some(NttSettlementLogic.getEventTypeId(constants(FIRST))(columns(FIRST), columns(SECOND)))
      case Some("ntt_get_exchange_rate") =>
        Some(NttSettlementLogic.getExchangeRate(columns(FIRST), columns(SECOND), columns(THIRD), columns(FOURTH), columns(FIFTH), columns(SIXTH)))
      case Some("ntt_get_received_amount") =>
        Some(NttSettlementLogic.getReceivedAmount(columns(FIRST), columns(SECOND), columns(THIRD), columns(FOURTH), columns(FIFTH)))
      case _ => None
    }

  /** PayPal specific payment settlement functions */
  def paypalSettlementFunction(functionName: Option[String], constants: Vector[String], columns: Vector[Column]): Option[Column] =
    functionName match {
      case Some("paypal_get_prebooking_id") =>
        Some(PaypalSettlementLogic.getPreBookingId(constants(FIRST))(columns(FIRST), columns(SECOND)))
      case Some("paypal_get_itinerary_id") =>
        Some(PaypalSettlementLogic.getItineraryId(constants(FIRST))(columns(FIRST), columns(SECOND)))
      case Some("paypal_get_event_type_id") =>
        Some(PaypalSettlementLogic.getEventTypeId(constants(FIRST))(columns(FIRST), columns(SECOND)))
      case Some("paypal_get_source_amount") =>
        Some(PaypalSettlementLogic.getSourceAmount(columns(FIRST), columns(SECOND)))
      case Some("paypal_get_fee_currency") =>
        Some(PaypalSettlementLogic.getFeeCurrency(columns(FIRST), columns(SECOND)))
      case Some("paypal_get_received_currency") =>
        Some(PaypalSettlementLogic.getReceivedCurrency(columns(FIRST), columns(SECOND)))
      case Some("paypal_get_received_amount") =>
        Some(PaypalSettlementLogic.getReceivedAmount(columns(FIRST), columns(SECOND)))
      case Some("paypal_get_fee_amount") =>
        Some(PaypalSettlementLogic.getFeeAmount(columns(FIRST), columns(SECOND)))
      case Some("paypal_get_exchange_rate") =>
        Some(PaypalSettlementLogic.getExchangeRate(columns(FIRST)))
      case _ => None
    }

  /** Juspay specific payment settlement functions */
  def juspaySettlementFunction(functionName: Option[String], constants: Vector[String], columns: Vector[Column]): Option[Column] =
    functionName match {
      case Some("juspay_get_gateway_id") =>
        Some(JuspaySettlementLogic.getGatewayId(columns(FIRST)))
      case Some("juspay_get_source_amount") =>
        Some(JuspaySettlementLogic.getSourceAmount(columns(FIRST), columns(SECOND)))
      case Some("juspay_get_received_amount") =>
        Some(
          JuspaySettlementLogic.getReceivedAmount(
            columns(FIRST),
            columns(SECOND),
            columns(THIRD),
            columns(FOURTH),
            columns(FIFTH),
            columns(SIXTH),
            columns(SEVENTH),
            columns(EIGHT),
            columns(NINETH)
          )
        )
      case Some("juspay_get_exchange_rate") =>
        Some(
          JuspaySettlementLogic.getExchangeRate(
            columns(FIRST),
            columns(SECOND),
            columns(THIRD),
            columns(FOURTH),
            columns(FIFTH),
            columns(SIXTH),
            columns(SEVENTH),
            columns(EIGHT),
            columns(NINETH)
          )
        )
      case Some("juspay_get_fee_amount") =>
        Some(JuspaySettlementLogic.getFeeAmount(columns(FIRST), columns(SECOND), columns(THIRD), columns(FOURTH)))
      case Some("juspay_get_mid") =>
        Some(JuspaySettlementLogic.getMid(columns(FIRST), columns(SECOND)))
      case Some("juspay_get_ar_event_type_id") =>
        Some(JuspaySettlementLogic.getEventTypeId(constants(FIRST))(columns(FIRST), columns(SECOND), columns(THIRD)))
      case _ => None
    }

  /** Primero specific payment settlement functions */
  def primeroSettlementFunction(functionName: Option[String], constants: Vector[String], columns: Vector[Column]): Option[Column] =
    functionName match {
      case Some("primero_get_source_currency") =>
        Some(PrimeroSettlementLogic.getSourceCurrency(columns(FIRST), columns(SECOND), columns(THIRD)))
      case Some("primero_get_exchange_rate") =>
        Some(
          PrimeroSettlementLogic.getExchangeRate(
            columns(FIRST),
            columns(SECOND),
            columns(THIRD),
            columns(FOURTH),
            columns(FIFTH),
            columns(SIXTH),
            columns(SEVENTH),
            columns(EIGHT),
            columns(NINETH)
          )
        )
      case Some("primero_get_source_amount") =>
        Some(PrimeroSettlementLogic.getSourceAmount(columns(FIRST), columns(SECOND), columns(THIRD), columns(FOURTH), columns(FIFTH)))
      case Some("primero_get_received_amount") =>
        Some(
          PrimeroSettlementLogic.getReceivedAmount(
            columns(FIRST),
            columns(SECOND),
            columns(THIRD),
            columns(FOURTH),
            columns(FIFTH),
            columns(SIXTH)
          )
        )
      case Some("primero_get_event_type_id") =>
        Some(
          PrimeroSettlementLogic.getEventTypeId(constants(FIRST))(columns(FIRST), columns(SECOND), columns(THIRD), columns(FOURTH), columns(FIFTH))
        )
      case Some("primero_get_fee_amount") =>
        Some(PrimeroSettlementLogic.getFeeAmount(columns(FIRST), columns(SECOND)))
      case _ => None
    }

  /** Afterpay specific payment settlement functions */
  def afterpaySettlementFunction(functionName: Option[String], constants: Vector[String], columns: Vector[Column]): Option[Column] =
    functionName match {
      case Some("afterpay_get_amount") =>
        Some(AfterPaySettlementLogic.getAmount(columns(FIRST)))
      case Some("afterpay_get_gateway_unique_id") =>
        Some(AfterPaySettlementLogic.getGatewayUniqueId(columns(FIRST), columns(SECOND), columns(THIRD)))
      case Some("afterpay_convert_datestring_to_bigint") =>
        val defaultFormat = "yyyy-MM-dd'T'HH:mm:ss"
        Some(
          AfterPaySettlementLogic.convertDateStringtoBigInt(
            if (constants.isEmpty || constants(FIRST).isEmpty) defaultFormat else constants(FIRST)
          )(columns(FIRST))
        )
      case _ => None
    }

  /** Main function to handle all payment settlement specific functions */
  def paymentSettlementFunction(functionName: Option[String], constants: Vector[String], columns: Vector[Column]): Option[Column] =
    genericPaymentSettlementFunction(functionName, constants, columns)
      .orElse(adyenSettlementFunction(functionName, columns))
      .orElse(primeroSettlementFunction(functionName, constants, columns))
      .orElse(alipayOspSettlementFunction(functionName, columns))
      .orElse(afterpaySettlementFunction(functionName, constants, columns))
      .orElse(nttSettlementFunction(functionName, constants, columns))
      .orElse(juspaySettlementFunction(functionName, constants, columns))
      .orElse(paypalSettlementFunction(functionName, constants, columns))
}
