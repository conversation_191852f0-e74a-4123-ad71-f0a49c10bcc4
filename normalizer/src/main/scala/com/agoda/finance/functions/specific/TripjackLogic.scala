package com.agoda.finance.functions.specific

import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions.udf

import java.sql.Timestamp
import java.text.SimpleDateFormat
import java.util.concurrent.TimeUnit

object TripjackLogic extends LazyLogging {

  def getTripjackTransactionDate: UserDefinedFunction = udf {
    (transactionType: String, bookingDate: Timestamp, bookingTime: String, amendmentDate: String) =>
      val transactionTimestamp = transactionType match {
        case "REFUND" | "REISSUE" =>
          // Use amendmentDate
          if (amendmentDate != null) {
            val amendmentFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS")
            new Timestamp(amendmentFormat.parse(amendmentDate).getTime)
          } else {
            null
          }

        case _ =>
          // Combine bookingDate and bookingTime for all other transaction types
          if (bookingDate != null && bookingTime != null) {
            val bookingDateStr      = new SimpleDateFormat("yyyy-MM-dd").format(bookingDate)
            val combinedDateTimeStr = s"$bookingDateStr $bookingTime"
            val combinedFormat      = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
            new Timestamp(combinedFormat.parse(combinedDateTimeStr).getTime)
          } else {
            null
          }
      }

      // Offset by 5 hours and 30 minutes
      if (transactionTimestamp != null) {
        val offsetTimeInMillis = TimeUnit.HOURS.toMillis(-5) + TimeUnit.MINUTES.toMillis(-30)
        new Timestamp(transactionTimestamp.getTime + offsetTimeInMillis)
      } else {
        null
      }
  }

  def getTripjackTransactionType: UserDefinedFunction = udf { (amendmentType: String) =>
    if (amendmentType == null) {
      "SALE"
    } else {
      val amendmentTypeMapping = Map(
        "CANCELLATION"           -> "REFUND",
        "CANCELLATION QUOTATION" -> "REFUND",
        "CORRECTION"             -> "REISSUE",
        "CUSTOM"                 -> "REFUND",
        "FARE CHANGE"            -> "REISSUE",
        "FULL REFUND"            -> "REFUND",
        "MISCELLANEOUS"          -> "REFUND",
        "NO SHOW"                -> "REFUND",
        "REISSUE"                -> "REISSUE",
        "REISSUE QUOTATION"      -> "REISSUE",
        "SSR"                    -> "REFUND",
        "VOIDED"                 -> "REFUND"
      )

      amendmentTypeMapping.getOrElse(amendmentType, "SALE")
    }
  }
}
