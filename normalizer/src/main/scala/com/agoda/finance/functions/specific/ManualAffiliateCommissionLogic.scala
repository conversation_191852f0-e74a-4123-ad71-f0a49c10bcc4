package com.agoda.finance.functions.specific

import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import org.apache.spark.sql.{Column, DataFrame}
import scala.collection.Seq

object ManualAffiliateCommissionLogic extends LazyLogging {

  private final val gimmonixAids = List(218861, 205840, 224829, 219813, 255727, 184015, 265736, 263224, 260486, 282887)
  private final val peakworkAids = List(292272, 292274, 279766, 279599)
  private final val gimmonixCids = List(1830365, 1816554, 1830384, 1802573)
  private final val juniperAids = List(136384, 148808, 181855, 191937, 202982, 221956, 226230, 298404, 299907, 228312, 232130, 234223, 248748, 252328,
    258502, 259018, 259389, 268910, 280321, 241843, 237026, 249238, 250781, 248665, 268412, 288613, 300255, 300253, 300267, 232323, 320252, 211269)
  private final val juniperCids = List(1837728, 1838037)
  private final val dcsAids     = List(201786, 300174, 300171, 305201)
  // double check AIDs in normalizer/src/main/resources/controls_automation/manual_affiliate_commissions/pah_partner_normalized_data/settings.conf
  private val pahAffiliateIdMapping = Map(
    Seq(243996)                 -> CommonAffiliateName.tiket_pah.toString,
    Seq(267603, 282665)         -> CommonAffiliateName.rakuten_pah.toString,
    Seq(299836, 300264, 300265) -> CommonAffiliateName.ixigo_pah.toString,
    Seq(285434, 300099)         -> CommonAffiliateName.dida_pah.toString
  )

  // double check AIDs in normalizer/src/main/resources/controls_automation/manual_affiliate_commissions/switchfly_normalized_data/settings.conf
  private val switchflyAffiliateIdMapping = Map(
    Seq(218445)         -> SwitchflyPartnerNames.AADV.toString,
    Seq(210387)         -> SwitchflyPartnerNames.AAV.toString,
    Seq(196000)         -> SwitchflyPartnerNames.FlightCentre.toString,
    Seq(224829)         -> SwitchflyPartnerNames.SnapTravel.toString,
    Seq(265736)         -> SwitchflyPartnerNames.SnapTravelCCC.toString,
    Seq(252975, 253029) -> SwitchflyPartnerNames.JALPAK.toString,
    Seq(235477)         -> SwitchflyPartnerNames.MastercardAU.toString,
    Seq(234903)         -> SwitchflyPartnerNames.MastercardHK.toString,
    Seq(260581)         -> SwitchflyPartnerNames.Jetblue.toString
  )

  def enrichIntegratorPartnerData(df: DataFrame): DataFrame =
    defineIntegratorCommonAffiliateName(defineIntegratorPartnerName(defineIntegratorPlatformFee(df)))

  private def defineIntegratorCommonAffiliateName(df: DataFrame): DataFrame =
    df.withColumn(
      "common_affiliate_name",
      when(
        col("attribution_affiliate_id").isin(gimmonixAids: _*) || col("attribution_site_id").isin(gimmonixCids: _*),
        lit(CommonAffiliateName.gimmonix.toString)
      )
        .when(col("attribution_affiliate_id").isin(peakworkAids: _*), lit(CommonAffiliateName.peakwork.toString))
        .when(
          col("attribution_affiliate_id").isin(juniperAids: _*) || col("attribution_site_id").isin(juniperCids: _*),
          lit(CommonAffiliateName.juniper.toString)
        )
        .when(col("attribution_affiliate_id").isin(dcsAids: _*), lit(CommonAffiliateName.dcs.toString))
        .otherwise(lit(null))
    )

  private def defineIntegratorPlatformFee(df: DataFrame): DataFrame =
    df.withColumn(
      "platform_fee_amount",
      when(
        col("booking_status_id").isin(1, 2, 3, 4),
        when(col("attribution_affiliate_id").isin(218861, 205840, 224829, 265736), lit(0.5))
          .when(col("attribution_site_id").isin(1816554, 1830384, 1802573), lit(0.5))
          .when(col("attribution_affiliate_id").isin(219813, 255727, 263224, 260486, 282887), lit(1))
          .when(col("attribution_affiliate_id").isin(184015) && to_date(col("booking_date")).>=(lit("2019-09-11")), lit(1))
          .when(col("attribution_site_id").isin(1830365), lit(1))
          .otherwise(lit(0))
      )
        .otherwise(lit(0))
    ).withColumn("platform_fee_amount", col("platform_fee_amount").cast(DoubleType))

  private def defineIntegratorPartnerName(df: DataFrame): DataFrame = {
    // Define the mapping for attribution_affiliate_id using the enum
    val partnerNameMapping = Map(
      Seq(226230, 298404)                 -> IntegratorPartnerName.DubaiLinkNetRate,
      Seq(234223)                         -> IntegratorPartnerName.DubaiLinkSellRate,
      Seq(280321)                         -> IntegratorPartnerName.DubaiLinkNetRateCC,
      Seq(299907)                         -> IntegratorPartnerName.DubaiLinkRHG,
      Seq(221956)                         -> IntegratorPartnerName.Escalabeds,
      Seq(181855)                         -> IntegratorPartnerName.Prism,
      Seq(202982, 320252)                 -> IntegratorPartnerName.W2M,
      Seq(232130)                         -> IntegratorPartnerName.LCI,
      Seq(252328)                         -> IntegratorPartnerName.LOHUnderWebbeds,
      Seq(268910)                         -> IntegratorPartnerName.LOHSell,
      Seq(228312)                         -> IntegratorPartnerName.Arabjet,
      Seq(136384)                         -> IntegratorPartnerName.SpiderHoliday,
      Seq(259018)                         -> IntegratorPartnerName.DestyTravel,
      Seq(148808, 191937)                 -> IntegratorPartnerName.AIMIAMiddleEast,
      Seq(248748)                         -> IntegratorPartnerName.RoyalAdventureNet,
      Seq(258502)                         -> IntegratorPartnerName.VNTravelive,
      Seq(259389)                         -> IntegratorPartnerName.Viajersa,
      Seq(241843)                         -> IntegratorPartnerName.VictoriaTour,
      Seq(237026)                         -> IntegratorPartnerName.VietravelNetRate,
      Seq(249238)                         -> IntegratorPartnerName.VietravelSellRates,
      Seq(250781)                         -> IntegratorPartnerName.ExploreGlobalSellRates,
      Seq(248665)                         -> IntegratorPartnerName.ExploreGlobalNetRates,
      Seq(268412)                         -> IntegratorPartnerName.FursanTravelSellRate,
      Seq(288613)                         -> IntegratorPartnerName.FursanTravelNetNew,
      Seq(300255)                         -> IntegratorPartnerName.PrimeTravel,
      Seq(300253)                         -> IntegratorPartnerName.Peakpoint,
      Seq(300267)                         -> IntegratorPartnerName.Jumbotours,
      Seq(218861)                         -> IntegratorPartnerName.TajawalOffline,
      Seq(205840)                         -> IntegratorPartnerName.TajawalCom,
      Seq(224829, 265736)                 -> IntegratorPartnerName.SnapTravel,
      Seq(219813)                         -> IntegratorPartnerName.HISSkyhub,
      Seq(255727)                         -> IntegratorPartnerName.TaleTravel,
      Seq(184015)                         -> IntegratorPartnerName.Cozmo,
      Seq(263224)                         -> IntegratorPartnerName.TravalaUK,
      Seq(260486)                         -> IntegratorPartnerName.Travelor,
      Seq(282887)                         -> IntegratorPartnerName.LockTrip,
      Seq(201786, 300174, 300171, 305201) -> IntegratorPartnerName.Dcs,
      Seq(232323)                         -> IntegratorPartnerName.SmileHolidayKuwait,
      Seq(211269)                         -> IntegratorPartnerName.Easemytrip,
      Seq(292272, 292274, 279766, 279599) -> IntegratorPartnerName.DER
    )

    // Define the mapping for attribution_site_id using the enum
    val siteIdMapping = Map(
      Seq(1837728)          -> IntegratorPartnerName.SmileHoliday,
      Seq(1838037)          -> IntegratorPartnerName.AIMIAME,
      Seq(1830365)          -> IntegratorPartnerName.AIC,
      Seq(1816554, 1830384) -> IntegratorPartnerName.NustaySellRate,
      Seq(1802573)          -> IntegratorPartnerName.NustayWholesaleRate
    )

    val affiliateIdColumn = partnerNameMapping.foldLeft(lit(null).cast("string")) { case (column, (ids, partnerName)) =>
      when(col("attribution_affiliate_id").isin(ids: _*), lit(partnerName.toString)).otherwise(column)
    }
    val siteIdColumn = siteIdMapping.foldLeft(lit(null).cast("string")) { case (column, (ids, partnerName)) =>
      when(col("attribution_site_id").isin(ids: _*), lit(partnerName.toString)).otherwise(column)
    }

    df.withColumn("partner_name", coalesce(affiliateIdColumn, siteIdColumn))
      .withColumn(
        "partner_name",
        when(col("partner_name").isNull, lit("no name"))
          .otherwise(col("partner_name"))
      )
  }

  private[specific] def safeSum(df: DataFrame, columnName: String, aliasName: String): Column =
    if (df.columns.contains(columnName)) {
      sum(columnName).alias(aliasName)
    } else {
      lit(null).alias(aliasName) // Default value if the column is missing
    }

  private[specific] def getAgodaValidTransactions(
      agoda_transaction: DataFrame,
      affiliate_invalid_transaction: DataFrame
  ): DataFrame =
    agoda_transaction
      .join(
        affiliate_invalid_transaction,
        Seq("booking_id", "datamonth", "transaction_type", "common_affiliate_name"),
        "leftanti"
      )

  private[specific] def getFullOuterJoinAgodaAndPartnerTransactions(
      agoda_transaction: DataFrame,
      partner_report: DataFrame,
      partner_transaction_identifier_column_name: String
  ): DataFrame = {
    val agodaAlias   = agoda_transaction.alias("agoda")
    val partnerAlias = partner_report.alias("partner")
    agodaAlias
      .join(
        partnerAlias,
        partnerAlias(partner_transaction_identifier_column_name) === agodaAlias("booking_id"),
        "fullouter"
      )
      .withColumn("existed_in_agoda", col("agoda.booking_id").isNotNull)
      .withColumn("existed_in_partner", col(s"partner.$partner_transaction_identifier_column_name").isNotNull)
      .withColumn(
        "classification",
        when(col("existed_in_agoda") && col("existed_in_partner"), "reconciled")
          .when(col("existed_in_agoda") && !col("existed_in_partner"), "mismatched_agoda")
          .when(!col("existed_in_agoda") && col("existed_in_partner"), "mismatched_partner")
      )
      .withColumn("joined_common_affiliate_name", coalesce(col("agoda.common_affiliate_name"), col("partner.common_affiliate_name")))
  }

  private[specific] def processAccrualCommission(
      agoda_transaction: DataFrame,
      affiliate_invalid_transaction: DataFrame,
      partner_report: DataFrame,
      groupByColumns: Seq[String],
      partner_transaction_identifier_column_name: String,
      partner_commission_column_name: String
  ): DataFrame = {

    val agodaValidTransaction = getAgodaValidTransactions(agoda_transaction, affiliate_invalid_transaction)

    val agodaValidTransactionAggregations = Seq(
      count("*").alias(SummaryTableAdditionalColumn.number_of_agoda_bookings),
      collect_set("affiliate_id").alias(SummaryTableAdditionalColumn.affiliate_ids),
      sum("commission_amount").alias(SummaryTableAdditionalColumn.total_agoda_calculated_commission_usd),
      safeSum(
        agodaValidTransaction,
        "commission_amount_partner_currency",
        SummaryTableAdditionalColumn.total_agoda_calculated_commission_partner_currency
      )
    )

    val agodaCompleteTransaction = agodaValidTransaction
      .groupBy(groupByColumns.map(col): _*)
      .agg(agodaValidTransactionAggregations.head, agodaValidTransactionAggregations.tail: _*)

    val joinedDF = getFullOuterJoinAgodaAndPartnerTransactions(agodaValidTransaction, partner_report, partner_transaction_identifier_column_name)

    // Group by classification and aggregate metrics
    val aggregatedDF = joinedDF
      .groupBy("classification", "joined_common_affiliate_name")
      .agg(
        count("*").alias("number_of_bookings"),
        sum("agoda.commission_amount").alias("total_agoda_commission_usd"),
        safeSum(joinedDF, "agoda.commission_amount_partner_currency", "total_agoda_commission_partner_currency"),
        sum(s"partner.$partner_commission_column_name").alias("total_partner_commission")
      )

    val additionalColumnsDF = aggregatedDF
      .groupBy("joined_common_affiliate_name")
      .agg(
        sum(when(col("classification") === "reconciled", col("number_of_bookings")))
          .alias(SummaryTableAdditionalColumn.number_of_reconciled_bookings),
        sum(when(col("classification") === "reconciled", col("total_agoda_commission_usd")))
          .alias(SummaryTableAdditionalColumn.total_reconciled_agoda_commission_usd),
        sum(when(col("classification") === "reconciled", col("total_agoda_commission_partner_currency")))
          .alias(SummaryTableAdditionalColumn.total_reconciled_agoda_commission_partner_currency),
        sum(when(col("classification") === "reconciled", col("total_partner_commission")))
          .alias(SummaryTableAdditionalColumn.total_reconciled_partner_commission),
        sum(when(col("classification") === "mismatched_agoda", col("number_of_bookings")))
          .alias(SummaryTableAdditionalColumn.number_of_mismatched_agoda_bookings),
        sum(when(col("classification") === "mismatched_partner", col("number_of_bookings")))
          .alias(SummaryTableAdditionalColumn.number_of_mismatched_partner_bookings)
      )

    val aggregatedPartnerDF = partner_report
      .groupBy("common_affiliate_name")
      .agg(
        count("*").alias(SummaryTableAdditionalColumn.number_of_partner_bookings),
        sum(partner_commission_column_name).alias(SummaryTableAdditionalColumn.partner_commission_partner_currency)
      )
      .select(
        SummaryTableAdditionalColumn.number_of_partner_bookings,
        SummaryTableAdditionalColumn.partner_commission_partner_currency,
        "common_affiliate_name"
      )

    agodaCompleteTransaction
      .join(aggregatedPartnerDF, agodaCompleteTransaction("common_affiliate_name") === aggregatedPartnerDF("common_affiliate_name"), "fullouter")
      .join(
        additionalColumnsDF,
        agodaCompleteTransaction("common_affiliate_name") === additionalColumnsDF("joined_common_affiliate_name"),
        "fullouter"
      )
      .select(
        agodaCompleteTransaction("*"),
        aggregatedPartnerDF(SummaryTableAdditionalColumn.number_of_partner_bookings),
        aggregatedPartnerDF(SummaryTableAdditionalColumn.partner_commission_partner_currency),
        additionalColumnsDF(SummaryTableAdditionalColumn.number_of_reconciled_bookings),
        additionalColumnsDF(SummaryTableAdditionalColumn.total_reconciled_agoda_commission_usd),
        additionalColumnsDF(SummaryTableAdditionalColumn.total_reconciled_agoda_commission_partner_currency),
        additionalColumnsDF(SummaryTableAdditionalColumn.total_reconciled_partner_commission),
        additionalColumnsDF(SummaryTableAdditionalColumn.number_of_mismatched_agoda_bookings),
        additionalColumnsDF(SummaryTableAdditionalColumn.number_of_mismatched_partner_bookings)
      )
  }

  private def cjSummaryAccrualCommission(
      agoda_transaction: DataFrame,
      affiliate_invalid_transaction: DataFrame,
      partner_report: DataFrame
  ): DataFrame =
    processAccrualCommission(
      agoda_transaction,
      affiliate_invalid_transaction,
      partner_report.withColumn("common_affiliate_name", lit("cj")),
      Seq("datamonth", "common_affiliate_name", "transaction_type", "partner_currency"),
      "order_id",
      "adv_comm_fc"
    )

  private def rakutenSummaryAccrualCommission(
      agoda_transaction: DataFrame,
      affiliate_invalid_transaction: DataFrame,
      partner_report: DataFrame
  ): DataFrame =
    processAccrualCommission(
      agoda_transaction,
      affiliate_invalid_transaction,
      partner_report.withColumn("common_affiliate_name", lit("rakuten")),
      Seq("datamonth", "common_affiliate_name", "transaction_type", "partner_currency"),
      "order_id",
      "total_commission"
    )

  private def pahSummaryAccrualCommission(
      agoda_transaction: DataFrame,
      affiliate_invalid_transaction: DataFrame,
      partner_report: DataFrame
  ): DataFrame =
    processAccrualCommission(
      agoda_transaction,
      affiliate_invalid_transaction,
      partner_report,
      Seq("datamonth", "common_affiliate_name", "transaction_type"),
      "booking_id",
      "commission_amount"
    )

  def summaryAccrualCommissionWithPartnerReport(
      agoda_transaction: DataFrame,
      affiliate_invalid_transaction: DataFrame,
      partner_report: DataFrame,
      common_affiliate_name: String
  ): DataFrame =
    common_affiliate_name match {
      case "cj"      => cjSummaryAccrualCommission(agoda_transaction, affiliate_invalid_transaction, partner_report)
      case "pah"     => pahSummaryAccrualCommission(agoda_transaction, affiliate_invalid_transaction, partner_report)
      case "rakuten" => rakutenSummaryAccrualCommission(agoda_transaction, affiliate_invalid_transaction, partner_report)
      case _         => throw new IllegalArgumentException(s"Invalid common_affiliate_name value $common_affiliate_name")
    }

  def mappingPAHAffiliateName: UserDefinedFunction = udf { (affiliate_id: Int) =>
    pahAffiliateIdMapping
      .find { case (keySeq, _) => keySeq.contains(affiliate_id) }
      .map(_._2)
      .getOrElse("")
  }

  def mappingSwitchflyAffiliateName: UserDefinedFunction = udf { (affiliate_id: Int) =>
    switchflyAffiliateIdMapping
      .find { case (keySeq, _) => keySeq.contains(affiliate_id) }
      .map(_._2)
      .getOrElse(SwitchflyPartnerNames.NoName.toString)
  }

  def anaMarginCalculation(constant: BigDecimal): UserDefinedFunction = udf {
    (margin: BigDecimal, originalSellingAmount: BigDecimal, originalPaymentAmount: BigDecimal) =>
      val notNullMargin                = Option(margin).getOrElse(BigDecimal(0))
      val notNullOriginalSellingAmount = Option(originalPaymentAmount).orElse(Option(originalSellingAmount)).getOrElse(BigDecimal(0))
      notNullMargin - (notNullOriginalSellingAmount * constant)
  }

  def anaVehicleBooking: UserDefinedFunction = udf { (is_cancelled: Boolean, is_departed: Boolean, vehicle_booking_state: String) =>
    if (is_cancelled) {
      "Booking Cancelled"
    } else if (is_departed) {
      "Booking Departed"
    } else {
      vehicle_booking_state
    }
  }

  def mergeShopbackActivityData(
      factBookingActivity: DataFrame,
      factBookingAttributionActivity: DataFrame,
      activityGrouping: DataFrame,
      activityUpsize: DataFrame
  ): DataFrame = {
    val fba  = factBookingActivity.alias("fba")
    val fbaa = factBookingAttributionActivity.alias("fbaa")
    val ag = activityGrouping
      .withColumn("cpa_group_coalesced", coalesce(lower(col("cpa_group")), lit("general")))
      .alias("ag")
    val upsize = activityUpsize
      .withColumn("cpa_group_lower", lower(col("cpa_group")))
      .alias("upsize")

    val factBookingActivityWithAttribution = fba.join(
      fbaa,
      fba("booking_id") === fbaa("booking_id") &&
        fba("datamonth") === fbaa("datamonth"),
      "inner"
    )

    val withGrouping = factBookingActivityWithAttribution.join(
      ag,
      factBookingActivityWithAttribution("activity_id") === ag("activity_id"),
      "left"
    )

    val result = withGrouping.join(
      upsize,
      withGrouping("lpc_30_site_id") === upsize("site_id") &&
        withGrouping("booking_date") === upsize("campaign_date") &&
        coalesce(withGrouping("cpa_group_coalesced"), lit("general")) === upsize("cpa_group_lower"),
      "left"
    )

    result.select(
      col("fba.booking_id"),
      col("fba.lpc_30_affiliate_id"),
      col("fba.lpc_30_affiliate_name"),
      col("fba.lpc_30_site_id"),
      col("fba.lpc_30_site_name"),
      col("fba.is_cancelled"),
      col("fbaa.tag"),
      col("fba.booking_state_name"),
      col("fba.booking_date"),
      col("fba.booking_start_date"),
      col("fba.booking_end_date"),
      col("fba.selling_amount"),
      col("fba.original_payment_model_name"),
      col("upsize.upsize_percent"),
      coalesce(col("ag.cpa_group"), lit("General")).as("cpa_group")
    )
  }

  def normalizeComstatData(
      bookingDF: DataFrame,
      fbDF: DataFrame,
      siteDF: DataFrame,
      dmcDF: DataFrame
  ): DataFrame = {

    val booking = bookingDF.alias("booking")

    val fb = fbDF.alias("fb")
    val bookingFbDF = booking
      .join(
        fb,
        booking("booking_id") === fb("booking_id") &&
          booking("master").cast("int") === fb("datamonth"),
        "inner"
      )

    val site = siteDF.alias("site")
    val bookingFbSiteDF = bookingFbDF
      .join(
        site,
        bookingFbDF("attributed_site_id") === site("site_id"),
        "inner"
      )

    val dmc = dmcDF.alias("dmc")
    val joinedDF = bookingFbSiteDF
      .join(
        dmc,
        col("fb.original_supplier_id") === dmc("dmc_id"),
        "left"
      )

    joinedDF.select(
      col("booking.booking_id"),
      col("booking.booking_status"),
      col("booking.affiliate_id"),
      col("site.affiliate_name"),
      col("booking.attributed_site_id"),
      col("site.site_name"),
      col("booking.booking_date"),
      col("booking.from_date").as("checkin_date"),
      col("booking.to_date").as("checkout_date"),
      col("booking.cancellation_date"),
      col("booking.payment_due_date"),
      col("fb.original_payment_model_name"),
      when(col("booking.finalization_date").isNotNull, col("booking.affiliate_commision")).otherwise(lit(0)).as("affiliate_commission"),
      when(col("booking.finalization_date").isNull, col("booking.affiliate_commision")).otherwise(lit(0)).as("est_affiliate_commission"),
      col("booking.cust_invoice_amount"),
      col("booking.cust_invoice_amount_sell_exclusive"),
      col("booking.processing_percent"),
      col("booking.origin"),
      col("fb.platform_group_name"),
      col("booking.tag"),
      col("fb.country_name"),
      col("dmc.dmc_code"),
      col("booking.discount_amount")
    )
  }
}

object IntegratorPartnerName extends Enumeration {
  type PartnerName = Value

  val DubaiLinkNetRate       = Value("Dubai Link Net rate")
  val DubaiLinkSellRate      = Value("Dubai Link Sell rate")
  val DubaiLinkNetRateCC     = Value("Dubai Link - Net Rate CC")
  val DubaiLinkRHG           = Value("Dubai Link RHG")
  val Escalabeds             = Value("Escalabeds")
  val Prism                  = Value("Prism")
  val W2M                    = Value("W2M")
  val LCI                    = Value("LCI")
  val LOHUnderWebbeds        = Value("LOH (under webbeds)")
  val LOHSell                = Value("LOH (Sell)")
  val Arabjet                = Value("Arabjet")
  val SpiderHoliday          = Value("Spider Holiday")
  val DestyTravel            = Value("Desty Travel")
  val AIMIAMiddleEast        = Value("AIMIA Middle East")
  val RoyalAdventureNet      = Value("Royal Adventure (Net)")
  val VNTravelive            = Value("VN TRAVELLIVE")
  val Viajersa               = Value("Viajersa")
  val VictoriaTour           = Value("Victoria Tour")
  val VietravelNetRate       = Value("VIETRAVEL NET RATE")
  val VietravelSellRates     = Value("VIETRAVEL SELL RATES")
  val ExploreGlobalSellRates = Value("Explore Global Sell rates")
  val ExploreGlobalNetRates  = Value("Explore Global Net rates")
  val FursanTravelSellRate   = Value("Fursan Travel SELL RATE")
  val FursanTravelNetNew     = Value("Fursan Travel Net (New)")
  val SmileHoliday           = Value("Smile Holiday")
  val AIMIAME                = Value("AIMIA ME")
  val Dcs                    = Value("DCS+")
  val TajawalOffline         = Value("Tajawal Offline")
  val TajawalCom             = Value("Tajawal.com")
  val SnapTravel             = Value("Snap Travel")
  val HISSkyhub              = Value("HIS (Skyhub)")
  val TaleTravel             = Value("Tale Travel")
  val Cozmo                  = Value("Cozmo")
  val TravalaUK              = Value("Travala (UK)")
  val Travelor               = Value("Travelor")
  val LockTrip               = Value("Lock Trip")
  val AIC                    = Value("AIC")
  val NustaySellRate         = Value("Nustay Sell Rate")
  val NustayWholesaleRate    = Value("Nustay Wholesale Rate")
  val PrimeTravel            = Value("Prime Travel")
  val Peakpoint              = Value("Peakpoint")
  val Jumbotours             = Value("Jumbotours")
  val SmileHolidayKuwait     = Value("Smile Holidays Kuwait")
  val Easemytrip             = Value("Easemytrip")
  val DER                    = Value("DER")

}

object SwitchflyPartnerNames extends Enumeration {
  type PartnerName = Value

  val AADV          = Value("AADV")
  val AAV           = Value("AAV")
  val FlightCentre  = Value("Flight Centre")
  val SnapTravel    = Value("Snap Travel")
  val SnapTravelCCC = Value("Snap Travel CCC")
  val JALPAK        = Value("JAL PAK")
  val MastercardAU  = Value("Mastercard AU")
  val MastercardHK  = Value("Mastercard HK")
  val Jetblue       = Value("Jetblue")
  val NoName        = Value("no name")
}

object CommonAffiliateName extends Enumeration {
  type AffiliateName = Value

  val gimmonix    = Value
  val juniper     = Value
  val dcs         = Value("dcs+")
  val tiket_pah   = Value("tiket pah")
  val rakuten_pah = Value("rakuten pah")
  val ixigo_pah   = Value("ixigo pah")
  val dida_pah    = Value("dida pah")
  val peakwork    = Value("peakwork")
}

object SummaryTableAdditionalColumn {
  val number_of_reconciled_bookings                      = "number_of_reconciled_bookings"
  val total_reconciled_agoda_commission_usd              = "total_reconciled_agoda_commission_usd"
  val total_reconciled_agoda_commission_partner_currency = "total_reconciled_agoda_commission_partner_currency"
  val total_reconciled_partner_commission                = "total_reconciled_partner_commission"
  val number_of_mismatched_agoda_bookings                = "number_of_mismatched_agoda_bookings"
  val number_of_mismatched_partner_bookings              = "number_of_mismatched_partner_bookings"
  val number_of_partner_bookings                         = "number_of_partner_bookings"
  val partner_commission_partner_currency                = "partner_commission_partner_currency"
  val affiliate_ids                                      = "affiliate_ids"
  val number_of_agoda_bookings                           = "number_of_agoda_bookings"
  val total_agoda_calculated_commission_usd              = "total_agoda_calculated_commission_usd"
  val total_agoda_calculated_commission_partner_currency = "total_agoda_calculated_commission_partner_currency"
}
