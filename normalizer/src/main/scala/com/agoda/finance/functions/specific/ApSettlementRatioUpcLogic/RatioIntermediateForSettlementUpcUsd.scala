package com.agoda.finance.functions.specific.ApSettlementRatioUpcLogic

import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.DoubleType
import org.apache.spark.sql.{DataFrame, SQLContext}

class RatioIntermediateForSettlementUpcUsd(implicit
    SQLContext: SQLContext
) extends LazyLogging
    with ApSettlementUdf {
  def prepareRatioIntermediateTableForUsdCalculation(
      financialTransactionsDf: DataFrame,
      settlementMatchedUnmatchedDf: DataFrame,
      settlementAdjustments: DataFrame,
      dataDate: Int
  ): DataFrame = {

    /** *******************************************************************************
      * DataFrame: groupedFinancialTransactionsDf
      * -------------------------------------------------------------------------------
      * Description:
      *   - Aggregates financial_transaction and ledger_booking_balance records at the (booking_id, supplier_currency) level.
      *   - For every combination of booking_id and supplier_currency it will have 1 line
      *
      * Transformations:
      *   - Groups by:
      *       • booking_id
      *       • supplier_currency (aliased as booking_settlement_currency)
      *   - Aggregates:
      *       • uuid: Maximum UUID value within each group (for traceability)
      *       • local_total_amount_inc_gst: Sum of local amounts (rounded to 2 decimals, cast to DECIMAL(24,2))
      *       • usd_total_amount_inc_gst: Sum of USD amounts (rounded to 2 decimals, cast to DECIMAL(24,2))
      *       • transaction_date: Latest transaction date in the group
      *       • source: Latest source value in the group
      *       • source_table: Concatenated list of all source_table values (comma-separated)
      *   - Adds columns:
      *       • reference: Literal value "ALL" for all rows
      *       • booking_settlement_ex_rate: Literal value 0.0 (DoubleType) for all rows.
      *
      * Purpose:
      *   - Produces a booking_id, currency-specific summary of financial transactions with
      *   summed local_total and usd_total amounts.
      *
      * *******************************************************************************
      */

    logger.info("Starting prepareRatioIntermediateTableForUsdCalculation")

    val groupedFinancialTransactionsDf = financialTransactionsDf
      .groupBy(
        col("booking_id"),
        col("supplier_currency").as("booking_settlement_currency")
      )
      .agg(
        max("uuid").as("uuid"),
        round(sum("local_total_amount_inc_gst"), 2).cast("decimal(24,2)").as("local_total_amount_inc_gst"),
        round(sum("usd_total_amount_inc_gst"), 2).cast("decimal(24,2)").as("usd_total_amount_inc_gst"),
        max("transaction_date").as("transaction_date"),
        max("source").as("source"),
        concat_ws(",", collect_list(col("source_table"))).as("source_table")
      )
      .withColumns(
        Map(
          "reference"                  -> lit("ALL"),
          "booking_settlement_ex_rate" -> lit(0).cast(DoubleType)
        )
      )

    /** *******************************************************************************
      * DataFrame: settlementsForMatchedDf
      * -------------------------------------------------------------------------------
      * Description:
      *   - Extracts and transforms settlement records that are classified as "MATCHED"
      *     from the combined matched/unmatched settlements DataFrame.
      *
      * Transformations:
      *   - Filters:
      *       • Only includes rows where the "case" column equals "MATCHED".
      *   - Selects and transforms columns:
      *       • uuid: Unique identifier for the settlement record.
      *       • booking_id: Identifier for the related booking.
      *       • booking_settlement_currency: Currency in which the settlement is made.
      *       • local_total_amount_inc_gst: booking_settlement_amount, rounded to 2 decimals and cast to DECIMAL(24,2).
      *       • usd_total_amount_inc_gst: Sets to 0.00 (DECIMAL(24,2)) for all rows, as these are local currency settlements.
      *       • transaction_date: Date of the settlement transaction.
      *       • source: Source system or process for the record.
      *       • source_table: Originating table for the record.
      *       • reference: To identify the record whether it is part of current or history
      *       • booking_settlement_ex_rate: Exchange rate used for the settlement (if applicable).
      *
      * Purpose:
      *   - Produces a DataFrame of only matched settlement records, where booking reference is
      *   available
      * *******************************************************************************
      */

    val settlementsForMatchedDf = settlementMatchedUnmatchedDf
      .filter(col("case") === "MATCHED")
      .select(
        col("uuid"),
        col("booking_id"),
        col("booking_settlement_currency"),
        round(col("booking_settlement_amount"), 2).cast("decimal(24,2)").as("local_total_amount_inc_gst"),
        lit(0).as("usd_total_amount_inc_gst").cast("decimal(24, 2)"),
        col("transaction_date"),
        col("source"),
        col("source_table"),
        col("reference"),
        col("booking_settlement_ex_rate")
      )

    /*
    val settlementsCurrencyDf: DataFrame = settlementsForMatchedDf
      .select(
        col("booking_id"),
        col("booking_settlement_currency")
      )
      .distinct()

     */

    /*
          Need to raise concern on this how to handle it
     */

    /*
    val settlementAdjustmentDf: DataFrame = settlementAdjustments
      .join(settlementsCurrencyDf, Seq("booking_id"), "inner")
      .select(
        col("uuid"),
        col("booking_id"),
        col("booking_settlement_currency"),
        col("local_total_amount_inc_gst").as("local_total_amount_inc_gst").cast("decimal(24, 2)"),
        lit(0).as("usd_total_amount_inc_gst").cast("decimal(24, 2)"),
        col("transaction_date"),
        col("source"),
        col("source_table")
      ).withColumns(
        Map(
         // "booking_settlement_exchange_rate" -> lit(null).cast(StringType),
          "reference" -> lit("ALL")
        )
      )*/

    /** *******************************************************************************
      * DataFrame Transformation Pipeline to Prepare data for Ratio Calculation
      * -------------------------------------------------------------------------------
      * Description:
      *   - Combines booking and settlement records, assigns row numbers, calculates running
      *     balances, determines matched statuses, and prepares grouped row structures for
      *     downstream UDF processing.
      *
      * Steps:
      *   1. Window Definitions:
      *       • rowNumWindow: Assigns row numbers within each (booking_id, currency) partition,
      *         ordered by source and transaction_date.
      *       • sumExpectedWindow: Sums balances within (booking_id, source, row_num) partitions.
      *       • previousMatchedStatusesWindow: Collects previous matched statuses up to the current row.
      *       • runningBalanceWindow: Calculates running sum of local amounts within each partition.
      *       • groupedRowWindowForUdf: Collects all relevant columns as a list of structs for UDFs.
      *
      *   2. Combine DataFrames:
      *       • Unites booking and settlement DataFrames (by name) for joint processing.
      *      Assign Row Numbers:
      *       • Adds a sequential row number within each (booking_id, currency) partition.
      *
      *   3. Calculate Running Local Balance:
      *       • Computes a running sum of local_total_amount_inc_gst for each partition.
      *
      *   4. Determine Matched Status:
      *       • Assigns a matched status ("MATCHED", "MATCHED_UNDER", "MATCHED_OVER", "NOT_APPLICABLE")
      *         based on the running balance and source.
      *
      *   5. Collect Previous Matched Statuses:
      *       • Gathers all previous matched statuses up to the current row for each partition.
      *
      *   6. Prepare Grouped Rows for UDF:
      *       • Collects all relevant columns into a list of structs for each partition, to be used
      *         in downstream UDFs or further processing.
      *
      * Purpose:
      *   - Enables detailed, row-level tracking and reconciliation of bookings and settlements,
      *     to support and generate calculated USD values.
      * *******************************************************************************
      */

    // 1. Window Definitions
    val rowNumWindow = Window
      .partitionBy("booking_id", "booking_settlement_currency")
      .orderBy(col("source"), col("transaction_date"))

    val sumExpectedWindow = Window
      .partitionBy("booking_id", "source", "row_num")

    val previousMatchedStatusesWindow = Window
      .partitionBy("booking_id", "booking_settlement_currency")
      .orderBy(col("row_num"))
      .rowsBetween(Window.unboundedPreceding, -1) // upto previous row

    val runningBalanceWindow = Window
      .partitionBy("booking_id", "booking_settlement_currency")
      .orderBy(col("row_num"))

    // 2. Combine DataFrames and Assign Row Numbers
    val withRowNumCombinedDf = groupedFinancialTransactionsDf
      .unionByName(settlementsForMatchedDf)
      //.unionByName(settlementAdjustmentDf)
      .withColumn(
        "row_num",
        row_number().over(rowNumWindow)
      )

    // 3. Calculate Running Local Balance
    val withRunningBalanceOnCombinedDf = withRowNumCombinedDf
      .withColumn("running_local_balance", round(sum(col("local_total_amount_inc_gst")).over(runningBalanceWindow), 2))

    // 4. Determine Matched Status
    val transformedBookingAndSettlementCombined = withRunningBalanceOnCombinedDf
      .withColumns(
        Map(
          "matched_status" ->
            when(round(sum(col("running_local_balance")).over(sumExpectedWindow), 2) === 0 && col("source") === "SETTLEMENT", "MATCHED")
              .when(round(sum(col("running_local_balance")).over(sumExpectedWindow), 2) > 0 && col("source") === "SETTLEMENT", "MATCHED_UNDER")
              .when(round(sum(col("running_local_balance")).over(sumExpectedWindow), 2) < 0 && col("source") === "SETTLEMENT", "MATCHED_OVER")
              .otherwise("NOT_APPLICABLE")
        )
      )

    // 5. Collect Previous Matched Statuses
    val withPreviousMatchedStatusOnCombined = transformedBookingAndSettlementCombined
      .withColumn("previous_matched_statuses", collect_list(col("matched_status")).over(previousMatchedStatusesWindow))

    val groupedRowWindowForUdf = Window
      .partitionBy("booking_id", "booking_settlement_currency")
      .orderBy(col("source"), col("row_num"))

    //6. Prepare Grouped Rows for UDF
    val rowGenerationForUdfDF = withPreviousMatchedStatusOnCombined
      .withColumn(
        "grouped_rows_list",
        collect_list(
          struct(
            col("local_total_amount_inc_gst").cast("double").as("local_total_amount_inc_gst"),
            col("usd_total_amount_inc_gst").cast("double").as("usd_total_amount_inc_gst"),
            col("source"),
            col("running_local_balance").cast("double").as("running_local_balance"),
            col("matched_status"),
            col("previous_matched_statuses"),
            col("booking_settlement_ex_rate")
          )
        ).over(groupedRowWindowForUdf)
      )

    /** *******************************************************************************
      * DataFrame Transformation: USD Settlement Calculation and Assignment
      * -------------------------------------------------------------------------------
      * Description:
      *   - Applies a registered UDF to compute the USD settlement amount for each row,
      *     based on the grouped row context.
      *   - Assigns the calculated USD amount to each row using its row number as an index.
      *
      * Steps:
      *   1. Register UDF:
      *       • Ensures the custom UDF `toGetSettlementUsdValue` is available in the Spark session.
      *
      *   2. Generate USD Amount List:
      *       • Adds a column `calculated_usd_amount_list` by applying the UDF to the
      *         `grouped_rows_list` column, producing a list of calculated USD amounts
      *         for each group.
      *
      *   3. Assign USD Amount to Each Row:
      *       • Extracts the USD amount corresponding to the current row's position
      *         (using `row_num` as the index) from the calculated list.
      *       • Casts the result to DECIMAL(24,2) for consistency.
      *
      * Purpose:
      *   - Produces a DataFrame where each row has its corresponding calculated USD
      *     settlement amount.
      * *******************************************************************************
      */

    //1. Register UDF
    registerSettlementUsdCalculationUdf()(SQLContext.sparkSession)

    //2. Generate USD Amount List
    val usdListGeneratedDf = rowGenerationForUdfDF
      .withColumn("calculated_usd_amount_list", expr("toGetSettlementUsdValue(grouped_rows_list)"))

    //3. Assign USD Amount to Each Row
    val usdAssignedToGeneratedDf = usdListGeneratedDf
      .withColumn(
        "calculated_usd_amount",
        element_at(col("calculated_usd_amount_list"), col("row_num")).cast("decimal(24,2)").as("calculated_usd_amount")
      )

    logger.info("Completed prepareRatioIntermediateTableForUsdCalculation")

    usdAssignedToGeneratedDf.withColumn("datadate", lit(dataDate))

  }

}

object RatioIntermediateForSettlementUpcUsd {

  /** Static entry point for USD calculation.
    */
  def calculateUSD(
      financialTransactionsDf: DataFrame,
      settlementMatchedUnmatchedDf: DataFrame,
      settlementAdjustments: DataFrame,
      dataDate: Int
  )(implicit sqlContext: SQLContext): DataFrame =
    new RatioIntermediateForSettlementUpcUsd().prepareRatioIntermediateTableForUsdCalculation(
      financialTransactionsDf,
      settlementMatchedUnmatchedDf,
      settlementAdjustments,
      dataDate
    )

}
