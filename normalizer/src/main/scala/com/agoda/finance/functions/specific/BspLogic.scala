package com.agoda.finance.functions.specific

import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{Column, DataFrame, SQLContext}

object BspLogic extends LazyLogging {

  // 1. Extract PNR
  private def extractPnr(colName: String): Column = {
    val pnrBeforeSlash = regexp_extract(col(colName), """([A-Z0-9]{6})(?=/)""", 1)
    val pnrAfterSlash  = regexp_extract(col(colName), """/([A-Z0-9]{6})""", 1)
    when(length(pnrBeforeSlash) > 0, pnrBeforeSlash).otherwise(pnrAfterSlash)
  }

  // 2. Calculate Tax Amount
  private def calculateTaxAmount(
      col1: String = "tax_miscellaneous_fee_amount_1",
      col2: String = "tax_miscellaneous_fee_amount_2",
      col3: String = "tax_miscellaneous_fee_amount_3"
  ): Column =
    coalesce(col(col1), lit(0)) +
      coalesce(col(col2), lit(0)) +
      coalesce(col(col3), lit(0))

  // 3. Add PNR Column
  private def withPnr(df: DataFrame, srcCol: String = "pnr_reference_and_or_airline_data", outCol: String = "pnr_extracted"): DataFrame =
    df.withColumn(outCol, extractPnr(srcCol))

  // 4. Add Tax Column
  private def withTax(df: DataFrame, outCol: String = "tax_amount"): DataFrame =
    df.withColumn(outCol, calculateTaxAmount())

  // 5. Aggregation
  private def aggregateBsp(df: DataFrame): DataFrame =
    df.filter(col("ticket_document_number").isNotNull)
      .groupBy(
        col("transaction_number"),
        col("ticket_document_number"),
        col("file_id"),
        col("date_of_issue"),
        col("reporting_date"),
        col("datadate")
      )
      .agg(
        max(col("pnr_extracted")).as("pnr"),
        max(col("transaction_code")).as("transaction_code"),
        sum(col("commission_amount")).as("commission_amount"),
        sum(col("commissionable_amount")).as("cost_amount"),
        sum(col("net_fare_amount")).as("net_fare_amount"),
        sum(col("tax_amount")).as("tax_amount"),
        sum(col("ticket_document_amount")).as("ticket_document_amount"),
        max(expr("substring(currency_type, 1, length(currency_type)-1)")).as("currency_type"),
        md5(concat_ws(",", collect_list(col("hash_key")))).as("hash_key")
      )
      .filter(col("transaction_code").isNotNull)

  // 6. Unique TKTT
  private def uniqueTktt(df: DataFrame): DataFrame =
    df
      .filter(col("transaction_code") === "TKTT")
      .groupBy(col("ticket_document_number"))
      .agg(
        max(col("pnr_reference_and_or_airline_data")).as("pnr"),
        count(lit(1)).as("cnt")
      )
      .filter(col("cnt") === 1)
      .select(col("ticket_document_number"), col("pnr"))

  // 7. Final Join
  private def joinAggregatedWithTktt(agg: DataFrame, tktt: DataFrame): DataFrame =
    agg
      .alias("a")
      .join(tktt.alias("u"), Seq("ticket_document_number"), "left")
      .select(
        col("a.transaction_number"),
        col("a.ticket_document_number"),
        col("a.date_of_issue"),
        col("a.reporting_date"),
        col("a.datadate"),
        when(
          col("a.transaction_code").isin("RFND", "CANX") && col("a.pnr").isNull,
          col("u.pnr")
        ).otherwise(col("a.pnr")).as("pnr"),
        col("a.transaction_code"),
        col("a.commission_amount"),
        col("a.cost_amount"),
        col("a.net_fare_amount"),
        col("a.tax_amount"),
        col("a.ticket_document_amount"),
        col("a.currency_type"),
        col("a.file_id"),
        col("a.hash_key")
      )

  // 8. Compose the pipeline
  def normalizeBspRawData(df: DataFrame)(implicit sqlContext: SQLContext): DataFrame = {
    val dfWithPnr = withPnr(df)
    val dfWithTax = withTax(dfWithPnr)
    val agg       = aggregateBsp(dfWithTax)
    val tkttHistoricalData = sqlContext.sql(
      """
      SELECT * FROM finance_sftp_downloader_staging.holiday_tour_iata_daily
      WHERE datadate >= date_format(date_sub(current_date(), 90), 'yyyyMMdd')
      AND datadate < date_format(current_date(), 'yyyyMMdd')
      """
    )
    val tktt = uniqueTktt(tkttHistoricalData)
    joinAggregatedWithTktt(agg, tktt)
  }

}
