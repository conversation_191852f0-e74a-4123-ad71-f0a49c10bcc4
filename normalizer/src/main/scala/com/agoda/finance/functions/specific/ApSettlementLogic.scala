package com.agoda.finance.functions.specific

import com.agoda.finance.platform.enums.ProductType
import org.apache.spark.sql._
import org.apache.spark.sql.expressions._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._

import java.util.UUID
import scala.language.postfixOps
import scala.util._

object ApSettlementLogic {
  private final val DefaultDecimalType = DecimalType(24, 2)

  private val additionalCurrencyCode: Map[String, String] = Map(
    "157" -> "CNH"
  )
  private def getTTKCurrency: UserDefinedFunction = udf { (numericCode: String, currencyCode: String) =>
    numericCode match {
      case n if n != null && n.nonEmpty && additionalCurrencyCode.contains(n.trim) => additionalCurrencyCode.getOrElse(n.trim, null)
      case _                                                                       => currencyCode
    }
  }
  case class TransactionNarrative(
      merchantCountry: String,
      merchName: String,
      merchAddress: String,
      merchCity: String,
      merchPostCode: String
  )

  /** Parses the transaction narrative string and extracts merchant details.
    *
    * @param transactionNarrative The transaction narrative string to parse.
    * @return A TransactionNarrative object containing parsed details.
    */
  private def parseTransactionNarrative(transactionNarrative: String): TransactionNarrative = {
    // Split the transaction narrative string using "\\" as the delimiter
    val merchantValue: Array[String] = Option(transactionNarrative)
      .map(_.split("\\\\"))
      .getOrElse(Array.empty[String])

    if (merchantValue.length >= 4) {
      val merchantCountry = merchantValue(3).replaceAll("\\[.*]", "").trim.takeRight(3)
      val merchName       = merchantValue(0)
      val merchAddress    = merchantValue(1)
      val merchCity       = merchantValue(2)
      val merchPostCode   = merchantValue(3).split(" ").headOption.getOrElse("")

      TransactionNarrative(merchantCountry, merchName, merchAddress, merchCity, merchPostCode)
    } else {
      TransactionNarrative("", "", "", "", "")
    }
  }

  def wexReportDate(): Column = regexp_extract(col("file_name"), "(\\d{8})", 0).cast(StringType)

  def enettReportDate(): Column = regexp_extract(col("file_name"), "(\\d{8})$", 0).cast(StringType)

  def ttkReportDate(): Column = regexp_extract(col("file_name"), "(\\d{4}\\d{2}\\d{2})", 0).cast(StringType)

  def ttkReportDateTimestamp(): Column = to_timestamp(ttkReportDate(), "yyyyMMdd").cast(TimestampType)

  def ttkReportDateMinusOneTimestamp(): Column = date_sub(ttkReportDateTimestamp(), 1).cast(TimestampType)

  def checkoutReportDate(): Column =
    regexp_replace(date_add(col("settled_on").cast(TimestampType), 1), "^(\\d{4})-(\\d{2})-(\\d{2}).*$", "$1$2$3").cast(IntegerType)

  def checkoutSettledOnDate(): Column =
    regexp_replace(col("settled_on").cast(TimestampType), "^(\\d{4})-(\\d{2})-(\\d{2}).*$", "$1$2$3").cast(IntegerType)

  def checkOutTransactionDateTimestamp(): Column = to_timestamp(col("settled_on")).cast(TimestampType)

  def checkoutDatePlusOneTimestamp(): Column = date_add(to_timestamp(col("settled_on").cast(TimestampType), "yyyyMMdd"), 1).cast(TimestampType)

  def appendUpcAdditionalInfo(
      settlementDf: DataFrame,
      upcInfoRawDf: DataFrame,
      identifierKey: String,
      appendCampaignDetails: Option[(DataFrame, (DataFrame, DataFrame) => DataFrame)] = None
  ): DataFrame = {
    def extractAdditionalInfo: UserDefinedFunction = udf { (upcMetaData: String, extractKey: String, defaultValue: String) =>
      if (upcMetaData != null) {
        val mapPattern = "^Map\\((.*)\\)".r
        val extractedRaw = mapPattern.findFirstIn(upcMetaData) match {
          case Some(patternMatched) =>
            patternMatched
              .stripPrefix("Map(")
              .stripSuffix(")")
              .split(",")
              .map {
                _.split("->")
              }
              .toSeq
              .filter(_.length == 2)
              .map(p => p(0).trim -> p(1).trim)
              .toMap
          case _ => Map[String, String]()
        }
        extractedRaw.getOrElse(extractKey, defaultValue)
      } else { defaultValue }
    }

    def validUUID: UserDefinedFunction = udf { (id: String) =>
      Try(UUID.fromString(id)) match {
        case Success(_) => 1
        case Failure(_) => 0
      }
    }

    def toMap(text: String): Map[String, String] =
      Try {
        text
          .split("&")
          .map { pair =>
            val Array(key, value) = pair.split("=")
            (key.trim.toUpperCase, value.trim)
          }
          .toMap
      } match {
        case Success(m) => m
        case Failure(_) => Map()
      }

    def extract(default: String, keys: String*): UserDefinedFunction = udf { (text: String) =>
      val map = toMap(text)
      keys.flatMap(k => map.get(k)).headOption.getOrElse(default)
    }

    def mapProductTypeToText: UserDefinedFunction = udf { (productType: String) =>
      (productType match {
        case "5" => ProductType.FLIGHT
        case _   => ProductType.HOTEL
      }).toString
    }

    val partitionLength = 3

    val settlementWithUpcInfoDf = settlementDf
      .withColumn("isUUID", validUUID(col(identifierKey)))
      .join(
        upcInfoRawDf.withColumnRenamed("datadate", "upc_datadate"),
        substring(settlementDf(identifierKey), 0, partitionLength) === upcInfoRawDf("partition_key")
          && settlementDf(identifierKey) === upcInfoRawDf("cardidentifier"),
        "left"
      )
      .withColumn(
        "row_number",
        row_number.over(
          Window
            .partitionBy(identifierKey, "hash_key")
            .orderBy(col("logtime").desc)
        )
      )
      .where(col("row_number") === 1)
      .drop("row_number")
      .withColumns(
        Map(
          "booking_id" ->
            when(col("isUUID") === 1, extractAdditionalInfo(upcInfoRawDf("metadata"), lit("bookingId"), lit("0")))
              .otherwise(extract("0", "BOOKINGID")(col(identifierKey))),
          "batch_id" ->
            when(col("isUUID") === 1, extractAdditionalInfo(upcInfoRawDf("metadata"), lit("batchId"), lit("0")))
              .otherwise(extract("0", "BATCHID")(col(identifierKey))),
          "sub_supplier_id" ->
            when(col("isUUID") === 1, extractAdditionalInfo(upcInfoRawDf("metadata"), lit("hotelId"), lit("0")))
              .otherwise(extract("0", "MERCHANTID", "BUSINESSID")(col(identifierKey))),
          "dmc_id" ->
            when(col("isUUID") === 1, extractAdditionalInfo(upcInfoRawDf("metadata"), lit("dmcId"), lit("0")))
              .otherwise(extract("0", "DMCID")(col(identifierKey)))
        ).++(
          appendCampaignDetails.map(_ =>
            "provider_campaign_reference" ->
              when(col("isUUID") === 1, extractAdditionalInfo(upcInfoRawDf("metadata"), lit("providerCampaignReference"), lit(null)))
          )
        )
      )
      .withColumns(
        Map(
          "booking_id" -> // vehicle booking in messaging, sub_supplier_id=999, booking_id is in businessId
            when(col("sub_supplier_id") =!= "999", col("booking_id"))
              .when(
                col("isUUID") === 1,
                coalesce(
                  extractAdditionalInfo(upcInfoRawDf("metadata"), lit("businessId"), lit(null)),
                  extractAdditionalInfo(upcInfoRawDf("metadata"), lit("bookingId"), lit("0"))
                )
              )
              .otherwise(extract("0", "BOOKINGID")(col(identifierKey))),
          "product_type" -> // vehicle booking in messaging, sub_supplier_id=999
            when(col("sub_supplier_id") === "999", lit(ProductType.VEHICLE.toString))
              .when(col("isUUID") === 1, mapProductTypeToText(extractAdditionalInfo(upcInfoRawDf("metadata"), lit("productType"), lit("0"))))
              .otherwise(lit(ProductType.HOTEL.toString))
        )
      )

    (appendCampaignDetails match {
      case Some((campaignDf, joinCampaignDf)) => joinCampaignDf(settlementWithUpcInfoDf, campaignDf)
      case None                               => settlementWithUpcInfoDf
    }).drop("cardidentifier", "metadata", "logtime", "upc_datadate", "partition_key", "isUUID")
  }

  def appendPtyCampaignDetails(settlementdf: DataFrame, campaignDetails: DataFrame): DataFrame = {
    val extractCampaignName = udf { (fileId: String) =>
      val pos = fileId.indexOf("DailySettlements")
      if (pos != -1) fileId.substring(0, pos) else fileId
    }

    val resultDf = settlementdf
      .join(
        broadcast(campaignDetails),
        settlementdf("provider_campaign_reference") === campaignDetails("provider_campaign_reference")
          || lower(extractCampaignName(settlementdf("file_name"))) === lower(
            regexp_replace(campaignDetails("campaign_name"), "[ _]", "")
          ), //fallback logic to join using campaignName from file
        "left"
      )

    resultDf.select(
      settlementdf("*"),
      campaignDetails("product_name"),
      campaignDetails("provider_campaign_reference") as "provider_card_classification_id"
    )
  }

  def appendCheckoutCampaignDetails(settlementdf: DataFrame, campaignDetails: DataFrame): DataFrame =
    settlementdf
      .join(
        broadcast(campaignDetails),
        settlementdf("provider_campaign_reference") === campaignDetails("provider_campaign_reference"),
        "left"
      )
      .select(
        settlementdf("*"),
        campaignDetails("product_name"),
        campaignDetails("provider_campaign_reference") as "provider_card_classification_id"
      )
      .withColumns(
        Map(
          "product_name" ->
            when(col("product_name").isNotNull, col("product_name"))
              .when(col("provider_card_classification_id").isNull, lit("MTA"))
              .otherwise(lit(null))
        )
      )

  def appendWexAdditionalInfo(
      settlementDf: DataFrame,
      hotelHistory: DataFrame,
      exchangeRates: DataFrame
  ): DataFrame = {
    val wexDf =
      settlementDf
        .withColumn("sub_supplier_id", coalesce(col("hotel_id"), lit(0)).cast(LongType))
        .withColumn("transaction_datetime", to_timestamp(col("transaction_date"), "MM/dd/yyyy").cast(TimestampType))
        .withColumn("posting_datetime", to_timestamp(col("posting_date"), "MM/dd/yyyy").cast(TimestampType))
        .withColumn("report_date", wexReportDate().cast(IntegerType))
        .withColumn(
          "product_type",
          when(col("hotel_id") === 999, lit("VEHICLE"))
            .otherwise("HOTEL")
        ) // discussed with Tactical team TODO: to support new product type, please re-verify this logic

    calculateRateCurrencyAmounts(wexDf, hotelHistory, exchangeRates)(
      transactionDate = wexDf("transaction_datetime"),
      postingDate = wexDf("posting_datetime"),
      hotelId = wexDf("sub_supplier_id"),
      settlementCurrency = correctCNY(wexDf("source_currency")),
      settlementAmount = wexDf("source_amount"),
      postingCurrency = correctCNY(wexDf("billing_currency")),
      postingAmount = wexDf("transaction_amount")
    ).checkValidTransaction()
  }

  def appendEnettAdditionalInfo(
      settlementDf: DataFrame,
      upcInfoRawDf: DataFrame,
      hotelHistory: DataFrame,
      exchangeRates: DataFrame
  ): DataFrame = {
    val enettSettlementDf = settlementDf
      .withColumn("enett_hotel_id", coalesce(col("hotel_id"), lit(0)).cast(StringType))
      .withColumn("enett_dmc_id", coalesce(col("dmc_id"), lit(0)).cast(StringType))
      .withColumn("enett_booking_id", coalesce(col("booking_id")).cast(StringType))
      .withColumn("enett_batch_id", coalesce(col("batch_id"), lit("0")).cast(StringType))
      .withColumn("report_date", enettReportDate().cast(IntegerType))
    val enettDf = appendUpcAdditionalInfo(enettSettlementDf, upcInfoRawDf, "integrator_reference")
      .withColumn("sub_supplier_id", when(col("sub_supplier_id") === "0", col("enett_hotel_id")).otherwise(col("sub_supplier_id")))
      .withColumn("dmc_id", when(col("dmc_id") === "0", col("enett_dmc_id")).otherwise(col("dmc_id")))
      .withColumn("booking_id", when(col("booking_id") === "0", col("enett_booking_id")).otherwise(col("booking_id")))
      .withColumn("batch_id", when(col("batch_id") === "0", col("enett_batch_id")).otherwise(col("batch_id")))
      .withColumn("sub_supplier_id", col("sub_supplier_id").cast(LongType))
      .withColumn("transaction_datetime", to_timestamp(col("settlement_date"), "yyyy-MM-dd HH:mm:ss:SSS").cast(TimestampType))
      .withColumn("posting_datetime", to_timestamp(col("postdate"), "yyyy-MM-dd HH:mm:ss:SSS").cast(TimestampType))
    calculateRateCurrencyAmounts(enettDf, hotelHistory, exchangeRates)(
      transactionDate = enettDf("transaction_datetime"),
      postingDate = enettDf("posting_datetime"),
      hotelId = enettDf("sub_supplier_id"),
      settlementCurrency = correctCNY(enettDf("trans_currency_code")),
      settlementAmount = enettDf("trans_amount"),
      postingCurrency = correctCNY(enettDf("post_currency_code")),
      postingAmount = enettDf("post_amount")
    ).checkValidTransaction()
  }

  def appendTutukaAdditionalInfo(
      settlementDf: DataFrame,
      upcInfoRawDf: DataFrame,
      currenciesDf: DataFrame,
      hotelHistory: DataFrame,
      exchangeRates: DataFrame,
      campaignDetails: DataFrame
  ): DataFrame = {
    val upcEnrichedCampaignDetails =
      appendUpcAdditionalInfo(
        settlementDf,
        upcInfoRawDf,
        "wallet_reference",
        Some((campaignDetails, appendPtyCampaignDetails))
      )
    val vcardDf = manageTutukaAmounts(settlementDf = appendTutukaCurrencyCode(upcEnrichedCampaignDetails, currenciesDf))
      .withColumn("sub_supplier_id", col("sub_supplier_id").cast(LongType))
      .withColumn("transaction_datetime", ttkReportDateMinusOneTimestamp())
      .withColumn("posting_datetime", ttkReportDateTimestamp())
      .withColumn("report_date", ttkReportDate().cast(IntegerType))

    // --- Merchant parsing logic here ---
    val parseTransactionNarrativeUDF = udf { (transactionNarrative: String) =>
      if (transactionNarrative == null || transactionNarrative.trim.isEmpty) {
        Map(
          "merchant_country"   -> null,
          "merchant_name"      -> null,
          "merchant_address"   -> null,
          "merchant_city"      -> null,
          "merchant_post_code" -> null
        )
      } else {
        val parsed = parseTransactionNarrative(transactionNarrative)
        Map(
          "merchant_country"   -> parsed.merchantCountry,
          "merchant_name"      -> parsed.merchName,
          "merchant_address"   -> parsed.merchAddress,
          "merchant_city"      -> parsed.merchCity,
          "merchant_post_code" -> parsed.merchPostCode
        )
      }
    }

    val vcardWithMerchant = vcardDf
      .withColumn("parsed_transaction_narrative", parseTransactionNarrativeUDF(col("transaction_narrative")))
      .withColumn("merchant_country", col("parsed_transaction_narrative")("merchant_country"))
      .withColumn("merchant_name", col("parsed_transaction_narrative")("merchant_name"))
      .withColumn("merchant_address", col("parsed_transaction_narrative")("merchant_address"))
      .withColumn("merchant_city", col("parsed_transaction_narrative")("merchant_city"))
      .withColumn("merchant_post_code", col("parsed_transaction_narrative")("merchant_post_code"))
      .drop("parsed_transaction_narrative")
    // --- End merchant parsing logic ---

    calculateRateCurrencyAmounts(vcardWithMerchant, hotelHistory, exchangeRates)(
      transactionDate = vcardDf("transaction_datetime"),
      postingDate = vcardDf("posting_datetime"),
      hotelId = vcardDf("sub_supplier_id"),
      settlementCurrency = correctCNY(vcardDf("cardholder_currency_code")),
      settlementAmount = vcardDf("transaction_cardholder_amount"),
      postingCurrency = correctCNY(vcardDf("cardholder_currency_code")),
      postingAmount = vcardDf("transaction_cardholder_amount")
    ).checkValidTransaction()
  }

  def appendCheckoutAdditionalInfo(
      settlementDf: DataFrame,
      upcInfoRawDf: DataFrame,
      hotelHistory: DataFrame,
      exchangeRates: DataFrame,
      campaignDetails: DataFrame
  ): DataFrame = {
    val upcEnrichedCampaignDetails =
      appendUpcAdditionalInfo(settlementDf, upcInfoRawDf, "card_reference", Some((campaignDetails, appendCheckoutCampaignDetails)))
    val upcDf = upcEnrichedCampaignDetails
      .withColumn("sub_supplier_id", col("sub_supplier_id").cast(LongType))
      .withColumn("report_date", checkoutReportDate())
      .withColumn("transaction_datetime", checkOutTransactionDateTimestamp())
      .withColumn("posting_datetime", checkoutDatePlusOneTimestamp())
      .withColumn("reconciliation_amount", col("reconciliation_amount").cast(DoubleType))
      .withColumn("transaction_description", getCheckoutTransactionDescription(col("message_type"), col("transaction_type")))
      .withColumn("settled_on", checkoutSettledOnDate())

    calculateRateCurrencyAmounts(upcDf, hotelHistory, exchangeRates)(
      transactionDate = upcDf("transaction_datetime"),
      postingDate = upcDf("posting_datetime"),
      hotelId = upcDf("sub_supplier_id"),
      settlementCurrency = correctCNY(upcDf("billing_currency")),
      settlementAmount = upcDf("billing_amount"),
      postingCurrency = correctCNY(upcDf("billing_currency")),
      postingAmount = upcDf("billing_amount")
    ).checkValidTransaction()
  }

  def appendApprovalExchangeRates(settlementDf: DataFrame, exchangeRates: DataFrame): DataFrame =
    addExchangeRates(settlementDf, exchangeRates)(
      rateCurrency = col("local_currency"),
      settlementCurrency = correctCNY(col("local_currency")),
      transactionDate = col("transaction_date"),
      postingCurrency = correctCNY(col("local_currency")),
      postingDate = col("transaction_date")
    )
      .withColumn("report_date", col("datadate").cast(IntegerType))

  def getTutukaSourceTransactionCode: UserDefinedFunction = udf { (transactionId: String, trackingNumber: String, transactionDescription: String) =>
    val shortenDescription = transactionDescription match {
      case "DEDUCT"                   => "DD"
      case "LOAD"                     => "LD"
      case "LOAD REVERSAL"            => "LR"
      case "REVERSAL"                 => "RV"
      case "CHARGEBACK"               => "CB"
      case "CHARGEBACK REVERSED"      => "CR"
      case "CHARGEBACK REJECTED"      => "CRJ"
      case "2ND PRESENTMENT"          => "SP"
      case "2ND PRESENTMENT REVERSAL" => "SR"
      case "ARBITRATION"              => "AR"
      case "PRE ARBITRATION"          => "PA"
    }
    transactionId + "_" + trackingNumber + "_" + shortenDescription
  }

  def getCheckoutTransactionDescription: UserDefinedFunction = udf { (messageType: String, transactionType: String) =>
    (messageType, transactionType) match {
      case ("presentment", "purchase")           => "DEDUCT"
      case ("presentment_reversal", "purchase")  => "REVERSAL"
      case ("chargeback", _)                     => "CHARGEBACK"
      case ("chargeback_reversal", _)            => "CHARGEBACK REVERSED"
      case ("representment", "purchase")         => "2ND PRESENTMENT"
      case ("pre_arbitration_or_arbitration", _) => "PRE ARBITRATION"
      case ("presentment", "refund")             => "LOAD"
      case ("presentment_reversal", "refund")    => "LOAD REVERSAL"
      case ("representment_reversal", _)         => "2ND PRESENTMENT REVERSAL"
    }
  }

  private def getPaymentMethod(bookingId: String, approvalId: String, isValidTransaction: Boolean): (Int, String) =
    (Option(bookingId).getOrElse("0").trim, Option(approvalId).getOrElse("0").trim, isValidTransaction) match {
      case ("0", "0", true) => (2, "PlasticCard")
      case ("0", _, true)   => (5, "UpcOnEPass")
      case (_, _, true)     => (3, "UniversalPlasticCard")
      case (_, _, false)    => (-1, "Unknown") // Catch fail to join from upcInfoRawDf (No record from left join)
    }

  def getPaymentMethodId: UserDefinedFunction = udf { (bookingId: String, approvalId: String, isValidTransaction: Boolean) =>
    getPaymentMethod(bookingId, approvalId, isValidTransaction)._1
  }

  def getPaymentMethodName: UserDefinedFunction = udf { (bookingId: String, approvalId: String, isValidTransaction: Boolean) =>
    getPaymentMethod(bookingId, approvalId, isValidTransaction)._2
  }

  def appendTutukaCurrencyCode(settlementDf: DataFrame, currenciesDf: DataFrame): DataFrame =
    settlementDf
      .join(
        broadcast(currenciesDf.as("SCCY")),
        settlementDf("settlement_currency") === col("SCCY.numeric_code"),
        "left"
      )
      .withColumn("settlement_currency_code", getTTKCurrency(settlementDf("settlement_currency"), col("SCCY.currency_code")))
      .drop(col("SCCY.numeric_code"))
      .drop(col("SCCY.currency_code"))
      .drop(col("settlement_currency"))
      .join(
        broadcast(currenciesDf.as("PCCY")),
        settlementDf("cardholder_currency") === col("PCCY.numeric_code"),
        "left"
      )
      .withColumn("cardholder_currency_code", getTTKCurrency(settlementDf("cardholder_currency"), col("PCCY.currency_code")))
      .drop(col("PCCY.numeric_code"))
      .drop(col("PCCY.currency_code"))

  def manageTutukaAmounts(settlementDf: DataFrame): DataFrame = {
    val revSignDescription =
      List("LOAD", "REVERSAL", "CHARGEBACK", "2ND PRESENTMENT REVERSAL", "PRE ARBITRATION", "ARBITRATION")

    settlementDf
      .withColumn(
        "transaction_settlement_amount",
        when(col("transaction_description").isin(revSignDescription: _*), negate(col("transaction_settlement_amount")))
          .otherwise(col("transaction_settlement_amount"))
      )
      .withColumn(
        "transaction_cardholder_amount",
        when(col("transaction_description").isin(revSignDescription: _*), negate(col("transaction_cardholder_amount")))
          .otherwise(col("transaction_cardholder_amount"))
      )
  }

  private def addExchangeRates(
      data: DataFrame,
      exchangeRates: DataFrame
  )(rateCurrency: Column, settlementCurrency: Column, transactionDate: Column, postingCurrency: Column, postingDate: Column): DataFrame =
    data
      .withColumn("ex_transaction_datadate", date_format(transactionDate.cast(TimestampType), "yyyyMMdd").cast(StringType))
      .withColumn("ex_posting_datadate", date_format(postingDate.cast(TimestampType), "yyyyMMdd").cast(StringType))
      .join(
        broadcast(exchangeRates.withColumn("datadate", col("datadate").cast(IntegerType)).as("REX")),
        rateCurrency === col("REX.currency_code") && col("ex_transaction_datadate") === col("REX.datadate"),
        "left"
      )
      .join(
        broadcast(exchangeRates.withColumn("datadate", col("datadate").cast(IntegerType)).as("SEX")),
        settlementCurrency === col("SEX.currency_code") && col("ex_transaction_datadate") === col("SEX.datadate"),
        "left"
      )
      .join(
        broadcast(exchangeRates.withColumn("datadate", col("datadate").cast(IntegerType)).as("PEX")),
        postingCurrency === col("PEX.currency_code") && col("ex_posting_datadate") === col("PEX.datadate"),
        "left"
      )
      .withColumn("rate_ex", col("REX.real_exchange_rate"))
      .withColumn("settlement_ex", col("SEX.real_exchange_rate"))
      .withColumn("posting_ex", col("PEX.real_exchange_rate"))
      .select(
        data("*"),
        col("rate_ex"),
        col("settlement_ex"),
        col("posting_ex")
      )

  private def calculateRateCurrencyAmounts(
      settlementDf: DataFrame,
      hotelHistory: DataFrame,
      exchangeRates: DataFrame
  )(
      transactionDate: Column,
      postingDate: Column,
      hotelId: Column,
      settlementCurrency: Column,
      settlementAmount: Column,
      postingCurrency: Column,
      postingAmount: Column
  ): DataFrame = {
    val settlementWithRateCcyDf = settlementDf
      .withColumn("uuid", expr("uuid()"))
      .withColumn("transaction_datadate", date_format(transactionDate, "yyyyMMdd").cast(IntegerType))
      .join(hotelHistory.as("HH"), hotelId === col("HH.hotel_id") && col("HH.datadate") === col("transaction_datadate"), "left")
      .withColumn(
        "rate_currency",
        when(col("product_type").isin("HOTEL", "VEHICLE"), col("HH.rate_currency_code"))
          .otherwise(lit(settlementCurrency))
      ) // discussed with Tactical team TODO: to support new product type, please re-verify this logic

    addExchangeRates(settlementWithRateCcyDf, exchangeRates)(
      rateCurrency = col("rate_currency"),
      settlementCurrency = settlementCurrency,
      transactionDate = transactionDate,
      postingCurrency = postingCurrency,
      postingDate = postingDate
    )
      .withColumn("original_settlement_currency", settlementCurrency)
      .withColumn("settlement_currency", col("rate_currency"))
      .withColumn("original_settlement_amount", settlementAmount)
      .withColumn(
        "settlement_amount",
        when(col("rate_ex").isNull, lit(null).cast(DoubleType))
          .when(col("rate_ex") === 0.00, lit(null).cast(DoubleType))
          .otherwise(
            coalesce(settlementAmount * col("settlement_ex"), postingAmount * col("posting_ex")) / col("rate_ex")
          )
      )
      .withColumn(
        "settlement_amount_usd",
        coalesce(settlementAmount * col("settlement_ex"), postingAmount * col("posting_ex"))
      )
      .select(
        settlementDf("*"),
        col("uuid"),
        col("original_settlement_currency"),
        col("settlement_currency"),
        col("original_settlement_amount"),
        col("settlement_amount"),
        col("settlement_amount_usd"),
        col("rate_currency"),
        col("rate_ex"),
        col("settlement_ex"),
        col("posting_ex")
      )
  }

  def correctCNY(currency: Column): Column =
    when(upper(trim(currency)).isin("CNH", "RMB"), lit("CNY")).otherwise(currency)

  implicit class ColumnExt(column: Column) {
    def isNullOrEqual(value: String): Column = coalesce(column.cast(StringType), lit("0")) === value
  }

  implicit class DataFrameExt(df: DataFrame) {
    def checkValidTransaction(
        bookingId: Column = col("booking_id"),
        approvalId: Column = col("batch_id"),
        subSupplierId: Column = col("sub_supplier_id")
    ): DataFrame = df.withColumn(
      "is_valid_transaction",
      not(bookingId.isNullOrEqual("0") && approvalId.isNullOrEqual("0") && subSupplierId.isNullOrEqual("0"))
    )
  }
}
