package com.agoda.finance.functions.specific.ApSettlementRatioUpcLogic

import org.apache.spark.sql.functions.{col, trim, when}

object ApSettlementErrorCheckerFactory {

  def settlementChecker: ApSettlementDataFrameChecker = {
    val errorCondition =
      col("booking_id").isNotNull &&
        (
          col("original_settlement_currency").isNull ||
            col("settlement_currency").isNull ||
            col("original_settlement_amount").isNull || trim(col("original_settlement_amount")) === "" ||
            col("settlement_amount").isNull || trim(col("settlement_amount")) === "" ||
            col("transaction_date").isNull || trim(col("transaction_date")) === ""
        )

    val errorReasonCol =
      when(col("original_settlement_currency").isNull, "original_settlement_currency is null")
        .when(col("settlement_currency").isNull, "settlement_currency is null")
        .when(
          col("original_settlement_amount").isNull || trim(col("original_settlement_amount")) === "",
          "original_settlement_amount is null or empty"
        )
        .when(col("settlement_amount").isNull || trim(col("settlement_amount")) === "", "settlement_amount is null or empty")
        .when(col("transaction_date").isNull || trim(col("transaction_date")) === "", "transaction_date is null or empty")
        .otherwise("Unknown error")

    new ApSettlementGenericErrorChecker(errorCondition, errorReasonCol)
  }

  def bookingUniverseChecker: ApSettlementDataFrameChecker = {

    val nullOrEmptyCondition =
      col("booking_id").isNull ||
        col("supplier_currency").isNull || trim(col("supplier_currency")) === "" ||
        col("local_total_amount_inc_gst").isNull || trim(col("local_total_amount_inc_gst").cast("string")) === "" ||
        col("usd_total_amount_inc_gst").isNull || trim(col("usd_total_amount_inc_gst").cast("string")) === ""

    val nullOrEmptyReason =
      when(col("booking_id").isNull, "booking_id is null")
        .when(col("supplier_currency").isNull || trim(col("supplier_currency")) === "", "supplier_currency is null or empty")
        .when(
          col("local_total_amount_inc_gst").isNull || trim(col("local_total_amount_inc_gst").cast("string")) === "",
          "local_total_amount_inc_gst is null or empty"
        )
        .when(
          col("usd_total_amount_inc_gst").isNull || trim(col("usd_total_amount_inc_gst").cast("string")) === "",
          "usd_total_amount_inc_gst is null or empty"
        )
        .otherwise("Unknown null/empty error")

    new ApSettlementGenericErrorChecker(
      errorCondition = nullOrEmptyCondition,
      errorReasonCol = nullOrEmptyReason,
      multiCurrencyCheck = true,
      currencyCol = "supplier_currency",
      bookingIdCol = "booking_id",
      multiCurrencyErrorMsg = "booking_id has multiple currencies"
    )
  }

}
