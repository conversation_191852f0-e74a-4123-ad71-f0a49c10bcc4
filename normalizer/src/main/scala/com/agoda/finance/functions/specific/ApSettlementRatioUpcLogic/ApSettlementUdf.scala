package com.agoda.finance.functions.specific.ApSettlementRatioUpcLogic

import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions.udf
import org.apache.spark.sql.{Row, SparkSession}

trait ApSettlementUdf {

  /** Registers the UDF for calculating settlement USD values.
    * Makes the function available as "toGetSettlementUsdValue" in Spark SQL.
    */
  def registerSettlementUsdCalculationUdf()(implicit spark: SparkSession): Unit =
    spark.udf.register("toGetSettlementUsdValue", getSettlementUsdValue)

  private def getSettlementUsdValue: UserDefinedFunction = udf { (rows: Seq[Row]) =>
    /** Core logic for calculating the USD amount for a SETTLEMENT row.
      * - Uses running cumulative USD/local amounts and previous statuses to determine
      *   the correct allocation method.
      * - Handles each matching status with its own business logic.
      */
    def calculateSettlementUsdAmount(
        row: Row,
        cumulativeUsdAmt: Double,
        cumulativeLocalAmt: Double,
        firstMatchUnderBalance: Double
    ): (Double, Double, Double) = {
      val localTotalAmountIncGst = row.getAs[Double]("local_total_amount_inc_gst")
      val remainingBalance       = row.getAs[Double]("running_local_balance")
      val matchedStatus          = row.getAs[String]("matched_status")
      val previousStatus         = Option(row.getAs[Seq[String]]("previous_matched_statuses")).getOrElse(Seq.empty[String]).toList
      val exchangeRate           = row.getAs[Double]("booking_settlement_ex_rate")

      // Helper to avoid division by zero in ratio calculations
      def safeRatio(numerator: Double, denominator: Double): Double =
        if (denominator != 0.0) numerator / denominator else 0.0

      matchedStatus match {
        // If this is the first under-matched settlement, or if the cumulative local amount is nearly zero,
        // use the exchange rate directly. Otherwise, use the running ratio of USD/local to allocate.
        case "MATCHED_UNDER" =>
          val currentRatio = safeRatio(cumulativeUsdAmt, cumulativeLocalAmt)
          val finalUsdAmount =
            if (previousStatus.lastOption.contains("MATCHED") || math.abs(cumulativeLocalAmt) < 1) exchangeRate * localTotalAmountIncGst
            else currentRatio * localTotalAmountIncGst
          val newFirstMatchUnderBalance = if (matchedStatus == "MATCHED_UNDER") localTotalAmountIncGst else firstMatchUnderBalance
          (finalUsdAmount, newFirstMatchUnderBalance, localTotalAmountIncGst)

        // For a perfectly matched settlement, use the running ratio unless the cumulative local is nearly zero,
        // in which case use the exchange rate.
        case "MATCHED" =>
          val currentRatio              = safeRatio(cumulativeUsdAmt, cumulativeLocalAmt)
          val finalUsdAmount            = if (math.abs(cumulativeLocalAmt) < 1) exchangeRate * localTotalAmountIncGst else currentRatio * localTotalAmountIncGst
          val newFirstMatchUnderBalance = if (matchedStatus == "MATCHED_UNDER") localTotalAmountIncGst else firstMatchUnderBalance
          (finalUsdAmount, newFirstMatchUnderBalance, localTotalAmountIncGst)

        case "MATCHED_OVER" =>
          // For over-matched settlements, logic depends on the previous status:
          if (previousStatus.lastOption.exists(Set("MATCHED_UNDER", "NOT_APPLICABLE").contains)) {
            // If the previous was under-matched or not applicable, allocate the overage:
            // - Use the negative of the cumulative local * ratio (to offset previous allocation)
            // - Add the remaining balance at the current exchange rate
            val currentRatio          = safeRatio(cumulativeUsdAmt, cumulativeLocalAmt)
            val intermediate          = -(cumulativeLocalAmt * currentRatio)
            val additionalWithVatRate = remainingBalance * exchangeRate
            val finalUsdAmount        = if (math.abs(cumulativeLocalAmt) < 1) exchangeRate * localTotalAmountIncGst else intermediate + additionalWithVatRate
            (finalUsdAmount, firstMatchUnderBalance, localTotalAmountIncGst)

          } else if (previousStatus.lastOption.exists(Set("MATCHED", "MATCHED_OVER").contains)) {
            // If the previous was matched or over-matched, just use the exchange rate
            val finalUsdAmount = localTotalAmountIncGst * exchangeRate
            (finalUsdAmount, firstMatchUnderBalance, localTotalAmountIncGst)

          } else {
            // Default: use the exchange rate
            val finalUsdAmount = localTotalAmountIncGst * exchangeRate
            (finalUsdAmount, firstMatchUnderBalance, localTotalAmountIncGst)
          }
      }
    }

    rows
      .foldLeft((0.0, 0.0, 1.0, List.empty[Double])) { case ((cumulativeUsdAmt, cumulativeLocalAmt, firstMatchUnderBalance, usdAmountResults), row) =>
        val source = row.getAs[String]("source")
        source match {
          case "BOOKING" =>
            val usdTotalAmountIncGst   = Option(row.getAs[Double]("usd_total_amount_inc_gst")).getOrElse(0.0)
            val localTotalAmountIncGst = Option(row.getAs[Double]("local_total_amount_inc_gst")).getOrElse(0.0)
            (
              cumulativeUsdAmt + usdTotalAmountIncGst,
              cumulativeLocalAmt + localTotalAmountIncGst,
              firstMatchUnderBalance,
              usdAmountResults :+ usdTotalAmountIncGst
            )

          case "SETTLEMENT" =>
            val (finalUsdAmount, newFirstMatchUnderBalance, localTotalAmountIncGst) =
              calculateSettlementUsdAmount(row, cumulativeUsdAmt, cumulativeLocalAmt, firstMatchUnderBalance)
            (
              cumulativeUsdAmt + finalUsdAmount,
              cumulativeLocalAmt + localTotalAmountIncGst,
              newFirstMatchUnderBalance,
              usdAmountResults :+ finalUsdAmount
            )
        }
      }
      ._4
  }

}
