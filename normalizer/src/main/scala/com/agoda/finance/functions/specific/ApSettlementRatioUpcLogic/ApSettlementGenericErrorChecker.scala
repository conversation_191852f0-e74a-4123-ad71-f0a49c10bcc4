package com.agoda.finance.functions.specific.ApSettlementRatioUpcLogic

import com.agoda.finance.Main.spark
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{StringType, StructField, StructType}
import org.apache.spark.sql.{Column, DataFrame, Row}

class ApSettlementGenericErrorChecker(
    errorCondition: Column,
    errorReasonCol: Column,
    multiCurrencyCheck: Boolean = false,
    currencyCol: String = "supplier_currency",
    bookingIdCol: String = "booking_id",
    multiCurrencyErrorMsg: String = "booking_id has multiple currencies"
) extends ApSettlementDataFrameChecker {

  override def collectErrors(df: DataFrame, todaySettlementsUpc: DataFrame): DataFrame = {
    val rowLevelErrors = df
      .filter(errorCondition)
      .withColumn("error_reason", errorReasonCol)

    val allErrors = multiCurrencyCheck match {
      case true =>
        val multiCurrencyBookingIds = df
          .groupBy(bookingIdCol)
          .agg(countDistinct(currencyCol).as("currency_count"))
          .filter(col("currency_count") > 1)
          .select(bookingIdCol)
          .withColumn("error_reason", lit(multiCurrencyErrorMsg))

        val multiCurrencyErrors = df
          .join(multiCurrencyBookingIds, Seq(bookingIdCol), "inner")
          .select(df.columns.map(col) :+ col("error_reason"): _*)

        rowLevelErrors.unionByName(multiCurrencyErrors)
      case false =>
        rowLevelErrors
    }

    val errorSchema = StructType(
      df.schema.fields :+ StructField("error_reason", StringType, nullable = true)
    )

    val crossDfError: DataFrame = todaySettlementsUpc match {
      case settlements =>
        val factBookingRowCount                  = df.count()
        val todaySettlementsDistinctBookingCount = settlements.select("booking_id").distinct().count()
        if (factBookingRowCount != todaySettlementsDistinctBookingCount) {
          val errorMsg =
            s"Row count mismatch: factBookingData has $factBookingRowCount rows, but todaySettlementsUpc has $todaySettlementsDistinctBookingCount distinct booking_id values."
          val errorRow = Row.fromSeq(Seq.fill(df.columns.length)(null) :+ errorMsg)
          spark.createDataFrame(spark.sparkContext.parallelize(Seq(errorRow)), errorSchema)
        } else {
          spark.createDataFrame(spark.sparkContext.emptyRDD[Row], errorSchema)
        }
      case _ =>
        spark.createDataFrame(spark.sparkContext.emptyRDD[Row], errorSchema)
    }
    allErrors.unionByName(crossDfError)

  }
}
