package com.agoda.finance.functions.specific.PaymentGatewaySettlements

import com.agoda.finance.functions.utils.PaymentGatewaySettlementUtils
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions.udf
import scala.util.Try

object AlipayOspSettlementLogic extends LazyLogging {
  def getSourceAmount: UserDefinedFunction = udf { (transactionAmount: String, paymentMethodType: String) =>
    val amountNumeric = Try(transactionAmount.toDouble).getOrElse(0.0)

    // Formula: transaction_amount_value / 100
    val result = if (paymentMethodType.equalsIgnoreCase("TOSSPAY")) amountNumeric else amountNumeric / 100.0

    PaymentGatewaySettlementUtils.formatDoubleToString(result, 4)
  }

  def getFeeAmount: UserDefinedFunction = udf { (feeAmount: String) =>
    val amountNumeric = Try(feeAmount.toDouble).getOrElse(0.0)

    // Formula: fee_amount_value / 100 * -1
    val result = amountNumeric / 100.0 * -1.0

    PaymentGatewaySettlementUtils.formatDoubleToString(result, 4)
  }

  def getReceivedAmount: UserDefinedFunction = udf { (settlementAmount: String, feeAmount: String) =>
    val settlementAmountNumeric = Try(settlementAmount.toDouble).getOrElse(0.0)
    val feeAmountNumeric        = Try(feeAmount.toDouble).getOrElse(0.0)

    // Formula: settlement_amount_value / 100 + fee_amount_value / 100 * -1
    val result = (settlementAmountNumeric / 100.0) + (feeAmountNumeric / 100.0 * -1.0)

    PaymentGatewaySettlementUtils.formatDoubleToString(result, 4)
  }
}
