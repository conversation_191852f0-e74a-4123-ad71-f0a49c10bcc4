package com.agoda.finance.functions

import com.agoda.finance.functions.common._
import com.agoda.finance.functions.specific.APFifoLifoLogic.APSettlementFifoLifoLogic
import com.agoda.finance.functions.specific._
import com.agoda.finance.preprocess._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{Column, DataFrame, SQLContext}

import java.math.BigDecimal

object FunctionMapper {

  val FIRST   = 0
  val SECOND  = 1
  val THIRD   = 2
  val FOURTH  = 3
  val FIFTH   = 4
  val SIXTH   = 5
  val SEVENTH = 6

  /** This fieldsMapping function should use to handle each column */
  def fieldsMapping(
      functionName: Option[String],
      functionColumns: Option[List[String]],
      extraConstants: Option[List[String]],
      extraOperators: Option[List[String]],
      extraMultiConstants: Option[List[List[String]]]
  ): Option[Column] = {

    val columns: Vector[Column] = functionColumns.map(_.toVector).getOrElse(Vector.empty).map(col)
    val constants               = extraConstants.map(_.toVector).getOrElse(Vector.empty)

    genericFunction(functionName, constants, columns)
      .orElse(genericTextFunction(functionName, constants, columns, extraMultiConstants))
      .orElse(genericMathFunction(functionName, constants, columns, extraOperators, extraMultiConstants))
      .orElse(genericDateFunction(functionName, constants, columns))
      .orElse(supplierSpecificFunction(functionName, columns))
      .orElse(affiliateSpecificFunction(functionName, columns))
      .orElse(cashbackSpecificFunction(functionName, columns))
      .orElse(apSettlementSpecificFunction(functionName, columns))
      .orElse(genericXMLFunction(functionName, constants, columns))
      .orElse(genericJSONFunction(functionName, constants, columns))
      .orElse(summarySettlementSpecificFunction(functionName, columns))
      .orElse(paymentSettlementSpecificFunction(functionName, constants, columns))
      .orElse(manualAffiliateCommissionSpecificFunction(functionName, constants, columns))
  }

  private def genericFunction(functionName: Option[String], constants: Vector[String], columns: Vector[Column]) =
    functionName match {
      case Some("generic_is_not_null") =>
        Some(CommonFilterFunctions.isNotNull(columns(FIRST)))
      case Some("generic_is_equal_to") =>
        Some(CommonFilterFunctions.isEqualTo(constants(FIRST))(columns(FIRST)))
      case Some("generic_is_in") =>
        Some(CommonFilterFunctions.isIn(constants)(columns(FIRST)))
      case Some("generic_is_not_in") =>
        Some(CommonFilterFunctions.isNotIn(constants)(columns(FIRST)))
      case Some("generic_is_match_with_regex") =>
        Some(CommonFilterFunctions.isMatchWithRegex(constants(FIRST))(columns(FIRST)))
      case Some("generic_is_not_match_with_regex") =>
        Some(!CommonFilterFunctions.isMatchWithRegex(constants(FIRST))(columns(FIRST)))
      case Some("generic_get_normalized_amount_within_brackets") =>
        Some(CommonTextFunctions.getNormalizedAmountWithinBrackets(columns(FIRST)))
      case Some("generic_is_date_match_with_file_name") =>
        Some(CommonFilterFunctions.isDateMatchWithFileName(constants(FIRST))(columns(FIRST), columns(SECOND)))
      case Some("generic_uuid")           => Some(expr("uuid()"))
      case Some("generic_is_not_contain") => Some(!CommonFilterFunctions.isContain(constants(FIRST))(columns(FIRST)))
      case Some("generic_is_date_in_time_range_of_current_date") =>
        Some(CommonFilterFunctions.isDateInTimeRangeOfCurrentDate(constants(FIRST))(columns(FIRST)))
      case Some("generic_is_not_equal_to_for_some") =>
        Some(CommonFilterFunctions.isNotEqualToForSome(columns, constants))
      case Some("generic_hash") => Some(xxhash64(columns: _*))
      case _                    => None
    }

  private def genericTextFunction(
      functionName: Option[String],
      constants: Vector[String],
      columns: Vector[Column],
      extraMultiConstants: Option[List[List[String]]]
  ) = {
    val multiConstants = extraMultiConstants.getOrElse(List.empty)
    functionName match {
      case Some("generic_exclude_suffix") =>
        Some(CommonTextFunctions.excludeSuffix(constants(FIRST))(columns(FIRST)))
      case Some("generic_trim") =>
        Some(CommonTextFunctions.trim()(columns(FIRST)))
      case Some("generic_upper_case") =>
        Some(CommonTextFunctions.toUpperCaseString(columns(FIRST)))
      case Some("generic_text_from_decimal_comparison") =>
        Some(CommonTextFunctions.textFromDecimalComparison(constants(FIRST), constants(SECOND))(columns(FIRST)))
      case Some("generic_replace_last_characters") =>
        Some(CommonTextFunctions.replaceLastCharacters(constants(FIRST).toInt, constants(SECOND))(columns(FIRST)))
      case Some("generic_remove_word") =>
        Some(CommonTextFunctions.removeWord(constants(FIRST))(columns(FIRST)))
      case Some("generic_coalesce") =>
        Some(coalesce(columns: _*))
      case Some("generic_check_defined_null_or_whitespace") =>
        Some(CommonTextFunctions.setStringFromDefined(List(null, "NULL", "", "null"))(columns(FIRST), columns(SECOND)))
      case Some("generic_set_matching_value_with_null") =>
        Some(CommonTextFunctions.setMatchingRegexWithNull(constants(FIRST))(columns(FIRST)))
      case Some("generic_split_and_get") =>
        Some(CommonTextFunctions.splitAndGetByIndex(constants(FIRST), constants(SECOND).toInt)(columns(FIRST)))
      case Some("generic_multi_split_and_get_with_null") =>
        Some(CommonTextFunctions.multiSplitAndGetByIndexesWithNull(multiConstants)(columns(FIRST)))
      case Some("generic_concat_with_delimiter") =>
        Some(CommonTextFunctions.concatWithDelimiter(constants(FIRST))(array(columns: _*)))
      case Some("generic_set_matching_regex_with_constant") =>
        Some(CommonTextFunctions.setMatchingRegexWithConstant(constants(FIRST), constants(SECOND))(columns(FIRST)))
      case Some("generic_text_from_first_not_null") =>
        Some(CommonTextFunctions.setTextFromFirstNonNull(constants)(array(columns: _*)))
      case _ => None
    }
  }

  private def genericXMLFunction(functionName: Option[String], constants: Vector[String], columns: Vector[Column]) =
    functionName match {
      case Some("generic_xml_string_extract") =>
        Some(CommonXMLFunctions.extractXMLString(constants(FIRST))(columns(FIRST)))

      case _ => None
    }

  private def genericJSONFunction(functionName: Option[String], constants: Vector[String], columns: Vector[Column]) =
    functionName match {
      case Some("generic_json_long_extract") =>
        Some(CommonJSONFunctions.extractJSONLong(constants(FIRST))(columns(FIRST)))
      case _ => None
    }

  private def genericMathFunction(
      functionName: Option[String],
      constants: Vector[String],
      columns: Vector[Column],
      extraOperators: Option[List[String]],
      extraMultiConstants: Option[List[List[String]]]
  ) = {
    val operators      = extraOperators.getOrElse(List.empty)
    val multiConstants = extraMultiConstants.getOrElse(List.empty)

    functionName match {
      case Some("generic_sum") =>
        Some(CommonMathsFunctions.sum(array(columns: _*)))
      case Some("generic_revert") =>
        Some(CommonMathsFunctions.revert(columns(FIRST)))
      case Some("generic_revert_with_condition") =>
        Some(CommonMathsFunctions.revertWithCondition(constants(FIRST))(columns(FIRST), columns(SECOND)))
      case Some("generic_revert_sum_with_condition") =>
        Some(CommonMathsFunctions.revertWithCondition(constants(FIRST))(columns(FIRST), CommonMathsFunctions.sum(array(columns.drop(1): _*))))
      case Some("generic_or_logical") =>
        Some(CommonMathsFunctions.or(operators, constants)(array(columns: _*)))
      case Some("generic_multiconstants_or_logical") =>
        Some(CommonMathsFunctions.or(multiConstants)(array(columns: _*)))
      case Some("generic_is_null_or_empty") =>
        Some(CommonMathsFunctions.is_null_or_empty()(array(columns: _*)))
      case Some("generic_advance_sum") =>
        Some(CommonMathsFunctions.advanceSum(constants(FIRST))(array(columns: _*)))
      case Some("generic_set_matching_value") =>
        Some(CommonTextFunctions.setMatchingValue(constants(FIRST), constants(SECOND), constants(THIRD))(columns(FIRST)))
      case Some("generic_convert_string_to_long") =>
        Some(CommonMathsFunctions.convertFromStringToLong(columns(FIRST)))
      case Some("generic_convert_string_to_big_decimal") =>
        Some(CommonMathsFunctions.convertFromStringToBigDecimal(columns(FIRST)))
      case Some("generic_set_number_is_null") =>
        Some(CommonMathsFunctions.setIfNull(constants(FIRST), constants(SECOND))(columns(FIRST)))
      case Some("generic_is_positive") =>
        Some(CommonMathsFunctions.isPositive(columns(FIRST)))
      case Some("generic_compare_number") =>
        Some(CommonMathsFunctions.compare(columns(FIRST), constants(FIRST), extraOperators.getOrElse(List(""))(FIRST)))
      case Some("generic_multiply") =>
        Some(CommonMathsFunctions.multiply(array(columns: _*)))
      case Some("generic_multiply_constant") =>
        Some(CommonMathsFunctions.multiplyConstant(BigDecimal.valueOf(constants(FIRST).toDouble))(array(columns: _*)))
      case Some("generic_divide") =>
        Some(CommonMathsFunctions.divide(columns(FIRST), columns(SECOND)))
      case Some("generic_get_not_null_decimal") =>
        Some(CommonMathsFunctions.getNotNullDecimalUdf(columns(FIRST)))
      case _ => None
    }
  }

  private def genericDateFunction(functionName: Option[String], constants: Vector[String], columns: Vector[Column]) =
    functionName match {
      case Some("generic_get_first_date") =>
        Some(CommonDateFunctions.getFirstDateOfPreviousMonth(columns(FIRST)))
      case Some("generic_get_last_date") =>
        Some(CommonDateFunctions.getLastDateOfPreviousMonth(columns(FIRST)))
      case Some("generic_convert_date_format") =>
        Some(CommonDateFunctions.convertStringToDate(constants(FIRST), constants(SECOND))(columns(FIRST)))
      case Some("generic_offset_timestamp") =>
        Some(CommonDateFunctions.offsetTimestamp(constants(FIRST).toLong, constants(SECOND).toLong)(columns(FIRST)))
      case Some("generic_offset_datadate") =>
        Some(CommonDateFunctions.offsetDatadate(constants(FIRST).toInt)(columns(FIRST)))
      case Some("generic_offset_datamonth") =>
        Some(CommonDateFunctions.offsetDatamonth(columns(FIRST), constants(FIRST).toInt))
      case Some("generic_offset_datamonth_gte") =>
        Some(CommonDateFunctions.offsetDatamonth(columns(FIRST), constants(FIRST).toInt))
      case Some("generic_datestring_to_timestamp") =>
        Some(CommonDateFunctions.convertDateStringToTimestamp(constants(FIRST))(columns(FIRST)))
      case Some("generic_datadate_lte_today") =>
        Some(columns(FIRST)) //comparison will be in ExtractProcessor
      case Some("generic_datadate_lt_today") =>
        Some(columns(FIRST)) //comparison will be in ExtractProcessor
      case Some("generic_timestamp_lt_datadate") =>
        Some(columns(FIRST)) //comparison will be in ExtractProcessor
      case Some("generic_is_date_in_month_range") =>
        Some(CommonDateFunctions.isDateInMonthRange(columns(FIRST), constants(FIRST).toInt, constants(SECOND).toInt, constants(THIRD)))
      case Some("generic_offset_date_greater_than_process_month") =>
        Some(CommonDateFunctions.offsetDateToDataMonth(columns(FIRST), constants(FIRST).toInt, constants(SECOND)))
      case Some("generic_offset_date_less_than_process_month") =>
        Some(CommonDateFunctions.offsetDateToDataMonth(columns(FIRST), constants(FIRST).toInt, constants(SECOND)))
      case Some("generic_combine_datetime") =>
        Some(CommonDateFunctions.combineDateTime(columns(FIRST), columns(SECOND)))
      case Some("generic_combine_datetime_with_offset") =>
        Some(
          CommonDateFunctions.combineDateTimeWithOffset(constants(FIRST).toLong, constants(SECOND).toLong, constants(THIRD))(
            columns(FIRST),
            columns(SECOND)
          )
        )
      case Some("generic_add_months_and_format_date") =>
        Some(CommonDateFunctions.addMonthsAndFormatDate(columns(FIRST), constants(FIRST).toInt, constants(SECOND)))
      case Some("generic_offset_timestamp_convert_to_date") =>
        Some(CommonDateFunctions.offsetTimestampConvertToDate(constants(FIRST).toLong, constants(SECOND).toLong, constants(THIRD))(columns(FIRST)))
      case _ => None
    }

  private def supplierSpecificFunction(functionName: Option[String], columns: Vector[Column]) =
    functionName match {
      case Some("get_lionair_total_amount") =>
        Some(LionAirLogic.getTotalAmount(columns(FIRST), columns(SECOND), columns(THIRD)))
      case Some("get_citilink_total_amount") =>
        Some(CitilinkLogic.getTotalAmount(columns(FIRST), columns(SECOND), columns(THIRD)))
      case Some("marriott_transaction_within_date") =>
        Some(col("datadate")) //comparison will be in ExtractProcessor
      case Some("violet_messaginglog_previous_date") =>
        Some(col("datadate")) //comparison will be in ExtractProcessor
      case Some("fact_booking_within_month") =>
        Some(col("datamonth")) //comparison will be in ExtractProcessor
      case Some("loyalty_invoices_within_date") =>
        Some(col("datadate")) //comparison will be in ExtractProcessor
      case Some("get_dbs_booking_id") =>
        Some(LoyaltyLogic.getDBSBookingId(columns(FIRST)))
      case Some("get_dbs_transaction_type") =>
        Some(LoyaltyLogic.getDBSTransactionType(columns(FIRST)))
      case Some("get_dbs_transaction_id") =>
        Some(LoyaltyLogic.getDBSTransactionId(columns(FIRST)))
      case Some("get_dbs_settlement_date") =>
        Some(LoyaltyLogic.getDBSSettlementDate(columns(FIRST)))
      case Some("get_monthly_invoice_transaction_type") =>
        Some(LoyaltyLogic.getMonthlyInvoiceTransactionType(columns(FIRST)))
      case Some("get_jtb_whitelabel_id") =>
        Some(JtbLogic.getJtbWhitelabelId(columns(FIRST)))
      case Some("get_tripjack_transaction_date") =>
        Some(TripjackLogic.getTripjackTransactionDate(columns(FIRST), columns(SECOND), columns(THIRD), columns(FOURTH)))
      case Some("get_tripjack_transaction_type") =>
        Some(TripjackLogic.getTripjackTransactionType(columns(FIRST)))
      case _ => None
    }

  private def affiliateSpecificFunction(functionName: Option[String], columns: Vector[Column]) =
    functionName match {
      case Some("get_violet_endpoint")  => Some(VioletLogic.getEndpoint(columns(FIRST), columns(SECOND)))
      case Some("get_violet_issuccess") => Some(VioletLogic.getIsSuccess(columns(FIRST)))
      case _                            => None

    }

  private def cashbackSpecificFunction(functionName: Option[String], columns: Vector[Column]) =
    functionName match {
      case Some("get_cashback_wise_is_used_for_reconciliation") => Some(CashbackLogic.getIsUsedForWiseReconciliation(columns(FIRST)))
      case Some("get_wise_cashback_event_type")                 => Some(CashbackLogic.getWiseEventType(columns(FIRST)))
      case _                                                    => None
    }

  private def apSettlementSpecificFunction(functionName: Option[String], columns: Vector[Column]) =
    functionName match {
      case Some("get_tutuka_source_transaction_code") =>
        Some(ApSettlementLogic.getTutukaSourceTransactionCode(columns(FIRST), columns(SECOND), columns(THIRD)))
      case Some("get_payment_method_name") =>
        Some(ApSettlementLogic.getPaymentMethodName(columns(FIRST), columns(SECOND), columns(THIRD)))
      case Some("get_payment_method_id") =>
        Some(ApSettlementLogic.getPaymentMethodId(columns(FIRST), columns(SECOND), columns(THIRD)))
      case Some("correct_cny") =>
        Some(ApSettlementLogic.correctCNY(columns(FIRST)))
      case _ => None
    }

  private def summarySettlementSpecificFunction(functionName: Option[String], columns: Vector[Column]) =
    functionName match {
      case Some("get_checkout_item_type") =>
        Some(SummarySettlementLogic.getCheckoutItemTypeMapping(columns(FIRST), columns(SECOND), columns(THIRD)))
      case Some("get_credit_amount") =>
        Some(SummarySettlementLogic.getCreditAmount(columns(FIRST)))
      case Some("get_debit_amount") =>
        Some(SummarySettlementLogic.getDebitAmount(columns(FIRST)))
      case _ => None
    }

  private def manualAffiliateCommissionSpecificFunction(functionName: Option[String], constants: Vector[String], columns: Vector[Column]) =
    functionName match {
      case Some("mapping_pah_affiliate_name") =>
        Some(ManualAffiliateCommissionLogic.mappingPAHAffiliateName(columns(FIRST)))
      case Some("mapping_switchfly_affiliate_name") =>
        Some(ManualAffiliateCommissionLogic.mappingSwitchflyAffiliateName(columns(FIRST)))
      case Some("ana_margin_calculation") =>
        Some(
          ManualAffiliateCommissionLogic
            .anaMarginCalculation(BigDecimal.valueOf(constants(FIRST).toDouble))(columns(FIRST), columns(SECOND), columns(THIRD))
        )
      case Some("ana_vehicle_booking_state") =>
        Some(ManualAffiliateCommissionLogic.anaVehicleBooking(columns(FIRST), columns(SECOND), columns(THIRD)))
      case _ => None
    }

  /** This dataSetMapping function should use to handle each dataframe which is the result from extraction function */
  def dataSetMapping(inputDf: List[DataFrame], enrichDf: List[DataFrame], source: Source, args: Arguments)(implicit
      sqlContext: SQLContext
  ): DataFrame = {
    // TODO : To process and consolidate specific logic of multiple dataframe to be only one dataframe
    val consolidatedDF = source.dataSetLogicFunction
      .foldLeft(inputDf) { (df: List[DataFrame], function: DataSetLogicFunction) =>
        List(function.functionName match {
          // Add consolidate logic
          case "merge_lionair_data"                          => LionAirLogic.mergeLionAirData(df(FIRST), df(SECOND))
          case "merge_indigo_data"                           => IndiGoLogic.mergeIndiGoData(df(FIRST), df(SECOND))
          case "merge_marriott_data"                         => MarriottLogic.mergeMarriottData(df(FIRST), df(SECOND), df(THIRD), args.partitionDate)
          case "merge_hyatt_data"                            => HyattLogic.mergeHyattRequest(df(FIRST), df(SECOND))
          case "merge_violet_data"                           => VioletLogic.mergeVioletData(df(FIRST), df(SECOND))
          case "merge_loyalty_data"                          => LoyaltyLogic.mergeLoyaltyData(df(FIRST), df(SECOND))
          case "merge_loyalty_monthly_allocations"           => LoyaltyLogic.mergeLoyaltyMonthlyAllocations(df(FIRST), df(SECOND), df(THIRD), df(FOURTH))
          case "merge_loyalty_monthly_invoices"              => LoyaltyLogic.mergeLoyaltyMonthlyInvoices(df(FIRST), df(SECOND), df(THIRD))
          case "calculate_delta_jtb"                         => JtbLogic.calculateDelta(df(FIRST), df(SECOND), args.processDate)
          case "calculate_delta_jtb_bi_weekly"               => JtbLogic.calculateDeltaBiWeekly(df(FIRST), df(SECOND), args.processDate)
          case "merge_hilton_data"                           => HiltonLogic.mergeHiltonData(df(FIRST), df(SECOND))
          case "merge_avl_data"                              => AvlLogic.mergeAvlData(df(FIRST), enrichDf(FIRST))
          case "merge_kiwi_data"                             => KiwiLogic.mergeKiwiData(df(FIRST), df(SECOND), df(THIRD))
          case "cashback_transform_wise_transactions"        => CashbackLogic.transformWiseTransactions(df(FIRST))
          case "cashback_transform_mifinity_transactions"    => CashbackLogic.transformMifinityTransactions(df(FIRST))
          case "wallet_gateway_transform_adyen_transactions" => WalletGatewayLogic.transformAdyenTransactions(df(FIRST))
          case "append_tutuka_upc_additional_info" =>
            ApSettlementLogic
              .appendTutukaAdditionalInfo(df(FIRST), enrichDf(FIRST), enrichDf(SECOND), enrichDf(THIRD), enrichDf(FOURTH), enrichDf(FIFTH))
          case "append_enett_upc_additional_info" =>
            ApSettlementLogic.appendEnettAdditionalInfo(df(FIRST), enrichDf(FIRST), enrichDf(SECOND), enrichDf(THIRD))
          case "append_wex_upc_additional_info" =>
            ApSettlementLogic.appendWexAdditionalInfo(df(FIRST), enrichDf(FIRST), enrichDf(SECOND))
          case "append_approval_exchange_rate" =>
            ApSettlementLogic.appendApprovalExchangeRates(df(FIRST), enrichDf(FIRST))
          case "append_checkout_upc_additional_info" =>
            ApSettlementLogic
              .appendCheckoutAdditionalInfo(df(FIRST), enrichDf(FIRST), enrichDf(SECOND), enrichDf(THIRD), enrichDf(FOURTH))
          case "split_checkout_summary_settlement_info" =>
            SummarySettlementLogic.splitCheckoutSummarySettlementInfo(df(FIRST))
          case "prepare_tutuka_summary_settlement_info" =>
            SummarySettlementLogic.prepareTutukaSummarySettlement(df(FIRST))
          case "calculate_agp_exchange_rate" =>
            ApSettlementNonCardLogic.calculateAgpExchangeRate(df(FIRST), enrichDf(FIRST), enrichDf(SECOND), enrichDf(THIRD))(args)
          case "delta_settlement_calculation"      => DeltaApSettlementLogic.deltaSettlementCalculation(df(FIRST), enrichDf(FIRST), args)
          case "merge_airtrip_data"                => AirTripLogic.mergeAirTripData(df(FIRST), df(SECOND))
          case "merge_jejuair_data"                => JejuAirLogic.mergeJejuAirData(df(FIRST), df(SECOND))
          case "generic_join"                      => CommonJoinFunctions.genericJoin(source.tableList, df, function.arguments.asInstanceOf[JoinFunctionArguments].value)
          case "append_integrator_additional_data" => ManualAffiliateCommissionLogic.enrichIntegratorPartnerData(df(FIRST))
          case "summary_accrual_commission_with_partner_report" =>
            ManualAffiliateCommissionLogic.summaryAccrualCommissionWithPartnerReport(
              df(FIRST),
              df(SECOND),
              df(THIRD),
              function.arguments.asInstanceOf[StringListArguments].value(FIRST)
            )
          case "merge_shopback_acitivity_data" =>
            ManualAffiliateCommissionLogic.mergeShopbackActivityData(df(FIRST), df(SECOND), df(THIRD), df(FOURTH))
          case "normalize_comstat_data" =>
            ManualAffiliateCommissionLogic.normalizeComstatData(df(FIRST), df(SECOND), df(THIRD), df(FOURTH))
          case "calculate_bnpl_adjustments" =>
            BNPLAutoAdjustmentLogic.calculateBNPLAdjustments(df(FIRST), enrichDf(FIRST), enrichDf(SECOND), args.processDate)
          case "apply_fifo_lifo_logic" =>
            new APSettlementFifoLifoLogic(logFifoProcess = true, enableCorrupt = false)
              .processAndCalculateFifoLifo(df(FIRST), df(SECOND), df(THIRD), enrichDf(FIRST))
          case "amap_fee_usd_and_date_calculation" => AMAPLogic.calculateFeeUsdAndDate(df(FIRST))
          case "overwrite_supplier_not_found"      => PricelineLogic.overwriteSupplierMappingNotFound(df(FIRST), df(SECOND))
          case "bsp_normalized_logic"              => BspLogic.normalizeBspRawData(df(FIRST))
          case _                                   => df(FIRST)
        })
      }
      .headOption
      .orNull

    // TODO : To process individual dataframe
    source.dataSetLogicFunction.foldLeft(consolidatedDF)((df: DataFrame, function: DataSetLogicFunction) =>
      function.functionName match {
        // Specific Logic
        case "pcln_mapping" => PricelineLogic.groupAndPivot(df)
        // Apply Generic Functions
        case "generic_trim"            => CommonTextFunctions.trim(df)
        case "generic_drop_duplicates" => CommonFilterFunctions.dropDuplicates(df, function.arguments.asInstanceOf[StringListArguments].value)
        case "generic_aggregate"       => CommonAggregateFunctions.genericAggregate(df, function.arguments.asInstanceOf[AggregateFunctionArguments].value)
        case "generic_window" =>
          CommonAggregateFunctions.genericWindowAggregate(df, function.arguments.asInstanceOf[WindowFunctionArguments].value)
        case "generic_filter" =>
          df.where(
            function.arguments
              .asInstanceOf[FilterFunctionArguments]
              .value
              // If any value is None, it defaults to 'lit(true)', effectively allowing the row to pass through the filter.
              .map(f => f.getOrElse(lit(true)))
              // The resulting sequence of conditions is then combined using a logical AND operation, starting with 'lit(true)' as the initial condition.
              .foldLeft((lit(true)))(_ && _)
          )
        case _ => df
      }
    )
  }

  private def paymentSettlementSpecificFunction(functionName: Option[String], constants: Vector[String], columns: Vector[Column]) =
    PaymentGatewaySettlementsFunctionMapper.paymentSettlementFunction(functionName, constants, columns)

}
