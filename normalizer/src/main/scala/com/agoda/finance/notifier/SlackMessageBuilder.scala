package com.agoda.finance.notifier

import com.agoda.finance.externaldatapipeline.core.utils.StringUtil.escapedJsonCtlChar

object SlackMessageBuilder {

  def build(headerMsg: String, bodyString: String = "", badgeColor: String): String = {
    val jsonPayload =
      s"""
         | {
         |     "username": "Normalizer alert",
         |     "icon_emoji": ":baby_chick:",
         |     "text": "${escapedJsonCtlChar(headerMsg)}",
         |     "response_type": "in_channel",
         |     "attachments": [
         |		    {
         |			    "text": "${escapedJsonCtlChar(bodyString)}",
         |			    "color": "$badgeColor",
         |			    "attachment_type": "default"
         |       }
         |     ]
         | }
      """.stripMargin

    jsonPayload
  }

}
