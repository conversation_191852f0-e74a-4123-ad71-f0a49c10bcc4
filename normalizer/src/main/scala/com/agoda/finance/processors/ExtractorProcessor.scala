package com.agoda.finance.processors

import com.typesafe.scalalogging.LazyLogging
import com.agoda.finance.common.utils.DataTypeUtils
import com.agoda.finance.common.utils.DateFormats
import com.agoda.finance.constants.Status
import com.agoda.finance.constants.statusValue
import com.agoda.finance.functions.common.CommonDateFunctions
import com.agoda.finance.functions.specific.HyattLogic
import com.agoda.finance.functions.specific.LoyaltyLogic
import com.agoda.finance.functions.specific.MarriottLogic
import com.agoda.finance.functions.specific.VioletLogic
import com.agoda.finance.functions.utils.DateUtils
import com.agoda.finance.handler.ExceptionHandler
import com.agoda.finance.handler.Statusflow
import com.agoda.finance.preprocess._
import org.apache.spark.sql._
import org.apache.spark.sql.functions._

case class ResultsStatus(sourceDf: List[DataFrame], enrichDf: List[DataFrame], status: Status, sourceTable: Source, datadate: Int)

class ExtractProcessor(execInfo: ExecutionInfo, args: Arguments)(implicit sqlContext: SQLContext) extends LazyLogging {
  def apply: Seq[ResultsStatus] =
    execInfo.sourceList.flatMap { sourceList =>
      sourceList.sourceMapper.map { source =>
        ExceptionHandler.callbackResultsStatus(
          source,
          Statusflow(statusValue.ExtractFailed, s"Unable to extract DF from  : ${source.tableList.map(_.tableName).mkString(",")}"),
          Statusflow(statusValue.Extracted, s"Completely extract DF from  : ${source.tableList.map(_.tableName).mkString(",")}"),
          args,
          sourceList.datadate
        ) {
          val sourceDataSelect = applyExtraction(
            source.tableList,
            { case (name, mapping) =>
              name match {
                case "marriott_transaction_within_date" =>
                  mapping >= MarriottLogic.getTransactionDataDateWithinPeriod(sourceList.datadate)
                case "hyatt_transaction_within_date" =>
                  mapping >= HyattLogic.getTransactionDataDateWithinPeriod(sourceList.datadate)
                case "violet_messaginglog_previous_date" =>
                  mapping === VioletLogic.getTransactionDataDateWithinPeriod(sourceList.datadate)
                case "fact_booking_within_month" =>
                  mapping >= LoyaltyLogic.getBookingDataMonthWithinPeriod(sourceList.datadate)
                case "loyalty_invoices_within_date" =>
                  mapping >= LoyaltyLogic.getLoyaltyInvoicesWithinPeriod(sourceList.datadate)
                case "generic_offset_datadate"                        => mapping === sourceList.datadate
                case "generic_offset_datamonth"                       => mapping === DateUtils.convertDatadateToDatamonth(sourceList.datadate)
                case "generic_datadate_lte_today"                     => mapping <= sourceList.datadate
                case "generic_datadate_lt_today"                      => mapping < sourceList.datadate
                case "generic_timestamp_lt_datadate"                  => date_format(mapping, "yyyyMMdd").cast("int") < sourceList.datadate
                case "generic_offset_datamonth_gte"                   => mapping >= DateUtils.convertDatadateToDatamonth(sourceList.datadate)
                case "generic_offset_date_greater_than_process_month" => mapping >= DateUtils.convertDatadateToDatamonth(sourceList.datadate)
                case "generic_offset_date_less_than_process_month"    => mapping <= DateUtils.convertDatadateToDatamonth(sourceList.datadate)
                case _                                                => mapping
              }
            },
            sourceList.datadate
          )

          val enrichDataSelect = applyExtraction(
            source.enrichTableList,
            {
              case ("generic_offset_datadate", mapping) => mapping === sourceList.datadate
              case (_, mapping)                         => mapping
            },
            sourceList.datadate
          )

          DataFrameCollection(sourceDataSelect, enrichDataSelect)
        }
      }
    }

  private def applyExtraction(tableList: List[TableList], extraction: (String, Column) => Column, datadate: Int): List[DataFrame] =
    tableList.map { table =>
      val formattedPartitionDate = table.partitionDateFormat
        .map(DataTypeUtils.castDataDateStringToInt(datadate.toString, DateFormats.dateFormatter, _))
        .getOrElse(datadate)
      val data = sqlContext.table(table.tableName)
      val dataWithPartition =
        table.partitionDateKey
          .map { partitionDateKey =>
            data.filter(col(partitionDateKey) === formattedPartitionDate)
          }
          .getOrElse(data)
          .where(
            table.extractFunctions
              .map { case (name, mapping) => extraction(name, mapping) }
              .foldLeft(lit(true))(_ && _)
          )
      table.selectColumn
        .filter(_.nonEmpty)                                  //fitler empty list
        .map(cols => dataWithPartition.selectExpr(cols: _*)) //filter empty expression
        .getOrElse(dataWithPartition)
    }
}
