package com.agoda.finance.constants

object DeltaApSettlementConstants {
  // Interest original columns
  final val sourceTransactionCodeColumnName: String    = "source_transaction_code"
  final val originalSettlementAmountColumnName: String = "original_settlement_amount"
  final val transactionAmountColumnName: String        = "transaction_amount"
  final val transactionDateColumnName: String          = "transaction_date"
  final val settlementAmountColumnName: String         = "settlement_amount"

  // Temp columns
  final val groupingKeyColumnName: String            = "grouping_key"
  final val reprocessNumberColumnName: String        = "reprocess_number"
  final val transactionIdColumnName: String          = "transaction_id_from_source_transaction_code"
  final val trackingNumberColumnName: String         = "tracking_number"
  final val transactionDescriptionColumnName: String = "transaction_description"

  final val commonGroupByColumnNames: Seq[String] =
    Seq(
      "report_date",
      transactionIdColumnName,
      trackingNumberColumnName,
      transactionDescriptionColumnName,
      "original_settlement_currency",
      "transaction_currency",
      "sub_supplier_id"
    )

  final val groupByColumnNamesEcard: Seq[String]      = commonGroupByColumnNames :+ "approval_reference"
  final val groupByColumnNamesUpcOnEpass: Seq[String] = commonGroupByColumnNames :+ "approval_reference"
  final val groupByColumnNamesUpc: Seq[String]        = commonGroupByColumnNames :+ "booking_id"

  final val allAmountColumnNames: Seq[String] =
    Seq(
      originalSettlementAmountColumnName,
      settlementAmountColumnName,
      "posting_amount",
      "settlement_amount_usd",
      transactionAmountColumnName,
      "interchange_amount"
    )
}
