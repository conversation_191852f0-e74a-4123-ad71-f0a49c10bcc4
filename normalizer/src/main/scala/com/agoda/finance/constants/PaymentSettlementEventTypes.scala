package com.agoda.finance.constants

object PaymentSettlementEventTypes {
  val events: Map[String, Any] = Map(
    "12" -> Map(
      "MiscCosts"        -> "5",
      "PaymentCost"      -> "6",
      "Refunded"         -> "4",
      "RefundedReversed" -> "4",
      "Settled"          -> "2"
    ),
    "17" -> Map(
      "MiscCosts"          -> "5",
      "PaymentCost"        -> "6",
      "Refunded"           -> "4",
      "RefundedReversed"   -> "4",
      "Settled"            -> "2",
      "ChargebackReversed" -> "2",
      "Chargeback"         -> "4"
    ),
    "33" -> Map(
      "PAYMENT"         -> "2",
      "REFUND"          -> "4",
      "REFUND_REVERSAL" -> "2"
    ),
    "10" -> Map(
      "Settled" -> "2",
      "Refund"  -> "4"
    ),
    "20" -> Map(
      "Refunded" -> "4",
      "Settled"  -> "2"
    ),
    "24" -> Map(
      "Order"  -> "2",
      "Refund" -> "4"
    ),
    "40" -> Map(
      "Order"  -> "2",
      "Refund" -> "4"
    ),
    "39" -> Map(
      "Order"  -> "2",
      "Refund" -> "4"
    ),
    "41" -> Map(
      "Order"  -> "2",
      "Refund" -> "4"
    ),
    "7" -> Map(
      "T0003" -> "2",
      "T0006" -> "2",
      "T1107" -> "4"
    )
  )
}
