package com.agoda.finance.constants

object PaymentSettlementGatewayTransaction {
  // Map[FileTypeId -> Map[EventType -> Boolean]]
  val isGatewayTransactionRules: Map[String, Map[String, Boolean]] = Map(
    "10" -> Map(
      "REFUNDED"   -> true,
      "REFUND"     -> true,
      "SETTLED"    -> true,
      "SETTLE"     -> true,
      "CHARGEBACK" -> true
    ),
    "20" -> Map(
      "PAYMENTCOST"       -> true,
      "MANUALCORRECTED"   -> true,
      "SECONDCHARGEBACK"  -> true,
      "CHARGEBACKREVERSE" -> true,
      "SETTLED"           -> true,
      "REFUNDED"          -> true,
      "REFUNDREVERSE"     -> true,
      "CHARGEBACK"        -> true
    ),
    "7" -> Map(
      "T0003" -> true, //Paypal BNPL
      "T0006" -> true,
      "T1107" -> true,
      "T1108" -> true,
      "T1111" -> true,
      "T1202" -> true,
      "T0106" -> true,
      "T0800" -> true,
      "T1110" -> true,
      "T1106" -> true,
      "T1201" -> true,
      "T1900" -> true
    )
  )

  /** Get isGatewayTransaction rule for a specific file type and event type
    * Returns true if file type has no rules defined, otherwise returns the specific rule or false
    */
  def getIsGatewayTransactionRule(fileTypeId: String, eventType: String): Boolean =
    isGatewayTransactionRules.get(fileTypeId) match {
      case None        => true                              // File type not in map, return true
      case Some(rules) => rules.getOrElse(eventType, false) // File type in map, return rule or false if event type not found
    }
}
