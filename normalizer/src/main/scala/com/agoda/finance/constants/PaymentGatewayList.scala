package com.agoda.finance.constants

object PaymentGatewayList {
  // Map[gatewayName -> GatewayId]
  val gatewayMap: Map[String, Int] = Map(
    "BOKU"     -> 30,
    "RAZORPAY" -> 32
  )

  /** Get gatewayId from gateway name
    * Returns gatewayId if it is defined, otherwise returns 0
    */
  def getGatewayId(gatewayName: String): Int =
    gatewayMap.get(gatewayName) match {
      case None            => 0 // gateway name not defined
      case Some(gatewayId) => gatewayId
    }
}
