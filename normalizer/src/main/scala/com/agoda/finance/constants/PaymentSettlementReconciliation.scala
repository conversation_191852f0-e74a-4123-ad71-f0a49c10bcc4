package com.agoda.finance.constants

object PaymentSettlementReconciliation {
  // Map[FileTypeId -> Map[EventType -> Boolean]]
  val reconciliationRules: Map[String, Map[String, Boolean]] = Map(
    // Alipay OSP (file_type_id: 33)
    "33" -> Map(
      "PAYMENT" -> true,
      "REFUND"  -> true,
      "CANCEL"  -> true
    ),
    "20" -> Map(
      "REFUNDED"      -> true,
      "REFUNDREVERSE" -> true,
      "SETTLED"       -> true
    ),
    "10" -> Map(
      "REFUNDED" -> true,
      "REFUND"   -> true,
      "SETTLED"  -> true,
      "SETTLE"   -> true
    ),
    "42" -> Map(
      "REFUND" -> true,
      "ORDER"  -> true
    ),
    "7" -> Map(
      "T0006" -> true,
      "T1107" -> true,
      "T0003" -> true
    )
  )

  /** Get reconciliation rule for a specific file type and event type
    * Returns true if file type has no rules defined, otherwise returns the specific rule or false
    */
  def getReconciliationRule(fileTypeId: String, eventType: String): Boolean =
    reconciliationRules.get(fileTypeId) match {
      case None        => true                              // File type not in map, return true
      case Some(rules) => rules.getOrElse(eventType, false) // File type in map, return rule or false if event type not found
    }
}
