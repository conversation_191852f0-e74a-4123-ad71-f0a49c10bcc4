package com.agoda.finance.tables.APSettlementCardUpcTables

import com.agoda.finance.tables.Table
import com.agoda.ml.config.SimpleConfiguration
import com.typesafe.config.Config

import scala.util.Try

object SettlementsUpcUsd {
  final val table = Table(
    rootConfig.getString("schema"),
    Try(rootConfig.getString("normalizer.processed_settlementsupc_usd_table")).getOrElse("settlements_upc_details")
  )
  private val rootConfig: Config = SimpleConfiguration()
}
