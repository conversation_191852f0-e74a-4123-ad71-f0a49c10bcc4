package com.agoda.finance.tables.APSettlementCardUpcTables

import com.agoda.finance.tables.Table
import com.agoda.ml.config.SimpleConfiguration
import com.typesafe.config.Config

import scala.util.Try

object BookingUpc {
  private val rootConfig: Config = SimpleConfiguration()

  final val table = Table(
    rootConfig.getString("schema"),
    Try(rootConfig.getString("normalizer.processed_bookingupc_table")).getOrElse("booking_upc_details")
  )

}
