package com.agoda.finance.processors

import com.agoda.finance.common.enums.ItemSubType.daily
import com.agoda.finance.common.enums.ItemType.job
import com.agoda.finance.config._
import com.agoda.finance.constants.FinanceDecimalType.CalculatedDecimalType
import com.agoda.finance.constants.WalletConstant.TestWalletTransactionsConstant.SIT_ID
import com.agoda.finance.constants.WalletConstant.TransactionTypes.{REDEMPTION, REFUND, TOPUP, WITHDRAWAL}
import com.agoda.finance.constants.WalletConstant.walletTransactionsConstant.{CREATED_LOG_TIME, WALLET_TRANSACTION_ID}
import com.agoda.finance.externaldatapipeline.core.models.context.FCDInfo
import com.agoda.finance.externaldatapipeline.core.schema.Schema
import com.agoda.finance.externaldatapipeline.core.utils.RepositoryUtiImpl
import com.agoda.finance.models.WalletContext._
import com.agoda.finance.models.{ExecutionArguments, InputSchema, OutputSchema, WalletContext}
import com.agoda.finance.schema._
import com.agoda.finance.test_data.TestWalletTransactions
import com.agoda.finance.utils.TestUtil.assertDataFrameEquals
import com.agoda.ml.spark.services.etl.{DataFrameETLExtensionLocalTest, SparkETLLocalTest}
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.col
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{reset, verify}
import org.scalatest.BeforeAndAfter
import org.scalatest.mockito.MockitoSugar.mock

import java.sql.{Date, Timestamp}

class ValidationProcessorTest extends SparkETLLocalTest with DataFrameETLExtensionLocalTest with BeforeAndAfter {
  import sqlContext.implicits._

  val ignoredColumnsToCompare: Seq[String] = Seq(WALLET_TRANSACTION_ID, SIT_ID, CREATED_LOG_TIME)

  private lazy val config = WalletConfig(
    "wallet-data-pipeline.unit-test",
    liveMode = false,
    InputConfig(
      TableConfig("input", "mt_ledger_transactions_test"),
      TableConfig("input", "mt_ledger_entries_test")
    ),
    OutputConfig(
      TableConfig("output", "mt_wallet_transactions_test"),
      TableConfig("output", "mt_duplicated_wallet_transactions_test"),
      TableConfig("output", "mt_corrupted_wallet_transactions_test")
    ),
    Map(
      "BHFS_US" -> "ledger_acc_id_us",
      "BHFS_UK" -> "ledger_acc_id_uk",
      "BHFS_SG" -> "ledger_acc_id_sg"
    ).mapValues(WalletEntity("", null, 0, _, "")),
    TableConfig("output", "tracking_test")
  )

  private lazy val executionArgs = ExecutionArguments(datadate = 20241109, hour = 11)
  private lazy val fcdInfo       = FCDInfo("itemName", "description", job, daily, 20241109)

  private lazy implicit val repositoryUtil: RepositoryUtiImpl = mock[RepositoryUtiImpl]
  private lazy implicit val context: WalletContext = WalletContext(
    executionArgs,
    config,
    InputSchema(
      LedgerTransactionsSchema(config.inputConfig.ledgerTransactionsTable.getTableName),
      LedgerEntriesSchema(config.inputConfig.ledgerEntriesTable.getTableName)
    ),
    OutputSchema(
      WalletTransactionsSchema(config.outputConfig.walletTransactionsTable.getTableName),
      DuplicatedWalletTransactionsSchema(config.outputConfig.duplicatedWalletTransactionsTable.getTableName),
      CorruptedWalletTransactionsSchema(config.outputConfig.corruptedWalletTransactionsTable.getTableName)
    ),
    fcdInfo,
    TrackingSchema(config.tracking.getTableName)
  )

  private val corruptedWalletTransactionsSchema = context.outputSchema.corruptedWalletTransactionsSchema
  val validationProcessor                       = new ValidationProcessor(repositoryUtil)(context)

  before {
    reset(repositoryUtil)
  }

  test("validate data correctly") {
    val walletTransactionsData = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        meta_transaction_date_time = None
      ),
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        local_currency = None
      ),
      TestWalletTransactions(
        wallet_transaction_id = Some("47ec0580-7822-40cb-a86b-84275f6f80a2"),
        ledger_transaction_id = Some("47ec0580-7822-40cb-a86b-84275f6f80a2")
      ),
      TestWalletTransactions(
        wallet_transaction_id = Some("47ec0580-7822-40cb-a86b-84275f6f80a2"),
        ledger_transaction_id = Some("47ec0580-7822-40cb-a86b-84275f6f80a2"),
        wallet_entity = Some("BHFS_TH")
      )
    ).toDF()
      .withColumn("local_amount", col("local_amount").cast(CalculatedDecimalType))

    val expectedValidTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("47ec0580-7822-40cb-a86b-84275f6f80a2"),
        ledger_transaction_id = Some("47ec0580-7822-40cb-a86b-84275f6f80a2")
      )
    ).toDF()
      .withColumn("local_amount", col("local_amount").cast(CalculatedDecimalType))
      .withDatadateAndHour

    val expectedCorruptedTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        local_currency = None
      ),
      TestWalletTransactions(
        wallet_transaction_id = Some("47ec0580-7822-40cb-a86b-84275f6f80a2"),
        ledger_transaction_id = Some("47ec0580-7822-40cb-a86b-84275f6f80a2"),
        wallet_entity = Some("BHFS_TH")
      ),
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        meta_transaction_date_time = None
      )
    ).toDF()
      .withColumn("local_amount", col("local_amount").cast(CalculatedDecimalType))
      .withDatadateAndHour

    val actual = validationProcessor.validate(walletTransactionsData)

    //we cannot compare ID as ID is a random UUID
    assertDataFrameEquals(actual, expectedValidTransactions, ignoredColumnsToCompare: _*)

    val captureSchema: ArgumentCaptor[Schema]    = ArgumentCaptor.forClass(classOf[Schema])
    val captureDf: ArgumentCaptor[DataFrame]     = ArgumentCaptor.forClass(classOf[DataFrame])
    val captureWriteMode: ArgumentCaptor[String] = ArgumentCaptor.forClass(classOf[String])

    verify(repositoryUtil).write(captureSchema.capture(), captureDf.capture(), captureWriteMode.capture(), any[Boolean]())

    captureSchema.getValue should equal(corruptedWalletTransactionsSchema)
    assertDataFrameEquals(captureDf.getValue, expectedCorruptedTransactions, ignoredColumnsToCompare: _*)
    captureWriteMode.getValue should equal(null)
  }

  test("validate Completeness of META_LEDGER_TRANSACTION_TYPE") {
    val validTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        meta_ledger_transaction_type = Some("TOPUP")
      )
    )

    val invalidTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        meta_ledger_transaction_type = None
      )
    )

    testValidation(validTransactions, invalidTransactions)
  }

  test("validate Completeness of META_TRANSACTION_ID") {
    val validTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        meta_transaction_id = Some("001-9293-1923")
      )
    )

    val invalidTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        meta_transaction_id = None
      )
    )

    testValidation(validTransactions, invalidTransactions)
  }

  test("validate Completeness of META_COUNTRY_CODE") {
    val validTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        meta_country_code = Some("UK")
      )
    )

    val invalidTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        meta_country_code = None
      )
    )

    testValidation(validTransactions, invalidTransactions)
  }

  test("validate Completeness of META_WALLET_ACCOUNT_ID") {
    val validTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        meta_wallet_account_id = Some("d133353e-5ac5-4d59-b2d8-e20355367b00")
      )
    )

    val invalidTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        meta_wallet_account_id = None
      )
    )

    testValidation(validTransactions, invalidTransactions)
  }

  test("validate Completeness of META_TRANSACTION_DATE_TIME") {
    val validTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        meta_transaction_date_time = Some(Timestamp.valueOf("2024-11-10 21:28:34"))
      )
    )

    val invalidTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        meta_transaction_date_time = None
      )
    )

    testValidation(validTransactions, invalidTransactions)
  }

  test("validate Completeness of META_GATEWAY_ID") {
    val validTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        meta_gateway_id = Some(8),
        meta_ledger_transaction_type = Some(TOPUP)
      ),
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        meta_gateway_id = Some(8),
        meta_ledger_transaction_type = Some(WITHDRAWAL)
      ),
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        meta_gateway_id = Some(8),
        meta_ledger_transaction_type = Some(REDEMPTION)
      ),
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        meta_gateway_id = Some(8),
        meta_ledger_transaction_type = Some(REFUND)
      ),
      //meta_gateway_id mandatory only for TOPUP and WITHDRAWAL
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        meta_gateway_id = None,
        meta_ledger_transaction_type = Some(REDEMPTION)
      ),
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        meta_gateway_id = None,
        meta_ledger_transaction_type = Some(REFUND)
      )
    )

    val invalidTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        meta_gateway_id = None,
        meta_ledger_transaction_type = Some(TOPUP)
      ),
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        meta_gateway_id = None,
        meta_ledger_transaction_type = Some(WITHDRAWAL)
      )
    )

    testValidation(validTransactions, invalidTransactions)
  }

  test("validate Completeness of LOCAL_TRANSACTION_DATE") {
    val validTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        local_transaction_date = Some(Date.valueOf("2025-12-23"))
      )
    )

    val invalidTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        local_transaction_date = None
      )
    )

    testValidation(validTransactions, invalidTransactions)
  }

  test("validate Completeness of LOCAL_CURRENCY") {
    val validTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        local_currency = Some("GBP")
      )
    )

    val invalidTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        local_currency = None
      )
    )

    testValidation(validTransactions, invalidTransactions)
  }

  test("validate Completeness of LOCAL_AMOUNT") {
    val validTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        local_amount = Some(100L)
      )
    )

    val invalidTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        local_amount = None
      )
    )

    testValidation(validTransactions, invalidTransactions)
  }

  test("validate Completeness of WALLET_ENTITY") {
    val validTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        wallet_entity = Some("BHFS_UK")
      )
    )

    val invalidTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        wallet_entity = None
      ),
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        wallet_entity = Some("BHFS_XX")
      )
    )

    testValidation(validTransactions, invalidTransactions)
  }

  test("validate Completeness of LOCAL_DATE") {
    val validTransactions = Seq(
      TestWalletTransactions(
        transaction_reference_id = Some("1234567890"),
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        local_date = Some(20251017)
      )
    )

    val invalidTransactions = Seq(
      TestWalletTransactions(
        transaction_reference_id = Some("1234567890"),
        wallet_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        local_date = None
      )
    )

    testValidation(validTransactions, invalidTransactions)
  }

  test("validate Completeness of META_ITINERARY_ID") {
    val baseTestTxn = TestWalletTransactions(
      transaction_reference_id = Some("123"),
      wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
      ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134")
    )
    val validTransactions = Seq(
      baseTestTxn.copy(meta_ledger_transaction_type = Some("TOPUP")),
      baseTestTxn.copy(meta_ledger_transaction_type = Some("WITHDRAWAL")),
      baseTestTxn.copy(meta_ledger_transaction_type = Some("REDEMPTION")),
      baseTestTxn.copy(meta_ledger_transaction_type = Some("REFUND")),
      baseTestTxn.copy(meta_itinerary_id = None, meta_ledger_transaction_type = Some("TOPUP")),
      baseTestTxn.copy(meta_itinerary_id = None, meta_ledger_transaction_type = Some("WITHDRAWAL"))
    )

    val invalidTransactions = Seq(
      baseTestTxn.copy(meta_itinerary_id = None, meta_ledger_transaction_type = Some("REDEMPTION")),
      baseTestTxn.copy(meta_itinerary_id = None, meta_ledger_transaction_type = Some("REFUND"))
    )

    testValidation(validTransactions, invalidTransactions)
  }

  test("validate Completeness of META_ORIGINAL_TRANSACTION_ID") {
    val baseTestTxn = TestWalletTransactions(
      transaction_reference_id = Some("123"),
      wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
      ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134")
    )
    val validTransactions = Seq(
      baseTestTxn.copy(meta_ledger_transaction_type = Some("TOPUP")),
      baseTestTxn.copy(meta_ledger_transaction_type = Some("WITHDRAWAL")),
      baseTestTxn.copy(meta_ledger_transaction_type = Some("REDEMPTION")),
      baseTestTxn.copy(meta_ledger_transaction_type = Some("REFUND")),
      baseTestTxn.copy(meta_original_transaction_id = None, meta_ledger_transaction_type = Some("TOPUP")),
      baseTestTxn.copy(meta_original_transaction_id = None, meta_ledger_transaction_type = Some("WITHDRAWAL")),
      baseTestTxn.copy(meta_original_transaction_id = None, meta_ledger_transaction_type = Some("REDEMPTION"))
    )

    val invalidTransactions = Seq(
      baseTestTxn.copy(meta_original_transaction_id = None, meta_ledger_transaction_type = Some("REFUND"))
    )

    testValidation(validTransactions, invalidTransactions)
  }

  test("validate Completeness of LOCAL_HOUR") {
    val validTransactions = Seq(
      TestWalletTransactions(
        transaction_reference_id = Some("1234567890"),
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        local_hour = Some(13)
      )
    )

    val invalidTransactions = Seq(
      TestWalletTransactions(
        transaction_reference_id = Some("1234567890"),
        wallet_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        local_hour = None
      )
    )

    testValidation(validTransactions, invalidTransactions)
  }

  test("validate Completeness of transaction_reference_id") {
    val validTransactions = Seq(
      TestWalletTransactions(
        transaction_reference_id = Some("123"),
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        local_hour = Some(13),
        meta_ledger_transaction_type = Some("REDEEM")
      ),
      TestWalletTransactions(
        transaction_reference_id = Some("123"),
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        local_hour = Some(13),
        meta_ledger_transaction_type = Some("TOPUP")
      ),
      TestWalletTransactions(
        transaction_reference_id = Some("123"),
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        local_hour = Some(13),
        meta_ledger_transaction_type = Some("WITHDRAWAL")
      )
    )

    val invalidTransactions = Seq(
      TestWalletTransactions(
        transaction_reference_id = None,
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        local_hour = Some(13),
        meta_ledger_transaction_type = Some("TOPUP")
      ),
      TestWalletTransactions(
        transaction_reference_id = None,
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        local_hour = Some(13),
        meta_ledger_transaction_type = Some("WITHDRAWAL")
      ),
      TestWalletTransactions(
        transaction_reference_id = None,
        wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        local_hour = Some(13),
        meta_ledger_transaction_type = Some("REFUND")
      )
    )

    testValidation(validTransactions, invalidTransactions)
  }

  private def testValidation(validTransactions: Seq[TestWalletTransactions], invalidTransactions: Seq[TestWalletTransactions]): Unit = {
    val walletTransactionsData = (validTransactions ++ invalidTransactions)
      .toDF()
      .withColumn("local_amount", col("local_amount").cast(CalculatedDecimalType))

    val expectedValidTransactions = validTransactions
      .toDF()
      .withColumn("local_amount", col("local_amount").cast(CalculatedDecimalType))
      .withDatadateAndHour

    val expectedCorruptedTransactions = invalidTransactions
      .toDF()
      .withColumn("local_amount", col("local_amount").cast(CalculatedDecimalType))
      .withDatadateAndHour

    val actual = validationProcessor.validate(walletTransactionsData)

    //we cannot compare ID as ID is a random UUID
    assertDataFrameEquals(actual, expectedValidTransactions, ignoredColumnsToCompare: _*)

    val captureSchema: ArgumentCaptor[Schema]    = ArgumentCaptor.forClass(classOf[Schema])
    val captureDf: ArgumentCaptor[DataFrame]     = ArgumentCaptor.forClass(classOf[DataFrame])
    val captureWriteMode: ArgumentCaptor[String] = ArgumentCaptor.forClass(classOf[String])

    verify(repositoryUtil).write(captureSchema.capture(), captureDf.capture(), captureWriteMode.capture(), any[Boolean]())

    captureSchema.getValue should equal(corruptedWalletTransactionsSchema)
    assertDataFrameEquals(captureDf.getValue, expectedCorruptedTransactions, ignoredColumnsToCompare: _*)
    captureWriteMode.getValue should equal(null)
  }
}
