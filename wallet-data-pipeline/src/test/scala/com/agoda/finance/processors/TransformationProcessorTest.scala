package com.agoda.finance.processors

import com.agoda.finance.common.enums.ItemSubType.daily
import com.agoda.finance.common.enums.ItemType.job
import com.agoda.finance.config._
import com.agoda.finance.constants.FinanceDecimalType.CalculatedDecimalType
import com.agoda.finance.constants.WalletConstant.TestWalletTransactionsConstant.SIT_ID
import com.agoda.finance.constants.WalletConstant.walletTransactionsConstant.{CREATED_LOG_TIME, WALLET_TRANSACTION_ID}
import com.agoda.finance.externaldatapipeline.core.models.context.FCDInfo
import com.agoda.finance.models._
import com.agoda.finance.schema._
import com.agoda.finance.test_data.{
  EnrichedJoinedDataFrame,
  JoinedDataFrame,
  TestFilteredLedgerEntries,
  TestFilteredLedgerTransactions,
  TestWalletTransactions
}
import com.agoda.finance.utils.TestUtil.assertDataFrameEquals
import com.agoda.ml.spark.DataFrameMatchers.beEqualTo
import com.agoda.ml.spark.services.etl.{DataFrameETLExtensionLocalTest, SparkETLLocalTest}
import org.apache.spark.sql.Row
import org.apache.spark.sql.functions.col
import org.apache.spark.sql.types.{StructField, StructType}

import java.sql.{Date, Timestamp}
import java.time.ZoneId

class TransformationProcessorTest extends SparkETLLocalTest with DataFrameETLExtensionLocalTest {
  import sqlContext.implicits._

  private lazy val config = WalletConfig(
    "wallet-data-pipeline.unit-test",
    false,
    InputConfig(
      TableConfig("input", "mt_ledger_transactions_test"),
      TableConfig("input", "mt_ledger_entries_test")
    ),
    OutputConfig(
      TableConfig("output", "mt_wallet_transactions_test"),
      TableConfig("output", "mt_duplicated_wallet_transactions_test"),
      TableConfig("output", "mt_corrupted_wallet_transactions_test")
    ),
    Map(
      ("BHFS_US", WalletEntity("BHFS_US", ZoneId.of("America/New_York"), 2, "ledger_acc_id_us", "USD")),
      ("BHFS_UK", WalletEntity("BHFS_UK", ZoneId.of("Europe/London"), 2, "ledger_acc_id_uk", "GBP")),
      ("BHFS_SG", WalletEntity("BHFS_SG", ZoneId.of("Asia/Singapore"), 2, "ledger_acc_id_sg", "SGD"))
    ),
    TableConfig("output", "tracking_test")
  )

  private lazy val executionArgs = ExecutionArguments(datadate = ********, hour = 11)
  private lazy val fcdInfo       = FCDInfo("itemName", "description", job, daily, 20241213)

  private lazy implicit val context = WalletContext(
    executionArgs,
    config,
    InputSchema(
      LedgerTransactionsSchema(config.inputConfig.ledgerTransactionsTable.getTableName),
      LedgerEntriesSchema(config.inputConfig.ledgerEntriesTable.getTableName)
    ),
    OutputSchema(
      WalletTransactionsSchema(config.outputConfig.walletTransactionsTable.getTableName),
      DuplicatedWalletTransactionsSchema(config.outputConfig.duplicatedWalletTransactionsTable.getTableName),
      CorruptedWalletTransactionsSchema(config.outputConfig.corruptedWalletTransactionsTable.getTableName)
    ),
    fcdInfo,
    TrackingSchema(config.tracking.getTableName)
  )

  private lazy val ledgerTransactionsData = Seq(
    TestFilteredLedgerTransactions(
      id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
      meta_itinerary_id = Some("123"),
      meta_ledger_transaction_type = Some("REDEEM"),
      meta_gateway_id = None
    ),
    TestFilteredLedgerTransactions(
      id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
      posted_at = Some(Timestamp.valueOf("2024-11-11 08:15:35")),
      meta_itinerary_id = Some("456"),
      meta_whitelabel_id = Some("2"),
      meta_ledger_transaction_type = Some("TOPUP")
    )
  ).toDF()

  private lazy val ledgerEntriesData = Seq(
    TestFilteredLedgerEntries(
      ledger_account_id = Some("ledger_acc_id_us"),
      ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134")
    ),
    TestFilteredLedgerEntries(
      direction = Some("credit"),
      ledger_account_id = Some("ledger_acc_id_sg"),
      ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a")
    )
  ).toDF()

  private lazy val walletTransactionsByLocalDateData = Seq(
    TestWalletTransactions(
      wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
      ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134")
    )
  ).toDF().withColumn("local_amount", col("local_amount").cast(CalculatedDecimalType))

  private lazy val expectedTransformedWalletTransactionsData = Seq(
    TestWalletTransactions(
      meta_gateway_id = None,
      wallet_transaction_id = Some("f3a89573-ddc6-45c7-8178-78162143361b"),
      ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
      ledger_account_id = Some("ledger_acc_id_us"),
      local_amount = Some(1.00),
      local_date = Some(********),
      local_hour = Some(22),
      wallet_entity = Some("BHFS_US"),
      meta_ledger_transaction_type = Some("REDEEM"),
      meta_itinerary_id = Some(123L)
    ),
    TestWalletTransactions(
      transaction_reference_id = Some("89001"),
      wallet_transaction_id = Some("2578d2e9-3b27-4cc6-a21b-fdfa17497201"),
      ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
      posted_at = Some(Timestamp.valueOf("2024-11-11 08:15:35")),
      ledger_account_id = Some("ledger_acc_id_sg"),
      local_amount = Some(-1.00),
      local_date = Some(********),
      local_hour = Some(11),
      wallet_entity = Some("BHFS_SG"),
      local_currency = Some("SGD"),
      meta_whitelabel_id = Some(2),
      meta_itinerary_id = Some(456L)
    )
  ).toDF().withColumn("local_amount", col("local_amount").cast(CalculatedDecimalType))

  private lazy val transformationProcessor = new TransformationProcessor

  test("TransformationProcessor should correctly prepare data") {
    val filteredData: FilteredData = FilteredData(
      ledgerEntries = ledgerEntriesData,
      ledgerTransactions = ledgerTransactionsData,
      walletTransactionsByLocalDate = walletTransactionsByLocalDateData
    )

    val actual = transformationProcessor.prepareData(filteredData)
    assertDataFrameEquals(actual, expectedTransformedWalletTransactionsData, WALLET_TRANSACTION_ID, SIT_ID, CREATED_LOG_TIME)
    //actual.drop(WALLET_TRANSACTION_ID) should beEqualTo(expectedTransformedWalletTransactionsData.drop(WALLET_TRANSACTION_ID, SIT_ID))
  }

  test("TransformationProcessor should correctly prepare data for empty wallet transaction by local date") {
    val walletTransactionsSchema = context.outputSchema.walletTransactionsSchema

    val struct                 = StructType(walletTransactionsSchema.fields.map(f => StructField(f.name, f.dataType)))
    val emptyRDD               = sqlContext.sparkContext.emptyRDD[Row]
    val emptyWalletTransaction = sqlContext.createDataFrame(emptyRDD, struct)

    val filteredData: FilteredData = FilteredData(
      ledgerEntries = ledgerEntriesData,
      ledgerTransactions = ledgerTransactionsData,
      walletTransactionsByLocalDate = emptyWalletTransaction
    )

    val actual = transformationProcessor.prepareData(filteredData)

    assertDataFrameEquals(actual, expectedTransformedWalletTransactionsData, WALLET_TRANSACTION_ID, SIT_ID, CREATED_LOG_TIME)
  }

  test("TransformationProcessor.enrich should correctly convert meta_transaction_date_time to local_transaction_date") {
    val joinedDf = Seq(
      JoinedDataFrame("BHFS_UK", "trx-001", "2024-06-08T23:00:00.000000Z"),
      JoinedDataFrame("BHFS_UK", "trx-002", "2024-06-08T22:59:00.000000Z"),
      JoinedDataFrame("BHFS_SG", "trx-003", "2024-06-08T16:00:00.000000Z"),
      JoinedDataFrame("BHFS_SG", "trx-004", "2024-06-08T15:59:00.000000Z"),
      JoinedDataFrame("BHFS_US", "trx-005", "2024-06-08T04:00:00.000000Z"),
      JoinedDataFrame("BHFS_US", "trx-006", "2024-06-08T03:59:00.000000Z")
    ).toDF()

    val actual = transformationProcessor.enrichWalletTransactions(joinedDf)

    val expected = Seq(
      EnrichedJoinedDataFrame(
        "BHFS_UK",
        "trx-001",
        "2024-06-08T23:00:00.000000Z",
        Date.valueOf("2024-06-09"),
        ********,
        3,
        "GBP",
        "trx-001"
      ),
      EnrichedJoinedDataFrame(
        "BHFS_UK",
        "trx-002",
        "2024-06-08T22:59:00.000000Z",
        Date.valueOf("2024-06-08"),
        ********,
        3,
        "GBP",
        "trx-002"
      ),
      EnrichedJoinedDataFrame(
        "BHFS_SG",
        "trx-003",
        "2024-06-08T16:00:00.000000Z",
        Date.valueOf("2024-06-09"),
        ********,
        11,
        "SGD",
        "trx-003"
      ),
      EnrichedJoinedDataFrame(
        "BHFS_SG",
        "trx-004",
        "2024-06-08T15:59:00.000000Z",
        Date.valueOf("2024-06-08"),
        ********,
        11,
        "SGD",
        "trx-004"
      ),
      EnrichedJoinedDataFrame(
        "BHFS_US",
        "trx-005",
        "2024-06-08T04:00:00.000000Z",
        Date.valueOf("2024-06-08"),
        ********,
        22,
        "USD",
        "trx-005"
      ),
      EnrichedJoinedDataFrame(
        "BHFS_US",
        "trx-006",
        "2024-06-08T03:59:00.000000Z",
        Date.valueOf("2024-06-07"),
        ********,
        22,
        "USD",
        "trx-006"
      )
    ).toDF()

    assertDataFrameEquals(actual, expected, WALLET_TRANSACTION_ID, SIT_ID, CREATED_LOG_TIME)
  }
  test("TransformationProcessor.enrich should correctly create transaction_reference_id from meta_transaction_id") {
    val joinedDf = Seq(
      JoinedDataFrame("BHFS_UK", "trx-001", "2024-06-08T23:00:00.000000Z", meta_gateway_id = Some(8), meta_transaction_id = Some("89001-FA32CB")),
      JoinedDataFrame(
        "BHFS_UK",
        "trx-002",
        "2024-06-08T23:00:00.000000Z",
        meta_gateway_id = Some(3),
        meta_transaction_id = Some("123-18-333:US-I111D:T")
      ),
      JoinedDataFrame("BHFS_UK", "trx-003", "2024-06-08T23:00:00.000000Z", meta_transaction_id = Some("123"))
    ).toDF()

    val actual = transformationProcessor.enrichWalletTransactions(joinedDf)

    val expected = Seq(
      EnrichedJoinedDataFrame(
        "BHFS_UK",
        "trx-001",
        "2024-06-08T23:00:00.000000Z",
        Date.valueOf("2024-06-09"),
        ********,
        3,
        "GBP",
        "trx-001",
        meta_gateway_id = Some(8),
        meta_transaction_id = Some("89001-FA32CB"),
        transaction_reference_id = Some("89001")
      ),
      EnrichedJoinedDataFrame(
        "BHFS_UK",
        "trx-002",
        "2024-06-08T23:00:00.000000Z",
        Date.valueOf("2024-06-09"),
        ********,
        3,
        "GBP",
        "trx-002",
        meta_gateway_id = Some(3),
        meta_transaction_id = Some("123-18-333:US-I111D:T"),
        transaction_reference_id = Some("333")
      ),
      EnrichedJoinedDataFrame(
        "BHFS_UK",
        "trx-003",
        "2024-06-08T23:00:00.000000Z",
        Date.valueOf("2024-06-09"),
        ********,
        3,
        "GBP",
        "trx-003",
        meta_transaction_id = Some("123"),
        transaction_reference_id = Some("123")
      )
    ).toDF()

    assertDataFrameEquals(actual, expected, WALLET_TRANSACTION_ID, SIT_ID, CREATED_LOG_TIME)
  }
}
