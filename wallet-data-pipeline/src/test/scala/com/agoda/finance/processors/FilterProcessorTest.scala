package com.agoda.finance.processors

import com.agoda.finance.common.enums.ItemSubType.daily
import com.agoda.finance.common.enums.ItemType.job
import com.agoda.finance.config.{InputConfig, OutputConfig, TableConfig, WalletConfig, WalletEntity}
import com.agoda.finance.constants.FinanceDecimalType.CalculatedDecimalType
import com.agoda.finance.constants.WalletConstant.TestWalletTransactionsConstant.SIT_ID
import com.agoda.finance.constants.WalletConstant.ledgerEntriesConstant.{
  AMOUNT_DECIMAL,
  DISCARDED_AT_LOCK_VERSION,
  LEDGER_ACCOUNT_LOCK_VERSION,
  PENDING_CREDITS,
  PENDING_DEBITS,
  POSTED_CREDITS,
  POSTED_DEBITS
}
import com.agoda.finance.constants.WalletConstant.trackingConstant.MASTER
import com.agoda.finance.constants.WalletConstant.walletTransactionsConstant._
import com.agoda.finance.externaldatapipeline.core.models.context.FCDInfo
import com.agoda.finance.externaldatapipeline.core.schema.{Field, Schema}
import com.agoda.finance.externaldatapipeline.core.utils.{RepositoryUtiImpl, SchemaUtil}
import com.agoda.finance.models.{ExecutionArguments, InputSchema, OutputSchema, WalletContext}
import com.agoda.finance.schema._
import com.agoda.finance.test_data._
import com.agoda.finance.utils.TestUtil.assertDataFrameEquals
import com.agoda.ml.spark.DataFrameMatchers.beEqualTo
import com.agoda.ml.spark.{HiveSupport, SparkSharedLocalTest}
import com.agoda.ml.spark.services.etl.{DataFrameETLExtensionLocalTest, SparkETLLocalTest}
import org.apache.spark.sql.{DataFrame, Row}
import org.apache.spark.sql.functions.{col, current_timestamp}
import org.apache.spark.sql.types.{LongType, StructField, StructType}
import org.mockito.ArgumentCaptor

import java.sql.Timestamp
import java.time.ZoneId

class FilterProcessorTest extends SparkETLLocalTest with DataFrameETLExtensionLocalTest with SparkSharedLocalTest with HiveSupport {
  import sqlContext.implicits._

  def createEmptyTableWithPartition(
      schemaFields: Seq[Field],
      partitionColumns: Seq[String],
      tableName: String
  ): Unit = {
    val schema         = StructType(schemaFields.map(f => StructField(f.name, f.dataType)))
    val emptyRDD       = sqlContext.sparkContext.emptyRDD[Row]
    val emptyDataFrame = sqlContext.createDataFrame(emptyRDD, schema)

    emptyDataFrame.write
      .partitionBy(partitionColumns: _*)
      .saveAsTable(tableName)
  }

  private lazy val config = WalletConfig(
    "wallet-data-pipeline.unit-test",
    false,
    InputConfig(
      TableConfig("input", "mt_ledger_transactions_test"),
      TableConfig("input", "mt_ledger_entries_test")
    ),
    OutputConfig(
      TableConfig("output", "mt_wallet_transactions_test"),
      TableConfig("output", "mt_duplicated_wallet_transactions_test"),
      TableConfig("output", "mt_corrupted_wallet_transactions_test")
    ),
    Map(
      ("BHFS_US", WalletEntity("BHFS_US", ZoneId.of("America/New_York"), 0, "ledger_acc_id_us", "")),
      ("BHFS_UK", WalletEntity("BHFS_UK", ZoneId.of("Europe/London"), 0, "ledger_acc_id_uk", "")),
      ("BHFS_SG", WalletEntity("BHFS_SG", ZoneId.of("Asia/Singapore"), 0, "ledger_acc_id_sg", ""))
    ),
    TableConfig("output", "tracking_test")
  )

  private lazy val executionArgs = ExecutionArguments(datadate = ********, hour = 11)
  private lazy val fcdInfo       = FCDInfo("itemName", "description", job, daily, 20241213)

  private lazy implicit val context = WalletContext(
    executionArgs,
    config,
    InputSchema(
      LedgerTransactionsSchema(config.inputConfig.ledgerTransactionsTable.getTableName),
      LedgerEntriesSchema(config.inputConfig.ledgerEntriesTable.getTableName)
    ),
    OutputSchema(
      WalletTransactionsSchema(config.outputConfig.walletTransactionsTable.getTableName),
      DuplicatedWalletTransactionsSchema(config.outputConfig.duplicatedWalletTransactionsTable.getTableName),
      CorruptedWalletTransactionsSchema(config.outputConfig.corruptedWalletTransactionsTable.getTableName)
    ),
    fcdInfo,
    TrackingSchema(config.tracking.getTableName)
  )

  // Test data
  private lazy val trackingData = Seq(
    TestTracking(
      source = Some("ledger_transactions"),
      source_datadate = Some(********),
      source_hour = Some(9)
    ),
    TestTracking(
      source = Some("ledger_transactions"),
      source_datadate = Some(********),
      source_hour = Some(10)
    ),
    TestTracking(
      source = Some("ledger_entries"),
      source_datadate = Some(********),
      source_hour = Some(9)
    ),
    TestTracking(
      source = Some("ledger_entries"),
      source_datadate = Some(********),
      source_hour = Some(10)
    )
  ).toDF()

  // Test data
  private lazy val trackingData2 = Seq(
    TestTracking(
      source = Some("ledger_transactions"),
      source_datadate = Some(********),
      source_hour = Some(8)
    ),
    TestTracking(
      source = Some("ledger_entries"),
      source_datadate = Some(********),
      source_hour = Some(8)
    )
  ).toDF()

  private lazy val ledgerTransactionsData = Seq(
    TestLedgerTransactions(
      id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
      metadata_json = Some(
        """{"itineraryId":"123","gatewayId":"8","state":"AZ","countryCode":"US","transactionId":"89001-FA32CB","transactionDateTime":"2024-11-10T14:28:34.000000Z","ledgerTransactionType":"REDEEM","categoryId":"category","walletTransactionId":"1","walletAccountId":"wallet-account-id","originalTransactionId":"2","kycId":"kyc-id", "whitelabelId":"1", "paymentMethodId":"29"}"""
      ),
      status = Some("pending")
    ),
    TestLedgerTransactions(
      id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
      metadata_json = Some(
        """{"itineraryId":"456","gatewayId":"8","state":"AZ","countryCode":"SG","transactionId":"89001-FA32CB00","transactionDateTime":"2024-11-10T14:28:34.000000Z","ledgerTransactionType":"TOPUP","categoryId":"category","walletTransactionId":"1","walletAccountId":"wallet-account-id","originalTransactionId":"2","kycId":"kyc-id","whitelabelId":"2","paymentMethodId":"29"}"""
      ),
      posted_at = Some(Timestamp.valueOf("2024-11-11 08:15:35"))
    ),
    TestLedgerTransactions(
      id = Some("01931a26-a6b7-7156-8508-f15fe4b7849a"),
      metadata_json = Some(
        """{"itineraryId":"456","gatewayId":"8","state":"AZ","countryCode":"SG","transactionId":"89001-FA32CB00","transactionDateTime":"2024-11-10 14:28:34","ledgerTransactionType":"TOPUP","categoryId":"category","walletTransactionId":"1","walletAccountId":"wallet-account-id","originalTransactionId":"2","kycId":"kyc-id","whitelabelId":"2","paymentMethodId": "29"}"""
      ),
      live_mode = Some(true)
    )
  ).toDF()

  private lazy val ledgerTransactionsData2 = Seq(
    TestLedgerTransactions(
      id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
      metadata_json = Some(
        """{"itineraryId":"123","gatewayId":"8","state":"AZ","countryCode":"US","transactionId":"89001-FA32CB","transactionDateTime":"2024-11-10T14:28:34.000000Z","ledgerTransactionType":"REDEEM","categoryId":"category","walletTransactionId":"1","walletAccountId":"wallet-account-id","originalTransactionId":"2","kycId":"kyc-id","whitelabelId":"1","paymentMethodId":"29"}"""
      ),
      status = Some("pending")
    ),
    TestLedgerTransactions(
      id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
      metadata_json = Some(
        """{"itineraryId":"456","gatewayId":"8","state":"AZ","countryCode":"SG","transactionId":"89001-FA32CB00","transactionDateTime":"2024-11-10T14:28:34.000000Z","ledgerTransactionType":"TOPUP","categoryId":"category","walletTransactionId":"1","walletAccountId":"wallet-account-id","originalTransactionId":"2","kycId":"kyc-id","whitelabelId":"2","paymentMethodId":"29"}"""
      ),
      posted_at = Some(Timestamp.valueOf("2024-11-11 08:15:35")),
      datadate = Some(********),
      hour = Some(12)
    )
  ).toDF()

  private lazy val ledgerTransactionsData3 = Seq(
    TestLedgerTransactions(
      id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
      metadata_json = Some(
        """{"itineraryId":"123","gatewayId":"8","state":"AZ","countryCode":"US","transactionId":"89001-FA32CB", "transactionDateTime":"2024-11-10T14:28:34.000000Z","ledgerTransactionType":"REDEEM","categoryId":"category","walletTransactionId":"1","walletAccountId":"wallet-account-id","originalTransactionId":"2","kycId":"kyc-id","whitelabelId":"1","paymentMethodId":"29"}"""
      ),
      datadate = Some(********),
      hour = Some(7)
    ),
    TestLedgerTransactions(
      id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
      metadata_json = Some(
        """{"itineraryId":"456","gatewayId":"8","state":"AZ","countryCode":"US","transactionId":"89001-FA32CB","transactionDateTime":"2024-11-10T14:28:34.000000Z","ledgerTransactionType":"REDEEM","categoryId":"category","walletTransactionId":"1","walletAccountId":"wallet-account-id","originalTransactionId":"2","kycId":"kyc-id","whitelabelId":"1","paymentMethodId":"29"}"""
      ),
      datadate = Some(********),
      hour = Some(10)
    ),
    TestLedgerTransactions(
      id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
      metadata_json = Some(
        """{"itineraryId":"456","gatewayId":"8","state":"AZ","countryCode":"SG","transactionId":"89001-FA32CB00","transactionDateTime":"2024-11-10T14:28:34.000000Z","ledgerTransactionType":"TOPUP","categoryId":"category","walletTransactionId":"1","walletAccountId":"wallet-account-id","originalTransactionId":"2","kycId":"kyc-id","whitelabelId":"2","paymentMethodId":"29"}"""
      ),
      posted_at = Some(Timestamp.valueOf("2024-11-11 08:15:35")),
      datadate = Some(********),
      hour = Some(8)
    ),
    TestLedgerTransactions(
      id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
      metadata_json = Some(
        """{"itineraryId":"678","gatewayId":"8","state":"AZ","countryCode":"SG","transactionId":"89001-FA32CB00","transactionDateTime":"2024-11-10T14:28:34.000000Z","ledgerTransactionType":"TOPUP","categoryId":"category","walletTransactionId":"1","walletAccountId":"wallet-account-id","originalTransactionId":"2","kycId":"kyc-id","whitelabelId":"2","paymentMethodId":"29"}"""
      ),
      posted_at = Some(Timestamp.valueOf("2024-11-11 08:15:35")),
      datadate = Some(********),
      hour = Some(10)
    ),
    TestLedgerTransactions(
      id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
      metadata_json = Some(
        """{"itineraryId":"987","gatewayId":"8","state":"AZ","countryCode":"SG","transactionId":"89001-FA32CB00","transactionDateTime":"2024-11-10T14:28:34.000000Z","ledgerTransactionType":"TOPUP","categoryId":"category","walletTransactionId":"1","walletAccountId":"wallet-account-id","originalTransactionId":"2","kycId":"kyc-id","whitelabelId":"2","paymentMethodId":"29"}"""
      ),
      posted_at = Some(Timestamp.valueOf("2024-11-11 08:15:35")),
      datadate = Some(********),
      hour = Some(11)
    )
  ).toDF()

  private lazy val ledgerEntriesData = Seq(
    TestLedgerEntries(
      id = Some("01931a22-33a7-747f-81a7-8b3700796ab0"),
      ledger_account_id = Some("ledger_acc_id_us"),
      ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
      live_mode = Some(true),
      datadate = Some(********),
      hour = Some(22)
    ),
    TestLedgerEntries(
      direction = Some("credit"),
      id = Some("01931a22-33a8-723c-9af8-bbbe2f430f2b"),
      ledger_account_id = Some("per_customer_account"),
      ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134")
    ),
    TestLedgerEntries(
      direction = Some("credit"),
      id = Some("01931a4a-e344-7d4d-8ca7-708340778f0c"),
      ledger_account_id = Some("ledger_acc_id_sg"),
      ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a")
    ),
    TestLedgerEntries(
      id = Some("01931a4a-e346-7b8c-81f5-e59848cc65a3"),
      ledger_account_id = Some("per_customer_account"),
      ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a")
    ),
    TestLedgerEntries(
      id = Some("01931a4a-e346-7b8c-81f5-e59848cc75a5"),
      ledger_account_id = Some("ledger_acc_id_sg"),
      ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
      status = Some("pending")
    )
  ).toDF()

  private lazy val ledgerEntriesData3 = Seq(
    TestLedgerEntries(
      id = Some("01931a22-33a7-747f-81a7-8b3700796ab0"),
      ledger_account_id = Some("ledger_acc_id_us"),
      ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
      datadate = Some(********),
      hour = Some(7)
    ),
    TestLedgerEntries(
      id = Some("01931a22-33a7-747f-81a7-8b3700796ab0"),
      ledger_account_id = Some("ledger_acc_id_sg"),
      ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
      datadate = Some(********),
      hour = Some(10)
    ),
    TestLedgerEntries(
      direction = Some("credit"),
      id = Some("01931a22-33a8-723c-9af8-bbbe2f430f2b"),
      ledger_account_id = Some("per_customer_account"),
      ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134")
    ),
    TestLedgerEntries(
      direction = Some("credit"),
      id = Some("01931a4a-e344-7d4d-8ca7-708340778f0c"),
      ledger_account_id = Some("ledger_acc_id_sg"),
      ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
      datadate = Some(********),
      hour = Some(9)
    )
  ).toDF()

  private lazy val ledgerEntriesData2 = Seq(
    TestLedgerEntries(
      id = Some("01931a22-33a7-747f-81a7-8b3700796ab0"),
      ledger_account_id = Some("ledger_acc_id_us"),
      ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134")
    ),
    TestLedgerEntries(
      direction = Some("credit"),
      id = Some("01931a22-33a8-723c-9af8-bbbe2f430f2b"),
      ledger_account_id = Some("per_customer_account"),
      ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134")
    ),
    TestLedgerEntries(
      direction = Some("credit"),
      id = Some("01931a4a-e344-7d4d-8ca7-708340778f0c"),
      ledger_account_id = Some("ledger_acc_id_sg"),
      ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
      datadate = Some(********),
      hour = Some(11)
    )
  ).toDF()

  private lazy val walletTransactionsData = Seq(
    TestWalletTransactions(
      wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
      ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134")
    ),
    TestWalletTransactions(
      wallet_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
      ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
      posted_at = Some(Timestamp.valueOf("2024-11-11 08:15:35")),
      meta_country_code = Some("SG"),
      ledger_account_id = Some("ledger_acc_id_sg"),
      meta_itinerary_id = Some(456L),
      meta_transaction_id = Some("89001-FA32CB00"),
      local_amount = Some(-100.0),
      local_date = Some(********),
      local_hour = Some(11),
      wallet_entity = Some("BHFS_SG"),
      local_currency = Some("SGD")
    )
  ).toDF().withColumn(LOCAL_AMOUNT, col(LOCAL_AMOUNT).cast(CalculatedDecimalType)).withColumn(CREATED_LOG_TIME, current_timestamp())

  private lazy val expectedWalletTransactions = Seq(
    TestWalletTransactions(
      wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
      ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134")
    ),
    TestWalletTransactions(
      wallet_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
      ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
      local_date = Some(********),
      local_hour = Some(11),
      ledger_account_id = Some("ledger_acc_id_sg"),
      meta_transaction_id = Some("89001-FA32CB00"),
      meta_itinerary_id = Some(456L),
      local_currency = Some("SGD"),
      meta_country_code = Some("SG"),
      wallet_entity = Some("BHFS_SG"),
      local_amount = Some(-100.0),
      posted_at = Some(Timestamp.valueOf("2024-11-11 08:15:35"))
    )
  ).toDF()
    .withColumn("local_amount", col("local_amount").cast(CalculatedDecimalType))

  val schemaCaptor: ArgumentCaptor[Schema]           = ArgumentCaptor.forClass(classOf[Schema])
  private lazy val repositoryUtil: RepositoryUtiImpl = new RepositoryUtiImpl()(sqlContext)

  setupAll {
    sqlContext.sql(s"CREATE DATABASE IF NOT EXISTS input")
    sqlContext.sql(s"CREATE DATABASE IF NOT EXISTS output")

  }

  setup {
    spark.conf.set("spark.sql.sources.commitProtocolClass", "org.apache.spark.sql.execution.datasources.SQLHadoopMapReduceCommitProtocol")

    createEmptyTableWithPartition(context.trackingSchema.fields, Seq(MASTER), context.trackingSchema.tableName)
    createEmptyTableWithPartition(
      context.inputSchema.ledgerEntriesSchema.fields,
      Seq(DATADATE, HOUR),
      context.inputSchema.ledgerEntriesSchema.tableName
    )
    createEmptyTableWithPartition(
      context.inputSchema.ledgerTransactionsSchema.fields,
      Seq(DATADATE, HOUR),
      context.inputSchema.ledgerTransactionsSchema.tableName
    )
    createEmptyTableWithPartition(
      context.outputSchema.walletTransactionsSchema.fields,
      Seq(WALLET_ENTITY, LOCAL_DATE),
      context.outputSchema.walletTransactionsSchema.tableName
    )

    println(s"Commit Protocol: ${spark.conf.get("spark.sql.sources.commitProtocolClass")}")
    sqlContext.sql(s"DESCRIBE TABLE ${context.trackingSchema.tableName};")
    sqlContext.sql(s"DESCRIBE TABLE ${context.inputSchema.ledgerEntriesSchema.tableName};")
    sqlContext.sql(s"DESCRIBE TABLE ${context.inputSchema.ledgerTransactionsSchema.tableName};")
    sqlContext.sql(s"DESCRIBE TABLE ${context.outputSchema.walletTransactionsSchema.tableName};")

  }

  teardown {
    sqlContext.sql(s"DROP TABLE IF EXISTS ${context.trackingSchema.tableName}")
    sqlContext.sql(s"DROP TABLE IF EXISTS ${context.inputSchema.ledgerEntriesSchema.tableName}")
    sqlContext.sql(s"DROP TABLE IF EXISTS ${context.inputSchema.ledgerTransactionsSchema.tableName}")
    sqlContext.sql(s"DROP TABLE IF EXISTS ${context.outputSchema.walletTransactionsSchema.tableName}")
  }

  teardownAll {
    sqlContext.sql(s"DROP DATABASE IF EXISTS input CASCADE")
    sqlContext.sql(s"DROP DATABASE IF EXISTS output CASCADE")
  }

  private implicit class LedgerEntriesDataframe(val df: DataFrame) {
    def castEntriesNumberColumns(): DataFrame =
      df.withColumn(AMOUNT_DECIMAL, col(AMOUNT_DECIMAL).cast(CalculatedDecimalType))
        .withColumn(PENDING_CREDITS, col(PENDING_CREDITS).cast(CalculatedDecimalType))
        .withColumn(POSTED_CREDITS, col(POSTED_CREDITS).cast(CalculatedDecimalType))
        .withColumn(PENDING_DEBITS, col(PENDING_DEBITS).cast(CalculatedDecimalType))
        .withColumn(POSTED_DEBITS, col(POSTED_DEBITS).cast(CalculatedDecimalType))
        .withColumn(DISCARDED_AT_LOCK_VERSION, col(DISCARDED_AT_LOCK_VERSION).cast(LongType))
        .withColumn(LEDGER_ACCOUNT_LOCK_VERSION, col(LEDGER_ACCOUNT_LOCK_VERSION).cast(LongType))
  }

  test("FilterProcessor should correctly filter data in a normal scenario") {
    val walletTransactionsSchema: WalletTransactionsSchema = context.outputSchema.walletTransactionsSchema
    val trackingSchema: TrackingSchema                     = context.trackingSchema
    val ledgerEntriesSchema: LedgerEntriesSchema           = context.inputSchema.ledgerEntriesSchema
    val ledgerTransactionsSchema: LedgerTransactionsSchema = context.inputSchema.ledgerTransactionsSchema

    repositoryUtil.write(trackingSchema, trackingData)
    repositoryUtil.write(walletTransactionsSchema, SchemaUtil.transformColumn(walletTransactionsSchema.fields, walletTransactionsData))
    repositoryUtil.write(ledgerTransactionsSchema, ledgerTransactionsData)
    val alignedLedgerEntriesData = ledgerEntriesData
      .castEntriesNumberColumns()
    repositoryUtil.write(ledgerEntriesSchema, alignedLedgerEntriesData)

    val filterProcessor = new FilterProcessor(repositoryUtil)(context)

    val filteredData = filterProcessor.getFilteredData

    val expectedLedgerEntries = Seq(
      TestFilteredLedgerEntries(
        direction = Some("credit"),
        ledger_account_id = Some("ledger_acc_id_sg"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        local_amount = Some(100)
      )
    ).toDF()
      .withColumn("local_amount", col("local_amount").cast(CalculatedDecimalType))

    val expectedLedgerTransactions = Seq(
      TestFilteredLedgerTransactions(
        posted_at = Some(Timestamp.valueOf("2024-11-11 08:15:35")),
        id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        meta_itinerary_id = Some("456"),
        meta_transaction_id = Some("89001-FA32CB00"),
        meta_whitelabel_id = Some("2"),
        meta_country_code = Some("SG")
      )
    ).toDF()

    filteredData.ledgerEntries should beEqualTo(expectedLedgerEntries)
    filteredData.ledgerTransactions should beEqualTo(expectedLedgerTransactions)
    assertDataFrameEquals(filteredData.walletTransactionsByLocalDate, expectedWalletTransactions, WALLET_TRANSACTION_ID, SIT_ID, CREATED_LOG_TIME)
  }

  test("FilterProcessor should correctly filter data when previous job execution failed") {
    // run job at ******** 11
    // job at 9,10 was failed
    // latest source hour from tracking table will be 8
    val filterProcessor = new FilterProcessor(repositoryUtil)(context)

    val walletTransactionsSchema: WalletTransactionsSchema = context.outputSchema.walletTransactionsSchema
    val trackingSchema: TrackingSchema                     = context.trackingSchema
    val ledgerEntriesSchema: LedgerEntriesSchema           = context.inputSchema.ledgerEntriesSchema
    val ledgerTransactionsSchema: LedgerTransactionsSchema = context.inputSchema.ledgerTransactionsSchema

    repositoryUtil.write(trackingSchema, trackingData2)
    repositoryUtil.write(ledgerTransactionsSchema, ledgerTransactionsData3)
    repositoryUtil.write(walletTransactionsSchema, SchemaUtil.transformColumn(walletTransactionsSchema.fields, walletTransactionsData))
    val alignedLedgerEntriesData = ledgerEntriesData3
      .castEntriesNumberColumns()
    repositoryUtil.write(ledgerEntriesSchema, alignedLedgerEntriesData)

    val filteredData = filterProcessor.getFilteredData

    val expectedLedgerTransactions = Seq(
      TestFilteredLedgerTransactions(
        id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        meta_itinerary_id = Some("456"),
        meta_whitelabel_id = Some("2"),
        meta_transaction_id = Some("89001-FA32CB00"),
        posted_at = Some(Timestamp.valueOf("2024-11-11 08:15:35")),
        datadate = Some(********),
        hour = Some(8),
        meta_country_code = Some("SG")
      ),
      TestFilteredLedgerTransactions(
        id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
        meta_itinerary_id = Some("456"),
        datadate = Some(********),
        hour = Some(10),
        meta_ledger_transaction_type = Some("REDEEM")
      ),
      TestFilteredLedgerTransactions(
        id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        meta_itinerary_id = Some("678"),
        meta_whitelabel_id = Some("2"),
        meta_transaction_id = Some("89001-FA32CB00"),
        posted_at = Some(Timestamp.valueOf("2024-11-11 08:15:35")),
        datadate = Some(********),
        hour = Some(10),
        meta_country_code = Some("SG")
      ),
      TestFilteredLedgerTransactions(
        id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        meta_itinerary_id = Some("987"),
        meta_whitelabel_id = Some("2"),
        meta_transaction_id = Some("89001-FA32CB00"),
        posted_at = Some(Timestamp.valueOf("2024-11-11 08:15:35")),
        datadate = Some(********),
        hour = Some(11),
        meta_country_code = Some("SG")
      )
    ).toDF()

    val expectedLedgerEntries = Seq(
      TestFilteredLedgerEntries(
        ledger_account_id = Some("ledger_acc_id_sg"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a")
      ),
      TestFilteredLedgerEntries(
        direction = Some("credit"),
        ledger_account_id = Some("ledger_acc_id_sg"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        local_amount = Some(100.00)
      )
    ).toDF()
      .withColumn("local_amount", col("local_amount").cast(CalculatedDecimalType))

    filteredData.ledgerEntries should beEqualTo(expectedLedgerEntries)
    filteredData.ledgerTransactions should beEqualTo(expectedLedgerTransactions)
    assertDataFrameEquals(filteredData.walletTransactionsByLocalDate, expectedWalletTransactions, WALLET_TRANSACTION_ID, SIT_ID, CREATED_LOG_TIME)
  }

  test("FilterProcessor should handle empty tracking table gracefully") {
    val filterProcessor = new FilterProcessor(repositoryUtil)(context)

    val walletTransactionsSchema: WalletTransactionsSchema = context.outputSchema.walletTransactionsSchema
    val trackingSchema: TrackingSchema                     = context.trackingSchema
    val ledgerEntriesSchema: LedgerEntriesSchema           = context.inputSchema.ledgerEntriesSchema
    val ledgerTransactionsSchema: LedgerTransactionsSchema = context.inputSchema.ledgerTransactionsSchema

    val struct        = StructType(trackingSchema.fields.map(f => StructField(f.name, f.dataType)))
    val emptyRDD      = sqlContext.sparkContext.emptyRDD[Row]
    val emptyTracking = sqlContext.createDataFrame(emptyRDD, struct)

    repositoryUtil.write(trackingSchema, emptyTracking)
    repositoryUtil.write(walletTransactionsSchema, SchemaUtil.transformColumn(walletTransactionsSchema.fields, walletTransactionsData))
    repositoryUtil.write(ledgerTransactionsSchema, ledgerTransactionsData2)
    val alignedLedgerEntriesData = ledgerEntriesData2
      .castEntriesNumberColumns()
    repositoryUtil.write(ledgerEntriesSchema, alignedLedgerEntriesData)

    val filteredData = filterProcessor.getFilteredData

    val expectedLedgerEntries = Seq(
      TestFilteredLedgerEntries(
        ledger_account_id = Some("ledger_acc_id_us"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134")
      ),
      TestFilteredLedgerEntries(
        direction = Some("credit"),
        ledger_account_id = Some("ledger_acc_id_sg"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        local_amount = Some(100)
      )
    ).toDF().withColumn("local_amount", col("local_amount").cast(CalculatedDecimalType))

    val expectedLedgerTransactions = Seq(
      TestFilteredLedgerTransactions(
        posted_at = Some(Timestamp.valueOf("2024-11-11 08:15:35")),
        id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
        meta_itinerary_id = Some("456"),
        meta_transaction_id = Some("89001-FA32CB00"),
        datadate = Some(********),
        hour = Some(12),
        meta_whitelabel_id = Some("2"),
        meta_country_code = Some("SG")
      )
    ).toDF()

    filteredData.ledgerEntries should beEqualTo(expectedLedgerEntries)
    filteredData.ledgerTransactions should beEqualTo(expectedLedgerTransactions)
    assertDataFrameEquals(filteredData.walletTransactionsByLocalDate, expectedWalletTransactions, WALLET_TRANSACTION_ID, SIT_ID, CREATED_LOG_TIME)
  }

}
