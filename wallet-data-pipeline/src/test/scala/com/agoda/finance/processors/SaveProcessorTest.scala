package com.agoda.finance.processors

import com.agoda.finance.common.enums.ItemSubType.daily
import com.agoda.finance.common.enums.ItemType.job
import com.agoda.finance.config.{InputConfig, OutputConfig, TableConfig, WalletConfig, WalletEntity}
import com.agoda.finance.constants.FinanceDecimalType.CalculatedDecimalType
import com.agoda.finance.constants.WalletConstant.TestWalletTransactionsConstant.SIT_ID
import com.agoda.finance.constants.WalletConstant.walletTransactionsConstant.CREATED_LOG_TIME
import com.agoda.finance.externaldatapipeline.core.models.context.FCDInfo
import com.agoda.finance.externaldatapipeline.core.utils.RepositoryUtiImpl
import com.agoda.finance.models.{ExecutionArguments, InputSchema, OutputSchema, WalletContext}
import com.agoda.finance.schema.{
  CorruptedWalletTransactionsSchema,
  DuplicatedWalletTransactionsSchema,
  LedgerEntriesSchema,
  LedgerTransactionsSchema,
  TrackingSchema,
  WalletTransactionsSchema
}
import com.agoda.finance.test_data.{TestLedgerEntries, TestLedgerTransactions, TestTracking, TestWalletTransactions}
import com.agoda.finance.utils.TestUtil.assertDataFrameEquals
import com.agoda.ml.spark.DataFrameMatchers.beEqualTo
import com.agoda.ml.spark.services.etl.{DataFrameETLExtensionLocalTest, SparkETLLocalTest}
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.col
import org.mockito.{ArgumentCaptor, ArgumentMatchers}
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{reset, verify, when}
import org.scalatest.mockito.MockitoSugar.mock

import java.sql.Timestamp

class SaveProcessorTest extends SparkETLLocalTest with DataFrameETLExtensionLocalTest {
  import sqlContext.implicits._
  private lazy val config = WalletConfig(
    "wallet-data-pipeline.unit-test",
    false,
    InputConfig(
      TableConfig("input", "mt_ledger_transactions_test"),
      TableConfig("input", "mt_ledger_entries_test")
    ),
    OutputConfig(
      TableConfig("output", "mt_wallet_transactions_test"),
      TableConfig("output", "mt_duplicated_wallet_transactions_test"),
      TableConfig("output", "mt_corrupted_wallet_transactions_test")
    ),
    Map(
      ("bhfs_us_entity", "ledger_acc_id_us"),
      ("bhfs_uk_entity", "ledger_acc_id_uk"),
      ("bhfs_sg_entity", "ledger_acc_id_sg")
    ).mapValues(WalletEntity("", null, 0, _, "")),
    TableConfig("output", "tracking_test")
  )

  private lazy val executionArgs = ExecutionArguments(datadate = ********, hour = 11)
  private lazy val fcdInfo       = FCDInfo("itemName", "description", job, daily, ********)

  private lazy implicit val context = WalletContext(
    executionArgs,
    config,
    InputSchema(
      LedgerTransactionsSchema(config.inputConfig.ledgerTransactionsTable.getTableName),
      LedgerEntriesSchema(config.inputConfig.ledgerEntriesTable.getTableName)
    ),
    OutputSchema(
      WalletTransactionsSchema(config.outputConfig.walletTransactionsTable.getTableName),
      DuplicatedWalletTransactionsSchema(config.outputConfig.duplicatedWalletTransactionsTable.getTableName),
      CorruptedWalletTransactionsSchema(config.outputConfig.corruptedWalletTransactionsTable.getTableName)
    ),
    fcdInfo,
    TrackingSchema(config.tracking.getTableName)
  )

  private lazy val ledgerTransactionsData = Seq(
    TestLedgerTransactions(
      id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
      metadata_json = Some(
        """{"itinerary_id": 123,"gateway_id":8,"state":"AZ", "country":"US", "wallet_entity":1, "transaction_id":"89001-FA32CB", "transaction_date":"2024-11-10 14:28:34", "transaction_type":1, "category_id":"category","ref_ledger_transaction_id":"1","original_transaction_id":"2"}"""
      )
    ),
    TestLedgerTransactions(
      id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
      metadata_json = Some(
        """{"itinerary_id": 456, "gateway_id":8, "state":"AZ", "country":"US", "wallet_entity":1, "transaction_id":"89001-FA32CB00", "transaction_date":"2024-11-10 14:28:34", "transaction_type":1, "category_id":"category","ref_ledger_transaction_id":"1","original_transaction_id":"2"}"""
      ),
      posted_at = Some(Timestamp.valueOf("2024-11-11 08:15:35"))
    )
  ).toDF()

  private lazy val ledgerEntriesData = Seq(
    TestLedgerEntries(
      id = Some("01931a22-33a7-747f-81a7-8b3700796ab0"),
      ledger_account_id = Some("ledger_acc_id_us"),
      ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134")
    ),
    TestLedgerEntries(
      direction = Some("credit"),
      id = Some("01931a22-33a8-723c-9af8-bbbe2f430f2b"),
      ledger_account_id = Some("per_customer_account"),
      ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134")
    ),
    TestLedgerEntries(
      direction = Some("credit"),
      id = Some("01931a4a-e344-7d4d-8ca7-708340778f0c"),
      ledger_account_id = Some("ledger_acc_id_sg"),
      ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a")
    ),
    TestLedgerEntries(
      id = Some("01931a4a-e346-7b8c-81f5-e59848cc65a3"),
      ledger_account_id = Some("per_customer_account"),
      ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a")
    )
  ).toDF()

  private lazy val walletTransactionsData = Seq(
    TestWalletTransactions(
      wallet_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134"),
      ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134")
    ),
    TestWalletTransactions(
      wallet_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
      ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a"),
      posted_at = Some(Timestamp.valueOf("2024-11-11 08:15:35")),
      ledger_account_id = Some("ledger_acc_id_sg"),
      meta_itinerary_id = Some(456L),
      meta_transaction_id = Some("89001-FA32CB00"),
      local_amount = Some(-100.0)
    )
  ).toDF().withColumn("local_amount", col("local_amount").cast(CalculatedDecimalType))

  private lazy val emptyTracking = Seq.empty[TestTracking].toDF()

  private lazy val prodTracking = Seq(
    TestTracking(
      source = Some("ledger_transactions"),
      source_datadate = Some(********),
      source_hour = Some(10),
      job_schedule_time = Some(Timestamp.valueOf("2024-11-09 10:00:00"))
    ),
    TestTracking(
      source = Some("ledger_entries"),
      source_datadate = Some(********),
      source_hour = Some(10),
      job_schedule_time = Some(Timestamp.valueOf("2024-11-09 10:00:00"))
    )
  ).toDF()

  private lazy val incomingTrackingData = Seq(
    TestTracking(
      source = Some("ledger_transactions"),
      source_datadate = Some(********),
      source_hour = Some(11)
    ),
    TestTracking(
      source = Some("ledger_entries"),
      source_datadate = Some(********),
      source_hour = Some(11)
    )
  ).toDF()

  private lazy implicit val repositoryUtil: RepositoryUtiImpl         = mock[RepositoryUtiImpl]
  private lazy val saveProcessor                                      = new SaveProcessor(repositoryUtil)(context, spark)
  private lazy val walletTransactionsSchema: WalletTransactionsSchema = context.outputSchema.walletTransactionsSchema
  private lazy val trackingSchema: TrackingSchema                     = context.trackingSchema

  private def verifyCallsAndAssertDataFrame(expectedWalletDf: DataFrame, expectedTrackingDf: DataFrame): Unit = {
    val captureWalletDf: ArgumentCaptor[DataFrame]   = ArgumentCaptor.forClass(classOf[DataFrame])
    val captureTrackingDf: ArgumentCaptor[DataFrame] = ArgumentCaptor.forClass(classOf[DataFrame])

    verify(repositoryUtil).write(
      ArgumentMatchers.eq(walletTransactionsSchema),
      captureWalletDf.capture(),
      any[String](),
      any[Boolean]()
    )
    assertDataFrameEquals(captureWalletDf.getValue, expectedWalletDf, CREATED_LOG_TIME)

    verify(repositoryUtil).write(
      ArgumentMatchers.eq(trackingSchema),
      captureTrackingDf.capture(),
      any[String](),
      any[Boolean]()
    )

    captureTrackingDf.getValue should beEqualTo(expectedTrackingDf)
  }

  setup(reset(repositoryUtil))

  test("SaveProcessor should save wallet transactions and tracking data when prod tracking is empty") {
    when(repositoryUtil.readOrEmpty(trackingSchema)).thenReturn(emptyTracking)

    saveProcessor.saveWalletTransactions(walletTransactionsData)

    verifyCallsAndAssertDataFrame(walletTransactionsData, incomingTrackingData)
  }

  test("SaveProcessor should save wallet transactions and tracking data when prod tracking data exists") {
    when(repositoryUtil.readOrEmpty(trackingSchema)).thenReturn(prodTracking)

    saveProcessor.saveWalletTransactions(walletTransactionsData)

    verifyCallsAndAssertDataFrame(walletTransactionsData, incomingTrackingData.union(prodTracking))

  }

}
