package com.agoda.finance.processors

import com.agoda.finance.common.enums.ItemSubType.daily
import com.agoda.finance.common.enums.ItemType.job
import com.agoda.finance.config._
import com.agoda.finance.constants.FinanceDecimalType.CalculatedDecimalType
import com.agoda.finance.constants.WalletConstant.TestWalletTransactionsConstant.SIT_ID
import com.agoda.finance.constants.WalletConstant.duplicatedWalletTransactionsConstant._
import com.agoda.finance.constants.WalletConstant.walletTransactionsConstant._
import com.agoda.finance.externaldatapipeline.core.models.context.FCDInfo
import com.agoda.finance.externaldatapipeline.core.schema._
import com.agoda.finance.externaldatapipeline.core.utils.RepositoryUtiImpl
import com.agoda.finance.models.WalletContext._
import com.agoda.finance.models.{ExecutionArguments, InputSchema, OutputSchema, WalletContext}
import com.agoda.finance.schema._
import com.agoda.finance.test_data.TestWalletTransactions
import com.agoda.finance.utils.TestUtil.assertDataFrameEquals
import com.agoda.ml.spark.services.etl.{DataFrameETLExtensionLocalTest, SparkETLLocalTest}
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.{col, lit, regexp_replace}
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.verify
import org.mockito.{ArgumentCaptor, Mockito}
import org.scalatest.mockito.MockitoSugar.mock

import java.sql.Timestamp

class DeduplicationProcessorTest extends SparkETLLocalTest with DataFrameETLExtensionLocalTest {
  import sqlContext.implicits._

  private val duplicatedWalletTransactionsSchema = context.outputSchema.duplicatedWalletTransactionsSchema

  private lazy val config = WalletConfig(
    "wallet-data-pipeline.unit-test",
    false,
    InputConfig(
      TableConfig("input", "mt_ledger_transactions_test"),
      TableConfig("input", "mt_ledger_entries_test")
    ),
    OutputConfig(
      TableConfig("output", "mt_wallet_transactions_test"),
      TableConfig("output", "mt_duplicated_wallet_transactions_test"),
      TableConfig("output", "mt_corrupted_wallet_transactions_test")
    ),
    Map(
      ("bhfs_us_entity", "ledger_acc_id_us"),
      ("bhfs_uk_entity", "ledger_acc_id_uk"),
      ("bhfs_sg_entity", "ledger_acc_id_sg")
    )
      .mapValues(WalletEntity("", null, 0, _, "")),
    TableConfig("output", "tracking_test")
  )

  private lazy val executionArgs = ExecutionArguments(datadate = 20241109, hour = 11)
  private lazy val fcdInfo       = FCDInfo("itemName", "description", job, daily, 20241109)

  private lazy implicit val repositoryUtil: RepositoryUtiImpl = mock[RepositoryUtiImpl]
  private lazy implicit val context: WalletContext = WalletContext(
    executionArgs,
    config,
    InputSchema(
      LedgerTransactionsSchema(config.inputConfig.ledgerTransactionsTable.getTableName),
      LedgerEntriesSchema(config.inputConfig.ledgerEntriesTable.getTableName)
    ),
    OutputSchema(
      WalletTransactionsSchema(config.outputConfig.walletTransactionsTable.getTableName),
      DuplicatedWalletTransactionsSchema(config.outputConfig.duplicatedWalletTransactionsTable.getTableName),
      CorruptedWalletTransactionsSchema(config.outputConfig.corruptedWalletTransactionsTable.getTableName)
    ),
    fcdInfo,
    TrackingSchema(config.tracking.getTableName)
  )

  private lazy val deduplicationProcessor = new DeduplicationProcessor(repositoryUtil)(context)

  private lazy val walletTransactionsData = Seq(
    // no duplicate
    TestWalletTransactions(
      wallet_transaction_id = Some("01931a22-Will-be-used-6d5629f59134"),
      ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134")
    ),
    // duplicate with prod wallet_transaction
    TestWalletTransactions(
      wallet_transaction_id = Some("01931a24-Wont-be-used-f15fe4b7849a"),
      ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a")
    ),
    // duplicate with prod wallet_transaction
    TestWalletTransactions(
      wallet_transaction_id = Some("47ec0580-Wont-be-used-84275f6f80a2"),
      ledger_transaction_id = Some("47ec0580-7822-40cb-a86b-84275f6f80a2")
    ),
    // duplicate with incoming data
    TestWalletTransactions(
      wallet_transaction_id = Some("11111111-Wont-be-used-76aa984b2508"),
      ledger_transaction_id = Some("2e5aaac3-4a7c-484d-ad15-76aa984b2508")
    ),
    // duplicate with incoming data
    TestWalletTransactions(
      wallet_transaction_id = Some("22222222-Wont-be-used-76aa984b2508"),
      ledger_transaction_id = Some("2e5aaac3-4a7c-484d-ad15-76aa984b2508"),
      updated_at = Some(Timestamp.valueOf("2024-11-09 17:00:05"))
    ),
    // no duplicate
    TestWalletTransactions(
      wallet_transaction_id = Some("33333333-Wont-be-used-76aa984b2508"),
      ledger_transaction_id = Some("2e5aaac3-4a7c-484d-ad15-76aa984b2508"),
      updated_at = Some(Timestamp.valueOf("2024-11-09 17:00:05")),
      entry_updated_at = Some(Timestamp.valueOf("2024-11-09 17:00:10"))
    ),
    TestWalletTransactions(
      wallet_transaction_id = Some("44444444-Will-be-used-76aa984b2508"),
      ledger_transaction_id = Some("3e5bbbc3-4a7c-484d-ad15-76aa984b2508"),
      mt_import_hour = Some(10),
      updated_at = Some(Timestamp.valueOf("2024-11-09 17:00:10"))
    ),
    TestWalletTransactions(
      wallet_transaction_id = Some("55555555-Wont-be-used-76aa984b2508"),
      ledger_transaction_id = Some("3e5bbbc3-4a7c-484d-ad15-76aa984b2508"),
      mt_import_hour = Some(10),
      updated_at = Some(Timestamp.valueOf("2024-11-09 17:00:05"))
    ),
    TestWalletTransactions(
      wallet_transaction_id = Some("66666666-Wont-be-used-76aa984b2508"),
      ledger_transaction_id = Some("3e5bbbc3-4a7c-484d-ad15-76aa984b2508")
    )
  ).toDF()
    .withColumn("local_amount", col("local_amount").cast(CalculatedDecimalType))

  test("dedupe data correctly") {
    val prodWalletTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a24-Wallet-prod-data-f15fe4b7849a"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a")
      ),
      TestWalletTransactions(
        wallet_transaction_id = Some("47ec0580-Wallet-prod-data-84275f6f80a2"),
        ledger_transaction_id = Some("47ec0580-7822-40cb-a86b-84275f6f80a2")
      )
    ).toDF().withColumn("local_amount", col("local_amount").cast(CalculatedDecimalType))

    val expectedNonDuplicatedTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a22-Will-be-used-6d5629f59134"),
        ledger_transaction_id = Some("01931a22-338a-7dfd-990c-6d5629f59134")
      ),
      TestWalletTransactions(
        wallet_transaction_id = Some("33333333-Will-be-used-76aa984b2508"),
        ledger_transaction_id = Some("2e5aaac3-4a7c-484d-ad15-76aa984b2508"),
        updated_at = Some(Timestamp.valueOf("2024-11-09 17:00:05")),
        entry_updated_at = Some(Timestamp.valueOf("2024-11-09 17:00:10"))
      ),
      TestWalletTransactions(
        wallet_transaction_id = Some("44444444-Will-be-used-76aa984b2508"),
        ledger_transaction_id = Some("3e5bbbc3-4a7c-484d-ad15-76aa984b2508"),
        mt_import_hour = Some(10),
        updated_at = Some(Timestamp.valueOf("2024-11-09 17:00:10"))
      )
    ).toDF().withColumn("local_amount", col("local_amount").cast(CalculatedDecimalType)).withDatadateAndHour

    val expectedDuplicatedTransactions = Seq(
      TestWalletTransactions(
        wallet_transaction_id = Some("01931a24-Wont-be-used-f15fe4b7849a"),
        ledger_transaction_id = Some("01931a24-a6b7-7156-8508-f15fe4b7849a")
      ),
      TestWalletTransactions(
        wallet_transaction_id = Some("47ec0580-Wont-be-used-84275f6f80a2"),
        ledger_transaction_id = Some("47ec0580-7822-40cb-a86b-84275f6f80a2")
      )
    ).toDF()
      .withColumn("local_amount", col("local_amount").cast(CalculatedDecimalType))
      .withColumn(PROD_WALLET_TRANSACTION_ID, regexp_replace(col(WALLET_TRANSACTION_ID), "Wont-be-used", "Wallet-prod-data"))
      .withColumn(PROD_WALLET_ENTITY, col(WALLET_ENTITY))
      .withColumn(PROD_DATADATE, col(DATADATE))
      .withColumn(PROD_HOUR, col(HOUR))
      .union(
        Seq(
          TestWalletTransactions(
            wallet_transaction_id = Some("11111111-Wont-be-used-76aa984b2508"),
            ledger_transaction_id = Some("2e5aaac3-4a7c-484d-ad15-76aa984b2508")
          ),
          TestWalletTransactions(
            wallet_transaction_id = Some("22222222-Wont-be-used-76aa984b2508"),
            ledger_transaction_id = Some("2e5aaac3-4a7c-484d-ad15-76aa984b2508"),
            updated_at = Some(Timestamp.valueOf("2024-11-09 17:00:05"))
          ),
          TestWalletTransactions(
            wallet_transaction_id = Some("55555555-Wont-be-used-76aa984b2508"),
            ledger_transaction_id = Some("3e5bbbc3-4a7c-484d-ad15-76aa984b2508"),
            mt_import_hour = Some(10),
            updated_at = Some(Timestamp.valueOf("2024-11-09 17:00:05"))
          ),
          TestWalletTransactions(
            wallet_transaction_id = Some("66666666-Wont-be-used-76aa984b2508"),
            ledger_transaction_id = Some("3e5bbbc3-4a7c-484d-ad15-76aa984b2508")
          )
        ).toDF()
          .withColumn("local_amount", col("local_amount").cast(CalculatedDecimalType))
          .withColumn(PROD_WALLET_TRANSACTION_ID, lit(null))
          .withColumn(PROD_WALLET_ENTITY, lit(null))
          .withColumn(PROD_DATADATE, lit(null))
          .withColumn(PROD_HOUR, lit(null))
          .withDatadateAndHour
      )
      .withDatadateAndHour

    Mockito.when(repositoryUtil.readOrEmpty(any(), any())).thenReturn(prodWalletTransactions)

    val actual = deduplicationProcessor.deduplicate(walletTransactionsData)

    //we cannot compare WALLET_TRANSACTION_ID as it's a random UUID
    assertDataFrameEquals(actual, expectedNonDuplicatedTransactions, WALLET_TRANSACTION_ID, SIT_ID, CREATED_LOG_TIME)

    val captureSchema: ArgumentCaptor[Schema]    = ArgumentCaptor.forClass(classOf[Schema])
    val captureDf: ArgumentCaptor[DataFrame]     = ArgumentCaptor.forClass(classOf[DataFrame])
    val captureWriteMode: ArgumentCaptor[String] = ArgumentCaptor.forClass(classOf[String])

    verify(repositoryUtil).write(captureSchema.capture(), captureDf.capture(), captureWriteMode.capture(), any[Boolean]())

    captureSchema.getValue should equal(duplicatedWalletTransactionsSchema)
    assertDataFrameEquals(captureDf.getValue, expectedDuplicatedTransactions, SIT_ID, CREATED_LOG_TIME)
    captureWriteMode.getValue should equal(null)
  }
}
