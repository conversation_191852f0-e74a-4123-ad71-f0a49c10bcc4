package com.agoda.finance.utils

import com.agoda.ml.spark.DataFrameMatchersFunctions
import org.apache.spark.sql.DataFrame
import org.scalatest.Matchers.convertToAnyShouldWrapper

object TestUtil extends DataFrameMatchersFunctions {
  def assertDataFrameEquals(actual: DataFrame, expected: DataFrame, ignoreColumns: String*): Unit = {
    val actualWithoutIgnored   = actual.drop(ignoreColumns: _*)
    val expectedWithoutIgnored = expected.drop(ignoreColumns: _*)
    actualWithoutIgnored should beEqualTo(expectedWithoutIgnored)
  }
}
