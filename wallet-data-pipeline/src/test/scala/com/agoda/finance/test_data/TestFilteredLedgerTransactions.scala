package com.agoda.finance.test_data

import java.sql.{Date, Timestamp}

case class TestFilteredLedgerTransactions(
    created_at: Option[Timestamp] = Some(Timestamp.valueOf("2024-11-09 17:00:00")),
    description: Option[String] = Some("description"),
    effective_at: Option[Timestamp] = Some(Timestamp.valueOf("2024-11-09 17:00:00")),
    effective_date: Option[Date] = Some(Date.valueOf("2024-11-09")),
    external_id: Option[String] = Some("1"),
    id: Option[String],
    ledger_id: Option[String] = Some("01931a15-a7f3-7a13-ac92-aacc41645686"),
    ledgerable_id: Option[String] = Some("1"),
    ledgerable_type: Option[String] = Some("ledgerable_type"),
    live_mode: Option[Boolean] = Some(false),
    meta_transaction_id: Option[String] = Some("89001-FA32CB"),
    meta_wallet_transaction_id: Option[String] = Some("1"),
    meta_original_transaction_id: Option[String] = Some("2"),
    meta_wallet_account_id: Option[String] = Some("wallet-account-id"),
    meta_ledger_transaction_type: Option[String] = Some("TOPUP"),
    meta_gateway_id: Option[String] = Some("8"),
    meta_kyc_id: Option[String] = Some("kyc-id"),
    meta_country_code: Option[String] = Some("US"),
    meta_state: Option[String] = Some("AZ"),
    meta_whitelabel_id: Option[String] = Some("1"),
    meta_transaction_date_time: Option[String] = Some("2024-11-10T14:28:34.000000Z"),
    meta_itinerary_id: Option[String] = Some("1"),
    meta_payment_method_id: Option[String] = Some("29"),
    organization_id: Option[String] = Some("1"),
    posted_at: Option[Timestamp] = Some(Timestamp.valueOf("2024-11-11 07:31:09")),
    reverses_ledger_transaction_id: Option[String] = Some("1"),
    status: Option[String] = Some("posted"),
    updated_at: Option[Timestamp] = Some(Timestamp.valueOf("2024-11-09 17:00:00")),
    datadate: Option[Int] = Some(********),
    hour: Option[Int] = Some(11)
)
