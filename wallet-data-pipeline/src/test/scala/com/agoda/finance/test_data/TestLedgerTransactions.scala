package com.agoda.finance.test_data

import java.sql.{Date, Timestamp}

case class TestLedgerTransactions(
    created_at: Option[Timestamp] = Some(Timestamp.valueOf("2024-11-09 17:00:00")),
    description: Option[String] = Some("description"),
    effective_at: Option[Timestamp] = Some(Timestamp.valueOf("2024-11-09 17:00:00")),
    effective_date: Option[Date] = Some(Date.valueOf("2024-11-09")),
    external_id: Option[String] = Some("1"),
    id: Option[String],
    ledger_id: Option[String] = Some("01931a15-a7f3-7a13-ac92-aacc41645686"),
    ledgerable_id: Option[String] = Some("1"),
    ledgerable_type: Option[String] = Some("ledgerable_type"),
    live_mode: Option[Boolean] = Some(false),
    metadata_json: Option[String] = Some(
      """{"itineraryId":"999","gatewayId":8,"state":"AZ", "countryCode":"US", "transactionId":"89001-FA32CB", "transactionDateTime":"2024-11-10 14:28:34", "transactionType":1, "categoryId":"category","walletTransactionId":"1","walletAccountId":"wallet-account-id","originalTransactionId":"2","kycId":"kyc-id","whitelabelId":"whitelabel-id", "paymentMethodId": 29}"""
    ),
    organization_id: Option[String] = Some("1"),
    posted_at: Option[Timestamp] = Some(Timestamp.valueOf("2024-11-11 07:31:09")),
    reverses_ledger_transaction_id: Option[String] = Some("1"),
    status: Option[String] = Some("posted"),
    updated_at: Option[Timestamp] = Some(Timestamp.valueOf("2024-11-09 17:00:00")),
    datadate: Option[Int] = Some(********),
    hour: Option[Int] = Some(11)
)
