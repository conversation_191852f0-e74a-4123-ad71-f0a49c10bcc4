package com.agoda.finance.test_data

import java.sql.{Date, Timestamp}

case class TestWalletTransactions(
    wallet_transaction_id: Option[String],
    ledger_transaction_id: Option[String],
    posted_at: Option[Timestamp] = Some(Timestamp.valueOf("2024-11-11 07:31:09")),
    ledger_id: Option[String] = Some("01931a15-a7f3-7a13-ac92-aacc41645686"),
    created_at: Option[Timestamp] = Some(Timestamp.valueOf("2024-11-09 17:00:00")),
    updated_at: Option[Timestamp] = Some(Timestamp.valueOf("2024-11-09 17:00:00")),
    ledger_account_id: Option[String] = Some("ledger_acc_id_us"),
    meta_transaction_id: Option[String] = Some("89001-FA32CB"),
    meta_wallet_transaction_id: Option[String] = Some("1"),
    meta_original_transaction_id: Option[String] = Some("2"),
    transaction_reference_id: Option[String] = Some("89001-FA32CB"),
    reverses_ledger_transaction_id: Option[String] = Some("1"),
    meta_wallet_account_id: Option[String] = Some("wallet-account-id"),
    meta_ledger_transaction_type: Option[String] = Some("TOPUP"),
    meta_gateway_id: Option[Int] = Some(8),
    meta_kyc_id: Option[String] = Some("kyc-id"),
    meta_country_code: Option[String] = Some("US"),
    meta_state: Option[String] = Some("AZ"),
    meta_whitelabel_id: Option[Int] = Some(1),
    meta_transaction_date_time: Option[Timestamp] = Some(Timestamp.valueOf("2024-11-10 21:28:34")),
    meta_itinerary_id: Option[Long] = Some(12345678L),
    meta_payment_method_id: Option[Int] = Some(29),
    entry_updated_at: Option[Timestamp] = Some(Timestamp.valueOf("2024-11-09 17:00:00")),
    local_amount: Option[BigDecimal] = Some(100.0),
    local_currency: Option[String] = Some("USD"),
    wallet_entity: Option[String] = Some("BHFS_US"),
    mt_import_datadate: Option[Int] = Some(********),
    mt_import_hour: Option[Int] = Some(11),
    datadate: Option[Int] = Some(********),
    hour: Option[Int] = Some(11),
    local_date: Option[Int] = Some(********),
    local_hour: Option[Int] = Some(22),
    local_transaction_date: Option[Date] = Some(Date.valueOf("2024-11-10")),
    sit_id: Option[String] = Some("UNKNOWN")
)
