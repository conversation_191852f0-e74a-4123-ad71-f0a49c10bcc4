package com.agoda.finance.constants

object WalletConstant {
  object ledgerEntriesConstant {
    val AMOUNT_DECIMAL              = "amount_decimal"
    val CREATED_AT                  = "created_at"
    val DIRECTION                   = "direction"
    val DISCARDED_AT                = "discarded_at"
    val DISCARDED_AT_LOCK_VERSION   = "discarded_at_lock_version"
    val EFFECTIVE_AT                = "effective_at"
    val ID                          = "id"
    val LEDGER_ACCOUNT_ID           = "ledger_account_id"
    val LEDGER_ACCOUNT_LOCK_VERSION = "ledger_account_lock_version"
    val LEDGER_ACCOUNT_PAYOUT_ID    = "ledger_account_payout_id"
    val LEDGER_TRANSACTION_ID       = "ledger_transaction_id"
    val LIVE_MODE                   = "live_mode"
    val METADATA_JSON               = "metadata_json"
    val ORGANIZATION_ID             = "organization_id"
    val PENDING_CREDITS             = "pending_credits"
    val PENDING_DEBITS              = "pending_debits"
    val POSTED_CREDITS              = "posted_credits"
    val POSTED_DEBITS               = "posted_debits"
    val UPDATED_AT                  = "updated_at"
  }

  object ledgerTransactionsConstant {
    val CREATED_AT                     = "created_at"
    val DESCRIPTION                    = "description"
    val EFFECTIVE_AT                   = "effective_at"
    val EFFECTIVE_DATE                 = "effective_date"
    val EXTERNAL_ID                    = "external_id"
    val ID                             = "id"
    val LEDGER_ID                      = "ledger_id"
    val LEDGERABLE_ID                  = "ledgerable_id"
    val LEDGERABLE_TYPE                = "ledgerable_type"
    val LIVE_MODE                      = "live_mode"
    val METADATA_JSON                  = "metadata_json"
    val WALLET_ENTITY                  = "wallet_entity"
    val ORGANIZATION_ID                = "organization_id"
    val POSTED_AT                      = "posted_at"
    val REVERSES_LEDGER_TRANSACTION_ID = "reverses_ledger_transaction_id"
    val STATUS                         = "status"
    val UPDATED_AT                     = "updated_at"
    val DATADATE                       = "datadate"
    val HOUR                           = "hour"
  }

  object ledgerTransactionMetadataConstant {
    val TRANSACTION_ID          = "transactionId"
    val WALLET_TRANSACTION_ID   = "walletTransactionId"
    val ORIGINAL_TRANSACTION_ID = "originalTransactionId"
    val WALLET_ACCOUNT_ID       = "walletAccountId"
    val LEDGER_TRANSACTION_TYPE = "ledgerTransactionType"
    val GATEWAY_ID              = "gatewayId"
    val KYC_ID                  = "kycId"
    val COUNTRY_CODE            = "countryCode"
    val STATE                   = "state"
    val WHITELABEL_ID           = "whitelabelId"
    val TRANSACTION_DATE_TIME   = "transactionDateTime"
    val ITINERARY_ID            = "itineraryId"
    val PAYMENT_METHOD_ID       = "paymentMethodId"
  }

  object walletTransactionsConstant {
    val WALLET_TRANSACTION_ID        = "wallet_transaction_id"
    val LEDGER_TRANSACTION_ID        = "ledger_transaction_id"
    val POSTED_AT                    = "posted_at"
    val LEDGER_ID                    = "ledger_id"
    val CREATED_AT                   = "created_at"
    val UPDATED_AT                   = "updated_at"
    val LEDGER_ACCOUNT_ID            = "ledger_account_id"
    val META_ITINERARY_ID            = "meta_itinerary_id"
    val META_GATEWAY_ID              = "meta_gateway_id"
    val META_KYC_ID                  = "meta_kyc_id"
    val META_LEDGER_TRANSACTION_TYPE = "meta_ledger_transaction_type"
    val META_TRANSACTION_ID          = "meta_transaction_id"
    val META_WALLET_ACCOUNT_ID       = "meta_wallet_account_id"
    val META_TRANSACTION_DATE_TIME   = "meta_transaction_date_time"
    val META_STATE                   = "meta_state"
    val META_WHITELABEL_ID           = "meta_whitelabel_id"
    val META_COUNTRY_CODE            = "meta_country_code"
    val META_WALLET_TRANSACTION_ID   = "meta_wallet_transaction_id"
    val META_ORIGINAL_TRANSACTION_ID = "meta_original_transaction_id"
    val META_PAYMENT_METHOD_ID       = "meta_payment_method_id"
    val ENTRY_UPDATED_AT             = "entry_updated_at"
    val LOCAL_AMOUNT                 = "local_amount"
    val LOCAL_CURRENCY               = "local_currency"
    val LOCAL_TRANSACTION_DATE       = "local_transaction_date"
    val MT_IMPORT_DATADATE           = "mt_import_datadate"
    val MT_IMPORT_HOUR               = "mt_import_hour"
    val WALLET_ENTITY                = "wallet_entity"
    val CREATED_LOG_TIME             = "created_log_time"
    val DATADATE                     = "datadate"
    val HOUR                         = "hour"
    val LOCAL_DATE                   = "local_date"
    val LOCAL_HOUR                   = "local_hour"
    val TRANSACTION_REFERENCE_ID     = "transaction_reference_id"
  }

  object TestWalletTransactionsConstant {
    val SIT_ID = "sit_id"
  }

  object duplicatedWalletTransactionsConstant {
    val PROD_WALLET_TRANSACTION_ID = "prod_wallet_transaction_id"
    val PROD_WALLET_ENTITY         = "prod_wallet_entity"
    val PROD_DATADATE              = "prod_datadate"
    val PROD_HOUR                  = "prod_hour"
  }

  object statusConstant {
    val POSTED = "posted"
  }

  object trackingConstant {
    val JOB_SCHEDULE_TIME = "job_schedule_time"
    val SOURCE_DATADATE   = "source_datadate"
    val SOURCE_HOUR       = "source_hour"
    val SOURCE            = "source"
    val MASTER            = "master"

    val ledgerEntriesSource      = "ledger_entries"
    val ledgerTransactionsSource = "ledger_transactions"
  }

  val secondsInAnHour   = 3600
  val dateTimeFormatter = "yyyyMMdd HH"

  object TransactionTypes {
    val TOPUP      = "TOPUP"
    val WITHDRAWAL = "WITHDRAWAL"
    val REDEMPTION = "REDEMPTION"
    val REFUND     = "REFUND"
  }
  object GatewayIds {
    val ADYEN_GATEWAY_ID = 8
    val WISE_GATEWAY_ID  = 3
  }
}
