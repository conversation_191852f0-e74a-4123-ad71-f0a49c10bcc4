package com.agoda.finance.processors

import com.agoda.finance.constants.WalletConstant.duplicatedWalletTransactionsConstant._
import com.agoda.finance.constants.WalletConstant.walletTransactionsConstant._
import com.agoda.finance.models.WalletContext
import com.agoda.finance.externaldatapipeline.core.utils.DataFrameUtil._
import com.agoda.finance.externaldatapipeline.core.utils.{RepositoryUtil, SchemaUtil, SegregatedDuplicateData}
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions.{asc, col, current_timestamp, desc, expr, lit, row_number}
import org.apache.spark.sql.{Column, DataFrame}

class DeduplicationProcessor(repositoryUtil: RepositoryUtil)(implicit context: WalletContext) {

  def deduplicate(df: DataFrame): DataFrame = {
    val walletTransactionsSchema = context.outputSchema.walletTransactionsSchema

    val segregatedIncomingTransactions = segregateDuplicateIncomingTransactions(df)

    val filter: Option[Column] = Some(
      col(DATADATE) < context.executionArguments.datadate || (col(DATADATE) === context.executionArguments.datadate && col(
        HOUR
      ) < context.executionArguments.hour)
    )
    val walletTransactions: DataFrame = repositoryUtil.readOrEmpty(walletTransactionsSchema, filter)

    val prodWalletTransactions =
      walletTransactions.select(
        col(LEDGER_TRANSACTION_ID),
        col(WALLET_TRANSACTION_ID).alias(PROD_WALLET_TRANSACTION_ID),
        col(WALLET_ENTITY).alias(PROD_WALLET_ENTITY),
        col(DATADATE).alias(PROD_DATADATE),
        col(HOUR).alias(PROD_HOUR)
      )

    val segregatedData =
      segregatedIncomingTransactions.nonDuplicatedData.segregateDuplicatedKeys(prodWalletTransactions, Seq(LEDGER_TRANSACTION_ID))

    saveDuplicatedTransactions(segregatedIncomingTransactions.duplicatedData, segregatedData.duplicatedData)

    SchemaUtil.transformColumn(walletTransactionsSchema.fields, segregatedData.nonDuplicatedData.withColumn(CREATED_LOG_TIME, current_timestamp()))
  }

  private def segregateDuplicateIncomingTransactions(df: DataFrame): SegregatedDuplicateData = {
    val ROW_COUNT_NAME = "row_count"
    val dfWithRowCount =
      df.withColumn(
        ROW_COUNT_NAME,
        row_number.over(
          Window
            .partitionBy(LEDGER_TRANSACTION_ID)
            .orderBy(
              asc(MT_IMPORT_DATADATE),
              asc(MT_IMPORT_HOUR),
              desc(UPDATED_AT),
              desc(ENTRY_UPDATED_AT)
            )
        )
      ).cache()

    val validData      = dfWithRowCount.where(col(ROW_COUNT_NAME) === 1)
    val duplicatedData = dfWithRowCount.where(col(ROW_COUNT_NAME) =!= 1)

    SegregatedDuplicateData(validData, duplicatedData)
  }

  private def saveDuplicatedTransactions(duplicatedIncomingData: DataFrame, duplicatedProductionData: DataFrame): Unit = {
    val duplicatedWalletTransactionsSchema = context.outputSchema.duplicatedWalletTransactionsSchema

    val transformedDuplicatedIncomingDataWithProd = SchemaUtil.transformColumn(
      duplicatedWalletTransactionsSchema.fields,
      duplicatedIncomingData
        .withColumn(PROD_WALLET_TRANSACTION_ID, lit(null))
        .withColumn(PROD_WALLET_ENTITY, lit(null))
        .withColumn(PROD_DATADATE, lit(null))
        .withColumn(PROD_HOUR, lit(null))
        .withColumn(CREATED_LOG_TIME, current_timestamp())
    )

    val transformedDuplicatedProductionData = SchemaUtil
      .transformColumn(
        duplicatedWalletTransactionsSchema.fields,
        duplicatedProductionData.withColumn(CREATED_LOG_TIME, current_timestamp())
      )

    repositoryUtil.write(
      duplicatedWalletTransactionsSchema,
      transformedDuplicatedIncomingDataWithProd.union(transformedDuplicatedProductionData)
    )
  }
}
