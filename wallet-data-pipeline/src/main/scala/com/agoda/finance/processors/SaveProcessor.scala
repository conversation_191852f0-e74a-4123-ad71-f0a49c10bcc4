package com.agoda.finance.processors

import com.agoda.finance.common.utils.DateFormats.dateFormatterObj
import com.agoda.finance.constants.WalletConstant.trackingConstant._
import com.agoda.finance.constants.WalletConstant.walletTransactionsConstant.CREATED_LOG_TIME
import com.agoda.finance.externaldatapipeline.core.utils.RepositoryUtil
import com.agoda.finance.models.WalletContext
import org.apache.spark.sql.functions.{current_timestamp, lit}
import org.apache.spark.sql.{DataFrame, SparkSession}

import java.sql.Timestamp
import java.time.LocalDate

class SaveProcessor(repositoryUtil: RepositoryUtil)(implicit context: WalletContext, sparkSession: SparkSession) {
  def saveWalletTransactions(walletTransactionsDf: DataFrame): Unit = {
    val walletTransactionsSchema = context.outputSchema.walletTransactionsSchema
    repositoryUtil.write(walletTransactionsSchema, walletTransactionsDf.withColumn(CREATED_LOG_TIME, current_timestamp()))

    saveTrackingTable()
  }

  private def saveTrackingTable(): Unit = {
    import sparkSession.implicits._
    val trackingSchema = context.trackingSchema

    val datadate = context.executionArguments.datadate
    val hour     = context.executionArguments.hour

    val localDate       = LocalDate.parse(datadate.toString, dateFormatterObj)
    val localDateTime   = localDate.atTime(hour, 0, 0)
    val jobScheduleTime = Timestamp.valueOf(localDateTime)

    val baseTrackingTable = Seq(
      (jobScheduleTime, datadate, hour, "", MASTER)
    ).toDF(JOB_SCHEDULE_TIME, SOURCE_DATADATE, SOURCE_HOUR, SOURCE, MASTER)

    val ledgerEntriesTracking      = baseTrackingTable.withColumn(SOURCE, lit(ledgerEntriesSource))
    val ledgerTransactionsTracking = baseTrackingTable.withColumn(SOURCE, lit(ledgerTransactionsSource))

    val tracking: DataFrame                       = repositoryUtil.readOrEmpty(trackingSchema)
    val entriesAndTransactionsTracking: DataFrame = ledgerTransactionsTracking.unionByName(ledgerEntriesTracking)

    repositoryUtil.write(trackingSchema, tracking.unionByName(entriesAndTransactionsTracking))
  }
}
