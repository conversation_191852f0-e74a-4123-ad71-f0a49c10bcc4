package com.agoda.finance.processors

import com.agoda.finance.common.utils.DateFormats.dateFormatter
import com.agoda.finance.constants.WalletConstant.ledgerEntriesConstant.{
  AMOUNT_DECIMAL,
  DIRECTION,
  LEDGER_ACCOUNT_ID,
  LEDGER_TRANSACTION_ID,
  LIVE_MODE
}
import com.agoda.finance.constants.WalletConstant.ledgerTransactionsConstant.{DATADATE, HOUR, STATUS, UPDATED_AT, WALLET_ENTITY}
import com.agoda.finance.constants.WalletConstant.statusConstant.POSTED
import com.agoda.finance.constants.WalletConstant.trackingConstant.{
  ledgerEntriesSource,
  ledgerTransactionsSource,
  SOURCE,
  SOURCE_DATADATE,
  SOURCE_HOUR
}
import com.agoda.finance.constants.WalletConstant.walletTransactionsConstant.{ENTRY_UPDATED_AT, LOCAL_AMOUNT, LOCAL_DATE}
import com.agoda.finance.externaldatapipeline.core.utils.{RepositoryUtil, SchemaUtil}
import com.agoda.finance.models.{FilteredData, WalletContext, WalletEntityLocalData}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{Column, DataFrame}
import org.apache.spark.sql.functions.{col, concat_ws, date_format, from_unixtime, lit, lpad, to_timestamp, unix_timestamp}
import org.apache.spark.sql.types.{IntegerType, StringType}
import com.agoda.finance.constants.WalletConstant.{dateTimeFormatter, secondsInAnHour}
import com.agoda.finance.utils.LocalDateLocalHourConverter

class FilterProcessor(repositoryUtil: RepositoryUtil)(implicit
    context: WalletContext
) extends LazyLogging {
  private val ledgerTransactionsSchema = context.inputSchema.ledgerTransactionsSchema
  private val ledgerEntriesSchema      = context.inputSchema.ledgerEntriesSchema
  private val walletTransactionsSchema = context.outputSchema.walletTransactionsSchema
  private val trackingSchema           = context.trackingSchema
  private val ledgerAccountIds         = context.config.ledgerAccountIds

  private val walletEntityToLocalDateAndHour: Seq[WalletEntityLocalData] =
    LocalDateLocalHourConverter.walletEntityToLocalData(
      context.executionArguments.datadate,
      context.executionArguments.hour,
      context.config.allWalletEntities
    )

  def getFilteredData: FilteredData = {
    val ledgerEntries      = filterLedgerEntries()
    val ledgerTransactions = filterLedgerTransaction()
    val walletTransactions = filterWalletTransactions()

    FilteredData(
      ledgerEntries = ledgerEntries,
      ledgerTransactions = ledgerTransactions,
      walletTransactionsByLocalDate = walletTransactions
    )
  }

  private def filterByLiveModeAndStatus(): Column =
    col(LIVE_MODE) === context.config.liveMode && col(STATUS) === POSTED

  private def filterLedgerTransaction(): DataFrame = {
    val ledgerTransactionsFilter = createFilterLedgerData(
      trackingSource = ledgerTransactionsSource,
      defaultFilter = filterByLiveModeAndStatus()
    )
    val filteredLedgerTransactions = repositoryUtil.readOrEmpty(ledgerTransactionsSchema, Some(ledgerTransactionsFilter))

    SchemaUtil.flattenJsonColumn(
      ledgerTransactionsSchema.fields,
      filteredLedgerTransactions
    )
  }

  private def filterLedgerEntries(): DataFrame = {
    val ledgerEntriesFilter = createFilterLedgerData(
      trackingSource = ledgerEntriesSource,
      defaultFilter = filterByLiveModeAndStatus()
    )

    val filteredLedgerEntries = repositoryUtil.readOrEmpty(ledgerEntriesSchema, Some(ledgerEntriesFilter))

    filteredLedgerEntries
      .select(
        col(LEDGER_ACCOUNT_ID),
        col(AMOUNT_DECIMAL).alias(LOCAL_AMOUNT),
        col(LEDGER_TRANSACTION_ID),
        col(DIRECTION),
        col(UPDATED_AT).alias(ENTRY_UPDATED_AT)
      )
      .where(col(LEDGER_ACCOUNT_ID).isin(ledgerAccountIds.values.toSeq: _*))
  }

  private def filterWalletTransactions(): DataFrame = {
    // Create a filter for wallet transactions based on countryCode and localDate.
    // Combine conditions with OR (||) to match any countryCode-localDate pair.

    val walletTransactionsFilter = walletEntityToLocalDateAndHour
      .map { entry =>
        (col(WALLET_ENTITY) === lit(
          entry.walletEntity.name
        )) && // Use lit() to ensure dynamic values are treated as constants for partition pruning.
        (col(LOCAL_DATE) === lit(entry.localDate))
      }
      .reduce(_ || _) // Example: (WALLET_ENTITY = "BHFS_US" AND LOCAL_DATE = ********) || (WALLET_ENTITY = "BHFS_UK" AND LOCAL_DATE = ********)

    repositoryUtil.readOrEmpty(walletTransactionsSchema, Some(walletTransactionsFilter))
  }

  private def createFilterLedgerData(
      trackingSource: String,
      defaultFilter: Column
  ): Column = {
    val tracking: DataFrame = repositoryUtil.readOrEmpty(trackingSchema)

    if (tracking.isEmpty) {
      defaultFilter
    } else {
      val endDateTime: Column = to_timestamp(
        lit(s"${context.executionArguments.datadate} ${context.executionArguments.hour.formatted("%02d")}"),
        dateTimeFormatter
      )

      val latestTrackingRow = tracking
        .filter(col(SOURCE) === trackingSource)
        .orderBy(col(SOURCE_DATADATE).desc, col(SOURCE_HOUR).desc)
        .first()

      val sourceDate = latestTrackingRow.getAs[Int](SOURCE_DATADATE)
      val sourceHour = latestTrackingRow.getAs[Int](SOURCE_HOUR)

      createTimeRangeFilter(
        endDateTime,
        context.executionArguments.datadate,
        sourceDate,
        sourceHour
      ) && defaultFilter
    }
  }

  private def createTimeRangeFilter(
      endDateTime: Column,
      datadate: Int,
      sourceDate: Int,
      sourceHour: Int
  ): Column = {

    val beginDateTime = from_unixtime(
      unix_timestamp(
        to_timestamp(lit(s"$sourceDate ${sourceHour.formatted("%02d")}"), dateTimeFormatter)
      )
    )
    val beginDate = date_format(beginDateTime, dateFormatter).cast(IntegerType)
    val timestampCol = to_timestamp(
      concat_ws(" ", col(DATADATE).cast(StringType), lpad(col(HOUR).cast(StringType), 2, "0")),
      dateTimeFormatter
    )

    val partitionFilter = col(DATADATE) >= lit(beginDate) && col(DATADATE) <= lit(datadate)
    val dataFilter      = timestampCol >= beginDateTime && timestampCol <= endDateTime

    partitionFilter && dataFilter
  }

}
