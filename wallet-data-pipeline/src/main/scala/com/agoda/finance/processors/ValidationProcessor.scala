package com.agoda.finance.processors

import com.agoda.dataq.datavalidation.validations.BinaryRule
import com.agoda.dataq.datavalidation.validations.rules.{CompletenessRule, EnumRule}
import com.agoda.finance.constants.WalletConstant.TransactionTypes.{REDEMPTION, REFUND, TOPUP, WITHDRAWAL}
import com.agoda.finance.constants.WalletConstant.walletTransactionsConstant._
import com.agoda.finance.externaldatapipeline.core.utils.{RepositoryUtil, SchemaUtil}
import com.agoda.finance.models.WalletContext
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.{col, current_timestamp}

class ValidationProcessor(repositoryUtil: RepositoryUtil)(implicit context: WalletContext) {

  def validate(df: DataFrame): DataFrame = {
    val corruptedWalletTransactionsSchema = context.outputSchema.corruptedWalletTransactionsSchema
    val walletTransactionsSchema          = context.outputSchema.walletTransactionsSchema

    val binaryRules: Seq[BinaryRule] = Seq(
      CompletenessRule(META_TRANSACTION_ID),
      CompletenessRule(META_COUNTRY_CODE),
      CompletenessRule(META_WALLET_ACCOUNT_ID),
      CompletenessRule(META_TRANSACTION_DATE_TIME),
      CompletenessRule(META_LEDGER_TRANSACTION_TYPE),
      CompletenessRule(META_GATEWAY_ID, scope = txnTypeIsOneOf(TOPUP, WITHDRAWAL)),
      CompletenessRule(TRANSACTION_REFERENCE_ID),
      CompletenessRule(META_ITINERARY_ID, scope = txnTypeIsOneOf(REDEMPTION, REFUND)),
      CompletenessRule(META_ORIGINAL_TRANSACTION_ID, scope = txnTypeIsOneOf(REFUND)),
      CompletenessRule(LOCAL_TRANSACTION_DATE),
      CompletenessRule(LOCAL_CURRENCY),
      CompletenessRule(LOCAL_AMOUNT),
      CompletenessRule(WALLET_ENTITY),
      EnumRule(WALLET_ENTITY, members = context.config.allWalletEntityStrings),
      CompletenessRule(LOCAL_DATE),
      CompletenessRule(LOCAL_HOUR)
    )

    val (valid, invalid) = BinaryRule.splitValidDataFrame(df, binaryRules)

    repositoryUtil.write(
      corruptedWalletTransactionsSchema,
      SchemaUtil.transformColumn(corruptedWalletTransactionsSchema.fields, invalid.withColumn(CREATED_LOG_TIME, current_timestamp()))
    )

    SchemaUtil.transformColumn(walletTransactionsSchema.fields, valid.withColumn(CREATED_LOG_TIME, current_timestamp()))
  }

  private def txnTypeIsOneOf(txnTypes: String*) = Some(
    col(META_LEDGER_TRANSACTION_TYPE).isin(txnTypes: _*).expr.sql
  )
}
