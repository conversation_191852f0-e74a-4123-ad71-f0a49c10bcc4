package com.agoda.finance.processors

import com.agoda.finance.constants.FinanceDecimalType.CalculatedDecimalType
import com.agoda.finance.constants.WalletConstant.GatewayIds.{ADYEN_GATEWAY_ID, WISE_GATEWAY_ID}
import com.agoda.finance.constants.WalletConstant.ledgerEntriesConstant._
import com.agoda.finance.constants.WalletConstant.ledgerTransactionMetadataConstant.GATEWAY_ID
import com.agoda.finance.constants.WalletConstant.walletTransactionsConstant.{LEDGER_ACCOUNT_ID, LEDGER_TRANSACTION_ID, _}
import com.agoda.finance.externaldatapipeline.core.utils.SchemaUtil
import com.agoda.finance.models.{FilteredData, WalletContext, WalletEntityLocalData}
import com.agoda.finance.models.WalletContext._
import com.agoda.finance.utils.LocalDateLocalHourConverter
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import org.apache.spark.sql.{Column, DataFrame, SparkSession}

class TransformationProcessor(implicit context: WalletContext, sparkSession: SparkSession) extends LazyLogging {
  private val walletTransactionsSchema = context.outputSchema.walletTransactionsSchema
  private val sessionTimezone          = sparkSession.conf.get("spark.sql.session.timeZone")

  private val walletEntitiesToLocalData: Seq[WalletEntityLocalData] =
    LocalDateLocalHourConverter.walletEntityToLocalData(
      context.executionArguments.datadate,
      context.executionArguments.hour,
      context.config.allWalletEntities
    )

  def prepareData(filteredData: FilteredData): DataFrame = {
    val config = context.config

    // assume ledger transaction and ledger entries come together in same day
    val walletEntity: UserDefinedFunction = udf { ledger_account_id: String =>
      config.findEntityByLedgerAccountId(ledger_account_id).name
    }

    val normalizeAmount: UserDefinedFunction = udf { (wallet_entity: String, amount: BigDecimal) =>
      config.findEntityByName(wallet_entity).normalizeCurrency(amount)
    }

    val localAmountWithSign   = when(col(DIRECTION) === "credit", -col(LOCAL_AMOUNT)).otherwise(col(LOCAL_AMOUNT))
    val normalizedLocalAmount = normalizeAmount(col(WALLET_ENTITY), localAmountWithSign).cast(CalculatedDecimalType)

    val transformFilteredLedgerEntries = filteredData.ledgerEntries
      .withColumn(WALLET_ENTITY, walletEntity(col(LEDGER_ACCOUNT_ID)))
      .withColumn(LOCAL_AMOUNT, normalizedLocalAmount)

    val joinedDf =
      transformFilteredLedgerEntries
        .join(filteredData.ledgerTransactions, transformFilteredLedgerEntries(LEDGER_TRANSACTION_ID) === filteredData.ledgerTransactions(ID))

    val enrichedDf = enrichWalletTransactions(joinedDf)

    val transformJoinedDf = SchemaUtil.transformColumn(walletTransactionsSchema.fields, enrichedDf.withDatadateAndHour)

    transformJoinedDf
  }

  private[processors] def enrichWalletTransactions(joinedDf: DataFrame): DataFrame = joinedDf.withColumns(
    Map(
      MT_IMPORT_DATADATE     -> col(DATADATE),
      MT_IMPORT_HOUR         -> col(HOUR),
      LEDGER_TRANSACTION_ID  -> col(ID),
      WALLET_TRANSACTION_ID  -> expr("uuid()"),
      LOCAL_DATE             -> walletEntityToLocalData(_.localDate, IntegerType),
      LOCAL_HOUR             -> walletEntityToLocalData(_.localHour, IntegerType),
      LOCAL_CURRENCY         -> walletEntityToLocalData(_.walletEntity.localCurrency),
      LOCAL_TRANSACTION_DATE -> toLocalDateColumn(col(META_TRANSACTION_DATE_TIME)),
      TRANSACTION_REFERENCE_ID -> when(col(META_GATEWAY_ID) === WISE_GATEWAY_ID, regexp_extract(col(META_TRANSACTION_ID), "^[^-]+-[^-]+-([^:]+):", 1))
        .when(col(META_GATEWAY_ID) === ADYEN_GATEWAY_ID, regexp_extract(col(META_TRANSACTION_ID), "^(\\w+)-*(\\w*)$", 1))
        .otherwise(
          col(META_TRANSACTION_ID)
        ),
      CREATED_LOG_TIME -> current_timestamp()
    )
  )

  private def walletEntityToLocalData(f: WalletEntityLocalData => Any, dataType: DataType = StringType): Column =
    walletEntitiesToLocalData.foldLeft(lit(null).cast(dataType)) { (column, entry) =>
      when(col(WALLET_ENTITY) === lit(entry.walletEntity.name), lit(f(entry)).cast(dataType)).otherwise(column)
    }

  private def toLocalDateColumn(dateCol: Column): Column =
    context.config.allWalletEntities.foldLeft(lit(null).cast(DateType)) { (column, entry) =>
      when(
        col(WALLET_ENTITY) === lit(entry.name),
        lit(to_date(from_utc_timestamp(to_utc_timestamp(dateCol, sessionTimezone), entry.zoneId.toString)))
      ).otherwise(column)
    }
}
