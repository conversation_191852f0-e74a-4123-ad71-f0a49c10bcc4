package com.agoda.finance.schema

import com.agoda.finance.constants.FinanceDecimalType.CalculatedDecimalType
import com.agoda.finance.constants.WalletConstant.ledgerTransactionsConstant.REVERSES_LEDGER_TRANSACTION_ID
import com.agoda.finance.constants.WalletConstant.walletTransactionsConstant._
import com.agoda.finance.externaldatapipeline.core.schema.Field
import org.apache.spark.sql.types.{DateType, IntegerType, LongType, StringType, TimestampType}

trait WalletTransactions {
  val walletTransactionsFields: Seq[Field] = Seq(
    Field(WALLET_TRANSACTION_ID, StringType),
    Field(LEDGER_TRANSACTION_ID, StringType),
    Field(POSTED_AT, TimestampType),
    Field(LEDGER_ID, StringType),
    Field(CREATED_AT, TimestampType),
    Field(UPDATED_AT, TimestampType),
    Field(LEDGER_ACCOUNT_ID, StringType),
    Field(META_TRANSACTION_ID, StringType),
    Field(META_WALLET_TRANSACTION_ID, StringType),
    Field(META_ORIGINAL_TRANSACTION_ID, StringType),
    Field(META_WALLET_ACCOUNT_ID, StringType),
    Field(META_LEDGER_TRANSACTION_TYPE, StringType),
    Field(META_GATEWAY_ID, IntegerType),
    Field(META_KYC_ID, StringType),
    Field(META_COUNTRY_CODE, StringType),
    Field(META_STATE, StringType),
    Field(META_WHITELABEL_ID, IntegerType),
    Field(META_TRANSACTION_DATE_TIME, TimestampType),
    Field(META_ITINERARY_ID, LongType),
    Field(META_PAYMENT_METHOD_ID, IntegerType),
    Field(TRANSACTION_REFERENCE_ID, StringType),
    Field(REVERSES_LEDGER_TRANSACTION_ID, StringType),
    Field(ENTRY_UPDATED_AT, TimestampType),
    Field(LOCAL_AMOUNT, CalculatedDecimalType),
    Field(LOCAL_CURRENCY, StringType),
    Field(LOCAL_TRANSACTION_DATE, DateType),
    Field(MT_IMPORT_DATADATE, IntegerType),
    Field(MT_IMPORT_HOUR, IntegerType),
    Field(CREATED_LOG_TIME, TimestampType),
    Field(WALLET_ENTITY, StringType),
    Field(DATADATE, IntegerType),
    Field(HOUR, IntegerType),
    Field(LOCAL_DATE, IntegerType),
    Field(LOCAL_HOUR, IntegerType)
  )
}
