package com.agoda.finance.data

import com.agoda.finance.config.{WalletConfig, WalletConfigFactory, WalletEntity}
import com.agoda.finance.models.WalletContextBuilder.createWalletContext
import com.agoda.finance.models.{ExecutionArguments, WalletContext, WalletEntityLocalData}
import com.agoda.finance.test_data.{TestLedgerEntries, TestLedgerTransactions, TestWalletTransactions}
import com.agoda.finance.utils.LocalDateLocalHourConverter
import com.typesafe.config.{Config, ConfigFactory}

trait TestDataTrait {
  lazy val datadate: Int                     = 20250523
  lazy val hour: Int                         = 14
  lazy val executionArgs: ExecutionArguments = ExecutionArguments(datadate, hour)
  val loadedConfig: Config                   = ConfigFactory.load("it.conf")
  lazy val config: WalletConfig              = WalletConfigFactory.load(config = loadedConfig)
  implicit lazy val context: WalletContext   = createWalletContext(executionArgs, config)

  val walletEntityName: String                                   = "BHFS_UK"
  val walletEntity: WalletEntity                                 = context.config.allWalletEntities.filter(_.name == walletEntityName).last

  val hourLocals: Seq[WalletEntityLocalData] =
    LocalDateLocalHourConverter.walletEntityToLocalData(
      context.executionArguments.datadate,
      context.executionArguments.hour,
      context.config.allWalletEntities
    )

  val datadateUK: Int                                            = hourLocals.filter(_.walletEntity.name == walletEntityName).last.localDate
  val hourUK: Int                                                = hourLocals.filter(_.walletEntity.name == walletEntityName).last.localHour
  val hourUKSub1: Int                                            = hourLocals.filter(_.walletEntity.name == walletEntityName).last.localHour - 1

  val ledgerTransactions: Seq[TestLedgerTransactions]
  val ledgerEntries: Seq[TestLedgerEntries]
  val expectedWalletTransactions: Seq[TestWalletTransactions]
  val expectedDuplicateWalletTransactions: Seq[TestWalletTransactions]
}
