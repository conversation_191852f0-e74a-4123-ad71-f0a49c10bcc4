package com.agoda.finance.data
import com.agoda.finance.constants.WalletConstant.statusConstant.POSTED
import com.agoda.finance.test_data.{TestLedgerEntries, TestLedgerTransactions, TestWalletTransactions}

import java.sql.{Date, Timestamp}

object DuplicateProductionTestData extends TestDataTrait {
  val ledgerId1             = "duplicate-production-ledger-id"
  val ledgerId2             = ledgerId1
  val walletTransactionId1  = "5418ce7a-486b-4df7-9b92-c11f2c28c321"
  val metaTransactionId1    = "89001-FA32CB"
  val transactionReferenceId1 = "89001"
  val walletTransactionId2  = "5418ce7a-Wallet-prod-data-c11f2c28c321"
  val ledgerTransactionType = "TOPUP"
  val amountDecimal         = 11178
  val prevHour              = hour - 1

  val existingWalletTransactions: Seq[TestWalletTransactions] = Seq(
    TestWalletTransactions(
      meta_transaction_id = Some(metaTransactionId1),
      transaction_reference_id = Some(transactionReferenceId1),
      meta_wallet_transaction_id = Some(walletTransactionId1),
      wallet_transaction_id = Some(walletTransactionId2),
      ledger_transaction_id = Some(ledgerId1),
      local_currency = Some(walletEntity.localCurrency),
      wallet_entity = Some(walletEntity.name),
      posted_at = Some(Timestamp.valueOf("2025-05-23 07:41:09")),
      updated_at = Some(Timestamp.valueOf("2025-05-23 14:10:00")),
      created_at = Some(Timestamp.valueOf("2025-05-23 17:00:00")),
      ledger_id = Some(ledgerId1),
      ledger_account_id = Some(walletEntity.ledgerAccountID),
      meta_original_transaction_id = None,
      meta_wallet_account_id = Some("UK-9CEPSQ74E"),
      meta_gateway_id = Some(8),
      meta_country_code = Some("UK"),
      meta_state = Some("London"),
      meta_whitelabel_id = Some(101),
      meta_transaction_date_time = Some(Timestamp.valueOf("2025-04-17 11:47:23")),
      meta_itinerary_id = None,
      meta_payment_method_id = None,
      reverses_ledger_transaction_id = None,
      entry_updated_at = Some(Timestamp.valueOf("2025-05-23 17:00:00")),
      local_amount = Some(111.78),
      datadate = Some(datadate),
      hour = Some(prevHour),
      local_date = Some(datadateUK),
      local_hour = Some(hourUK),
      meta_kyc_id = None,
      mt_import_datadate = Some(datadateUK),
      mt_import_hour = Some(hourUKSub1),
      meta_ledger_transaction_type = Some(ledgerTransactionType),
      local_transaction_date = Some(Date.valueOf("2025-04-17"))
    )
  )

  override val ledgerTransactions: Seq[TestLedgerTransactions] = Seq(
    TestLedgerTransactions(
      created_at = Some(Timestamp.valueOf("2025-05-23 17:00:00")),
      description = Some(ledgerTransactionType),
      effective_at = Some(Timestamp.valueOf("2025-05-23 17:00:00")),
      effective_date = Some(Date.valueOf("2025-05-23")),
      external_id = None,
      id = Some(ledgerId2),
      ledger_id = Some(ledgerId2),
      ledgerable_id = None,
      ledgerable_type = None,
      live_mode = Some(true),
      metadata_json = Some(
        s"""{"state":"London","countryCode":"UK","whitelabelId":"101","transactionId":"$metaTransactionId1","maskedCcNumber":"xxxxxx-496049","walletAccountId":"UK-9CEPSQ74E","transactionDateTime":"2025-04-17T04:47:23Z","walletTransactionId":"$walletTransactionId1","ledgerTransactionType":"$ledgerTransactionType","maskedBankAccountNumber":"xxxxxx-900943","gatewayId":"8"}"""
      ),
      organization_id = Some("1"),
      posted_at = Some(Timestamp.valueOf("2025-05-23 07:41:09")),
      reverses_ledger_transaction_id = None,
      status = Some(POSTED),
      updated_at = Some(Timestamp.valueOf(s"2025-05-23 $hour:10:00")),
      datadate = Some(datadateUK),
      hour = Some(hourUKSub1)
    )
  )
  override val ledgerEntries: Seq[TestLedgerEntries] = Seq(
    TestLedgerEntries(
      id = Some("0196fb32-343f-75c6-98ec-45271ec9e5df"),
      ledger_transaction_id = Some(ledgerId2),
      ledger_account_id = Some(walletEntity.ledgerAccountID),
      live_mode = Some(true),
      status = Some(POSTED),
      direction = Some("debit"), // + pay to BHFS UK
      amount_decimal = Some(amountDecimal),
      created_at = Some(Timestamp.valueOf("2025-05-23 17:00:00")),
      updated_at = Some(Timestamp.valueOf("2025-05-23 17:00:00")),
      datadate = Some(datadateUK),
      hour = Some(hourUKSub1)
    ),
    TestLedgerEntries(
      id = Some("0296fb39-0543-72a2-baff-5bc202282aa2"),
      ledger_transaction_id = Some(ledgerId2),
      ledger_account_id = Some("6f1b2c3d-4e5f-6789-abcd-ef0123456789"), // Replace with actual ledger account ID (customer a/c id)
      live_mode = Some(true),
      status = Some(POSTED),
      direction = Some("credit"), // - from customer a/c
      amount_decimal = Some(amountDecimal),
      created_at = Some(Timestamp.valueOf("2025-05-23 17:00:00")),
      updated_at = Some(Timestamp.valueOf("2025-05-23 17:00:00")),
      datadate = Some(datadateUK),
      hour = Some(hourUKSub1)
    )
  )
  override val expectedWalletTransactions: Seq[TestWalletTransactions] = Seq(
    TestWalletTransactions(
      meta_transaction_id = Some(metaTransactionId1),
      transaction_reference_id = Some(transactionReferenceId1),
      meta_wallet_transaction_id = Some(walletTransactionId1),
      wallet_transaction_id = Some(walletTransactionId2),
      ledger_transaction_id = Some(ledgerId1),
      local_currency = Some(walletEntity.localCurrency),
      wallet_entity = Some(walletEntity.name),
      posted_at = Some(Timestamp.valueOf("2025-05-23 07:41:09")),
      updated_at = Some(Timestamp.valueOf("2025-05-23 14:10:00")),
      created_at = Some(Timestamp.valueOf("2025-05-23 17:00:00")),
      ledger_id = Some(ledgerId1),
      ledger_account_id = Some(walletEntity.ledgerAccountID),
      meta_original_transaction_id = None,
      meta_wallet_account_id = Some("UK-9CEPSQ74E"),
      meta_gateway_id = Some(8),
      meta_country_code = Some("UK"),
      meta_state = Some("London"),
      meta_whitelabel_id = Some(101),
      meta_transaction_date_time = Some(Timestamp.valueOf("2025-04-17 11:47:23")),
      meta_itinerary_id = None,
      meta_payment_method_id = None,
      reverses_ledger_transaction_id = None,
      entry_updated_at = Some(Timestamp.valueOf("2025-05-23 17:00:00")),
      local_amount = Some(111.78),
      datadate = Some(datadate),
      hour = Some(prevHour),
      local_date = Some(datadateUK),
      local_hour = Some(hourUK),
      meta_kyc_id = None,
      mt_import_datadate = Some(datadateUK),
      mt_import_hour = Some(hourUKSub1),
      meta_ledger_transaction_type = Some(ledgerTransactionType),
      local_transaction_date = Some(Date.valueOf("2025-04-17")),
      sit_id = Some("FIN-UAT-P01-001")
    )
  )
  override val expectedDuplicateWalletTransactions: Seq[TestWalletTransactions] = Seq(
    TestWalletTransactions(
      meta_transaction_id = Some(metaTransactionId1),
      transaction_reference_id = Some(transactionReferenceId1),
      meta_wallet_transaction_id = Some(walletTransactionId1),
      wallet_transaction_id = Some(walletTransactionId1),
      ledger_transaction_id = Some(ledgerId1),
      local_currency = Some(walletEntity.localCurrency),
      wallet_entity = Some(walletEntity.name),
      posted_at = Some(Timestamp.valueOf("2025-05-23 07:41:09")),
      updated_at = Some(Timestamp.valueOf("2025-05-23 14:10:00")),
      created_at = Some(Timestamp.valueOf("2025-05-23 17:00:00")),
      ledger_id = Some(ledgerId1),
      ledger_account_id = Some(walletEntity.ledgerAccountID),
      meta_original_transaction_id = None,
      meta_wallet_account_id = Some("UK-9CEPSQ74E"),
      meta_gateway_id = Some(8),
      meta_country_code = Some("UK"),
      meta_state = Some("London"),
      meta_whitelabel_id = Some(101),
      meta_transaction_date_time = Some(Timestamp.valueOf("2025-04-17 11:47:23")),
      meta_itinerary_id = None,
      meta_payment_method_id = None,
      reverses_ledger_transaction_id = None,
      entry_updated_at = Some(Timestamp.valueOf("2025-05-23 17:00:00")),
      local_amount = Some(111.78),
      datadate = Some(datadate),
      hour = Some(hour),
      local_date = Some(datadateUK),
      local_hour = Some(hourUK),
      meta_kyc_id = None,
      mt_import_datadate = Some(datadateUK),
      mt_import_hour = Some(hourUKSub1),
      meta_ledger_transaction_type = Some(ledgerTransactionType),
      local_transaction_date = Some(Date.valueOf("2025-04-17")),
      sit_id = Some("FIN-UAT-P01-001")
    )
  )
}
