package com.agoda.finance

import com.agoda.finance.common.services.combined_dashboard.FinanceCombinedDashboardService
import com.agoda.finance.config.{WalletConfig, WalletConfigFactory, WalletEntity}
import com.agoda.finance.constants.FinanceDecimalType.CalculatedDecimalType
import com.agoda.finance.constants.WalletConstant.duplicatedWalletTransactionsConstant.{PROD_DATADATE, PROD_HOUR, PROD_WALLET_ENTITY, PROD_WALLET_TRANSACTION_ID}
import com.agoda.finance.constants.WalletConstant.walletTransactionsConstant
import com.agoda.finance.constants.WalletConstant.walletTransactionsConstant.{CREATED_LOG_TIME, WALLET_TRANSACTION_ID}
import com.agoda.finance.data._
import com.agoda.finance.externaldatapipeline.core.utils.{FCDBuilderUtil, RepositoryUtiImpl, RepositoryUtil, <PERSON>hemaUtil}
import com.agoda.finance.externaldatapipeline.core.utils.{FCDBuilderUtil, RepositoryUtiImpl, RepositoryUtil}
import com.agoda.finance.models.WalletContextBuilder.createWalletContext
import com.agoda.finance.models.{ExecutionArguments, WalletContext, WalletEntityLocalData}
import com.agoda.finance.processors._
import com.agoda.finance.schema.WalletTransactionsSchema
import com.agoda.finance.test_data.{TestLedgerEntries, TestLedgerTransactions, TestWalletTransactions}
import com.agoda.finance.utils.LocalDateLocalHourConverter
import com.agoda.finance.utils.TestUtil.assertDataFrameEquals
import com.agoda.ml.spark.services.etl.{DataFrameETLExtensionLocalTest, SparkETLLocalTest}
import com.softwaremill.macwire.wire
import com.typesafe.config.{Config, ConfigFactory}
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.{col, current_timestamp, lit}
import org.apache.spark.sql.types.{IntegerType, StringType}

class WalletDataPipelineIntegrationTest extends SparkETLLocalTest with DataFrameETLExtensionLocalTest {
  import sqlContext.implicits._

  lazy val datadate: Int                     = 20250523
  lazy val hour: Int                         = 14
  lazy val executionArgs: ExecutionArguments = ExecutionArguments(datadate, hour)
  val loadedConfig: Config                   = ConfigFactory.load("it.conf")
  val input_schema: String                   = loadedConfig.getString("wallet.input_schema")
  val output_schema: String                  = loadedConfig.getString("wallet.output_schema")
  lazy val config: WalletConfig              = WalletConfigFactory.load(config = loadedConfig)
  implicit lazy val context: WalletContext   = createWalletContext(executionArgs, config)
  val hourLocals: Seq[WalletEntityLocalData] =
    LocalDateLocalHourConverter.walletEntityToLocalData(
      context.executionArguments.datadate,
      context.executionArguments.hour,
      context.config.allWalletEntities
    )
  val walletEntityName: String                                   = "BHFS_UK"
  val walletEntity: WalletEntity                                 = context.config.allWalletEntities.filter(_.name == walletEntityName).last
  val datadateUK: Int                                            = hourLocals.filter(_.walletEntity.name == walletEntityName).last.localDate
  val hourUK: Int                                                = hourLocals.filter(_.walletEntity.name == walletEntityName).last.localHour
  val hourUKSub1: Int                                            = hourLocals.filter(_.walletEntity.name == walletEntityName).last.localHour - 1
  implicit lazy val fcdInstance: FinanceCombinedDashboardService = FCDBuilderUtil.getFcdInstance(context.fcdInfo)
  implicit lazy val repositoryUtil: RepositoryUtil               = wire[RepositoryUtiImpl]
  lazy val filterProcessor: FilterProcessor                      = wire[FilterProcessor]
  lazy val transformationProcessor: TransformationProcessor      = wire[TransformationProcessor]
  lazy val deduplicationProcessor: DeduplicationProcessor        = wire[DeduplicationProcessor]
  lazy val validationProcessor: ValidationProcessor              = wire[ValidationProcessor]
  lazy val saveProcessor: SaveProcessor                          = wire[SaveProcessor]
  lazy val walletDataPipeline: WalletDataPipeline                = wire[WalletDataPipeline]

  setupAll {
    sqlContext.sql(s"CREATE DATABASE IF NOT EXISTS $input_schema")
    sqlContext.sql(s"CREATE DATABASE IF NOT EXISTS $output_schema")
  }

  teardownAll {
    sqlContext.sql(s"DROP DATABASE IF EXISTS $input_schema CASCADE")
    sqlContext.sql(s"DROP DATABASE IF EXISTS $output_schema CASCADE")
  }

  teardown {
    logger.info("Start cleaning up test data...")
    sqlContext.sql(s"DROP TABLE IF EXISTS ${config.inputConfig.ledgerEntriesTable.getTableName}")
    sqlContext.sql(s"DROP TABLE IF EXISTS ${config.inputConfig.ledgerTransactionsTable.getTableName}")
    sqlContext.sql(s"DROP TABLE IF EXISTS ${config.outputConfig.walletTransactionsTable.getTableName}")
    sqlContext.sql(s"DROP TABLE IF EXISTS ${config.outputConfig.corruptedWalletTransactionsTable.getTableName}")
    sqlContext.sql(s"DROP TABLE IF EXISTS ${config.outputConfig.duplicatedWalletTransactionsTable.getTableName}")
    logger.info("Completed cleaning up test data...")
  }

  private def runPipeline(): Unit = {
    logger.info("Running wallet data pipeline...")
    walletDataPipeline.runPipeline()
    logger.info("Finished wallet data pipeline...")
  }

  private def mockLedgerTransactions(data: Seq[TestLedgerTransactions]): Unit =
    repositoryUtil.write(context.inputSchema.ledgerTransactionsSchema, data.toDF(), needSchemaCheck = false)

  private def mockLedgerEntries(data: Seq[TestLedgerEntries]): Unit =
    repositoryUtil.write(context.inputSchema.ledgerEntriesSchema, data.toDF(),  needSchemaCheck = false)

  private def mockWalletTransactions(data: Seq[TestWalletTransactions]): Unit =
    repositoryUtil.write(context.outputSchema.walletTransactionsSchema, SchemaUtil.transformColumn(walletTransactionsSchema.fields, data.toDF().withColumn(CREATED_LOG_TIME, current_timestamp())),  needSchemaCheck = false)

  private def getWalletTransactionsData = repositoryUtil.readOrEmpty(context.outputSchema.walletTransactionsSchema)

  private def getDuplicatedWalletTransactionsData = SchemaUtil.transformColumn(walletTransactionsSchema.fields, repositoryUtil.readOrEmpty(context.outputSchema.duplicatedWalletTransactionsSchema))

  implicit class DatasetHolder(val df: DataFrame) {
    def toDecimal(colName: String): DataFrame = df.withColumn(colName, col(colName).cast(CalculatedDecimalType))
    def withProdColumns(
        prodWalletTransactionId: Option[String] = None,
        prodWalletEntity: Option[String] = None,
        prodDatadate: Option[Int] = None,
        prodHour: Option[Int] = None
    ): DataFrame =
      df.withColumn(PROD_WALLET_TRANSACTION_ID, lit(prodWalletTransactionId.orNull).cast(StringType))
        .withColumn(PROD_WALLET_ENTITY, lit(prodWalletEntity.orNull).cast(StringType))
        .withColumn(PROD_DATADATE, lit(prodDatadate.orNull).cast(IntegerType))
        .withColumn(PROD_HOUR, lit(prodHour.orNull).cast(IntegerType))
  }

  val walletTransactionsSchema: WalletTransactionsSchema = context.outputSchema.walletTransactionsSchema

  val expectedWalletTransactions: Seq[TestWalletTransactions] =
    TopupTestData.expectedWalletTransactions
      .union(WithdrawalTestData.expectedWalletTransactions)
      .union(UtilizationTestData.expectedWalletTransactions)
      .union(RefundTestData.expectedWalletTransactions)
      .union(DuplicateIncomingTestData.expectedWalletTransactions)
      .union(DuplicateProductionTestData.expectedWalletTransactions)

  lazy val testName: String =
    expectedWalletTransactions.flatMap(_.sit_id)
      .distinct.mkString(",")

  test(s"[$testName] Wallet Data Pipeline Integration Test") {
    mockWalletTransactions(DuplicateProductionTestData.existingWalletTransactions)

    mockLedgerTransactions(
      TopupTestData.ledgerTransactions ++
        WithdrawalTestData.ledgerTransactions ++
        UtilizationTestData.ledgerTransactions ++
        RefundTestData.ledgerTransactions ++
        DuplicateIncomingTestData.ledgerTransactions ++
        DuplicateProductionTestData.ledgerTransactions
    )
    mockLedgerEntries(
      TopupTestData.ledgerEntries ++
        WithdrawalTestData.ledgerEntries ++
        UtilizationTestData.ledgerEntries ++
        RefundTestData.ledgerEntries ++
        DuplicateIncomingTestData.ledgerEntries ++
        DuplicateProductionTestData.ledgerEntries
    )

    runPipeline()

    val expectedDuplicatedWalletTransaction =
      SchemaUtil.transformColumn(walletTransactionsSchema.fields,
        (TopupTestData.expectedDuplicateWalletTransactions ++
          WithdrawalTestData.expectedDuplicateWalletTransactions ++
          UtilizationTestData.expectedDuplicateWalletTransactions ++
          RefundTestData.expectedDuplicateWalletTransactions ++
          DuplicateIncomingTestData.expectedDuplicateWalletTransactions ++
          DuplicateProductionTestData.expectedDuplicateWalletTransactions
          ).toDF()
        .withProdColumns().withColumn(CREATED_LOG_TIME, current_timestamp()))

    assertDataFrameEquals(
      getDuplicatedWalletTransactionsData,
      expectedDuplicatedWalletTransaction,
      WALLET_TRANSACTION_ID, CREATED_LOG_TIME
    )

    val expectedWalletTransaction =
      SchemaUtil.transformColumn(walletTransactionsSchema.fields,
        expectedWalletTransactions.toDF().withColumn(CREATED_LOG_TIME, current_timestamp()))

    assertDataFrameEquals(getWalletTransactionsData, expectedWalletTransaction, WALLET_TRANSACTION_ID, CREATED_LOG_TIME)
  }
}
